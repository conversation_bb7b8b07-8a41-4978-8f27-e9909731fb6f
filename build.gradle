// Top-level build file where you can add configuration options common to all sub-projects/modules.
apply from: 'property/property.gradle'

ext {
    gradle.ext.api = COMPILE_SDK_VERSION
    gradle.ext.buildTools = BUILDTOOLS_VERSION
    gradle.ext.minSdkVersion = MIN_SDK_VERSION
    gradle.ext.targetSdkVersion = TARGET_SDK_VERSION
    gradle.ext.androidJar = androidJar
    gradle.ext.layoutlibJar = layoutlibJar
    kotlin_version = '1.1.3-2'
    anko_version = '0.10.1'
    mockito_android_version = '2.7.9'
    platform = "TV"
    MIN_SDK_VERSION = 17

    def sign_file = rootProject.file("sign/sign_config.property")
    sign_config = new Properties()
    sign_config.load(new FileInputStream(sign_file))
}

buildscript {
    repositories {
        maven { url 'http://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url 'http://*************:8080/nexus/content/repositories/ClientApp/' }
        maven { url "http://*************:8080/nexus/content/repositories/snapshots/" }
        maven { url "http://*************:8080/nexus/content/repositories/ccos-releases/" }
        google()
        jcenter()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:3.6.2'
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.4.32'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        maven { url 'http://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url "http://*************:8080/nexus/content/repositories/ClientApp/" }
        maven { url "http://*************:8080/nexus/content/repositories/snapshots/" }
        maven { url "http://*************:8080/nexus/content/repositories/ccos-releases/" }
        google()
        jcenter()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
