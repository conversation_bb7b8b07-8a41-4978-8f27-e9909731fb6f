<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="RoundButton">
        <!-- 背景色 -->
        <attr name="btnSolidColor" format="color"/>
        <!-- 边框色 -->
        <attr name="btnStrokeColor" format="color"/>
        <!-- 边框厚度 -->
        <attr name="btnStrokeWidth" format="dimension"/>
        <!-- 边框虚线长度 -->
        <attr name="btnStrokeDashWidth" format="dimension"/>
        <!-- 边框虚线间隙 -->
        <attr name="btnStrokeDashGap" format="dimension"/>
        <!-- 圆角半径，stadium 表示半径为 min(height,width) / 2-->
        <attr name="btnCornerRadius" format="dimension">
            <enum name="stadium" value="-1"/>
        </attr>
        <attr name="btnPressedRatio" format="float"/>
    </declare-styleable>
</resources>
