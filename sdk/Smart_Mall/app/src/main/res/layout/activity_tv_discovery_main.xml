<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.coocaa.smartmall.TvDiscoveryMainActivity">
    <ScrollView
        android:id="@+id/discover_tab_main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none"
        >
        <LinearLayout
            android:id="@+id/tab_main_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            >
            <LinearLayout
                android:id="@+id/tab_two_large_view_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                >
            </LinearLayout>
        </LinearLayout>
    </ScrollView>
</RelativeLayout>