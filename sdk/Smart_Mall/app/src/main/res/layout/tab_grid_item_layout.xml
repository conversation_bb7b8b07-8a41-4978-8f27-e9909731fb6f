<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    >
    <com.coocaa.smartmall.tabui.RoundLinearLayout
        android:id="@+id/item_view_linea_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:focusedByDefault="true"
        android:descendantFocusability="blocksDescendants"
        >
        <ImageView
            android:id="@+id/tab_item_image"
            android:layout_width="@dimen/gride_view_item_wight"
            android:layout_height="@dimen/gride_view_item_hight"
            android:layout_margin="15px"
            />
        <TextView
            android:id="@+id/tab_item_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="@dimen/item_view_text_size"
            android:text="@string/tab_item_default_text"
            android:textColor="@color/item_view_text_color"
            android:layout_marginTop="@dimen/item_view_padding_top"
            android:layout_marginBottom="@dimen/item_view_padding_bottom"
            android:layout_marginLeft="@dimen/item_view_padding_left"
            android:layout_marginRight="@dimen/item_view_padding_right"
            />
    </com.coocaa.smartmall.tabui.RoundLinearLayout>
</LinearLayout>
