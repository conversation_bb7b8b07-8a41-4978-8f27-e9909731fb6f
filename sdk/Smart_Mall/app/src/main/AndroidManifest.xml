<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.coocaa.smartmall"
    tools:ignore="MissingLeanbackLauncher">

    <uses-feature
        android:name="android.permission.camera"
        android:required="false" />
    <uses-feature
        android:name="android.software.leanback"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <application
        android:name="com.coocaa.smartmall.MainApp"
        android:allowBackup="true"
        android:banner="@color/transparent"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <activity
            android:name="com.coocaa.smartmall.TvDiscoveryMainActivity"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

        </activity>

        <activity
            android:exported="true"
            android:name="com.coocaa.smartmall.detail.DetailActivity"
                >
            <intent-filter>
                <action android:name="com.coocaa.smartmall.detail" />
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <meta-data
            android:name="SMART_HOME_PLUGIN"
            android:value="com.coocaa.smartmall.MallPlugin">
        </meta-data>
        <meta-data
            android:name="isRelease"
            android:value="${isRelease}">
        </meta-data>
    </application>

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

</manifest>