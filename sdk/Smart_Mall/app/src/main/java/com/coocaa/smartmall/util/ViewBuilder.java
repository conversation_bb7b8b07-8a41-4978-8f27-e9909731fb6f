package com.coocaa.smartmall.util;

import android.content.Context;
import android.graphics.Typeface;
import android.graphics.drawable.GradientDrawable;
import android.os.Build;
import android.support.v7.widget.RecyclerView;
import android.view.Gravity;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import com.coocaa.smartmall.R;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;

/**
 * @ProjectName: FamilyDiscovery
 * @Package: com.coocaa.familydiscovery.util
 * @ClassName: ViewBuilder
 * @Description: view创建者
 * @Author: wangyuehui
 * @CreateDate: 2020/6/16 14:20
 * @UpdateUser: 更新者
 * @UpdateDate: 2020/6/16 14:20
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class ViewBuilder {
    /**
     *
     * 详情页
     *
     * */
    public static RelativeLayout getDetailImageLayout(Context context) {

        RelativeLayout parent = new RelativeLayout(context);
        parent.setLayoutParams(new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT,
                RelativeLayout.LayoutParams.MATCH_PARENT));

        RelativeLayout rootView = new RelativeLayout(context);
        rootView.setId(R.id.smart_home_detail_rootview);
        rootView.setBackground(context.getResources().getDrawable(R.mipmap.detail_bg));
        RelativeLayout.LayoutParams rootViewRL = new RelativeLayout.LayoutParams(Util.Div(1920),Util.Div(1080));
        rootViewRL.addRule(RelativeLayout.CENTER_IN_PARENT);
        parent.addView(rootView,rootViewRL);

        RecyclerView recyclerView = new RecyclerView(context);
        recyclerView.setId(R.id.smart_home_detail_recyclerView);
        RelativeLayout.LayoutParams recyclerViewRL = new RelativeLayout.LayoutParams(Util.Div(1920),Util.Div(1080));
        rootView.addView(recyclerView,recyclerViewRL);

        ImageView imageViewLeft = new ImageView(context);
        imageViewLeft.setId(R.id.smart_home_detail_direct_left);
        imageViewLeft.setImageDrawable(context.getResources().getDrawable(R.drawable.smart_home_detail_left));
        RelativeLayout.LayoutParams imageViewLeftRL = new RelativeLayout.LayoutParams(Util.Div(66),Util.Div(66));
        imageViewLeftRL.leftMargin = Util.Div(47);
        imageViewLeftRL.addRule(RelativeLayout.CENTER_VERTICAL);
        rootView.addView(imageViewLeft,imageViewLeftRL);


        ImageView imageViewRight = new ImageView(context);
        imageViewRight.setId(R.id.smart_home_detail_direct_right);
        imageViewRight.setImageDrawable(context.getResources().getDrawable(R.drawable.smart_home_detail_right));
        RelativeLayout.LayoutParams imageViewRightRL = new RelativeLayout.LayoutParams(Util.Div(66),Util.Div(66));
        imageViewRightRL.rightMargin = Util.Div(47);
        imageViewRightRL.addRule(RelativeLayout.CENTER_VERTICAL);
        imageViewRightRL.addRule(RelativeLayout.ALIGN_PARENT_RIGHT);
        rootView.addView(imageViewRight,imageViewRightRL);

        ImageView guideImageView = new ImageView(context);
        guideImageView.setId(R.id.smart_home_detail_guide);
        guideImageView.setImageDrawable(context.getResources().getDrawable(R.drawable.smart_detail_up_guide));
        RelativeLayout.LayoutParams guideRL = new RelativeLayout.LayoutParams(Util.Div(308),
                Util.Div(64));
        guideRL.leftMargin = Util.Div(806);
        guideRL.topMargin = Util.Div(996);
        rootView.addView(guideImageView,guideRL);

        //渐变效果
        GradientDrawable aDrawable =new GradientDrawable(GradientDrawable.Orientation.TOP_BOTTOM,
                new int[]{android.R.color.transparent,R.color.black_50});

        RelativeLayout contentLayout = new RelativeLayout(context);
        contentLayout.setBackground(aDrawable);
        contentLayout.setId(R.id.smart_home_detail_content_layout);
        RelativeLayout.LayoutParams contentRL = new RelativeLayout.LayoutParams(
                RelativeLayout.LayoutParams.MATCH_PARENT,Util.Div(500));
        contentRL.topMargin = Util.Div(580);
        rootView.addView(contentLayout,contentRL);


        //边框圆角效果
        GradientDrawable BackGroudDrawable = new GradientDrawable();
        BackGroudDrawable.setShape(GradientDrawable.RECTANGLE);
        BackGroudDrawable.setCornerRadius(Util.Div(8));
        BackGroudDrawable.setColor(context.getResources().getColor(R.color.white_100));


        RelativeLayout iconLayout = new RelativeLayout(context);
        iconLayout.setBackground(BackGroudDrawable);
        iconLayout.setPadding(Util.Div(10),Util.Div(10),Util.Div(10),Util.Div(10));
        RelativeLayout.LayoutParams iconRL = new RelativeLayout.LayoutParams(Util.Div(200),
                Util.Div(200));
        iconRL.leftMargin = Util.Div(470);
        iconRL.topMargin = Util.Div(250);
        contentLayout.addView(iconLayout,iconRL);

        View iconImageView = ImageLoader.getLoader().getView(context);
        iconImageView.setId(R.id.smart_home_detail_icon);
        iconLayout.addView(iconImageView,new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT,
                RelativeLayout.LayoutParams.MATCH_PARENT));

        TextView themeTextView = new TextView(context);
        themeTextView.setText("智能壁挂空调");
        themeTextView.setTextColor(context.getResources().getColor(R.color.white_100));
        themeTextView.setTextSize(Util.Dpi(36));
        themeTextView.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        themeTextView.setSingleLine(true);
        themeTextView.setMarqueeRepeatLimit(-1);
        themeTextView.setGravity(Gravity.START);
        themeTextView.setId(R.id.smart_home_detail_theme);
        RelativeLayout.LayoutParams themeRL = new RelativeLayout.LayoutParams(Util.Div(500),
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        themeRL.topMargin = Util.Div(250);
        themeRL.leftMargin = Util.Div(690);
        contentLayout.addView(themeTextView,themeRL);

        TextView describeTextView = new TextView(context);
        describeTextView.setId(R.id.smart_home_detail_describe);
        describeTextView.setText("大1匹｜全屋互联｜超一级能效｜自清洁｜舒适柔风｜全直流变频｜静音设计");
        describeTextView.setTextColor(context.getResources().getColor(R.color.white_100));
        describeTextView.setTextSize(Util.Dpi(24));
        describeTextView.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        describeTextView.setSingleLine(false);
        describeTextView.setGravity(Gravity.START);
        RelativeLayout.LayoutParams describeTextViewRL = new RelativeLayout.LayoutParams(Util.Div(500),
                Util.Div(64));
        describeTextViewRL.topMargin = Util.Div(295);
        describeTextViewRL.leftMargin = Util.Div(690);
        contentLayout.addView(describeTextView,describeTextViewRL);

        TextView priceTextView = new TextView(context);
        priceTextView.setId(R.id.smart_home_detail_price);
        priceTextView.setText("¥ 2399.0");
        priceTextView.setTextColor(context.getResources().getColor(R.color.color_FF5E00));
        priceTextView.setTextSize(Util.Dpi(24));
        priceTextView.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        priceTextView.setSingleLine(true);
        RelativeLayout.LayoutParams priceTextViewRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        priceTextViewRL.topMargin = Util.Div(414);
        priceTextViewRL.leftMargin = Util.Div(690);
        contentLayout.addView(priceTextView,priceTextViewRL);


        RelativeLayout qrcodeLayout = new RelativeLayout(context);
        qrcodeLayout.setBackground(BackGroudDrawable);
        qrcodeLayout.setPadding(Util.Div(10),Util.Div(10),Util.Div(10),Util.Div(10));
        RelativeLayout.LayoutParams qrcodeLayoutRL = new RelativeLayout.LayoutParams(Util.Div(200),
                Util.Div(200));
        qrcodeLayoutRL.leftMargin = Util.Div(1250);
        qrcodeLayoutRL.topMargin = Util.Div(250);
        contentLayout.addView(qrcodeLayout,qrcodeLayoutRL);

        ImageView qrcodeImageView = new ImageView(context);
        qrcodeImageView.setId(R.id.smart_home_detail_qrcode);
        qrcodeLayout.addView(qrcodeImageView,new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT,
                RelativeLayout.LayoutParams.MATCH_PARENT));

        RelativeLayout innerLayout = new RelativeLayout(context);
        innerLayout.setVisibility(View.INVISIBLE);
        innerLayout.setBackground(BackGroudDrawable);
        innerLayout.setPadding(Util.Div(5),Util.Div(5),Util.Div(5),Util.Div(5));
        RelativeLayout.LayoutParams innerLayoutRL = new RelativeLayout.LayoutParams(
                Util.Div(70),Util.Div(70));
        innerLayoutRL.addRule(RelativeLayout.CENTER_IN_PARENT);
        qrcodeLayout.addView(innerLayout,innerLayoutRL);

        //
        GradientDrawable qrcodeBackGroudDrawable = new GradientDrawable();
        qrcodeBackGroudDrawable.setShape(GradientDrawable.RECTANGLE);
        qrcodeBackGroudDrawable.setCornerRadius(Util.Div(4));
        qrcodeBackGroudDrawable.setColor(context.getResources().getColor(R.color.color_FF6131));

        TextView qrcodeTips = new TextView(context);
        qrcodeTips.setBackground(qrcodeBackGroudDrawable);
        qrcodeTips.setPadding(Util.Div(6),Util.Div(2),Util.Div(6),Util.Div(5));
        qrcodeTips.setLineSpacing(Util.Div(28),0);
        qrcodeTips.setText("扫码 购买");
        qrcodeTips.setTextSize(Util.Dpi(24));
        qrcodeTips.setTextColor(context.getResources().getColor(R.color.white_100));
        qrcodeTips.setSingleLine(false);

        RelativeLayout.LayoutParams qrcodeTipsRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        innerLayout.addView(qrcodeTips,qrcodeTipsRL);

        return parent;
    }

    //发现主页推荐列表的布局
    public static RelativeLayout getDiscoverTabMainLayout(Context context){
        RelativeLayout tabParent = new RelativeLayout(context);
        tabParent.setClipChildren(false);
        tabParent.setClipToPadding(false);
        tabParent.setLayoutParams(new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT,
                RelativeLayout.LayoutParams.MATCH_PARENT));

        ScrollView tabScrollView = new ScrollView(context);
        tabScrollView.setClipChildren(false);
        tabScrollView.setClipToPadding(false);
        tabScrollView.setId(R.id.smart_home_tab_plugin_scrollview);
        RelativeLayout.LayoutParams tabScrollViewParams = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT,
                RelativeLayout.LayoutParams.MATCH_PARENT);
        tabScrollViewParams.addRule(RelativeLayout.CENTER_IN_PARENT);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            tabScrollView.setNestedScrollingEnabled(false);
        }
        tabScrollView.setVerticalScrollBarEnabled(false);
        tabParent.addView(tabScrollView , tabScrollViewParams);

        LinearLayout tabMainLayoutPlugin = new LinearLayout(context);
        tabMainLayoutPlugin.setClipChildren(false);
        tabMainLayoutPlugin.setClipToPadding(false);
        tabMainLayoutPlugin.setId(R.id.tab_main_layout_plugin);
        ScrollView.LayoutParams tabMainLayoutPluginParams = new ScrollView.LayoutParams(ScrollView.LayoutParams.MATCH_PARENT,
                ScrollView.LayoutParams.MATCH_PARENT);
        tabMainLayoutPlugin.setOrientation(LinearLayout.VERTICAL);
        tabMainLayoutPlugin.setGravity(Gravity.CENTER_VERTICAL);
        tabScrollView.addView(tabMainLayoutPlugin , tabMainLayoutPluginParams);

        LinearLayout tabTwoLargeViewLayoutPlugin = new LinearLayout(context);
        tabTwoLargeViewLayoutPlugin.setClipChildren(false);
        tabTwoLargeViewLayoutPlugin.setClipToPadding(false);
        tabTwoLargeViewLayoutPlugin.setId(R.id.tab_two_large_view_layout_plugin);
        LinearLayout.LayoutParams tabTwoLargeViewLayoutPluginParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT);
        tabTwoLargeViewLayoutPlugin.setGravity(Gravity.CENTER_HORIZONTAL);
        tabMainLayoutPlugin.addView(tabTwoLargeViewLayoutPlugin , tabTwoLargeViewLayoutPluginParams);

        return tabParent;
    }

    public static RelativeLayout getRcyclerViewItemLayout(Context context) {
        RelativeLayout parent = new RelativeLayout(context);
        parent.setLayoutParams(new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT,
                RelativeLayout.LayoutParams.MATCH_PARENT));

        RelativeLayout rootView = new RelativeLayout(context);
        RelativeLayout.LayoutParams rootViewRL = new RelativeLayout.LayoutParams(Util.Div(1920),Util.Div(1080));
        rootViewRL.addRule(RelativeLayout.CENTER_IN_PARENT);
        parent.addView(rootView,rootViewRL);

        View view = ImageLoader.getLoader().getView(context);
        view.setId(R.id.smart_home_detail_recyclerView_item);
        rootView.addView(view,new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT,
                RelativeLayout.LayoutParams.MATCH_PARENT));

        return parent;
    }

    private static GradientDrawable getFocused(Context context,int radius) {
        GradientDrawable FocusedDrawable = new GradientDrawable();
        FocusedDrawable.setShape(GradientDrawable.RECTANGLE);
        FocusedDrawable.setCornerRadius(radius);
        FocusedDrawable.setStroke(Util.Div(4),context.getResources().getColor(R.color.white_100));
        return FocusedDrawable;
    }

    public static RelativeLayout getContentCarViewLayout(Context context){
        RelativeLayout contentCarViewParent = new RelativeLayout(context);
        contentCarViewParent.setClipChildren(false);
        contentCarViewParent.setClipToPadding(false);

        RelativeLayout parent = new RelativeLayout(context);
        parent.setId(R.id.car_view_parent_id);
        parent.setBackground(getFocused(context,Util.Div(24)));
        RelativeLayout.LayoutParams parentRL = new RelativeLayout.LayoutParams(Util.Div(410+16),Util.Div(540+16));
        parentRL.addRule(RelativeLayout.CENTER_IN_PARENT);
        contentCarViewParent.addView(parent,parentRL);
        parent.setVisibility(View.INVISIBLE);

        RelativeLayout carRootView = new RelativeLayout(context);
//        carRootView.setPadding(Util.Div(8), Util.Div(8), Util.Div(8), Util.Div(8));
        RelativeLayout.LayoutParams rootViewRL = new RelativeLayout.LayoutParams(Util.Div(410),Util.Div(540));
        rootViewRL.addRule(RelativeLayout.CENTER_IN_PARENT);
        contentCarViewParent.addView(carRootView,rootViewRL);

        GradientDrawable BackDrawable = new GradientDrawable();
        BackDrawable.setShape(GradientDrawable.RECTANGLE);
        BackDrawable.setCornerRadius(Util.Div(18));
        BackDrawable.setColor(context.getResources().getColor(R.color.white_10));
        View carImageView = ImageLoader.getLoader().getView(context);
        carImageView.setBackground(BackDrawable);

        RelativeLayout.LayoutParams imageViewRL = new RelativeLayout.LayoutParams(Util.Div(410),
                Util.Div(540));
        carImageView.setId(R.id.car_view_image_view);
        carRootView.addView(carImageView , imageViewRL);

        TextView carContentTitleView = new TextView(context);
        RelativeLayout.LayoutParams carContentTitleViewRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT ,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        carContentTitleViewRL.addRule(RelativeLayout.ALIGN_PARENT_LEFT);
        carContentTitleViewRL.leftMargin = Util.Div(30);
        carContentTitleViewRL.topMargin = Util.Div(30-4);
        carContentTitleView.setId(R.id.car_view_title_text);
        carContentTitleView.setTextSize(Util.Dpi(28));
        carContentTitleView.setTextColor(context.getResources().getColor(R.color.white_100));
        carContentTitleView.setText("商品推荐");
        carContentTitleView.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        carRootView.addView(carContentTitleView , carContentTitleViewRL);

        //渐变效果
        GradientDrawable aDrawable =new GradientDrawable(GradientDrawable.Orientation.TOP_BOTTOM,
                new int[]{android.R.color.transparent,R.color.black_50});
        float[] radii = {0f,0f,0f,0f,18f,18f,18f,18f};
        aDrawable.setCornerRadii(radii);

        RelativeLayout contentLayout = new RelativeLayout(context);
        contentLayout.setBackground(aDrawable);
        contentLayout.setId(R.id.smart_home_detail_content_layout);
        RelativeLayout.LayoutParams contentRL = new RelativeLayout.LayoutParams(
                Util.Div(410),Util.Div(270));
        contentRL.topMargin = Util.Div(270);
        carRootView.addView(contentLayout,contentRL);

        TextView carContentNameView = new TextView(context);
        RelativeLayout.LayoutParams carContentNameViewRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT ,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        carContentNameViewRL.addRule(RelativeLayout.ALIGN_PARENT_LEFT);
        carContentNameViewRL.leftMargin = Util.Div(30);
        carContentNameViewRL.topMargin = Util.Div(386-4);
        carContentNameView.setId(R.id.car_view_product_name_text);
        carContentNameView.setTextSize(Util.Dpi(32));
        carContentNameView.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        carContentNameView.setTextColor(context.getResources().getColor(R.color.white_100));
        carContentNameView.setText("智能立式空调");
        carRootView.addView(carContentNameView , carContentNameViewRL);

        TextView carContentDiscriptionView = new TextView(context);
        RelativeLayout.LayoutParams carContentDiscriptionViewRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT ,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        carContentDiscriptionViewRL.leftMargin = Util.Div(30);
        carContentDiscriptionViewRL.topMargin = Util.Div(438-4);
        carContentDiscriptionView.setId(R.id.car_view_product_discription_text);
        carContentDiscriptionView.setTextSize(Util.Dpi(24));
        carContentDiscriptionView.setTextColor(context.getResources().getColor(R.color.white_60));
        carContentDiscriptionView.setText("智能互联 | 节能省电 | 自清洁");
        carRootView.addView(carContentDiscriptionView , carContentDiscriptionViewRL);

        TextView carContentPriceView = new TextView(context);
        RelativeLayout.LayoutParams carContentPriceViewRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT ,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        carContentPriceViewRL.leftMargin = Util.Div(30);
        carContentPriceViewRL.topMargin = Util.Div(482);
        carContentPriceView.setId(R.id.car_view_product_price_text);
        carContentPriceView.setTextSize(Util.Dpi(28));
        carContentPriceView.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        carContentDiscriptionView.setTextColor(context.getResources().getColor(R.color.white_60));
        carContentPriceView.setTextColor(context.getResources().getColor(R.color.color_FF5E00));
        carContentPriceView.setText("￥2399.0");
        carRootView.addView(carContentPriceView , carContentPriceViewRL);

        return contentCarViewParent;
    }

}
