package com.coocaa.smartmall.detail;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.util.Log;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import com.coocaa.smartmall.R;
import com.coocaa.smartmall.util.ThreadManager;
import com.coocaa.mylibrary.discover.DetailResult.Detail;
import com.coocaa.player.CCPlayerListener;
import com.coocaa.player.CCPlayerParameter;
import com.coocaa.player.CCPlayerState;
import com.coocaa.player.CCVideoView;
import com.coocaa.player.IMediaPlayer;
import com.coocaa.player.IjkTimedText;
import com.skyworth.ui.api.SkyWithBGLoadingView;
import com.skyworth.util.Util;

import java.util.HashMap;
import java.util.List;

/**
 * @Description: 视频详情
 * @Author: wzh
 * @CreateDate: 2020/6/18
 */
public class VideoDetailLayout extends FrameLayout {

    private CCVideoView mCCVideoView;
    private TextView mTitle;
    private ImageView mPauseIcon;
    private ImageView mKeyDownLookDetail;
    private PayQrCodeLayout mPayQrCodeLayout;
    private SkyWithBGLoadingView mLoadingView;
    private PlayEndPayLayout mPlayEndPayLayout;
    private Detail mData;

    public VideoDetailLayout(Context context) {
        super(context);
        mCCVideoView = new CCVideoView(context);
        mCCVideoView.setListener(ccPlayerListener);
        LayoutParams params = new LayoutParams(Util.Div(1920), Util.Div(1080));
        addView(mCCVideoView, params);

        //渐变效果
        GradientDrawable gradientDrawable = new GradientDrawable(GradientDrawable.Orientation.TOP_BOTTOM, new int[]{R.color.black_50, android.R.color.transparent});
        mTitle = new TextView(context);
        mTitle.setTextColor(Color.WHITE);
        mTitle.setTextSize(Util.Dpi(40));
        mTitle.getPaint().setFakeBoldText(true);
        mTitle.setPadding(Util.Div(80), Util.Div(50), 0, 0);
        mTitle.setBackground(gradientDrawable);
        params = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, Util.Div(200));
        addView(mTitle, params);

        mPauseIcon = new ImageView(context);
        mPauseIcon.setImageResource(R.mipmap.ic_video_pause);
        params = new LayoutParams(Util.Div(110), Util.Div(110));
        params.gravity = Gravity.CENTER;
        addView(mPauseIcon, params);
        mPauseIcon.setVisibility(GONE);

        mLoadingView = new SkyWithBGLoadingView(context);
        params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.gravity = Gravity.CENTER;
        addView(mLoadingView, params);

        mKeyDownLookDetail = new ImageView(context);
        mKeyDownLookDetail.setImageResource(R.drawable.smart_detail_up_guide);
        params = new LayoutParams(Util.Div(308), Util.Div(64));
        params.topMargin = Util.Div(996);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        addView(mKeyDownLookDetail, params);

        mPayQrCodeLayout = new PayQrCodeLayout(context);
        params = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.gravity = Gravity.BOTTOM | Gravity.CENTER_HORIZONTAL;
        addView(mPayQrCodeLayout, params);
        mPayQrCodeLayout.setVisibility(GONE);

        mPlayEndPayLayout = new PlayEndPayLayout(context);
        params = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        addView(mPlayEndPayLayout, params);
        mPlayEndPayLayout.setVisibility(GONE);
    }

//    @RequiresApi(api = Build.VERSION_CODES.N)
    public void setData(Detail data) {
        mData = data;
        mTitle.setText(mData.getProductName());
        List<String> videos=mData.getVideos();
        if (videos!=null&&videos.size()>0) {
            mLoadingView.showLoading();

            //todo header 未传
            mCCVideoView.load(videos.get(0), new HashMap<String, String>());
        }
        mPayQrCodeLayout.setData(data);
    }

    private CCPlayerListener ccPlayerListener = new CCPlayerListener() {
        @Override
        public void onPrepared(IMediaPlayer iMediaPlayer) {
            mLoadingView.dismissLoading();
            mCCVideoView.start();
        }

        @Override
        public void onCompletion(IMediaPlayer iMediaPlayer) {
            showPlayEndView();
        }

        @Override
        public void onBufferingUpdate(IMediaPlayer iMediaPlayer, int i) {

        }

        @Override
        public void onSeekComplete(IMediaPlayer iMediaPlayer) {

        }

        @Override
        public void onVideoSizeChanged(IMediaPlayer iMediaPlayer, int i, int i1, int i2, int i3) {

        }

        @Override
        public boolean onError(IMediaPlayer iMediaPlayer, int i, int i1, String s) {
            Log.i("MALL_VIDEO", "onError: " + s);
            mLoadingView.dismissLoading();
            Toast.makeText(getContext(), "播放失败", Toast.LENGTH_LONG).show();
            return false;
        }

        @Override
        public boolean onInfo(IMediaPlayer iMediaPlayer, CCPlayerParameter.CC_PLAY_INFO cc_play_info, String s) {
            return false;
        }

        @Override
        public void onTimedText(IMediaPlayer iMediaPlayer, IjkTimedText ijkTimedText) {

        }
    };

    public boolean onKeyDown(KeyEvent event) {
        switch (event.getKeyCode()) {
            case KeyEvent.KEYCODE_DPAD_DOWN:
                if (!isDetailShowing()) {
                    showDetail();
                    ThreadManager.getInstance().uiThread(delayHideDetailRunnable, 10000);
                    return true;
                }
                return false;
            case KeyEvent.KEYCODE_DPAD_CENTER:
                if (mPlayEndPayLayout.getVisibility() == VISIBLE) {
                    hidePlayEndView();
                    mCCVideoView.start();
                } else {
                    if (mCCVideoView.isPlaying()) {
                        showDetail();
                        mPauseIcon.setVisibility(VISIBLE);
                        mCCVideoView.pause();
                    } else {
                        hideDetail();
                        mPauseIcon.setVisibility(GONE);
                        if (mCCVideoView.getPlayerState() == CCPlayerState.STATE_PAUSED) {
                            mCCVideoView.start();
                        }
                    }
                }
                return true;
            case KeyEvent.KEYCODE_BACK:
                if (isDetailShowing()) {
                    hideDetail();
                    return true;
                }
                break;
            default:
                break;
        }
        return false;
    }

    private void showPlayEndView() {
        mPlayEndPayLayout.setData(mData);
        hideDetail();
        mPlayEndPayLayout.setVisibility(VISIBLE);
        mKeyDownLookDetail.setVisibility(GONE);
    }

    private void hidePlayEndView() {
        mPlayEndPayLayout.setVisibility(GONE);
        mKeyDownLookDetail.setVisibility(VISIBLE);
    }

    private boolean isDetailShowing() {
        return mPayQrCodeLayout.getVisibility() == VISIBLE;
    }

    private void showDetail() {
        mPayQrCodeLayout.setVisibility(VISIBLE);
        mKeyDownLookDetail.setVisibility(GONE);
    }

    private void hideDetail() {
        mPayQrCodeLayout.setVisibility(GONE);
        mKeyDownLookDetail.setVisibility(VISIBLE);
        ThreadManager.getInstance().removeUiThread(delayHideDetailRunnable);
    }

    private Runnable delayHideDetailRunnable = new Runnable() {
        @Override
        public void run() {
            hideDetail();
        }
    };

    public void onResume() {
        if (mCCVideoView.getPlayerState() == CCPlayerState.STATE_PAUSED) {
            mCCVideoView.start();
        }
    }

    public void onPause() {
        if (mCCVideoView.isPlaying() && mCCVideoView.getPlayerState() == CCPlayerState.STATE_PLAYING) {
            mCCVideoView.pause();
        }
    }

    public void onDestroy() {
        ThreadManager.getInstance().removeUiThread(delayHideDetailRunnable);
        mCCVideoView.release();
        mLoadingView.dismissLoading();
    }
}
