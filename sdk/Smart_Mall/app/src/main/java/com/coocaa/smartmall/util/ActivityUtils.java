package com.coocaa.smartmall.util;

import android.content.Context;
import android.content.Intent;

import com.coocaa.smartmall.detail.DetailActivity;
import com.coocaa.mylibrary.discover.RecommandResult.Recommand;

public class ActivityUtils {
    public static void startDetail(Context context,  Recommand data){
        Intent intent = new Intent(context, DetailActivity.class);
//        intent.putExtra("display_type", data.get);
        intent.putExtra("product_id", String.valueOf(data.getProductId()));
        intent.putExtra("image_url", data.getProductImage());
        context.startActivity(intent);
    }
}
