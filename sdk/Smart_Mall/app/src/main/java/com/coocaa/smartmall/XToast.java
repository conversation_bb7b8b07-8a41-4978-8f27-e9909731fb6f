package com.coocaa.smartmall;

import android.content.Context;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.skyworth.ui.api.SkyToastView;
import com.skyworth.util.Util;

/**
 * 不会重叠的Toast弹窗
 * <p>
 * Created by wzh on 2018-07-14.
 */
public class XToast {

    private static SkyToastView toastView;

    public static void showToast(Context context, String txt, SkyToastView.ShowTime showTime) {
        try {
            if (toastView == null) {
                toastView = new SkyToastView(context);
            }
            toastView.setTostString(txt);
            FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT,
                    Gravity.BOTTOM | Gravity.CENTER_HORIZONTAL);
            params.bottomMargin = Util.Div(120);
            toastView.showToast(showTime, params);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void showToast(Context context, String txt) {
        showToast(context, txt, SkyToastView.ShowTime.SHOTTIME);
    }
}
