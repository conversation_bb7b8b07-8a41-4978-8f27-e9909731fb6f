package com.coocaa.smartmall.detail.adpter;

import android.content.Context;
import android.support.v7.widget.RecyclerView;
import android.view.View;
import android.view.ViewGroup;

import com.coocaa.smartmall.R;
import com.coocaa.smartmall.util.ImageUtils;
import com.coocaa.smartmall.util.ViewBuilder;
import com.skyworth.util.Util;

import java.util.List;

/**
 *
 *
 */
public class AutoAdapter extends RecyclerView.Adapter<AutoAdapter.AdViewHolder> {
    private Context mContext;
    private long between = 0;
    private List<String> images;
    public AutoAdapter(Context context, List<String> images) {
        mContext = context;
        this.images = images;
    }


    @Override
    public AdViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {

        return new AdViewHolder(ViewBuilder.getRcyclerViewItemLayout(mContext));
    }

    @Override
    public void onBindViewHolder(AdViewHolder holder,int position) {

        if (images.size() > 0) {
            int imagesPosition = position%images.size();
            ImageUtils.loadImage(holder.view,images.get(imagesPosition),Util.Div(1920), Util.Div(1080));
        }

    }


    @Override
    public int getItemCount() {

        return Integer.MAX_VALUE;
    }

    class AdViewHolder extends RecyclerView.ViewHolder {
        public View view;

        private AdViewHolder(View itemView) {
            super(itemView);
            view = itemView.findViewById(R.id.smart_home_detail_recyclerView_item);
        }
    }

    @Override
    public void onViewRecycled(AdViewHolder holder) {
        super.onViewRecycled(holder);
    }
}
