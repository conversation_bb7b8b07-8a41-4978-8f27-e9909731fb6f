package com.coocaa.smartmall.detail;

import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.support.annotation.RequiresApi;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;

import com.coocaa.smartmall.tabui.BaseActivity;
import com.coocaa.mylibrary.api.HttpApi;
import com.coocaa.mylibrary.api.HttpSubscribe;
import com.coocaa.mylibrary.api.HttpThrowable;
import com.coocaa.mylibrary.discover.DetailResult.Detail;
import com.coocaa.mylibrary.discover.DetailResult;

import java.util.List;


/**
 * @ProjectName: FamilyDiscovery
 * @Package: com.coocaa.familydiscovery.detail
 * @ClassName: DetailActivity
 * @Description: 详情页
 * @Author: wangyuehui
 * @CreateDate: 2020/6/16 16:43
 * @UpdateUser: 更新者
 * @UpdateDate: 2020/6/16 16:43
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class DetailActivity extends BaseActivity implements HttpSubscribe<DetailResult> {

    private ImageDetailLayout mImageDetailLayout;
    private VideoDetailLayout mVideoLayout;

    private String mProductId = "";
    private String mSmallImage = "";

    private Detail mProductDetailInfo;


    @RequiresApi(api = Build.VERSION_CODES.N)
    @Override
    protected void onCreate(Bundle savedInstanceState) {
//        //去掉标题栏
//        this.requestWindowFeature(Window.FEATURE_NO_TITLE);
//        //设置全屏
//        this.getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);
        super.onCreate(savedInstanceState);
//        Util.instence(getApplicationContext());
        Intent disPlayIntent = getIntent();
        mProductId = disPlayIntent.getStringExtra("product_id");
        mSmallImage = disPlayIntent.getStringExtra("image_url");
        if(!TextUtils.isEmpty(mSmallImage)){
            mSmallImage.replace("\\", "");
        }
        HttpApi.getInstance().getDetail(DetailActivity.this, mProductId);
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN) {
            boolean onKeyDown = false;
            if (mVideoLayout != null) {
                onKeyDown = mVideoLayout.onKeyDown(event);
            }
            if (!onKeyDown) {
                return super.dispatchKeyEvent(event);

            } else {
                return true;
            }
        }
        return super.dispatchKeyEvent(event);
    }

    private void showImageDetailLayout() {

        mImageDetailLayout = new ImageDetailLayout(this);
        setContentView(mImageDetailLayout);
        Log.i("OKHTTP-LOG", "howImageDetailLayout()--->product_id = " + mProductDetailInfo.getProductId() + "   product_name = " + mProductDetailInfo.getProductName()
                + "    product_price = " + mProductDetailInfo.getPrice());
        mImageDetailLayout.createView(mProductDetailInfo);
    }

    //    @RequiresApi(api = Build.VERSION_CODES.N)
    private void showVideoDetailLayout() {
        mVideoLayout = new VideoDetailLayout(this);
        setContentView(mVideoLayout);
        mVideoLayout.setData(mProductDetailInfo);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (mVideoLayout != null) {
            mVideoLayout.onResume();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (mVideoLayout != null) {
            mVideoLayout.onPause();
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        if (mImageDetailLayout != null) {
            mImageDetailLayout.onStop();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mVideoLayout != null) {
            mVideoLayout.onDestroy();
        }

    }

    @Override
    public void onSuccess(DetailResult result) {

        if (result != null && result.getProduct_detail() != null) {
            mProductDetailInfo = result.getProduct_detail();

            if(TextUtils.isEmpty(mSmallImage)){
                if(mProductDetailInfo.getDisplayType().equals("image")){
                    List<String> images=mProductDetailInfo.getImages();
                    if(images!=null&&images.size()>0){
                        mProductDetailInfo.setImageUrl(images.get(0));
                    }
                }else{
                    mProductDetailInfo.setImageUrl("http://update-nj.skyworth-cloud.com/nj_apk/weixin/20200718103937nzodgs.png");
                }
            }else{
                mProductDetailInfo.setImageUrl(mSmallImage);
            }
            if(!TextUtils.isEmpty(mProductDetailInfo.getLittle_image())){
                mProductDetailInfo.setImageUrl(mProductDetailInfo.getLittle_image());
            }
            String display_type = mProductDetailInfo.getDisplayType();
            Log.d("OKHTTP-LOG", Thread.currentThread().getName());
            if (Detail.DISPLAY_TYPE_IMAGE.equals(display_type)) {//
                showImageDetailLayout();
            } else if (Detail.DISPLAY_TYPE_VIDEO.equals(display_type)) {
                showVideoDetailLayout();
            }
        }
    }

    @Override
    public void onError(HttpThrowable error) {
        Log.d("OKHTTP-LOG", "getErrCode = " + error.getErrCode() + "     getErrMsg = " + error.getErrMsg());
    }
}
