package com.coocaa.smartmall.tabui;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.support.annotation.Nullable;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.widget.LinearLayout;

public class RoundLinearLayout extends LinearLayout implements View.OnFocusChangeListener, CustomViewInterface, View.OnHoverListener {
    private ColorStateList mSolidColor;
    private int mCornerRadius;
    CustomFocusedChanged focusedChanged;
    public RoundLinearLayout(Context context) {
        super(context);
        init();
    }

    public RoundLinearLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    public RoundLinearLayout(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init();
    }

    public RoundLinearLayout(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
//        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.RoundButton);
//        float pressedRatio = a.getFloat(R.styleable.RoundButton_btnPressedRatio, 0.80f);
//        mCornerRadius = a.getLayoutDimension(R.styleable.RoundButton_btnCornerRadius, getResources().getDimensionPixelSize(R.dimen.radius_default));
//        mSolidColor = a.getColorStateList(R.styleable.RoundButton_btnSolidColor);
//        int strokeColor = a.getColor(R.styleable.RoundButton_btnStrokeColor, Color.GRAY);
//        int strokeWidth = a.getDimensionPixelSize(R.styleable.RoundButton_btnStrokeWidth, 0);
//        int strokeDashWidth = a.getDimensionPixelSize(R.styleable.RoundButton_btnStrokeDashWidth, 0);
//        int strokeDashGap = a.getDimensionPixelSize(R.styleable.RoundButton_btnStrokeDashGap, 0);
//        a.recycle();
//        RoundDrawable rd = new RoundDrawable(mCornerRadius == -1);
//        rd.setCornerRadius(mCornerRadius == -1 ? 0 : mCornerRadius);
//        rd.setStroke(strokeWidth, strokeColor, strokeDashWidth, strokeDashGap);
//
//        if (mSolidColor == null) {
            mSolidColor = ColorStateList.valueOf(Color.GRAY);
//        }
//        if (mSolidColor.isStateful()) {
//            rd.setSolidColors(mSolidColor);
//        } else if (pressedRatio > 0.0001f) {
//            rd.setSolidColors(csl(mSolidColor.getDefaultColor(), pressedRatio));
//        } else {
//            rd.setColor(mSolidColor.getDefaultColor());
//        }
//        setBackground(rd);
    }

    public void setSolidColor(int color) {
        RoundDrawable rd = new RoundDrawable(mCornerRadius == -1);
        rd.setCornerRadius(mCornerRadius == -1 ? 0 : mCornerRadius);
        mSolidColor = ColorStateList.valueOf(color);
        if (mSolidColor.isStateful()) {
            rd.setSolidColors(mSolidColor);
        }  else {
            rd.setColor(mSolidColor.getDefaultColor());
        }
        setBackground(rd);
    }

    int darker(int color, float ratio) {
        color = (color >> 24) == 0 ? 0x22808080 : color;
        float[] hsv = new float[3];
        Color.colorToHSV(color, hsv);
        hsv[2] *= ratio;
        return Color.HSVToColor(color >> 24, hsv);
    }

    ColorStateList csl(int normal, float ratio) {
        //        int disabled = greyer(normal);
        int pressed = darker(normal, ratio);
        int[][] states = new int[][]{{android.R.attr.state_pressed}, {}};
        int[] colors = new int[]{pressed, normal};
        return new ColorStateList(states, colors);
    }

    @Override
    public void onFocusChange(View view, boolean hasFocus) {
        Log.d("RoundLinearLayout" , "onFocusChange() LinearLayout view = "+view.getTransitionName()+"   hasFocus = "+hasFocus);
        if (focusedChanged != null) {
            //focusedChanged.onFocusChange(view, hasFocus);//外部接口调用
        }
//        DisplayUtil.setViewAnimator(view, hasFocus);//放大缩小动画
    }

    @Override
    public boolean onHover(View view, MotionEvent event) {
        Log.d("RoundLinearLayout" , "onHover() LinearLayout view = "+view.getTransitionName());
//        if (event.getAction() == MotionEvent.ACTION_HOVER_ENTER) {
//            DisplayUtil.setViewAnimator(view, true);
//        } else if (event.getAction() == MotionEvent.ACTION_HOVER_EXIT) {
//            DisplayUtil.setViewAnimator(view, false);
//        }
        return false;
    }

    @Override
    public void init() {
        Log.d("RoundLinearLayout" , "init() LinearLayout view = ");
        this.setOnFocusChangeListener(this);
        this.setOnHoverListener(this);
    }

    @Override
    public void setFocusedChanged(CustomFocusedChanged focusedChanged) {
        this.focusedChanged = focusedChanged;
    }
    @Override
    public boolean isFocused() {
        return true;
    }
}
