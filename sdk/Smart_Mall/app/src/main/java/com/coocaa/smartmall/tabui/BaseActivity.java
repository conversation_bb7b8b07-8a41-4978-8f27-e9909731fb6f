package com.coocaa.smartmall.tabui;

import android.os.Bundle;
import android.support.v7.app.AppCompatActivity;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;


import com.skyworth.ui.blurbg.BlurBgLayout;
import com.skyworth.util.Util;

/**
 * Created by <PERSON>wen<PERSON>eng on 2020/6/3.
 */

public class BaseActivity extends AppCompatActivity {

    protected FrameLayout mMainLayout;
    private BlurBgLayout mBlurBg;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //去掉标题栏
        this.requestWindowFeature(Window.FEATURE_NO_TITLE);
        //设置全屏
        this.getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);
        mMainLayout = new FrameLayout(this);
        mMainLayout.setClipChildren(false);
        mMainLayout.setClipToPadding(false);
        setContentView(mMainLayout);
        initBackground();
    }

    /**
     * 默认背景
     */
    private void initBackground() {
        mBlurBg = new BlurBgLayout(this);
        mBlurBg.setPageType(BlurBgLayout.PAGETYPE.SECONDE_PAGE);
        mMainLayout.addView(mBlurBg, new FrameLayout.LayoutParams(Util.Div(1920), Util.Div(1080)));
    }
}
