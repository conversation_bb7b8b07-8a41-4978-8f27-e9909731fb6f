package com.coocaa.smartmall.tabadepter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.coocaa.smartmall.R;
import com.coocaa.smartmall.tabui.CustomFocusedChanged;
import com.coocaa.smartmall.tabui.RoundLinearLayout;
import com.coocaa.mylibrary.discover.RecommandResult.Recommand;
import java.util.List;


public class TabMainAdapter extends BaseAdapter {
    private Context mContext;
    private List<Recommand> allProducts;
    private CustomFocusedChanged mCustomFocusedChanged;

    public TabMainAdapter(Context context , List<Recommand> allDisCoverProduct , CustomFocusedChanged customFocusedChanged){
        mContext = context;
        allProducts = allDisCoverProduct;
        mCustomFocusedChanged = customFocusedChanged;
    }

    @Override
    public int getCount() {
        return allProducts.size();
    }

    @Override
    public Object getItem(int position) {
        return allProducts.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder itemViewHolder;
        if(convertView == null){
            convertView = LayoutInflater.from(mContext).inflate(R.layout.tab_grid_item_layout , null);
            itemViewHolder = new ViewHolder();
            itemViewHolder.mItemImage = (ImageView)convertView.findViewById(R.id.tab_item_image);
            itemViewHolder.mItemText = (TextView)convertView.findViewById(R.id.tab_item_text);
            itemViewHolder.mItmeRoundLinearLayout = (RoundLinearLayout)convertView.findViewById(R.id.item_view_linea_layout);
            convertView.setTag(itemViewHolder);
        }else{
            itemViewHolder = (ViewHolder)convertView.getTag();
        }
        itemViewHolder.mItmeRoundLinearLayout.setFocusedChanged(mCustomFocusedChanged);
        if(allProducts!=null && allProducts.size()>0){
            itemViewHolder.mItemText.setText(allProducts.get(position).getProductDescription());
        }
        return convertView;
    }

    public static String dealWithHttpStr(String originalHttpStr){
        return originalHttpStr.replace("http://","https://");
    }

    class ViewHolder{
        ImageView mItemImage;
        TextView mItemText;
        RoundLinearLayout mItmeRoundLinearLayout;
    }
}
