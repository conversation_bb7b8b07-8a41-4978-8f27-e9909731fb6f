package com.coocaa.smartmall;

import android.content.Context;
import android.view.View;

import com.coocaa.smartmall.tabui.DiscoverTabMainPluglayout;
import com.coocaa.smartmall.tabui.recyview.CarContentViewLayout;
import com.coocaa.smartmall.tabui.recyview.XThemeUtils;
import com.skyworth.smarthome_tv.smarthomeplugininterface.BaseSmartHomePlugin;
import com.skyworth.smarthome_tv.smarthomeplugininterface.IViewBoundaryCallback;
import com.skyworth.smarthome_tv.smarthomeplugininterface.LifeCycleCallback;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;

import static com.coocaa.mylibrary.discover.SmartMallRequestConfig.initPaths;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/19
 */
public class MallPlugin extends BaseSmartHomePlugin implements LifeCycleCallback {
    private Context mContext;

    @Override
    public void onContextSet(Context pluginContext) {
        mContext = pluginContext;
    }

    @Override
    public void onPluginInit() {
        try {
            //初始化
            Util.instence(mContext);
            initPaths(mContext);
            XThemeUtils.init(mContext);
            ImageLoader.getLoader().init(mContext);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public View getContentCardView(IViewBoundaryCallback callback) {
        CarContentViewLayout carContentViewLayout = new CarContentViewLayout(mContext , callback);
        return carContentViewLayout;
    }

    @Override
    public View getPanelView(IViewBoundaryCallback callback) {
        //发现版面View
        DiscoverTabMainPluglayout tabMainPluglayout = new DiscoverTabMainPluglayout(mContext, callback);
        tabMainPluglayout.createDiscoverTabPluginLayout();
        return tabMainPluglayout;
    }

    @Override
    public LifeCycleCallback getContentLifeCycleCallback() {
        return this;
    }

    @Override
    public LifeCycleCallback getPanelLifeCycleCallback() {
        return this;
    }

    @Override
    public void onResume() {

    }

    @Override
    public void onPause() {

    }

    @Override
    public void onStop() {

    }

    @Override
    public void onDestroy() {

    }
}
