package com.coocaa.smartmall.util;

import android.graphics.Bitmap;
import android.text.TextUtils;
import android.widget.ImageView;
import android.widget.TextView;

import com.coocaa.mylibrary.discover.DetailResult.Detail;
import com.skyworth.util.Util;

import java.util.List;

public class StringUtils {
    public static String getTags(List<String> data){
        StringBuffer sb = new StringBuffer();
        if (EmptyUtils.isNotEmpty(data)) {
            for (int i = 0; i < data.size(); i++) {
                sb.append(data.get(i));
                if (i != data.size() - 1) {
                    sb.append(" | ");
                }

            }
        }
        return sb.toString();
    }
    public static String getQRString(Detail data){
        StringBuffer sb=new StringBuffer();
                 sb.append("product_id = ").append(data.getProductId())
                .append("  image_url = ").append(data.getImageUrl())
                .append("  product_name = ").append(data.getProductName())
                .append("  product_price = ").append(data.getPrice());
       return sb.toString();
    }
    public static void initQRInfo(TextView name, TextView price, TextView tag, ImageView qrcodeImageView, Detail data){
        //设置主题
        if (!TextUtils.isEmpty(data.getProductName())) {
            name.setText(data.getProductName());
        }
        //设置价格
        if (!TextUtils.isEmpty(data.getPrice())) {
            price.setText(data.getPrice());
        }
        //设置标志

        tag.setText(StringUtils.getTags(data.getTags()));
        //设置二维码
        try {
            //todo 二维码内容没有字段
            final Bitmap bitmap = QRUtils.createQRImage(data.getQrcode_url(), Util.Div(430), Util.Div(430), null);
            if (bitmap != null) {
                qrcodeImageView.setImageBitmap(bitmap);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
