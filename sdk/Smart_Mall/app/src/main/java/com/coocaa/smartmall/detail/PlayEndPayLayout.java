package com.coocaa.smartmall.detail;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.graphics.drawable.GradientDrawable;
import android.view.Gravity;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.coocaa.smartmall.R;
import com.coocaa.smartmall.util.StringUtils;
import com.coocaa.smartmall.util.UiUtil;
import com.coocaa.mylibrary.discover.DetailResult.Detail;
import com.skyworth.util.Util;

/**
 * @Description: 播放完成后的支付二维码View
 * @Author: wzh
 * @CreateDate: 2020/6/18
 */
public class PlayEndPayLayout extends FrameLayout {

    private FrameLayout mCenterLayout;
    private TextView themeTextView;
    private TextView describeTextView;
    private TextView priceTextView;
    private ImageView qrcodeImageView;
    private Button mLookAgainBtn;

    public PlayEndPayLayout(Context context) {
        super(context);
        GradientDrawable bg = new GradientDrawable(GradientDrawable.Orientation.TOP_BOTTOM, new int[]{R.color.black_10, R.color.black_50});
        setBackground(bg);
    }

    private void initView() {
        mCenterLayout = new FrameLayout(getContext());
        mCenterLayout.setBackground(UiUtil.getDrawable(getResources().getColor(R.color.black_80), 0, 0, Util.Div(16)));
        LayoutParams params = new LayoutParams(Util.Div(860), Util.Div(380));
        params.topMargin = Util.Div(330);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        addView(mCenterLayout, params);

        themeTextView = new TextView(getContext());
        themeTextView.setTextColor(getContext().getResources().getColor(R.color.white_100));
        themeTextView.setTextSize(Util.Dpi(40));
        themeTextView.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        themeTextView.setSingleLine(false);
        themeTextView.setGravity(Gravity.START);
        LayoutParams themeRL = new LayoutParams(Util.Div(450), Util.Div(96));
        themeRL.topMargin = Util.Div(40);
        themeRL.leftMargin = Util.Div(50);
        mCenterLayout.addView(themeTextView, themeRL);

        describeTextView = new TextView(getContext());
        describeTextView.setTextColor(getContext().getResources().getColor(R.color.white_100));
        describeTextView.setTextSize(Util.Dpi(24));
        describeTextView.setSingleLine(false);
        describeTextView.setGravity(Gravity.START);
        LayoutParams describeTextViewRL = new LayoutParams(Util.Div(450), Util.Div(64));
        describeTextViewRL.topMargin = Util.Div(166);
        describeTextViewRL.leftMargin = Util.Div(50);
        mCenterLayout.addView(describeTextView, describeTextViewRL);

        priceTextView = new TextView(getContext());
        priceTextView.setTextColor(getContext().getResources().getColor(R.color.color_FF5E00));
        priceTextView.setTextSize(Util.Dpi(40));
        priceTextView.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        priceTextView.setSingleLine(true);
        LayoutParams priceTextViewRL = new LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        priceTextViewRL.topMargin = Util.Div(290);
        priceTextViewRL.leftMargin = Util.Div(50);
        mCenterLayout.addView(priceTextView, priceTextViewRL);

        //边框圆角效果
        GradientDrawable backGroudDrawable = new GradientDrawable();
        backGroudDrawable.setShape(GradientDrawable.RECTANGLE);
        backGroudDrawable.setCornerRadius(Util.Div(8));
        backGroudDrawable.setColor(getContext().getResources().getColor(R.color.white_100));

        RelativeLayout qrcodeLayout = new RelativeLayout(getContext());
        qrcodeLayout.setBackground(backGroudDrawable);
        qrcodeLayout.setPadding(Util.Div(10), Util.Div(10), Util.Div(10), Util.Div(10));
        LayoutParams qrcodeLayoutRL = new LayoutParams(Util.Div(280), Util.Div(280));
        qrcodeLayoutRL.leftMargin = Util.Div(530);
        qrcodeLayoutRL.topMargin = Util.Div(50);
        mCenterLayout.addView(qrcodeLayout, qrcodeLayoutRL);

        qrcodeImageView = new ImageView(getContext());
        qrcodeLayout.addView(qrcodeImageView, new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT, RelativeLayout.LayoutParams.MATCH_PARENT));

        RelativeLayout innerLayout = new RelativeLayout(getContext());
        innerLayout.setBackground(backGroudDrawable);
        innerLayout.setPadding(Util.Div(5), Util.Div(5), Util.Div(5), Util.Div(5));
        RelativeLayout.LayoutParams innerLayoutRL = new RelativeLayout.LayoutParams(Util.Div(70), Util.Div(70));
        innerLayoutRL.addRule(RelativeLayout.CENTER_IN_PARENT);
        qrcodeLayout.addView(innerLayout, innerLayoutRL);

        GradientDrawable qrcodeBackGroudDrawable = new GradientDrawable();
        qrcodeBackGroudDrawable.setShape(GradientDrawable.RECTANGLE);
        qrcodeBackGroudDrawable.setCornerRadius(Util.Div(4));
        qrcodeBackGroudDrawable.setColor(getContext().getResources().getColor(R.color.color_FF6131));

        TextView qrcodeTips = new TextView(getContext());
        qrcodeTips.setBackground(qrcodeBackGroudDrawable);
        qrcodeTips.setPadding(Util.Div(6), Util.Div(2), Util.Div(6), Util.Div(5));
        qrcodeTips.setLineSpacing(Util.Div(28), 0);
        qrcodeTips.setText("扫码 购买");
        qrcodeTips.setTextSize(Util.Dpi(24));
        qrcodeTips.setTextColor(getContext().getResources().getColor(R.color.white_100));
        qrcodeTips.setSingleLine(false);

        RelativeLayout.LayoutParams qrcodeTipsRL = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.WRAP_CONTENT,
                RelativeLayout.LayoutParams.WRAP_CONTENT);
        innerLayout.addView(qrcodeTips, qrcodeTipsRL);

        View btnFocusView = new View(getContext());
        btnFocusView.setBackground(UiUtil.getDrawable(0, Color.WHITE, Util.Div(4), Util.Div(53)));
        params = new LayoutParams(Util.Div(308), Util.Div(88));
        params.topMargin = Util.Div(786);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        addView(btnFocusView, params);

        mLookAgainBtn = new Button(getContext());
        mLookAgainBtn.setTextColor(Color.BLACK);
        mLookAgainBtn.setTextSize(Util.Dpi(28));
        mLookAgainBtn.setGravity(Gravity.CENTER);
        mLookAgainBtn.setText("再看一遍");
        mLookAgainBtn.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        mLookAgainBtn.setBackground(UiUtil.getDrawable(Color.WHITE, 0, 0, Util.Div(40)));
        params = new LayoutParams(Util.Div(292), Util.Div(72));
        params.topMargin = Util.Div(794);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        addView(mLookAgainBtn, params);
        mLookAgainBtn.requestFocus();
    }

//    @RequiresApi(api = Build.VERSION_CODES.N)
    public void setData(Detail data) {
        if (mCenterLayout != null) {
            return;
        }
        initView();
        StringUtils.initQRInfo(themeTextView,priceTextView,describeTextView,qrcodeImageView,data);
    }
}
