package com.coocaa.smartmall.util;

import android.net.Uri;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;

import com.skyworth.util.imageloader.ImageLoader;

public class ImageUtils {
    public static void loadImage(View target,String url,int w,int h){
         loadImage(target,url,w,h,0,0,0,0);
    }
    public static void loadImage(View target,String url,int w,int h,int l,int top,int r,int b){
        if(!TextUtils.isEmpty(url)&&target!=null){
            ImageLoader.getLoader()
                    .with(target.getContext())
                    .load(Uri.parse(url))
                    .resize(w, h)
                    .setScaleType(ImageView.ScaleType.FIT_XY)
                    .setLeftBottomCorner(l)
                    .setLeftTopCorner(top)
                    .setRightBottomCorner(r)
                    .setRightTopCorner(b)
                    .into(target);
        }
    }
}
