apply plugin: 'com.android.application'

android {
    compileSdkVersion 29
    buildToolsVersion '30.0.1'

    defaultConfig {
        applicationId "com.coocaa.smartmall"
        minSdkVersion 17
        targetSdkVersion 27
        versionCode 1
        versionName "1.0"
        manifestPlaceholders = [isRelease: false]
        ndk {
            abiFilters "armeabi-v7a"
        }
    }

    signingConfigs {
        release {
            keyAlias sign_config["keystore.alias"]
            keyPassword sign_config["keystore.alias_password"]
            storeFile file("../${sign_config["keystore.path"]}")
            storePassword sign_config["keystore.password"]
        }
    }


    buildTypes {
        debug {
            minifyEnabled false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

        }

        release {
            minifyEnabled false
            signingConfig signingConfigs.release
            manifestPlaceholders = [isRelease: true]
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    sourceSets {
        main {
            res {
                srcDirs 'src/main/res', 'src/main/res/drawable'
            }
        }
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])

    implementation 'swaiotos.support:video:+'
    implementation 'swaiotos.ui:common:+'
    implementation 'swaiotos.ui:imageloader:+'
    implementation 'swaiotos.support:video:+'
    api 'swaiotos:sal:+'
    //noinspection GradleCompatible
    implementation 'com.android.support:support-v4:28.0.0'
    //noinspection GradleCompatible
    implementation 'com.android.support:recyclerview-v7:24.0.0-alpha1'
    implementation 'com.alibaba:fastjson:1.1.54.android'
    //noinspection GradleCompatible
    implementation 'com.android.support:appcompat-v7:28.0.0'
    implementation project(':mylibrary')
    compileOnly project(':SmartHomePluginInterface')
}