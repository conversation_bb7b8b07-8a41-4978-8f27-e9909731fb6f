package com.coocaa.mylibrary;


import com.coocaa.mylibrary.api.HttpManager;
import com.coocaa.mylibrary.discover.DetailResult;
import com.coocaa.mylibrary.discover.RecommandResult;
import com.coocaa.mylibrary.discover.SmartMallRequestConfig;

import java.util.Map;

import retrofit2.Call;

/**
 */
public class SmartMallRequestService extends HttpManager<ISmartMallRequestMethod> {
    private static final String REQUEST_CHANNEL = "tv";
    private static final String REQUEST_OUTPUT_FORMAT = "JSON";

    public  static  final SmartMallRequestService SERVICE = new SmartMallRequestService();
    @Override
    protected Class<ISmartMallRequestMethod> getServiceClass() {
        return ISmartMallRequestMethod.class;
    }

    @Override
    protected Map<String, String> getHeaders() {
        return SmartMallRequestConfig.getInstance().mAllDefaultHeaders;
    }

    @Override
    protected String getBaseUrl() {
        return SmartMallRequestConfig.TAB_PRODUCT_BASE_URL;
    }

    public Call<RecommandResult> getRecommand(){
        return getHttpService().getRecommend();
    }

    public Call<DetailResult> getDetail(String product_id){
        return getHttpService().getDetail( product_id );
    }
}
