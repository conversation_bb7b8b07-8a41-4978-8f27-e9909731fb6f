package com.coocaa.mylibrary;

import com.coocaa.mylibrary.discover.DetailResult;
import com.coocaa.mylibrary.discover.RecommandResult;

import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Query;

public interface ISmartMallRequestMethod {
    @GET("shop/index_recommend")//"/api/awesome/recommends"
    Call<RecommandResult> getRecommend();

    @GET("shop/tv_product_detail")//"/api/awesome/product_details"
    Call<DetailResult> getDetail(@Query("product_id") String product_id );

}
