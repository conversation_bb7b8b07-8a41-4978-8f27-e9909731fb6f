// Top-level build file where you can add configuration options common to all sub-projects/modules.
apply from: 'swaiots_property/property.gradle'

ext{
    def sign_file = rootProject.file("sign/sign_config.property")
    sign_config = new Properties()
    sign_config.load(new FileInputStream(sign_file))
}

buildscript {
    repositories {
        google()
        //jcenter()
        maven {url 'http://maven.aliyun.com/nexus/content/groups/public/'}
        maven {url "http://*************:8080/nexus/content/repositories/ClientApp/"}
        maven{ url'http://maven.aliyun.com/nexus/content/repositories/jcenter'}
    }
    dependencies {
        classpath "com.android.tools.build:gradle:3.6.3"
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        //jcenter()
        maven {url 'http://maven.aliyun.com/nexus/content/groups/public/'}
        maven {url "http://*************:8080/nexus/content/repositories/ClientApp/"}
        maven{ url'http://maven.aliyun.com/nexus/content/repositories/jcenter'}
    }
}

tasks.withType(JavaCompile) {
    options.encoding = "UTF-8"
}

task clean(type: Delete) {
    delete rootProject.buildDir
}