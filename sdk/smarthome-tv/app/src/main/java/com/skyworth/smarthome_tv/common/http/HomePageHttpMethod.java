package com.skyworth.smarthome_tv.common.http;

import com.coocaa.operate6_0.model.Container;
import com.coocaa.operate6_0.model.MainData;
import com.skyworth.smarthome_tv.common.bean.HomeTabBean;
import com.smarthome.common.model.SmartBaseData;
import com.smarthome.common.model.SmartBaseListData;

import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Query;
import retrofit2.http.Url;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/10
 */
public interface HomePageHttpMethod {

    @GET("tvos/getHomeTabs")
    Call<SmartBaseData<HomeTabBean>> getHomeTabs(@Query("type") String type);

    @GET("tvos/getBusinessTabs")
    Call<SmartBaseData<HomeTabBean>> getBusinessTabs(@Query("businessType") int businessType);

    @GET("tvos/getTvosSixContent")
    Call<SmartBaseData<MainData>> getOperateFirstPageData(@Query("tag_id") String tag_id,
                                                          @Query("layout_style") String layout_style,
                                                          @Query("page") String page,
                                                          @Query("page_size") String page_size,
                                                          @Query("fixed_pagesize") String fixed_pagesize);

    @GET
    Call<SmartBaseListData<Container>> getOperateNextPageData(@Url String url);
}
