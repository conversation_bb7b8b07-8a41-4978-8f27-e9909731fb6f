package com.skyworth.smarthome_tv.common.util;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

import com.skyworth.smarthome_tv.home.custom.Custom;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.EmptyUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/9/3
 */
public class CCPackageManager {
    private final static String TAG = "CCPackageManager";
    private final static String ACTION_PACKAGE_ADDED = "android.intent.action.PACKAGE_ADDED";
    private final static String ACTION_PACKAGE_REMOVED = "android.intent.action.PACKAGE_REMOVED";

    public final static CCPackageManager INSTANCE = new CCPackageManager();
    private Map<String, InstallListener> mInstallListeners = new HashMap<>();
    private List<String> mPkgs = new ArrayList<>();
    private boolean hasPackageChange = false;//有应用安装或者卸载

    private BroadcastReceiver mPackageBroadcastReceiver = new BroadcastReceiver() {

        @Override
        public void onReceive(Context context, Intent intent) {
            CCLog.i(TAG, "onReceive:" + intent.getAction() + "---getDataString:" + intent.getDataString());
            try {
                String pkg = intent.getDataString().substring(8);
                if (ACTION_PACKAGE_ADDED.equals(intent.getAction())) {
                    InstallListener listener = mInstallListeners.get(pkg);
                    if (listener != null) {
                        listener.onPackageAdded(pkg);
                    }

                    if (EmptyUtils.isNotEmpty(pkg)) {
                        if (mPkgs.contains(pkg)) {
                            hasPackageChange = true;
                        }
                        if (Custom.PKG_IOT_CHANNEL.equals(pkg)) {
                            CCLog.i(TAG, "onReceive: start iotchannel service.");
                            callIotChannelService(context);
                        }
                    }
                } else if (ACTION_PACKAGE_REMOVED.equals(intent.getAction())) {
                    InstallListener listener = mInstallListeners.get(pkg);
                    if (listener != null) {
                        listener.onPackageRemoved(pkg);
                    }
                    if (EmptyUtils.isNotEmpty(pkg)) {
                        if (mPkgs.contains(pkg)) {
                            hasPackageChange = true;
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    };

    private void callIotChannelService(Context context) {
        try {
            Intent iotChannelIntent = new Intent("swaiotos.intent.action.provide.external.service");
            iotChannelIntent.setPackage(Custom.PKG_IOT_CHANNEL);
            iotChannelIntent.putExtra("provision_service_type", 99);
            context.startService(iotChannelIntent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private CCPackageManager() {
        mPkgs.add(Custom.PKG_MESSAGE);
        mPkgs.add(Custom.PKG_IOT_CHANNEL);
    }

    public void init(Context context) {
        IntentFilter filter = new IntentFilter();
        filter.addAction(ACTION_PACKAGE_ADDED);
        filter.addAction(ACTION_PACKAGE_REMOVED);
        filter.addDataScheme("package");
        context.registerReceiver(mPackageBroadcastReceiver, filter);
    }

    public boolean hasPackageChange() {
        return hasPackageChange;
    }

    public void addListener(String pkg, InstallListener listener) {
        mInstallListeners.put(pkg, listener);
    }

    public void removeListener(String pkg) {
        mInstallListeners.remove(pkg);
    }

    public void destroy(Context context) {
        hasPackageChange = false;
        mInstallListeners.clear();
        try{
            context.unregisterReceiver(mPackageBroadcastReceiver);
        }catch (IllegalArgumentException e){
            e.printStackTrace();
        }

    }

    public interface InstallListener {
        void onPackageAdded(String pkg);

        void onPackageRemoved(String pkg);
    }
}
