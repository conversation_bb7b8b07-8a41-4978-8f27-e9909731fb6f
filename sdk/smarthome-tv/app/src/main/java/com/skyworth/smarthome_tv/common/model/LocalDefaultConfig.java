package com.skyworth.smarthome_tv.common.model;

import com.alibaba.fastjson.JSONObject;
import com.coocaa.operate6_0.model.MainData;
import com.skyworth.smarthome_tv.home.main.view.data.NavigationData;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 智慧家庭本地默认配置数据（拿不到后台数据或者异常的情况下使用，保证进入APP不会为空）
 * @Author: wzh
 * @CreateDate: 2020/7/9
 */
public class LocalDefaultConfig {

    public final static int TAG_ID_MALL = 1;
    public final static int TAG_ID_AI = 2;
    public final static int TAG_ID_SMARTDEVICE = 3;

    /**
     * 导航栏tab数据
     *
     * @return
     */
    public static List<NavigationData> getHomeTags(boolean isSystem_8) {
        List<NavigationData> datas = new ArrayList<>();
//        datas.add(new NavigationData(TAG_ID_MALL, "发现"));
//        datas.add(new NavigationData(TAG_ID_AI, "智屏"));
        datas.add(new NavigationData(TAG_ID_SMARTDEVICE, "设备"));
        return datas;
    }

    public static MainData getMainData(int tagId) {
        switch (tagId) {
            case TAG_ID_MALL:
                return getMall();
            case TAG_ID_SMARTDEVICE:
                return getSmartDevice();
            case TAG_ID_AI:
                return getAI();
            default:
                return new MainData();
        }
    }

    /**
     * 发现-购物版面数据(插件形式)
     *
     * @return
     */
    public static MainData getMall() {
        String json = "{\n" +
                "    \"content\": {\n" +
                "        \"bg\": \"\",\n" +
                "        \"contents\": [\n" +
                "            {\n" +
                "                \"bg\": \"\",\n" +
                "                \"contents\": null,\n" +
                "                \"extra\": {\n" +
                "                    \"data_from\": {\n" +
                "                        \"info\": null,\n" +
                "                        \"type\": \"mark\"\n" +
                "                    },\n" +
                "                    \"dmp_info\": null,\n" +
                "                    \"focus_config\": \"\",\n" +
                "                    \"focus_shape\": 0,\n" +
                "                    \"panel_id\": \"10234433\",\n" +
                "                    \"panel_name\": \"智慧家庭-购物组件\",\n" +
                "                    \"panel_source_type\": \"0\",\n" +
                "                    \"panel_version\": \"V2\",\n" +
                "                    \"rec_stream_info\": null,\n" +
                "                    \"sub_panels\": null,\n" +
                "                    \"title\": null\n" +
                "                },\n" +
                "                \"focusable\": 0,\n" +
                "                \"height\": 0,\n" +
                "                \"id\": \"1025729\",\n" +
                "                \"parents\": \"\",\n" +
                "                \"type\": \"SMART_HOME_PANEL_MALL\",\n" +
                "                \"width\": 0,\n" +
                "                \"x\": 0,\n" +
                "                \"y\": 0\n" +
                "            }\n" +
                "        ],\n" +
                "        \"extra\": {\n" +
                "            \"bgLong\": \"\",\n" +
                "            \"orientation\": 1,\n" +
                "            \"space\": 40,\n" +
                "            \"use_bg\": 0\n" +
                "        },\n" +
                "        \"focusable\": 0,\n" +
                "        \"height\": 0,\n" +
                "        \"id\": \"\",\n" +
                "        \"parents\": \"\",\n" +
                "        \"type\": \"Expander\",\n" +
                "        \"width\": 0,\n" +
                "        \"x\": 0,\n" +
                "        \"y\": 0\n" +
                "    },\n" +
                "    \"cycle_time\": 120,\n" +
                "    \"md5\": \"Y7ZtiN4SifOQP9Dm\",\n" +
                "    \"refresh_info\": null,\n" +
                "    \"tab_config\": {\n" +
                "        \"block_field_focus_color\": \"\",\n" +
                "        \"block_line_focus_color\": \"\",\n" +
                "        \"block_title_focus_color\": \"\",\n" +
                "        \"block_title_unfocus_color\": \"\",\n" +
                "        \"panel_title_color\": \"\"\n" +
                "    }\n" +
                "}";
        return JSONObject.parseObject(json, MainData.class);
    }

    /**
     * 智能设备版面数据(插件形式，默认只有设备列表和场景列表)
     *
     * @return
     */
    public static MainData getSmartDevice() {
        String json = "{\n" +
                "    \"content\": {\n" +
                "        \"bg\": \"\",\n" +
                "        \"contents\": [\n" +
                "            {\n" +
                "                \"bg\": \"\",\n" +
                "                \"contents\": null,\n" +
                "                \"extra\": {\n" +
                "                    \"data_from\": {\n" +
                "                        \"info\": null,\n" +
                "                        \"type\": \"mark\"\n" +
                "                    },\n" +
                "                    \"dmp_info\": null,\n" +
                "                    \"focus_config\": \"\",\n" +
                "                    \"focus_shape\": 0,\n" +
                "                    \"panel_id\": \"10234436\",\n" +
                "                    \"panel_name\": \"智慧家庭-设备列表组件\",\n" +
                "                    \"panel_source_type\": \"0\",\n" +
                "                    \"panel_version\": \"V1\",\n" +
                "                    \"rec_stream_info\": null,\n" +
                "                    \"sub_panels\": null,\n" +
                "                    \"title\": null\n" +
                "                },\n" +
                "                \"focusable\": 0,\n" +
                "                \"height\": 0,\n" +
                "                \"id\": \"1025733\",\n" +
                "                \"parents\": \"\",\n" +
                "                \"type\": \"SMART_HOME_PANEL_DEVICE\",\n" +
                "                \"width\": 0,\n" +
                "                \"x\": 0,\n" +
                "                \"y\": 0\n" +
                "            },\n" +
                "            {\n" +
                "                \"bg\": \"\",\n" +
                "                \"contents\": null,\n" +
                "                \"extra\": {\n" +
                "                    \"data_from\": {\n" +
                "                        \"info\": null,\n" +
                "                        \"type\": \"mark\"\n" +
                "                    },\n" +
                "                    \"dmp_info\": null,\n" +
                "                    \"focus_config\": \"\",\n" +
                "                    \"focus_shape\": 0,\n" +
                "                    \"panel_id\": \"10234438\",\n" +
                "                    \"panel_name\": \"智慧家庭-场景列表组件\",\n" +
                "                    \"panel_source_type\": \"0\",\n" +
                "                    \"panel_version\": \"V1\",\n" +
                "                    \"rec_stream_info\": null,\n" +
                "                    \"sub_panels\": null,\n" +
                "                    \"title\": null\n" +
                "                },\n" +
                "                \"focusable\": 0,\n" +
                "                \"height\": 0,\n" +
                "                \"id\": \"1025734\",\n" +
                "                \"parents\": \"\",\n" +
                "                \"type\": \"SMART_HOME_PANEL_SCENE\",\n" +
                "                \"width\": 0,\n" +
                "                \"x\": 0,\n" +
                "                \"y\": 0\n" +
                "            },\n" +
                "            {\n" +
                "                \"bg\": \"\",\n" +
                "                \"contents\": [\n" +
                "                    {\n" +
                "                        \"extra\": {\n" +
                "                            \"block_new_title_info\": {\n" +
                "                                \"sub_title\": {\n" +
                "                                    \"show\": 0,\n" +
                "                                    \"text\": \"\"\n" +
                "                                },\n" +
                "                                \"title\": {\n" +
                "                                    \"show\": 0,\n" +
                "                                    \"text\": \"智能生态\"\n" +
                "                                }\n" +
                "                            },\n" +
                "                            \"block_content_info\": {\n" +
                "                                \"imgs\": {\n" +
                "                                    \"corner_icons\": [],\n" +
                "                                    \"focus_img_url\": \"\",\n" +
                "                                    \"poster\": {\n" +
                "                                        \"images\": [\n" +
                "                                            \"http://img.sky.fs.skysrt.com/tvos6_imgs_master/20200819/20200819104212329694_560*220.png\"\n" +
                "                                        ]\n" +
                "                                    }\n" +
                "                                },\n" +
                "                                \"action\": \"{\\\"byvalue\\\":\\\"coocaa.intent.action.browser\\\",\\\"packagename\\\":\\\"com.coocaa.app_browser\\\",\\\"dowhat\\\":\\\"startActivity\\\",\\\"versioncode\\\":\\\"4000451\\\",\\\"params\\\":{\\\"url\\\":\\\"https://webapp.skysrt.com/cc7.0/guide2/index.html?source=aiot&index=0\\\"},\\\"bywhat\\\":\\\"action\\\",\\\"exception\\\":{\\\"name\\\":\\\"onclick_exception\\\",\\\"value\\\":{\\\"packagename\\\":\\\"com.tianci.appstore\\\",\\\"dowhat\\\":\\\"startActivity\\\",\\\"versioncode\\\":\\\"-1\\\",\\\"params\\\":{\\\"id\\\":\\\"com.coocaa.app_browser\\\"},\\\"byvalue\\\":\\\"coocaa.intent.action.APP_STORE_DETAIL\\\",\\\"bywhat\\\":\\\"action\\\"}}}\",\n" +
                "                                \"title\": \"智能生态\"\n" +
                "                            },\n" +
                "                            \"block_type_info\": {\n" +
                "                                \"lucency_flag\": \"0\"\n" +
                "                            },\n" +
                "                            \"params\": \"{}\",\n" +
                "                            \"vector_tag_flag\": 0,\n" +
                "                            \"normal_special_flag\": 1\n" +
                "                        },\n" +
                "                        \"width\": 560,\n" +
                "                        \"x\": 0,\n" +
                "                        \"y\": 0,\n" +
                "                        \"focusable\": 1,\n" +
                "                        \"id\": \"102365577\",\n" +
                "                        \"type\": \"Block\",\n" +
                "                        \"height\": 220\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"extra\": {\n" +
                "                            \"block_new_title_info\": {\n" +
                "                                \"sub_title\": {\n" +
                "                                    \"show\": 0,\n" +
                "                                    \"text\": \"\"\n" +
                "                                },\n" +
                "                                \"title\": {\n" +
                "                                    \"show\": 0,\n" +
                "                                    \"text\": \"设备互联互通\"\n" +
                "                                }\n" +
                "                            },\n" +
                "                            \"block_content_info\": {\n" +
                "                                \"imgs\": {\n" +
                "                                    \"corner_icons\": [],\n" +
                "                                    \"focus_img_url\": \"\",\n" +
                "                                    \"poster\": {\n" +
                "                                        \"images\": [\n" +
                "                                            \"http://img.sky.fs.skysrt.com/tvos6_imgs_master/20200819/20200819104953575550_560*220.png\"\n" +
                "                                        ]\n" +
                "                                    }\n" +
                "                                },\n" +
                "                                \"action\": \"{\\\"byvalue\\\":\\\"coocaa.intent.action.browser\\\",\\\"packagename\\\":\\\"com.coocaa.app_browser\\\",\\\"dowhat\\\":\\\"startActivity\\\",\\\"versioncode\\\":\\\"4000451\\\",\\\"params\\\":{\\\"url\\\":\\\"https://webapp.skysrt.com/cc7.0/guide2/index.html?source=aiot&index=1\\\"},\\\"bywhat\\\":\\\"action\\\",\\\"exception\\\":{\\\"name\\\":\\\"onclick_exception\\\",\\\"value\\\":{\\\"packagename\\\":\\\"com.tianci.appstore\\\",\\\"dowhat\\\":\\\"startActivity\\\",\\\"versioncode\\\":\\\"-1\\\",\\\"params\\\":{\\\"id\\\":\\\"com.coocaa.app_browser\\\"},\\\"byvalue\\\":\\\"coocaa.intent.action.APP_STORE_DETAIL\\\",\\\"bywhat\\\":\\\"action\\\"}}}\",\n" +
                "                                \"title\": \"设备互联互通\"\n" +
                "                            },\n" +
                "                            \"block_type_info\": {\n" +
                "                                \"lucency_flag\": \"0\"\n" +
                "                            },\n" +
                "                            \"params\": \"{}\",\n" +
                "                            \"vector_tag_flag\": 0,\n" +
                "                            \"normal_special_flag\": 2\n" +
                "                        },\n" +
                "                        \"width\": 560,\n" +
                "                        \"x\": 600,\n" +
                "                        \"y\": 0,\n" +
                "                        \"focusable\": 1,\n" +
                "                        \"id\": \"102365578\",\n" +
                "                        \"type\": \"Block\",\n" +
                "                        \"height\": 220\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"extra\": {\n" +
                "                            \"block_new_title_info\": {\n" +
                "                                \"sub_title\": {\n" +
                "                                    \"show\": 0,\n" +
                "                                    \"text\": \"\"\n" +
                "                                },\n" +
                "                                \"title\": {\n" +
                "                                    \"show\": 0,\n" +
                "                                    \"text\": \"语音\"\n" +
                "                                }\n" +
                "                            },\n" +
                "                            \"block_content_info\": {\n" +
                "                                \"imgs\": {\n" +
                "                                    \"corner_icons\": [],\n" +
                "                                    \"focus_img_url\": \"\",\n" +
                "                                    \"poster\": {\n" +
                "                                        \"images\": [\n" +
                "                                            \"http://img.sky.fs.skysrt.com/tvos6_imgs_master/20200819/20200819104212487839_560*220.png\"\n" +
                "                                        ]\n" +
                "                                    }\n" +
                "                                },\n" +
                "                                \"action\": \"{\\\"byvalue\\\":\\\"coocaa.intent.action.browser\\\",\\\"packagename\\\":\\\"com.coocaa.app_browser\\\",\\\"dowhat\\\":\\\"startActivity\\\",\\\"versioncode\\\":\\\"4000451\\\",\\\"params\\\":{\\\"url\\\":\\\"https://webapp.skysrt.com/cc7.0/guide2/index.html?source=aiot&index=2\\\"},\\\"bywhat\\\":\\\"action\\\",\\\"exception\\\":{\\\"name\\\":\\\"onclick_exception\\\",\\\"value\\\":{\\\"packagename\\\":\\\"com.tianci.appstore\\\",\\\"dowhat\\\":\\\"startActivity\\\",\\\"versioncode\\\":\\\"-1\\\",\\\"params\\\":{\\\"id\\\":\\\"com.coocaa.app_browser\\\"},\\\"byvalue\\\":\\\"coocaa.intent.action.APP_STORE_DETAIL\\\",\\\"bywhat\\\":\\\"action\\\"}}}\",\n" +
                "                                \"title\": \"语音\"\n" +
                "                            },\n" +
                "                            \"block_type_info\": {\n" +
                "                                \"lucency_flag\": \"0\"\n" +
                "                            },\n" +
                "                            \"params\": \"{}\",\n" +
                "                            \"vector_tag_flag\": 0,\n" +
                "                            \"normal_special_flag\": 3\n" +
                "                        },\n" +
                "                        \"width\": 560,\n" +
                "                        \"x\": 1200,\n" +
                "                        \"y\": 0,\n" +
                "                        \"focusable\": 1,\n" +
                "                        \"id\": \"102365579\",\n" +
                "                        \"type\": \"Block\",\n" +
                "                        \"height\": 220\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"extra\": {\n" +
                "                    \"data_from\": {\n" +
                "                        \"info\": null,\n" +
                "                        \"type\": \"ops\"\n" +
                "                    },\n" +
                "                    \"dmp_info\": null,\n" +
                "                    \"focus_shape\": 0,\n" +
                "                    \"panel_id\": \"10259520\",\n" +
                "                    \"panel_name\": \"玩法精选\",\n" +
                "                    \"panel_source_type\": \"0\",\n" +
                "                    \"panel_version\": \"V6\",\n" +
                "                    \"rec_stream_info\": null,\n" +
                "                    \"title\": {\n" +
                "                        \"align\": 1,\n" +
                "                        \"color\": \"\",\n" +
                "                        \"size\": 0,\n" +
                "                        \"text\": \"玩法精选\"\n" +
                "                    }\n" +
                "                },\n" +
                "                \"focusable\": 0,\n" +
                "                \"height\": 0,\n" +
                "                \"id\": \"1025687\",\n" +
                "                \"parents\": \"\",\n" +
                "                \"type\": \"Panel\",\n" +
                "                \"width\": 0,\n" +
                "                \"x\": 0,\n" +
                "                \"y\": 0\n" +
                "            }\n" +
                "        ],\n" +
                "        \"extra\": {\n" +
                "            \"bgLong\": \"\",\n" +
                "            \"orientation\": 1,\n" +
                "            \"space\": 40,\n" +
                "            \"use_bg\": 0\n" +
                "        },\n" +
                "        \"focusable\": 0,\n" +
                "        \"height\": 0,\n" +
                "        \"id\": \"\",\n" +
                "        \"parents\": \"\",\n" +
                "        \"type\": \"Expander\",\n" +
                "        \"width\": 0,\n" +
                "        \"x\": 0,\n" +
                "        \"y\": 0\n" +
                "    },\n" +
                "    \"cycle_time\": 120,\n" +
                "    \"md5\": \"75Jmg96ii3vYTlRd\",\n" +
                "    \"refresh_info\": null,\n" +
                "    \"tab_config\": {\n" +
                "        \"block_field_focus_color\": \"\",\n" +
                "        \"block_line_focus_color\": \"\",\n" +
                "        \"block_title_focus_color\": \"\",\n" +
                "        \"block_title_unfocus_color\": \"\",\n" +
                "        \"panel_title_color\": \"\"\n" +
                "    }\n" +
                "}";
        return JSONObject.parseObject(json, MainData.class);
    }

    public static MainData getAI() {
        String json = "{\n" +
                "    \"content\": {\n" +
                "        \"bg\": \"\",\n" +
                "        \"contents\": [\n" +
                "            {\n" +
                "                \"bg\": \"\",\n" +
                "                \"contents\": [\n" +
                "                    {\n" +
                "                        \"bg\": \"\",\n" +
                "                        \"contents\": null,\n" +
                "                        \"extra\": {\n" +
                "                            \"block_bg\": null,\n" +
                "                            \"block_content_info\": null,\n" +
                "                            \"block_new_title_info\": null,\n" +
                "                            \"block_rec_stream\": null,\n" +
                "                            \"block_type_info\": null,\n" +
                "                            \"data_from\": null,\n" +
                "                            \"dmp_info\": null,\n" +
                "                            \"intervene_flag\": false,\n" +
                "                            \"normal_special_flag\": null,\n" +
                "                            \"params\": \"{}\",\n" +
                "                            \"vector_tag_flag\": null\n" +
                "                        },\n" +
                "                        \"focusable\": 1,\n" +
                "                        \"height\": 360,\n" +
                "                        \"id\": \"10305\",\n" +
                "                        \"parents\": \"Block\",\n" +
                "                        \"type\": \"SMART_HOME_BLOCK_IOT_CHANNEL\",\n" +
                "                        \"width\": 860,\n" +
                "                        \"x\": 0,\n" +
                "                        \"y\": 0\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"bg\": \"\",\n" +
                "                        \"contents\": null,\n" +
                "                        \"extra\": {\n" +
                "                            \"block_bg\": null,\n" +
                "                            \"block_content_info\": null,\n" +
                "                            \"block_new_title_info\": null,\n" +
                "                            \"block_rec_stream\": null,\n" +
                "                            \"block_type_info\": null,\n" +
                "                            \"data_from\": null,\n" +
                "                            \"dmp_info\": null,\n" +
                "                            \"intervene_flag\": false,\n" +
                "                            \"normal_special_flag\": null,\n" +
                "                            \"params\": \"{}\",\n" +
                "                            \"vector_tag_flag\": null\n" +
                "                        },\n" +
                "                        \"focusable\": 1,\n" +
                "                        \"height\": 360,\n" +
                "                        \"id\": \"10270\",\n" +
                "                        \"parents\": \"Block\",\n" +
                "                        \"type\": \"SMART_HOME_BLOCK_MESSAGE\",\n" +
                "                        \"width\": 860,\n" +
                "                        \"x\": 900,\n" +
                "                        \"y\": 0\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"extra\": {\n" +
                "                    \"data_from\": {\n" +
                "                        \"info\": null,\n" +
                "                        \"type\": \"ops\"\n" +
                "                    },\n" +
                "                    \"dmp_info\": null,\n" +
                "                    \"focus_shape\": 0,\n" +
                "                    \"panel_id\": \"10265188\",\n" +
                "                    \"panel_name\": \"跨屏互动\",\n" +
                "                    \"panel_source_type\": \"0\",\n" +
                "                    \"panel_version\": \"V1\",\n" +
                "                    \"rec_stream_info\": null,\n" +
                "                    \"title\": null\n" +
                "                },\n" +
                "                \"focusable\": 0,\n" +
                "                \"height\": 0,\n" +
                "                \"id\": \"1027302\",\n" +
                "                \"parents\": \"\",\n" +
                "                \"type\": \"Panel\",\n" +
                "                \"width\": 0,\n" +
                "                \"x\": 0,\n" +
                "                \"y\": 0\n" +
                "            },\n" +
                "            {\n" +
                "                \"bg\": \"\",\n" +
                "                \"contents\": [\n" +
                "                    {\n" +
                "                        \"bg\": \"\",\n" +
                "                        \"contents\": null,\n" +
                "                        \"extra\": {\n" +
                "                            \"block_bg\": null,\n" +
                "                            \"block_content_info\": {\n" +
                "                                \"action\": \"{\\\"byvalue\\\":\\\"com.tianci.de.skycast.StartUpPageActivity\\\",\\\"packagename\\\":\\\"com.tianci.de\\\",\\\"dowhat\\\":\\\"startActivity\\\",\\\"versioncode\\\":\\\"7\\\",\\\"params\\\":{},\\\"bywhat\\\":\\\"class\\\",\\\"exception\\\":{\\\"name\\\":\\\"onclick_exception\\\",\\\"value\\\":{\\\"packagename\\\":\\\"com.tianci.appstore\\\",\\\"dowhat\\\":\\\"startActivity\\\",\\\"versioncode\\\":\\\"-1\\\",\\\"params\\\":{\\\"id\\\":\\\"com.tianci.de\\\"},\\\"byvalue\\\":\\\"coocaa.intent.action.APP_STORE_DETAIL\\\",\\\"bywhat\\\":\\\"action\\\"}}}\",\n" +
                "                                \"imgs\": {\n" +
                "                                    \"corner_icons\": [],\n" +
                "                                    \"focus_img_url\": \"\",\n" +
                "                                    \"poster\": {\n" +
                "                                        \"fall_image\": \"\",\n" +
                "                                        \"images\": [\n" +
                "                                            \"http://img.sky.fs.skysrt.com/tvos6_imgs_master/20200828/20200828141215163130_860*360.png\"\n" +
                "                                        ],\n" +
                "                                        \"merge\": null\n" +
                "                                    }\n" +
                "                                },\n" +
                "                                \"title\": \"爱投屏\"\n" +
                "                            },\n" +
                "                            \"block_new_title_info\": {\n" +
                "                                \"sub_title\": {\n" +
                "                                    \"show\": 0,\n" +
                "                                    \"text\": \"\"\n" +
                "                                },\n" +
                "                                \"title\": {\n" +
                "                                    \"show\": 0,\n" +
                "                                    \"text\": \"爱投屏\"\n" +
                "                                }\n" +
                "                            },\n" +
                "                            \"block_rec_stream\": null,\n" +
                "                            \"block_type_info\": {\n" +
                "                                \"lucency_flag\": \"0\"\n" +
                "                            },\n" +
                "                            \"data_from\": null,\n" +
                "                            \"dmp_info\": null,\n" +
                "                            \"intervene_flag\": false,\n" +
                "                            \"normal_special_flag\": 1,\n" +
                "                            \"params\": \"{}\",\n" +
                "                            \"vector_tag_flag\": 0\n" +
                "                        },\n" +
                "                        \"focusable\": 1,\n" +
                "                        \"height\": 360,\n" +
                "                        \"id\": \"102416042\",\n" +
                "                        \"parents\": \"\",\n" +
                "                        \"type\": \"Block\",\n" +
                "                        \"width\": 860,\n" +
                "                        \"x\": 0,\n" +
                "                        \"y\": 0\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"bg\": \"\",\n" +
                "                        \"contents\": null,\n" +
                "                        \"extra\": {\n" +
                "                            \"block_bg\": null,\n" +
                "                            \"block_content_info\": {\n" +
                "                                \"action\": \"{\\\"byvalue\\\":\\\"com.skyworth.srtnj.lafitevoice.action.START\\\",\\\"packagename\\\":\\\"com.skyworth.lafite.srtnj.speechserver\\\",\\\"dowhat\\\":\\\"startActivity\\\",\\\"versioncode\\\":\\\"-1\\\",\\\"params\\\":{\\\"index\\\":\\\"0\\\"},\\\"bywhat\\\":\\\"action\\\",\\\"exception\\\":{\\\"name\\\":\\\"onclick_exception\\\",\\\"value\\\":{\\\"packagename\\\":\\\"com.tianci.appstore\\\",\\\"dowhat\\\":\\\"startActivity\\\",\\\"versioncode\\\":\\\"-1\\\",\\\"params\\\":{\\\"id\\\":\\\"com.skyworth.lafite.srtnj.speechserver\\\"},\\\"byvalue\\\":\\\"coocaa.intent.action.APP_STORE_DETAIL\\\",\\\"bywhat\\\":\\\"action\\\"}}}\",\n" +
                "                                \"imgs\": {\n" +
                "                                    \"corner_icons\": [],\n" +
                "                                    \"focus_img_url\": \"\",\n" +
                "                                    \"poster\": {\n" +
                "                                        \"fall_image\": \"\",\n" +
                "                                        \"images\": [\n" +
                "                                            \"http://img.sky.fs.skysrt.com/tvos6_imgs_master/20200828/20200828141221738331_860*360.png\"\n" +
                "                                        ],\n" +
                "                                        \"merge\": null\n" +
                "                                    }\n" +
                "                                },\n" +
                "                                \"title\": \"\"\n" +
                "                            },\n" +
                "                            \"block_new_title_info\": {\n" +
                "                                \"sub_title\": {\n" +
                "                                    \"show\": 0,\n" +
                "                                    \"text\": \"\"\n" +
                "                                },\n" +
                "                                \"title\": {\n" +
                "                                    \"show\": 0,\n" +
                "                                    \"text\": \"\"\n" +
                "                                }\n" +
                "                            },\n" +
                "                            \"block_rec_stream\": null,\n" +
                "                            \"block_type_info\": {\n" +
                "                                \"lucency_flag\": \"0\"\n" +
                "                            },\n" +
                "                            \"data_from\": null,\n" +
                "                            \"dmp_info\": null,\n" +
                "                            \"intervene_flag\": false,\n" +
                "                            \"normal_special_flag\": 2,\n" +
                "                            \"params\": \"{}\",\n" +
                "                            \"vector_tag_flag\": 0\n" +
                "                        },\n" +
                "                        \"focusable\": 1,\n" +
                "                        \"height\": 360,\n" +
                "                        \"id\": \"102416043\",\n" +
                "                        \"parents\": \"\",\n" +
                "                        \"type\": \"Block\",\n" +
                "                        \"width\": 860,\n" +
                "                        \"x\": 900,\n" +
                "                        \"y\": 0\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"extra\": {\n" +
                "                    \"data_from\": {\n" +
                "                        \"info\": null,\n" +
                "                        \"type\": \"ops\"\n" +
                "                    },\n" +
                "                    \"dmp_info\": null,\n" +
                "                    \"focus_shape\": 0,\n" +
                "                    \"panel_id\": \"10265154\",\n" +
                "                    \"panel_name\": \"智慧家庭—更多功能\",\n" +
                "                    \"panel_source_type\": \"0\",\n" +
                "                    \"panel_version\": \"V1\",\n" +
                "                    \"rec_stream_info\": null,\n" +
                "                    \"title\": {\n" +
                "                        \"align\": 1,\n" +
                "                        \"color\": \"\",\n" +
                "                        \"size\": 0,\n" +
                "                        \"text\": \"更多功能\"\n" +
                "                    }\n" +
                "                },\n" +
                "                \"focusable\": 0,\n" +
                "                \"height\": 0,\n" +
                "                \"id\": \"1027324\",\n" +
                "                \"parents\": \"\",\n" +
                "                \"type\": \"Panel\",\n" +
                "                \"width\": 0,\n" +
                "                \"x\": 0,\n" +
                "                \"y\": 0\n" +
                "            }\n" +
                "        ],\n" +
                "        \"extra\": {\n" +
                "            \"bgLong\": \"\",\n" +
                "            \"orientation\": 1,\n" +
                "            \"space\": 40,\n" +
                "            \"use_bg\": 0\n" +
                "        },\n" +
                "        \"focusable\": 0,\n" +
                "        \"height\": 0,\n" +
                "        \"id\": \"\",\n" +
                "        \"parents\": \"\",\n" +
                "        \"type\": \"Expander\",\n" +
                "        \"width\": 0,\n" +
                "        \"x\": 0,\n" +
                "        \"y\": 0\n" +
                "    },\n" +
                "    \"cycle_time\": 120,\n" +
                "    \"md5\": \"9Rotz1834NqQl8Ek\",\n" +
                "    \"refresh_info\": null,\n" +
                "    \"tab_config\": {\n" +
                "        \"block_field_focus_color\": \"\",\n" +
                "        \"block_line_focus_color\": \"\",\n" +
                "        \"block_title_focus_color\": \"\",\n" +
                "        \"block_title_unfocus_color\": \"\",\n" +
                "        \"panel_title_color\": \"\"\n" +
                "    }\n" +
                "}";
        return JSONObject.parseObject(json, MainData.class);
    }

}
