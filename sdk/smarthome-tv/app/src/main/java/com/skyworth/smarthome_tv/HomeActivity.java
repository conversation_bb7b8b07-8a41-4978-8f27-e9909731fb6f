package com.skyworth.smarthome_tv;

import android.content.Intent;
import android.os.Bundle;
import android.view.KeyEvent;

import com.skyworth.smarthome_tv.common.base.BaseActivity;
import com.skyworth.smarthome_tv.common.util.CCPackageManager;
import com.skyworth.smarthome_tv.home.HomeUtil;
import com.skyworth.smarthome_tv.home.main.presenter.HomePresenter;
import com.skyworth.smarthome_tv.home.main.presenter.IHomePresenter;
import com.skyworth.smarthome_tv.home.main.view.HomeView;
import com.skyworth.smarthome_tv.home.main.view.IHomeView;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.Constants;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/3
 */
public class HomeActivity extends BaseActivity {

    private IHomeView mView;
    private IHomePresenter mPresenter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        CCPackageManager.INSTANCE.init(this);
        String deviceId = getIntent().getStringExtra(Constants.KEY_DEVICE_ID);
        HomeUtil.setOpenCtrlPanelDeviceId(deviceId);
        mView = new HomeView(this);
        mPresenter = new HomePresenter();
        mView.create(this, mPresenter);
        mPresenter.create(this, mView);
        setContentView(mView.getView());
        mPresenter.start(getIntent());
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        mView.onNewIntent(intent);
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN) {
            boolean ret = mView.onKeyDown(event.getKeyCode());
            if (ret) {
                return true;
            }
            if (event.getKeyCode() == KeyEvent.KEYCODE_BACK || event.getKeyCode() == KeyEvent.KEYCODE_HOME) {
                if (CCPackageManager.INSTANCE.hasPackageChange()) {
                    CCLog.i("hasPackageChange, killMainProcess");
                    SmartHomeApplication.killMainProcess();
                }
            }
        }
        return super.dispatchKeyEvent(event);
    }

    @Override
    protected void onResume() {
        super.onResume();
        mView.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
        mView.onPause();
    }

    @Override
    protected void onStop() {
        super.onStop();
        mView.onStop();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mView != null) {
            mView.onDestroy();
        }
        if (mPresenter != null) {
            mPresenter.destroy();
        }
        CCPackageManager.INSTANCE.destroy(this);
    }
}
