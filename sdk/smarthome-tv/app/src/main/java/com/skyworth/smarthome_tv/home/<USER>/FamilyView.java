package com.skyworth.smarthome_tv.home.family;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.account.IAppAccountManager;
import com.skyworth.smarthome.common.model.UserInfo;
import com.skyworth.smarthome.common.util.CommonUtil;
import com.skyworth.smarthome.common.util.ThreadManager;
import com.skyworth.smarthome.personal.PersonalCenterActivity;
import com.skyworth.smarthome_tv.home.family.data.FamilyStatusData;
import com.skyworth.smarthome_tv.home.main.view.HomeView;
import com.skyworth.smarthome_tv.home.main.view.IHomeView;
import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;
import com.skyworth.util.imageloader.fresco.CoocaaFresco;
import com.smarthome.common.utils.EmptyUtils;

import java.text.SimpleDateFormat;

/**
 * @Description: 家庭信息、环境信息
 * @Author: wzh
 * @CreateDate: 2020/6/4
 */
public class FamilyView extends FrameLayout {

    private FamilyPresenter mPresenter;
    private View mUserIcon, mUserIconFocus;
    private LinearLayout mFamilyTitleLayout;
    private LinearLayout mIndorInfoLayout;
    private TextView mFamilyName, mIndoorInfo, mDeviceOnlineCount;
    private TextView mTimeV;
    private IHomeView.ILayoutListener mLayoutListener;

    public FamilyView(Context context, IHomeView.ILayoutListener layoutListener) {
        super(context);
        mLayoutListener = layoutListener;
        addUserIcon();
        addFamilyName();
        addIndoorInfo();
        addTimeV();
    }

    private void addUserIcon() {
        mUserIcon = ImageLoader.getLoader().getView(getContext());
        mUserIcon.setFocusable(true);
        mUserIcon.setFocusableInTouchMode(true);
        mUserIcon.setBackgroundResource(R.drawable.ic_default_user);
        LayoutParams params = new LayoutParams(Util.Div(60), Util.Div(60));
        params.leftMargin = Util.Div(5);
        params.topMargin = Util.Div(5);
        addView(mUserIcon, params);

        mUserIconFocus = new View(getContext());
        mUserIconFocus.setBackground(new CCFocusDrawable(getContext(), true).setSolidVisible(false));
        params = new LayoutParams(Util.Div(70), Util.Div(70));
        addView(mUserIconFocus, params);
        mUserIconFocus.setVisibility(INVISIBLE);

        mUserIcon.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (!CommonUtil.isNetConnected(getContext())) {
                    return;
                }
                if (!IAppAccountManager.INSTANCE.hasLogin()) {
                    IAppAccountManager.INSTANCE.gotoLogin();
                } else {
                    getContext().startActivity(new Intent(getContext(), PersonalCenterActivity.class));
                }
            }
        });
        mUserIcon.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View view, boolean b) {
                mLayoutListener.onUserIconFocus(b);
                if (b) {
                    mUserIconFocus.setVisibility(VISIBLE);
                } else {
                    mUserIconFocus.setVisibility(INVISIBLE);
                }
            }
        });
        mUserIcon.setOnTouchListener(new OnTouchListener() {
            @Override
            public boolean onTouch(View view, MotionEvent motionEvent) {
                if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {
                    if (view.isFocusable() && view.isFocusableInTouchMode()) {
                        view.requestFocus();
                    }
                } else if (motionEvent.getAction() == MotionEvent.ACTION_UP) {
                    return view.callOnClick();
                }
                return false;
            }
        });
        mUserIcon.setOnKeyListener(new OnKeyListener() {
            @Override
            public boolean onKey(View view, int i, KeyEvent keyEvent) {
                if (keyEvent.getAction() == KeyEvent.ACTION_DOWN) {
                    if (i == KeyEvent.KEYCODE_DPAD_DOWN) {
                        return mLayoutListener.getFocus(HomeView.FOCUS_TO_TAB, HomeView.FROM_TOP);
                    } else {
                        return i == KeyEvent.KEYCODE_DPAD_RIGHT || i == KeyEvent.KEYCODE_DPAD_LEFT;
                    }
                }
                return false;
            }
        });
    }

    private Runnable mTimeRunnable = new Runnable() {
        @Override
        public void run() {
            long currentTime = System.currentTimeMillis();
            String timeNow = new SimpleDateFormat("HH:mm").format(currentTime);
            mTimeV.setText(timeNow);
            ThreadManager.getInstance().uiThread(mTimeRunnable,300);
        }
    };

    private void addFamilyName() {
        mFamilyTitleLayout = new LinearLayout(getContext());
        mFamilyTitleLayout.setGravity(Gravity.CENTER);
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.gravity = Gravity.CENTER_VERTICAL;
        params.leftMargin = Util.Div(84);
        addView(mFamilyTitleLayout, params);

        mFamilyName = new TextView(getContext());
        mFamilyName.setTextColor(Color.parseColor("#FFFFFF"));
        mFamilyName.setTextSize(Util.Dpi(36));
        mFamilyName.getPaint().setFakeBoldText(true);
        mFamilyTitleLayout.addView(mFamilyName);
    }

    private void addIndoorInfo() {
        mIndorInfoLayout = new LinearLayout(getContext());
        mIndorInfoLayout.setGravity(Gravity.CENTER);
        LinearLayout.LayoutParams ll = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        ll.leftMargin = Util.Div(20);
        mFamilyTitleLayout.addView(mIndorInfoLayout, ll);

        mIndoorInfo = new TextView(getContext());
        mIndoorInfo.setTextColor(Color.parseColor("#66FFFFFF"));
        mIndoorInfo.setTextSize(Util.Dpi(28));
//        mIndoorInfo.setText("温度0°C  |  湿度0%");
        ll = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        mIndorInfoLayout.addView(mIndoorInfo, ll);

        mDeviceOnlineCount = new TextView(getContext());
        mDeviceOnlineCount.setTextColor(Color.parseColor("#66FFFFFF"));
        mDeviceOnlineCount.setTextSize(Util.Dpi(28));
//        mDeviceOnlineCount.setText("| 0个设备运行中");
        ll = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        ll.leftMargin = Util.Div(14);
        mIndorInfoLayout.addView(mDeviceOnlineCount, ll);
        mIndorInfoLayout.setVisibility(GONE);
    }

    private void addTimeV() {
        mTimeV = new TextView(getContext());
        mTimeV.setTextColor(getResources().getColor(R.color.white_80));
        mTimeV.setTextSize(Util.Dpi(32));
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.gravity = Gravity.CENTER_VERTICAL | Gravity.END;
        addView(mTimeV, params);
        ThreadManager.getInstance().uiThread(mTimeRunnable);
    }

    public void setPresenter(FamilyPresenter presenter) {
        mPresenter = presenter;
    }

    public void refreshFamilyUI(String familyName) {
        String icon = UserInfo.getInstance().getAvatar();
        try {
            int radius = Util.Div(80);
            if (!EmptyUtils.isEmpty(icon)) {
                ImageLoader.getLoader().with(getContext()).load(icon).resize(Util.Div(60), Util.Div(60)).setScaleType(ImageView.ScaleType.FIT_XY)
                        .setLeftBottomCorner(radius).setLeftTopCorner(radius).setRightBottomCorner(radius).setRightTopCorner(radius).into(mUserIcon);
            } else {
                ImageLoader.getLoader().with(getContext()).load(CoocaaFresco.getFrescoResUri(getContext(), R.drawable.ic_default_user)).setScaleType(ImageView.ScaleType.FIT_XY).into(mUserIcon);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (IAppAccountManager.INSTANCE.hasLogin()) {
            if (IAppAccountManager.INSTANCE.isBindMobile()&& EmptyUtils.isNotEmpty(familyName)) {
                mFamilyName.setText(familyName);
            } else {
                mFamilyName.setText(UserInfo.getInstance().getNick_name());
            }
        } else {
            mFamilyName.setText("未登录");
        }
    }

    public void refreshStatusUI(FamilyStatusData familyStatusData) {
        mIndorInfoLayout.setVisibility(VISIBLE);
        mIndoorInfo.setText(familyStatusData.weatherInfoTip);
        if (EmptyUtils.isNotEmpty(familyStatusData.weatherInfoTip) && EmptyUtils.isNotEmpty(familyStatusData.onlineTip)) {
            mDeviceOnlineCount.setText("|  " + familyStatusData.onlineTip);
        } else {
            mDeviceOnlineCount.setText(familyStatusData.onlineTip);
        }
    }

    public boolean getFocus() {
        return mUserIcon.requestFocus();
    }

    public void destroy() {
        ThreadManager.getInstance().removeUiThread(mTimeRunnable);
    }
}
