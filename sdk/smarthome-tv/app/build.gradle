plugins {
    id 'com.android.application'
}

android {
    compileSdkVersion COMPILE_SDK_VERSION
    buildToolsVersion BUILDTOOLS_VERSION
    defaultConfig {
        multiDexEnabled true
        applicationId "com.skyworth.smarthome"
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION
        versionCode 1
        versionName "1.0"

    }


    signingConfigs {
        release {
            keyAlias sign_config["keystore.alias"]
            keyPassword sign_config["keystore.alias_password"]
            storeFile file("../${sign_config["keystore.path"]}")
            storePassword sign_config["keystore.password"]
        }
    }

    buildTypes {
        debug {
            debuggable true
            minifyEnabled false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            manifestPlaceholders = [DEVICE_SERVER_VALUE  : "https://api-test.skyworthiot.com/",
                                    HOMEPAGE_SERVER_VALUE: "http://beta-api-home.skysrt.com/",
                                    APPSTORE_SERVER_VALUE: "http://beta-tc.skysrt.com/appstore/appstorev3/"]
        }
        release {
            debuggable false
            minifyEnabled true
            shrinkResources true
            zipAlignEnabled true
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            manifestPlaceholders = [DEVICE_SERVER_VALUE  : "https://api.skyworthiot.com/",
                                    HOMEPAGE_SERVER_VALUE: "http://api.home.skysrt.com/v1/",
                                    APPSTORE_SERVER_VALUE: "https://tc.skysrt.com/appstore/appstorev3/"]
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {

    testImplementation 'junit:junit:4.+'
    implementation project(":smarthome-tv")
    implementation project(':Common')
    implementation project(":smarthome-aiot-sdk")

    //noinspection GradleCompatible
    implementation  'com.android.support:appcompat-v7:23.2.0'
    implementation 'org.greenrobot:eventbus:3.0.0'
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    //swaiotos maven
    implementation 'swaiotos.support:appcore:1.0.57'
    implementation 'swaiotos.ui:common:1.0.57'
    implementation 'swaiotos.ui:app-v6:1.0.57'
    implementation 'swaiotos.support:log:1.0.57'
    implementation 'swaiotos:sal:1.0.57'
    implementation 'swaiotos.ui:imageloader:1.0.57'
    implementation 'swaiotos.base:okhttp:1.0.57'
    implementation 'com.android.support:multidex:1.0.1'
}