plugins {
    id 'com.android.library'
}
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'

android {
    compileSdkVersion COMPILE_SDK_VERSION
    buildToolsVersion BUILDTOOLS_VERSION

    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION
        versionCode 1
        versionName "1.0"

        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        debug {
            debuggable true
            minifyEnabled false
            zipAlignEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            manifestPlaceholders = [DEVICE_SERVER_VALUE  : "https://api-sit.skyworthiot.com/",
                                    HOMEPAGE_SERVER_VALUE: "http://beta-api-home.skysrt.com/",
                                    APPSTORE_SERVER_VALUE: "http://beta-tc.skysrt.com/appstore/appstorev3/"]
        }
        release {
            debuggable false
            minifyEnabled true
            zipAlignEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            manifestPlaceholders = [DEVICE_SERVER_VALUE  : "https://api.skyworthiot.com/",
                                    HOMEPAGE_SERVER_VALUE: "http://api.home.skysrt.com/v1/",
                                    APPSTORE_SERVER_VALUE: "https://tc.skysrt.com/appstore/appstorev3/"]
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }


    tasks.withType(JavaCompile) {
        options.encoding = "UTF-8"
    }

}

configurations.all {
    resolutionStrategy {
        force 'com.alibaba:fastjson:1.2.48'
    }
}

repositories {
    flatDir {
        dirs 'libs'
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    testImplementation 'junit:junit:4.12'
    //others
    implementation 'org.greenrobot:eventbus:3.0.0'
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation  'com.android.support:appcompat-v7:23.2.0'
    implementation 'com.isupatches:wisefy:3.0.0'
    implementation 'com.github.promeg:tinypinyin:2.0.3'
    //local sdk
    implementation project(':Common')
    implementation project(":smarthome-aiot")
    implementation project(":smarthome-aiot-sdk")

    //swaiotos maven
    implementation 'swaiotos.support:appcore:1.0.57'
    implementation 'swaiotos.ui:common:1.0.57'
    implementation 'swaiotos.ui:app-v6:1.0.57'
    implementation 'swaiotos.support:log:1.0.57'
    implementation 'swaiotos:sal:1.0.57'
    implementation 'swaiotos.ui:imageloader:1.0.57'
    implementation 'swaiotos.base:okhttp:1.0.57'
    implementation 'com.github.bumptech.glide:glide:3.7.0'
}