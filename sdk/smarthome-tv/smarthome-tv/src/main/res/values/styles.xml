<resources>

    <style name="common_style" parent="@android:style/Theme.DeviceDefault.Light.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>

    <style name="global_dialog" parent="@android:style/Theme.Holo.Dialog">
        <!-- 是否有边框 -->
        <item name="android:windowFrame">@null</item>
        <!--是否在悬浮Activity之上  -->
        <item name="android:windowIsFloating">true</item>
        <!--标题  -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <!--阴影  -->
        <item name="android:windowIsTranslucent">false</item>
        <!--透明背景-->
        <item name="android:background">@android:color/transparent</item>
        <!--窗口背景透明-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!--弹窗背景是否变暗-->
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.6</item>
    </style>

    <style name="dialogWindowRightAnim" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/dialog_right_in</item>
        <item name="android:windowExitAnimation">@anim/dialog_right_out</item>
    </style>

    <style name="dialogWindowAnim" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/dialog_fade_in</item>
        <item name="android:windowExitAnimation">@anim/dialog_fade_out</item>
    </style>

    <!-- PopupWindow弹出/隐藏动画 12-28 -->
    <style name="popupWindowAnim">
        <item name="android:windowEnterAnimation">@anim/popwindow_fade_in</item>
        <item name="android:windowExitAnimation">@anim/popwindow_fade_out</item>
    </style>

    <style name="message_dialog" parent="@android:style/Theme.Holo.Dialog">
        <!-- 是否有边框 -->
        <item name="android:windowFrame">@null</item>
        <!--是否在悬浮Activity之上  -->
        <item name="android:windowIsFloating">true</item>
        <!--标题  -->
        <item name="android:windowNoTitle">true</item>
        <!--阴影  -->
        <item name="android:windowIsTranslucent">true</item>
        <!--透明背景-->
        <item name="android:background">@android:color/transparent</item>
        <!--窗口背景透明-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!--弹窗背景是否变暗-->
        <item name="android:backgroundDimEnabled">false</item>
    </style>

</resources>
