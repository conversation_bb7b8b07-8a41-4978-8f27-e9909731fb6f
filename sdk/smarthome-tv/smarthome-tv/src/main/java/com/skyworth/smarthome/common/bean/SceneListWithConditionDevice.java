package com.skyworth.smarthome.common.bean;

import java.util.List;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/11/20 10:33.
 */
public class SceneListWithConditionDevice {
    public String scene_id;//场景id
    public String scene_name;//场景名称
    public String icon;//场景图标
    public String voice_tips;//语音指令列表
    public String status;//场景状态, invalid:无效 auto:自动 manu:手动
    public List<ConditionInfo> conditions;//条件信息

    public static class ConditionInfo {
        public String device_id;//条件设备id
        public SceneInfo condition;//条件（与属性对应）
    }

    public static class SceneInfo {
        public int scene;//
    }
}
