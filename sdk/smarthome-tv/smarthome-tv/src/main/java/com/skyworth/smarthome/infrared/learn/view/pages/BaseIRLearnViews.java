package com.skyworth.smarthome.infrared.learn.view.pages;

import android.content.Context;
import android.widget.FrameLayout;

import android.support.annotation.NonNull;

import com.skyworth.smarthome.common.ui.DialogBg;
import com.skyworth.smarthome.infrared.learn.presenter.IIRLearnPresenter;

import java.io.Serializable;
import java.util.Map;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/7/3 10:00.
 */
public abstract class BaseIRLearnViews extends FrameLayout implements Serializable {
    protected IIRLearnPresenter mPresenter = null;

    public BaseIRLearnViews(@NonNull Context context) {
        super(context);
        initView();
    }

    protected void initView() {
        setBackground(new DialogBg());
    }

    public void setPresenter(IIRLearnPresenter mPresenter) {
        this.mPresenter = mPresenter;
    }

    public void show(Map<String, Object> params) {
        onShow(params);
    }

    protected abstract void onShow(Map<String, Object> params);

    public abstract int getViewWidth();

    public abstract int getViewHeight();

    public abstract String getName();
}
