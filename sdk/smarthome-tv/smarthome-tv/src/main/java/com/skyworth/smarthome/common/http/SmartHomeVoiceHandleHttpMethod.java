package com.skyworth.smarthome.common.http;

import com.skyworth.smarthome.common.bean.BaiduResultBean;
import com.skyworth.smarthome.common.bean.BaiduResultData;
import com.skyworth.smarthome.common.bean.ReportMediaBean;
import com.smarthome.common.model.SmartBaseData;

import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * Created by <PERSON> on 2018/4/23.
 */

public interface SmartHomeVoiceHandleHttpMethod {

    @POST("skyos/screen/voice-command/execute")
    Call<BaiduResultBean> voiceCommandExecute(@Query("appkey") String appkey,
                                              @Query("time") String time,
                                              @Query("uid") String uid,
                                              @Query("ak") String ak,
                                              @Query("vuid") String vuid,
                                              @Query("screen_id") String device_id,
                                              @Query("is_virtual") String is_virtual,
                                              @Query("sign") String sign,
                                              @Body RequestBody body);

    @GET("skyos/screen/devicelist-for-sync")
    Call<BaiduResultBean> deviceListForSync(@Query("appkey") String appkey,
                                            @Query("time") String time,
                                            @Query("uid") String uid,
                                            @Query("ak") String ak,
                                            @Query("vuid") String vuid,
                                            @Query("screen_id") String device_id,
                                            @Query("is_virtual") String is_virtual,
                                            @Query("sign") String sign);

    @POST("skyos/voice-command/execute")
    Call<BaiduResultData> sendVoiceCommand(@Query("appkey") String appkey,
                                           @Query("time") String time,
                                           @Query("uid") String uid,
                                           @Query("ak") String ak,
                                           @Query("vuid") String vuid,
                                           @Query("device_id") String device_id,
                                           @Query("is_virtual") String is_virtual,
                                           @Query("sign") String sign,
                                           @Body RequestBody body);

    @POST("skyos/report-current-media")
    Call<SmartBaseData<String>> reportMedia(@Query("appkey") String appkey,
                                            @Query("time") String time,
                                            @Query("uid") String uid,
                                            @Query("ak") String ak,
                                            @Query("vuid") String vuid,
                                            @Query("device_id") String device_id,
                                            @Query("sign") String sign,
                                            @Body ReportMediaBean body);

    @GET("skyos/devicelist-for-sync")
    Call<BaiduResultData> deviceListSync(@Query("appkey") String appkey,
                                         @Query("time") String time,
                                         @Query("uid") String uid,
                                         @Query("ak") String ak,
                                         @Query("vuid") String vuid,
                                         @Query("device_id") String device_id,
                                         @Query("is_virtual") String is_virtual,
                                         @Query("sign") String sign);
}
