package com.skyworth.smarthome.personal.thirdaccount.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.bean.ThridAccountHttpBean;
import com.skyworth.ui.api.widget.SimpleFocusDrawable;
import com.skyworth.ui.newrecycleview.NewRecycleAdapterItem;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XThemeUtils;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/9
 */
public class ThridAccountItemView extends FrameLayout implements NewRecycleAdapterItem<ThridAccountHttpBean>, View.OnTouchListener {

    private LinearLayout contentLayout;
    private View mIcon;
    private TextView mName;
    private TextView mStatus;
    private SimpleFocusDrawable mFocusBg;
    private Drawable mStatusFocusBg = XThemeUtils.getDrawable(0, getResources().getColor(R.color.black_40), Util.Div(1), Util.Div(34));
    private Drawable mStatusUnFocusBg = XThemeUtils.getDrawable(0, getResources().getColor(R.color.white_40), Util.Div(1), Util.Div(34));
    private ThridAccountHttpBean mData;

    public ThridAccountItemView(Context context) {
        super(context);
        setFocusable(true);
        setFocusableInTouchMode(true);
        setOnTouchListener(this);
        setPadding(Util.Div(8), Util.Div(8), Util.Div(8), Util.Div(8));

        mFocusBg = new SimpleFocusDrawable(context).setRadius(Util.Div(8));
        setBackground(mFocusBg);

        contentLayout = new LinearLayout(getContext());
        contentLayout.setGravity(Gravity.CENTER_VERTICAL);
//        contentLayout.setBackground(XThemeUtils.getDrawable(getResources().getColor(R.color.white_10), 0, 0, Util.Div(8)));
        addView(contentLayout, new LayoutParams(Util.Div(1000), Util.Div(130)));

        mIcon = ImageLoader.getLoader().getView(context);
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(Util.Div(80), Util.Div(80));
        params.leftMargin = Util.Div(25);
        contentLayout.addView(mIcon, params);

        mName = new TextView(context);
        mName.setTextColor(getResources().getColor(R.color.white));
        mName.setTextSize(Util.Dpi(32));
        mName.setSingleLine();
        mName.getPaint().setFakeBoldText(true);
        mName.setMaxWidth(Util.Div(550));
        mName.setEllipsize(TextUtils.TruncateAt.END);
        params = new LinearLayout.LayoutParams(Util.Div(550), ViewGroup.LayoutParams.WRAP_CONTENT);
        params.leftMargin = Util.Div(20);
        contentLayout.addView(mName, params);

        mStatus = new TextView(context);
        mStatus.setTextColor(getResources().getColor(R.color.white_80));
        mStatus.setTextSize(Util.Dpi(24));
        mStatus.setGravity(Gravity.CENTER);
        mStatus.getPaint().setFakeBoldText(true);
        mStatus.setBackground(mStatusUnFocusBg);
        params = new LinearLayout.LayoutParams(Util.Div(140), Util.Div(60));
        params.leftMargin = Util.Div(150);
        contentLayout.addView(mStatus, params);
    }

    @Override
    public View getView() {
        return this;
    }

    @Override
    public void onUpdateData(ThridAccountHttpBean data, int position) {
        mData = data;
        if (EmptyUtils.isNotEmpty(data.corp_name) && !data.account_title.contains(data.corp_name)) {
            mName.setText(data.account_title + "(" + data.corp_name + ")");
        } else {
            mName.setText(data.account_title);
        }
        String status = "0";
        if (!TextUtils.isEmpty(data.bind_status)) {
            status = data.bind_status;
        }
        refreshStatus(status);
    }

    public void refreshStatus(String status) {
        if (status.equals("1")) {
            mStatus.setText(getContext().getString(R.string.account_binded));
        } else {
            mStatus.setText(getContext().getString(R.string.account_bind));
        }
    }

    @Override
    public void clearItem() {

    }

    @Override
    public void refreshUI() {
        if (mData != null && !TextUtils.isEmpty(mData.icon)) {
            ImageLoader.getLoader().with(getContext()).resize(Util.Div(80), Util.Div(80))
                    .setScaleType(ImageView.ScaleType.FIT_XY)
                    .setLeftTopCorner(Util.Div(16)).setLeftBottomCorner(Util.Div(16)).setRightTopCorner(Util.Div(16)).setRightBottomCorner(Util.Div(16))
                    .load(mData.icon).into(mIcon);
        }
    }

    @Override
    public boolean onTouch(View v, MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            if (v.isFocusable() && v.isFocusableInTouchMode()) {
                v.requestFocus();
            }
        } else if (event.getAction() == MotionEvent.ACTION_UP) {
            return v.callOnClick();
        }
        return false;
    }

    public void onFocusChange(boolean hasFocus) {
        mFocusBg.setFocus(hasFocus);
        mStatus.setBackground(hasFocus ? mStatusFocusBg : mStatusUnFocusBg);
        if (hasFocus) {
            mName.setTextColor(Color.BLACK);
            mStatus.getPaint().setFakeBoldText(true);
            mStatus.setTextColor(getResources().getColor(R.color.black_80));
        } else {
            mName.setTextColor(Color.WHITE);
            mStatus.getPaint().setFakeBoldText(false);
            mStatus.setTextColor(getResources().getColor(R.color.white_80));
        }
    }

    @Override
    public void destroy() {

    }
}
