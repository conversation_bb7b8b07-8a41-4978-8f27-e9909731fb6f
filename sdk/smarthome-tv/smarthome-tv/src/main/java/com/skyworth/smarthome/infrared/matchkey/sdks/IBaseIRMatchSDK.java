package com.skyworth.smarthome.infrared.matchkey.sdks;

import com.skyworth.smarthome.common.bean.IRMatchKeyData;

import java.util.Map;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/9/2 16:09.
 */
public interface IBaseIRMatchSDK {
    /**
     * SDK初始化
     */
    void init(Map<String, String> params);

    /**
     * 开始匹配
     */
    void start();

    /**
     * 当前码值有响应
     */
    void onKeyResponse();

    /**
     * 当前码值无响应
     */
    void onKeyNotResponse();

    /**
     * 设置匹配结果回调
     *
     * @param listener
     */
    void setResultListener(IRMatchKeyResultListener listener);

    /**
     * 获取当前按键码值信息
     */
    IRMatchKeyData getCurrentKeyInfo();

    /**
     * 获取本组按键当前匹配到了第几个
     *
     * @return
     */
    int getCurrentIndex();

    /**
     * 获取本组按键总数量
     *
     * @return
     */
    int getCurrentKeyListCount();

    Map<String, Object> getSendIrParams();

    Map<String, Object> getSaveMatchResultParams();

    void destroy();
}
