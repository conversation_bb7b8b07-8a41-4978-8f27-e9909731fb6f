package com.skyworth.smarthome.service.push.binder;

import com.alibaba.fastjson.JSONObject;
import com.skyworth.smarthome.devices.discover.model.DiscoverDeviceModel;
import com.skyworth.smarthome.service.push.binder.model.AIOTDataPushModel;
import com.skyworth.smarthome.service.push.binder.model.IAiotDataPushModel;
import com.skyworth.smarthome.account.IAppAccountManager;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.service.model.ISmartHomeModel;
import com.skyworth.smarthome.service.push.local.DeviceDataPushUtil;
import com.skyworth.smarthome.voicehandle.model.OtherVoiceHandlerModel;
import com.smarthome.common.utils.EmptyUtils;
import com.swaiot.aiotlib.common.base.ReceiveListener;
import com.swaiot.aiotlib.common.entity.DiscoverNetworkDevice;
import com.swaiot.aiotlib.common.entity.DiscoverWifiDevice;

import java.util.ArrayList;
import java.util.List;


/**
 * @ClassName: AIOTPushServiceCallbackBinder
 * @Author: AwenZeng
 * @CreateDate: 2020/3/27 17:30
 * @Description:
 */
public class AIOTPushServiceCallbackBinder implements ReceiveListener {
    private IAiotDataPushModel mAiotDataPushModel;
    private DiscoverDeviceModel mDiscoverDeviceModel;
    private OtherVoiceHandlerModel mOtherVoiceHandlerModel;

    public AIOTPushServiceCallbackBinder() {
        mAiotDataPushModel = new AIOTDataPushModel();
        mDiscoverDeviceModel = new DiscoverDeviceModel();
        mOtherVoiceHandlerModel = new OtherVoiceHandlerModel();
    }

    @Override
    public void onInit(String data) {
        try{
            JSONObject jsonObject =  JSONObject.parseObject(data);
            String screenId = jsonObject.getString("screen_id");
            AppData.getInstance().setScreenId(screenId);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public void onAccountChange() {
        ISmartHomeModel.INSTANCE.getSmartDeviceList("");
        if(IAppAccountManager.INSTANCE.hasLogin(false)){
            ISmartHomeModel.INSTANCE.getFamilyList();
        }else{
            DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.FAMILY_LIST, "");
            DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.SCENE_LIST, "");
        }
    }

    @Override
    public void onReceive(String event, String data) {
        mAiotDataPushModel.onReceivedData(event, data);
    }

    @Override
    public void onHandleDataCallback(String object_type, String device_id, String data) {
        switch (object_type) {
            case AppConstants.KEY_HANDEL_DEVICE:
                mAiotDataPushModel.onHandleDeviceStatusData(data);
                DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.DEVICE_STATUS, data);
                break;
        }
    }

    @Override
    public void onDiscoverWifiDevice(List<DiscoverWifiDevice> deviceList) {
        mDiscoverDeviceModel.sendSmartDeviceInfo(deviceList);
    }

    @Override
    public void onDiscoverNetworkDevice(List<DiscoverNetworkDevice> deviceList) {
        if (EmptyUtils.isNotEmpty(deviceList)){
            List<DiscoverNetworkDevice> tempDeviceList = new ArrayList<>();
            for(DiscoverNetworkDevice device:deviceList){
               if(!device.product_type_id.equals("1")){//局域网中的设备屏蔽掉电视
                   tempDeviceList.add(device);
               }
            }
            AppData.getInstance().setDiscoverNetworkDevice(tempDeviceList);
            mDiscoverDeviceModel.checkDevicesBindStatus(tempDeviceList);
        }
    }

    @Override
    public void onSpecialVoiceHandle(String cmd) {
        mOtherVoiceHandlerModel.handleCmd(cmd);
    }

    @Override
    public void onHttpCallBack(int code, String msg, String data, String resource_type, String commandId) {
        if (EmptyUtils.isEmpty(commandId)) {
            switch (resource_type) {
                case AppConstants.KEY_HTTP_DEVCIE_LIST:
                    mAiotDataPushModel.onHandleDeviceListData(data);
                    DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.DEVICE_LIST, data);
                    break;
                case AppConstants.KEY_HTTP_SCENE_LIST:
                    mAiotDataPushModel.onHandleSceneListData(data);
                    DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.SCENE_LIST, data);
                    break;
                case AppConstants.KEY_HTTP_FAMILY_LIST:
                    mAiotDataPushModel.onHandleFamilyListData(data);
                    DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.FAMILY_LIST, data);
                    break;
                case AppConstants.KEY_HTTP_CURRENT_FAMILY:
                    mAiotDataPushModel.onHandleCurrentFamilyData(data);
                    DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.CURRENT_FAMILY, data);
                    break;
                case AppConstants.KEY_HTTP_HOME_STATUS:
                    DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.HOME_STATUS_INFO, data);
                    break;
                case AppConstants.KEY_HTTP_DEVICE_NOTIFY:
                    DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.DEVICE_NOTIFY, data);
                    break;
                default:
                    break;
            }
        }
    }
}