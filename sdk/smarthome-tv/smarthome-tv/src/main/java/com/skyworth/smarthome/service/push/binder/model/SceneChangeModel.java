package com.skyworth.smarthome.service.push.binder.model;

import com.alibaba.fastjson.JSONObject;
import com.skyworth.smarthome.common.bean.MessageBean;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.util.ThreadManager;
import com.skyworth.smarthome.message.MessageDialog;
import com.skyworth.smarthome.service.model.ISmartDeviceDataModel;
import com.smarthome.common.utils.EmptyUtils;
import com.swaiot.aiotlib.common.entity.SceneBean;

/**
 * @ClassName: SceneChangeModel
 * @Author: AwenZeng
 * @CreateDate: 2019/11/26 16:44
 * @Description: 场景变化Model
 */
public class SceneChangeModel implements ISceneChangeModel {

    @Override
    public void loadSceneListData(String data) {
    }

    @Override
    public void receiveSceneExecute(String data) {
        if (EmptyUtils.isEmpty(data))
            return;
        JSONObject jsonObject = JSONObject.parseObject(data);
        String sceneId = jsonObject.getString("scene_id");
        SceneBean sceneBean = ISmartDeviceDataModel.INSTANCE.getSceneInfo(sceneId);
        if (EmptyUtils.isNotEmpty(sceneBean)) {
            final MessageBean messageBean = new MessageBean();
            messageBean.type = AppConstants.MESSAGE_TYPE.SCENE_VOICE_CONTROL_RESULT;
            messageBean.voiceTips = sceneBean.voice_tips;
            messageBean.content = sceneBean.scene_name + "发送成功，执行场景中";
            ThreadManager.getInstance().uiThread(new Runnable() {
                @Override
                public void run() {
                    MessageDialog dialog = new MessageDialog();
                    dialog.showDialog(messageBean);
                }
            });
        }
    }
}
