package com.skyworth.smarthome.infrared.matchkey.presenter.step;

import com.skyworth.smarthome.infrared.matchkey.presenter.IMatchKeyPresenter;

import static com.skyworth.smarthome.infrared.matchkey.presenter.IMatchKeyPresenterImpl.HANDLE_KEY_SEND_FINISH;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/4/29 18:30.
 */
public class SendIRCodeStep extends IRConfigBaseStep<IMatchKeyPresenter> {
    public static final String STEP_TAG = "send_ir";

    @Override
    public void create() {
        super.create();
    }

    @Override
    public void run() {
        super.run();
        presenter.showSendingView();
        presenter.sendIR();
    }

    @Override
    public boolean input(String msg, Object... params) {
        switch (msg) {
            case HANDLE_KEY_SEND_FINISH:
                next();
                return true;
        }
        return false;
    }

    @Override
    public String getTag() {
        return STEP_TAG;
    }

    @Override
    public void destroy() {
        super.destroy();
    }

}
