package com.skyworth.smarthome.devices.apconfig.presenter.step.auto;

import com.coocaa.app.core.app.AppCoreApplication;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.util.Utils;
import com.skyworth.smarthome.devices.apconfig.presenter.helpers.ConfirmBindHelper;
import com.skyworth.smarthome.devices.apconfig.presenter.step.BaseStep;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_CONFIG_FINISH;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_CONFIRM_BIND_SHOW;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_INPUT_CONFIRM_BIND_CANCEL;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_INPUT_CONFIRM_BIND_OK;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_INPUT_SCREEN_SAVER_CLOSE;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/9/17 11:26.
 */
public class AutoStepConfirmBind extends BaseStep {
    public static final String STEP_TAG = "auto_confirm_bind";

    @Override
    public void run() {
        super.run();
        if (Utils.isScreenSaverShow()) {
            logi("wait for screen saver close");
            presenter.setConfirmHelperFlag(ConfirmBindHelper.UnConfirmData.STATUS_NOT_BIND);
        } else {
            if (presenter.hasShowLoginAndBind()) {
                logi("has show login and bind dialog confirm bind directly");
                confirmBind();
            } else {
                showConfirm();
            }
        }
    }

    @Override
    public boolean input(String msg, Object... params) {
        if (STEP_MSG_INPUT_SCREEN_SAVER_CLOSE.equals(msg)) {
            showConfirm();
            presenter.logBindAfterScreenSaver();
            return true;
        } else if (STEP_MSG_INPUT_CONFIRM_BIND_OK.equals(msg)) {
            confirmBind();
            return true;
        } else if (STEP_MSG_INPUT_CONFIRM_BIND_CANCEL.equals(msg)) {
            presenter.hideBindConfirm();
            AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
                @Override
                public Unit invoke() {
                    presenter.unbindCurrentDevice();
                    presenter.recordNotBindAndExit(AppConstants.APCONFIG_FAIL_REASON_USER_CANCEL_BIND);
                    return Unit.INSTANCE;
                }
            });
            return true;
        }
        return false;
    }

    private void confirmBind() {
        presenter.hideBindConfirm();
        output(STEP_MSG_CONFIG_FINISH);
        presenter.logAutoBindEnd(true);
    }

    private void showConfirm() {
        presenter.showDialog();
        presenter.clearConfirmHelperFlag();
        output(STEP_MSG_CONFIRM_BIND_SHOW);
    }

    @Override
    public String getTag() {
        return STEP_TAG;
    }

    @Override
    public void destroy() {
        super.destroy();
    }
}
