package com.skyworth.smarthome.infrared.learn.view.pages;

import android.content.Context;
import android.graphics.Color;
import android.text.SpannableStringBuilder;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.TextView;

import android.support.annotation.NonNull;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.util.SpannableStringUtils;
import com.skyworth.util.Util;

import java.util.HashMap;
import java.util.Map;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/7/2 18:25.
 */
public class IRLearnSuccessView extends BaseIRLearnViews {
    public static final String PARAMS_KEY_KEYNAME = "keyName";
    public static final String PARAMS_KEY_SECONDS = "seconds";
    private TextView mTitle = null;
    private TextView mTip = null;

    public IRLearnSuccessView(@NonNull Context context) {
        super(context);
        initTitle();
        initTip();
    }

    private void initTitle() {
        mTitle = new TextView(getContext());
        mTitle.setTextSize(Util.Dpi(36));
        mTitle.setTextColor(Color.WHITE);
        mTitle.getPaint().setFakeBoldText(true);
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.topMargin = Util.Div(80);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        addView(mTitle, layoutParams);
    }

    private void initTip() {
        mTip = new TextView(getContext());
        mTip.setTextColor(Color.parseColor("#aaFFFFFF"));
        mTip.setTextSize(Util.Dpi(32));
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.topMargin = Util.Div(144);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        addView(mTip, layoutParams);
    }

    @Override
    protected void onShow(Map<String, Object> params) {
        if (params == null || params.size() <= 0) {
            return;
        }
        String keyName = (String) params.get(PARAMS_KEY_KEYNAME);
        int seconds = (int) params.get(PARAMS_KEY_SECONDS);

        mTitle.setText(getResources().getString(R.string.ir_learn_success_title, keyName));
        String tip1 = getResources().getString(R.string.ir_learn_success_tip);
        SpannableStringBuilder tip = SpannableStringUtils.getBuilder(tip1).append(" ").append(seconds + "s").setBold().create();
        mTip.setText(tip);
    }

    @Override
    public int getViewWidth() {
        return Util.Div(714);
    }

    @Override
    public int getViewHeight() {
        return Util.Div(400);
    }

    public static Map<String, Object> getParams(String keyName, int seconds) {
        HashMap<String, Object> map = new HashMap<>(2);
        map.put(PARAMS_KEY_KEYNAME, keyName);
        map.put(PARAMS_KEY_SECONDS, seconds);
        return map;
    }

    @Override
    public String getName() {
        return "IRLearnSuccessView";
    }
}
