package com.skyworth.smarthome.service.model;

import android.os.Bundle;

import com.alibaba.fastjson.JSONObject;
import com.skyworth.smarthome.devices.apconfig.presenter.step.manual.ManualStepBindDevice;
import com.skyworth.smarthome.devices.discover.dialog.AddDeviceDialog;
import com.skyworth.smarthome.devices.discover.dialog.AddDeviceResultDialog;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.util.DialogLauncherUtil;
import com.skyworth.smarthome.common.util.SystemProperty;
import com.skyworth.smarthome.devices.apconfig.ApConfigService;
import com.smarthome.common.utils.EmptyUtils;
import com.swaiot.aiotlib.common.entity.DiscoverNetworkDevice;
import com.swaiot.aiotlib.common.entity.DiscoverWifiDevice;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:功能跳转Model
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/9/17
 */
public class FunctionGoToModel implements IFunctionGoToModel {

    @Override
    public void goToAddScanSmartDevice(String origin) {
        DialogLauncherUtil.showAddDevice(origin, AddDeviceDialog.SCAN_TIME);
    }

    @Override
    public void goToDeviceConfigAuto(DiscoverNetworkDevice device) {
        Bundle params = new Bundle();
        params.putString(ApConfigService.EXTRA_KEY, JSONObject.toJSONString(device.deviceInfo));
        params.putString(ApConfigService.TYPE_KEY, ApConfigService.TYPE_AUTO);
        ApConfigService.launch(params);
    }

    @Override
    public void goToDeviceConfigManual(final DiscoverNetworkDevice device) {
        try{
            if (isContain(device)) {
                Bundle params = new Bundle();
                params.putString(ApConfigService.EXTRA_KEY, JSONObject.toJSONString(device.deviceInfo));
                params.putString(ApConfigService.TYPE_KEY, ApConfigService.TYPE_MANUAL);
                ApConfigService.launch(params);
            } else {
                Map<String, String> map = new HashMap<>();
                map.put(AddDeviceResultDialog.KEY_DEVICEINFO, JSONObject.toJSONString(device));
                DialogLauncherUtil.showAddResultDialog(map);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public void goToDeviceConfigManualWithDiscoverDialog(DiscoverNetworkDevice device) {
        Bundle params = new Bundle();
        params.putString(ApConfigService.EXTRA_KEY, JSONObject.toJSONString(device.deviceInfo));
        params.putString(ApConfigService.TYPE_KEY, ApConfigService.TYPE_MANUAL);
        params.putStringArray(ApConfigService.EXTRA_FLAG_KEY, new String[]{ApConfigService.EXTRA_FLAG_SHOW_DISCOVER});
        ApConfigService.launch(params);
    }

    /**
     * 设备是否还处于配网中
     *
     * @param device
     * @return
     */
    private boolean isContain(DiscoverNetworkDevice device) {
        List<DiscoverWifiDevice> discoverDeviceInfos = AppData.getInstance().getDiscoverUnConfigNetDeviceList();
        if (EmptyUtils.isNotEmpty(discoverDeviceInfos)) {
            for (DiscoverWifiDevice item : discoverDeviceInfos) {
                if (EmptyUtils.isNotEmpty(item.getWifiInfo().BSSID)&&item.getWifiInfo().BSSID.equals(device.device_id)) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public void goToDeviceBind(DiscoverNetworkDevice device) {
        Bundle params = new Bundle();
        params.putString(ApConfigService.EXTRA_KEY, JSONObject.toJSONString(device));
        params.putString(ApConfigService.TYPE_KEY, ApConfigService.TYPE_MANUAL);
        params.putString(ApConfigService.DIRECT_TO_STEP_KEY, ManualStepBindDevice.STEP_TAG);
        ApConfigService.launch(params);
    }

    @Override
    public void goToAppDownloadGuide(Map<String, String> params) {
        DialogLauncherUtil.showDownloadRrCodeDialog(params);
    }

    @Override
    public void goToInfraredDeviceList(String from, String deviceId) {
        String deviceID = deviceId;
        if (EmptyUtils.isEmpty(deviceId)) {
            deviceID = SystemProperty.getDeviceId();
        }
        Map<String, String> params = new HashMap<>();
        params.put(AppConstants.KEY_SELECT_ID, deviceID);
        params.put(AppConstants.KEY_ENTER_INFRARED_FROM, from);
        DialogLauncherUtil.showInfraredDialog(params);
    }

    @Override
    public void goToAddInfraredDevice(String from) {
        String deviceID = SystemProperty.getDeviceId();
        if (EmptyUtils.isNotEmpty(deviceID)) {
            Map<String, String> params = new HashMap<>();
            params.put(AppConstants.KEY_SELECT_ID, deviceID);
            params.put(AppConstants.KEY_ENTER_INFRARED_ADD, "true");
            params.put(AppConstants.KEY_ENTER_INFRARED_FROM, from);
            DialogLauncherUtil.showInfraredDialog(params);
        }
    }
}
