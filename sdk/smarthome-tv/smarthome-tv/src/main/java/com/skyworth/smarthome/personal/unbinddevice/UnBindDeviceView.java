package com.skyworth.smarthome.personal.unbinddevice;

import android.content.Context;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.widget.TextView;

import com.skyworth.smarthome.home.smartdevice.devicelist.BaseDeviceItemView;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.util.ViewsBuilder;
import com.skyworth.ui.newrecycleview.NewRecycleAdapterItem;
import com.skyworth.util.Util;

/**
 * @ProjectName: NewTV_SmartHome
 * @Package: com.skyworth.smarthome_tv.personal.unbinddevice
 * @ClassName: UnBindDeviceView
 * @Description: java类作用描述
 * @Author: wangyuehui
 * @CreateDate: 2020/6/8 20:55
 * @UpdateUser: 更新者
 * @UpdateDate: 2020/6/8 20:55
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class UnBindDeviceView extends BaseDeviceItemView implements NewRecycleAdapterItem<DeviceInfo> {

    private View mView;
    private TextView mDeleteTv;

    public UnBindDeviceView(Context context) {
        super(context);
        addView(new View(getContext()), new LayoutParams(Util.Div(410 + 10), Util.Div(246 + 10), Gravity.CENTER));
        mView = ViewsBuilder.getUnbindItemMenuPage(getContext());
        mDeleteTv = mView.findViewById(R.id.unbind_devices_delete);
        LayoutParams lp = new LayoutParams(Util.Div(410), Util.Div(246));
        lp.gravity = Gravity.CENTER;
        addView(mView, lp);
        mView.setVisibility(INVISIBLE);
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN) {
            if (event.getKeyCode() == KeyEvent.KEYCODE_MENU) {
                mView.setVisibility(VISIBLE);
            } else if (event.getKeyCode() == KeyEvent.KEYCODE_BACK) {
                if (mView.getVisibility() == VISIBLE) {
                    mView.setVisibility(INVISIBLE);
                    return true;
                }
            }
        }
        return super.dispatchKeyEvent(event);
    }

    @Override
    public View getView() {
        return this;
    }

    @Override
    public void onUpdateData(DeviceInfo data, int position) {
        refreshUI(data,position);
        if(data.is_unBind){
            mDeleteTv.setText("删除");
        }
    }

    @Override
    public void clearItem() {

    }

    @Override
    public void refreshUI() {

    }

    public boolean getMenuViewStatus() {
        return mView.getVisibility() == VISIBLE;
    }

    public void removeUnbindView() {
        mView.setVisibility(INVISIBLE);
    }

    @Override
    public void onFocusChange(View view, boolean hasFocus) {
        super.onFocusChange(view, hasFocus);
        if (!hasFocus) {
            mView.setVisibility(INVISIBLE);
        }
    }

    @Override
    public void destroy() {
        onDestroy();
    }
}
