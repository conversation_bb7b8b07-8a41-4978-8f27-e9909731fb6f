package com.skyworth.smarthome.infrared.learn.model.statuslistener;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/7/5 17:24.
 */
public class IRReceiverFactory {
    private static final String IR_TYPE_IRTV = "1";
    private static final String IR_TYPE_AI_SPIRIT = "47";

    public static ILearnStatusReceiver create(String typeId) {
        switch (typeId) {
            case IR_TYPE_AI_SPIRIT:
                return new IRAISpiritReceiver();
            case IR_TYPE_IRTV:
                return new IRTVReceiver();
            default:
                return new IRTVReceiver();
        }
    }
}
