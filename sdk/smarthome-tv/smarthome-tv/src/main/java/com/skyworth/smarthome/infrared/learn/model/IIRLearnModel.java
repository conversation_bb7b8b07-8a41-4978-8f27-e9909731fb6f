package com.skyworth.smarthome.infrared.learn.model;

import com.skyworth.smarthome.common.base.IModel;
import com.skyworth.smarthome.common.bean.IRLearnKeyBean;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/7/2 14:43.
 */
public interface IIRLearnModel extends IModel {
    boolean loadRemoteLearnList(String type_id, String is_common);

    String createIRChildDevice(String deviceId, int typeId, String name);

    boolean saveLearntKey(String keyName, int keyId, String deviceId, String code);

    void regLearnNotify(IIRLearnNotify listener);

    int getSizeOfLearnList();

    IRLearnKeyBean getKeyNeedLearn(int index);

    void startListen(String irTypeId);

    boolean startLearnStatus(String deviceId, String key_id);

    void setDeviceId(String deviceId);

    void checkLearnResult(String STDRValue);

    interface IIRLearnNotify {
        void onLearnStart();

        void onLearnSuccess(int[] keyCode);

        void onLearnFail();
    }
}
