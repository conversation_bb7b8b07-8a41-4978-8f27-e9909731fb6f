package com.skyworth.smarthome.personal.thirdaccount;

import android.os.Bundle;

import com.skyworth.smarthome.personal.thirdaccount.presenter.IThirdAccountPresenter;
import com.skyworth.smarthome.personal.thirdaccount.presenter.ThirdAccountPresenter;
import com.skyworth.smarthome.personal.thirdaccount.view.IThirdAccountView;
import com.skyworth.smarthome.personal.thirdaccount.view.ThirdAccountLayout;
import com.skyworth.smarthome.common.base.BaseActivity;

/**
 * @Description: 关联账号
 * @Author: wzh
 * @CreateDate: 2020/6/3
 */
public class ThirdAccountActivity extends BaseActivity {

    private IThirdAccountView mView;
    private IThirdAccountPresenter mPresenter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mView = new ThirdAccountLayout(this);
        mPresenter = new ThirdAccountPresenter();
        mView.setPresenter(mPresenter);
        mPresenter.setView(mView);
        setContentView(mView.getView());
        mPresenter.loadData();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mView != null) {
            mView.destroy();
        }
        if (mPresenter != null) {
            mPresenter.destroy();
        }
    }
}
