package com.skyworth.smarthome.devices.apconfig;

import android.util.Log;
import android.view.KeyEvent;

import com.skyworth.smarthome.devices.apconfig.presenter.IApConfigPresenter;
import com.skyworth.smarthome.common.base.BaseSysDialog;
import com.skyworth.smarthome.common.util.DialogLauncherUtil;
import com.smarthome.common.utils.Android;

import java.util.Map;

import static com.skyworth.smarthome.devices.apconfig.ApConfigService.TAG;

/**
 * Description: wifi智能设备自动配网入口 <br>
 * Created by wzh on 2019/4/9 17:05.
 */
public class ApConfigDialog extends BaseSysDialog {

    private IApConfigPresenter presenter;

    @Override
    protected void initParams() {
        mDialogKey = DialogLauncherUtil.DIALOG_KEY_APCONFIG;
    }

    @Override
    protected void initContentView() {
        super.initContentView();
    }

    @Override
    public void showDialog(final Map<String, String> params){
        super.showDialog(params);
        Log.i(TAG, "onShowDialog");
        //禁用屏保
        Android.disableScreenSaver(mContext);
    }

    public void setPresenter(IApConfigPresenter presenter) {
        this.presenter = presenter;
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (presenter != null) {
            if (presenter.dispatchKeyEvent(event)) {
                return true;
            }
        }
        return super.dispatchKeyEvent(event);
    }

    @Override
    protected void onDismiss() {
        super.onDismiss();
        Log.i(TAG, "onDismiss");
        if (presenter != null) {
            presenter.onDialogDismiss();
        }
        //恢复屏保
        Android.releaseScreenSaver();
    }
}
