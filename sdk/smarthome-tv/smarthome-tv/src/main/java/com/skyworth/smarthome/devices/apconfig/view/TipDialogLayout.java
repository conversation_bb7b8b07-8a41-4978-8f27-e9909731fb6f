package com.skyworth.smarthome.devices.apconfig.view;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.skyworth.smarthome.common.ui.CardButton;
import com.skyworth.smarthome.common.ui.DialogBg;
import com.skyworth.util.Util;

public class TipDialogLayout extends FrameLayout {
    private TextView tip1 = null;
    private TextView tip2 = null;
    private CardButton button1 = null;
    private CardButton button2 = null;
    private int buttonCount = 0;
    private TipDialogListener listener = null;
    private PopupWindow mPopupWindow;
    private OnFocusChangeListener onFocusChangeListener = new OnFocusChangeListener() {
        @Override
        public void onFocusChange(View v, boolean hasFocus) {
            if (hasFocus && listener != null) {
                listener.onFocusChange();
            }
        }
    };

    public interface TipDialogListener {
        void onButton1();

        void onButton2();

        void onFocusChange();
    }

    public void setListener(TipDialogListener listener) {
        this.listener = listener;
    }

    public TipDialogLayout(Context context, int count) {
        super(context);
        buttonCount = count;
        initBackground();
        initButtons();
//        initPopWindow();
    }

    private void initBackground(){
        setBackground(new DialogBg());
    }

    private void initButtons() {
        button1 = createButton();
        button1.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (listener != null) {
                    listener.onButton1();
                }
            }
        });
        if (buttonCount == 1) {
            LayoutParams layoutParams = new LayoutParams(Util.Div(614), Util.Div(90));
            layoutParams.gravity = Gravity.BOTTOM | Gravity.CENTER_HORIZONTAL;
            layoutParams.bottomMargin = Util.Div(50);
            addView(button1, layoutParams);
        } else if (buttonCount == 2) {
            LayoutParams layoutParams = new LayoutParams(Util.Div(292), Util.Div(90));
            layoutParams.gravity = Gravity.BOTTOM;
            layoutParams.leftMargin = Util.Div(50);
            layoutParams.bottomMargin = Util.Div(50);
            addView(button1, layoutParams);

            button2 = createButton();
            button2.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (listener != null) {
                        listener.onButton2();
                    }
                }
            });
            layoutParams = new LayoutParams(Util.Div(292), Util.Div(90));
            layoutParams.gravity = Gravity.BOTTOM | Gravity.RIGHT;
            layoutParams.rightMargin = Util.Div(50);
            layoutParams.bottomMargin = Util.Div(50);
            addView(button2, layoutParams);
        }
    }

    private CardButton createButton() {
        CardButton cardButton = new CardButton(getContext());
        cardButton.setExtOnFocusChangeListener(onFocusChangeListener);
        return cardButton;
    }

    private void createTip1() {
        tip1 = new TextView(getContext());
        tip1.setTextColor(Color.WHITE);
        tip1.setTextSize(Util.Dpi(36));
        tip1.getPaint().setFakeBoldText(true);
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.topMargin = Util.Div(84);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        addView(tip1, layoutParams);
    }

    private void createTip2() {
        tip2 = new TextView(getContext());
        tip2.setTextColor(Color.parseColor("#aaFFFFFF"));
        tip2.setTextSize(Util.Dpi(32));
        tip2.setSingleLine();
        tip2.setGravity(Gravity.CENTER);
        LayoutParams layoutParams = new LayoutParams(Util.Div(614), ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.topMargin = Util.Div(148);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        addView(tip2, layoutParams);

    }
//屏蔽美的活动推荐提示
//    private void initPopWindow(){
//        if (mPopupWindow == null) {
//            View  view = new View(getContext());
//            view.setBackgroundResource(R.drawable.pop_media_discover_bg);
//            mPopupWindow = new PopupWindow(view, Util.Div(178), Util.Div(52));
//            mPopupWindow.setOutsideTouchable(false);
//        }
//    }
//    public void showPopWindow(){
//        ThreadManager.getInstance().uiThread(new Runnable() {
//            @Override
//            public void run() {
//                int[] xy = new int[2];
//                tip1.getLocationInWindow(xy);
//                mPopupWindow.showAtLocation(tip1, Gravity.NO_GRAVITY,
//                        xy[0] + tip1.getWidth() + Util.Div(5), xy[1]);
//            }
//        },500);
//
//    }

    public void setTip1Text(String tipText) {
        if (tip1 == null) {
            createTip1();
        }
        tip1.setText(tipText);
//        showPopWindow();
    }

    public TextView getTip1(){
        return tip1;
    }




    public void setTip2Text(String tip2Text) {
        if (tip2 == null) {
            createTip2();
        }
        tip2.setText(tip2Text);
    }

    public void setButton1Text(String buttonText1) {
        if (button1 != null) {
            button1.setText(buttonText1);
        }
    }

    public void setButton2Text(String buttonText2) {
        if (button2 != null) {
            button2.setText(buttonText2);
        }
    }

    public void setFocus(final int buttonIndex) {
        if (buttonIndex == 0) {
            if (button1 != null) {
                post(new Runnable() {
                    @Override
                    public void run() {
                        button1.requestFocus();
                    }
                });
            }
        } else if (buttonIndex == 1) {
            if (button2 != null) {
                post(new Runnable() {
                    @Override
                    public void run() {
                        button2.requestFocus();
                    }
                });
            }
        }
    }
}
