package com.skyworth.smarthome.home.smartdevice.controlpanel.view;

import android.content.Context;
import android.graphics.Color;
import android.support.v7.widget.NewRecycleAdapter;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;


import com.skyworth.smarthome.home.smartdevice.controlpanel.view.item.AlarmLogItem;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.bean.AlarmLogBean;
import com.skyworth.smarthome.common.event.CloseDeviceControlDialogEvent;
import com.skyworth.smarthome.service.model.FunctionGoToModel;
import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.ui.newrecycleview.NewRecycleAdapterItem;
import com.skyworth.ui.newrecycleview.NewRecycleLayout;
import com.skyworth.util.Util;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SensorRecordView extends LinearLayout {
    private NewRecycleLayout<AlarmLogBean> recordListLayout;
    private TextView tvMoreRecord;
    private NewRecycleAdapter<AlarmLogBean> mAdapter;
    private List<AlarmLogBean> mDataList = new ArrayList<>();

    public SensorRecordView(Context context) {
        super(context);
        initUI();
    }

    private void initUI() {
        setOrientation(VERTICAL);
        recordListLayout = new NewRecycleLayout<AlarmLogBean>(getContext());
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
        params.topMargin = Util.Div(40);
        addView(recordListLayout, params);

        tvMoreRecord = new TextView(getContext());
        CCFocusDrawable focusDrawable = new CCFocusDrawable(getContext()).setRadius(Util.Div(64));
        tvMoreRecord.setGravity(Gravity.CENTER);
        tvMoreRecord.setTextSize(Util.Dpi(24));
        tvMoreRecord.setTextColor(Color.BLACK);
        tvMoreRecord.setFocusable(true);
        tvMoreRecord.setGravity(Gravity.CENTER);
        tvMoreRecord.setFocusableInTouchMode(true);
        tvMoreRecord.setBackground(focusDrawable);
        tvMoreRecord.setText(getResources().getString(R.string.control_panel_more_records));
        tvMoreRecord.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                Map<String, String> params = new HashMap<>();
                FunctionGoToModel.INSTANCE.goToAppDownloadGuide(params);
                EventBus.getDefault().post(new CloseDeviceControlDialogEvent());
            }
        });

        params = new LayoutParams(Util.Div(354), Util.Div(64));
        params.leftMargin = Util.Div(113);
        params.topMargin = Util.Div(10);
        addView(tvMoreRecord, params);
        tvMoreRecord.setVisibility(GONE);
    }

    public void refreshUI(List<AlarmLogBean> dataList) {
        mDataList.clear();
        mDataList.addAll(dataList);
        if (dataList.size() == 0) {
            showEmptyView();
            return;
        }
        if (dataList.size() > 5) {
            tvMoreRecord.setVisibility(VISIBLE);
            tvMoreRecord.requestFocus();
        } else {
            tvMoreRecord.setVisibility(GONE);
        }
        if (mAdapter == null) {
            mAdapter = new NewRecycleAdapter<AlarmLogBean>(mDataList, 1) {
                @Override
                public NewRecycleAdapterItem<AlarmLogBean> onCreateItem(Object type) {
                    AlarmLogItem alarmLogItem = new AlarmLogItem(getContext());
                    alarmLogItem.setHideBottomLinePosition(mDataList.size() - 1);
                    return alarmLogItem;
                }
            };
            recordListLayout.setRecyclerAdapter(mAdapter);
        } else {
            recordListLayout.notifyDataSetChanged();
        }
    }

    public void showEmptyView() {
        removeAllViews();
        ImageView icon = new ImageView(getContext());
        icon.setBackgroundResource(R.drawable.empty_icon);
        LayoutParams params = new LayoutParams(Util.Div(400), Util.Div(200));
        params.topMargin = Util.Div(154);
        params.leftMargin = Util.Div(90);
        addView(icon, params);

        TextView mTips = new TextView(getContext());
        mTips.setTextSize(Util.Dpi(32));
        mTips.getPaint().setFakeBoldText(true);
        mTips.setTextColor(Color.parseColor("#CCCCCC"));
        mTips.setGravity(Gravity.CENTER);
        mTips.setText(getResources().getString(R.string.control_panel_no_records));
        params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.topMargin = Util.Div(30);
        params.leftMargin = Util.Div(226);
        addView(mTips, params);
    }
}
