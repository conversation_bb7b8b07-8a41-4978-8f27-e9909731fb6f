package com.skyworth.smarthome.devices.apconfig.presenter.step.manual;

import android.view.KeyEvent;

import com.coocaa.app.core.app.AppCoreApplication;
import com.skyworth.framework.skysdk.util.SkyObjectByteSerialzie;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.common.event.WifiConnectEvent;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.util.NetworkUtils;
import com.skyworth.smarthome.devices.apconfig.model.SwitchWifiInfo;
import com.skyworth.smarthome.devices.apconfig.presenter.step.BaseStep;
import com.tianci.net.data.SkySupplicantInfo;
import com.tianci.net.define.NetworkDefs;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_24GWIFI_CLICK;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_24GWIFI_HIDE;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_24GWIFI_SHOW;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_CONFIG_PROGRESS_HIDE;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_INPUT_PASSWORD_HIDE;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_INPUT_PASSWORD_OK;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_INPUT_PASSWORD_SHOW;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/1/7 21:54.
 */
public class ManualStepCheckWifi extends BaseStep {
    public static final String STEP_TAG = "manual_checkWifi";
    private SwitchWifiInfo switchWifiInfo = null;
    private AtomicBoolean isConnecting = new AtomicBoolean(false);
    private boolean isPasswordShow = false;

    @Override
    public void run() {
        super.run();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
        output(STEP_MSG_CONFIG_PROGRESS_HIDE);
        if (NetworkUtils.isCurrentWifi24G(context) && !presenter.isCurrentWifiIsAp()) {
            next();
        } else {
            presenter.show24GHzWifi();
        }
    }

    @Override
    public boolean input(String msg, Object... params) {
        if ((msg.equals(String.valueOf(KeyEvent.ACTION_DOWN)) || msg.equals(String.valueOf(KeyEvent.ACTION_UP))) && params.length >= 1) {
            Object keycode = params[0];
            if (keycode != null && (int) keycode == KeyEvent.KEYCODE_BACK) {
                if (isPasswordShow&&!AppData.getInstance().isOpenSoftPan()) {
                    output(STEP_MSG_INPUT_PASSWORD_HIDE);
                    output(STEP_MSG_24GWIFI_SHOW);
                    isPasswordShow = false;
                    return true;
                }
            }
        }
        if (isConnecting.get()) {
            logi("input: wifi connecting!");
            return false;
        }
        if (msg.equals(STEP_MSG_24GWIFI_CLICK)) {
            if (params != null && params.length > 0 && params[0] instanceof SwitchWifiInfo) {
                switchWifiInfo = (SwitchWifiInfo) params[0];
                switch (switchWifiInfo.lockStatus){
                    case 1://有锁
                    case 2://有锁有缓存密码
                        isPasswordShow = true;
                        output(STEP_MSG_INPUT_PASSWORD_SHOW, switchWifiInfo.wifiName);
                        break;
                    case 3://无锁
                        connectWifi(switchWifiInfo);
                        break;
                }
            }
            return true;
        } else if (msg.equals(STEP_MSG_INPUT_PASSWORD_OK)) {
            connectWifi(switchWifiInfo);
            return true;
        }
        return false;
    }

    private void connectWifi(final SwitchWifiInfo switchWifiInfo) {
        isPasswordShow = false;
        output(STEP_MSG_INPUT_PASSWORD_HIDE);
        if (switchWifiInfo.raw.BSSID.equals(NetworkUtils.getCurrentWifiBSSID(context))) {
            logi("connectWifi: wifi has connected");
            next();
            return;
        }
        if (isConnecting.compareAndSet(false, true)) {
            AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
                @Override
                public Unit invoke() {
                    presenter.connectWifiByDhcp(switchWifiInfo);
                    return Unit.INSTANCE;
                }
            });
        } else {
            logi("connectWifi: is connecting!!");
        }
    }

    @Subscribe()
    public void OnEvent(WifiConnectEvent event) {
        if(event.isConnected){
            logSwitchWifi(true);
            onWifiConnected();
        }else{
            isPasswordShow = true;
            output(STEP_MSG_INPUT_PASSWORD_SHOW, switchWifiInfo.wifiName, SmartHomeTvLib.getContext().getString(R.string.apconfig_connect_network_failed));
            isConnecting.set(false);
        }
//        logi("connect wifi OnEvent: cmd: " + event.cmd);
//        if (TC_NETWORK_BROADCAST_NET_WIFI_EVENT.toString().equals(event.cmd)) {
//            handleStateChange(event);
//
//        } else if (TC_NETWORK_BROADCAST_NET_WIFI_SUPPLICANT_STATE_CHANGED.toString().equals(event.cmd)) {
//            handleSupplicantChange(event);
//        }
    }

    private void handleStateChange(WifiConnectEvent event) {
        NetworkDefs.WifiEvent wifiState = SkyObjectByteSerialzie.toObject(event.body, NetworkDefs.WifiEvent.class);
        if (wifiState == null) {
            return;
        }
        logi("connect wifi handleStateChange: " + wifiState);
        switch (wifiState) {
            case EVENT_WIFI_CONNECT_DISCONNECTED:
                break;
            case EVENT_WIFI_CONNECT_SUCCEEDED:
                logSwitchWifi(true);
                onWifiConnected();
                break;
            case EVENT_WIFI_CONNECT_FAILD:
                logSwitchWifi(false);
                isConnecting.set(false);
                break;
            case EVENT_WIFI_SCAN_FAIL:
                break;
            case EVENT_WIFI_CONNECT_CONNECTING:
                //正在连接中.
                break;
            case EVENT_WIFI_RSSI_CHANGED:
                break;
            default:
                break;
        }
    }

    private void handleSupplicantChange(WifiConnectEvent event) {
        SkySupplicantInfo info = SkyObjectByteSerialzie.toObject(event.body, SkySupplicantInfo.class);
        if (info == null) {
            return;
        }
        logi("connect wifi handleSupplicantChange: " + info.state);
        if (info.state == NetworkDefs.SkySupplicantState.AUTHENTICATING) {
        } else if (info.state == NetworkDefs.SkySupplicantState.ASSOCIATING) {
        } else if (info.state == NetworkDefs.SkySupplicantState.COMPLETED) {
        } else if (info.state == NetworkDefs.SkySupplicantState.DISCONNECTED) {
            //连接超时
//            isPasswordShow = true;
//            output(STEP_MSG_INPUT_PASSWORD_SHOW, switchWifiInfo.wifiName, context.getString(R.string.apconfig_network_err_timeout));
//            isConnecting.set(false);
        } else if (info.state == NetworkDefs.SkySupplicantState.PWD_ERROR) {
            isPasswordShow = true;
            output(STEP_MSG_INPUT_PASSWORD_SHOW, switchWifiInfo.wifiName, context.getString(R.string.apconfig_network_err_pwd));
            isConnecting.set(false);
        } else if (info.state == NetworkDefs.SkySupplicantState.ASSOCIATING) {
        }
    }

    private void onWifiConnected() {
        if (isConnecting.compareAndSet(true, false)) {
            logi("onWifiConnected: reload");
            AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
                @Override
                public Unit invoke() {
                    next();
                    return Unit.INSTANCE;
                }
            });
        }
    }

    private void logSwitchWifi(boolean isSuccess) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put("network_switch_result", isSuccess ? "success" : "fail");
//            LoggerImpl.Companion.onEvent(EVENT_NETWORK_SWITCH24G_END, map);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public String getTag() {
        return STEP_TAG;
    }

    @Override
    public void destroy() {
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        switchWifiInfo = null;
        output(STEP_MSG_24GWIFI_HIDE);
        output(STEP_MSG_INPUT_PASSWORD_HIDE);
        isPasswordShow = false;
        super.destroy();
    }
}
