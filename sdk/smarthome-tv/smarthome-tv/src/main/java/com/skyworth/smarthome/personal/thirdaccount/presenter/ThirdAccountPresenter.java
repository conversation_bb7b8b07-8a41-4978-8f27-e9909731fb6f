package com.skyworth.smarthome.personal.thirdaccount.presenter;

import android.util.Log;

import com.coocaa.app.core.http.HttpServiceManager;
import com.skyworth.smarthome.personal.thirdaccount.view.IThirdAccountView;
import com.skyworth.smarthome.common.bean.ThridAccountHttpBean;
import com.skyworth.smarthome.common.http.SmartDevicesHttpService;
import com.skyworth.smarthome.common.util.ThreadManager;
import com.skyworth.smarthome.common.util.Utils;
import com.smarthome.common.model.SmartBaseData;
import com.smarthome.common.utils.EmptyUtils;

import java.util.List;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/9
 */
public class ThirdAccountPresenter implements IThirdAccountPresenter {

    private IThirdAccountView mView;

    @Override
    public void setView(IThirdAccountView view) {
        mView = view;
    }

    @Override
    public void loadData() {
        mView.showLoading();
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                final SmartBaseData<List<ThridAccountHttpBean>> data = HttpServiceManager.Companion.call(SmartDevicesHttpService.SERVICE.getThirdAccountList(Utils.getAcceptAccountTypes()));
                ThreadManager.getInstance().uiThread(new Runnable() {
                    @Override
                    public void run() {
                        mView.hideLoading();
                        if (data != null && EmptyUtils.isNotEmpty(data.data)) {
                            mView.hideErrorView();
                            mView.refreshUI(data.data);
                        } else {
                            mView.showErrorView("数据加载失败");
                        }
                    }
                });
            }
        });
    }

    @Override
    public void unbindAccount(final int position, final String account_type, final String accName) {
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                final SmartBaseData baseData = HttpServiceManager.Companion.call(SmartDevicesHttpService.SERVICE.unbindThirdAccount(account_type));
                ThreadManager.getInstance().uiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (baseData != null && baseData.code.equals("0")) {
                            Log.i("ThridAccount", "unbindAccount: code:" + baseData.code + "--msg:" + baseData.msg);
                            refreshAccountStatus(position, baseData.code);
                        } else {
                            //解绑失败
                            mView.showToast("解绑失败");
                        }
                    }
                });
            }
        });
    }

    private void refreshAccountStatus(final int position, final String status) {
        if (mView != null) {
            mView.refreshAccountStatus(position, status);
        }
        if (status.equals("1")) {
            mView.showToast("已绑定");
        } else {
            mView.showToast("已解绑");
        }
    }

    @Override
    public void destroy() {

    }
}
