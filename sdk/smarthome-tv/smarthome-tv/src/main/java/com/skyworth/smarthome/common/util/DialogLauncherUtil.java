package com.skyworth.smarthome.common.util;

import android.util.Log;

import com.skyworth.smarthome.common.dialog.DownloadRrCodeDialog;
import com.skyworth.smarthome.devices.apconfig.ApConfigDialog;
import com.skyworth.smarthome.devices.discover.dialog.AddDeviceDialog;
import com.skyworth.smarthome.devices.discover.dialog.AddDeviceResultDialog;
import com.skyworth.smarthome.devices.discover.dialog.DiscoverNearDeviceDialog;
import com.skyworth.smarthome.devices.discover.dialog.ScanNearbyDeviceHelpDialog;
import com.skyworth.smarthome.home.smartdevice.controlpanel.thirdapp.view.ThirdAppLoadingDialog;
import com.skyworth.smarthome.infrared.learn.IRLearnDialog;
import com.skyworth.smarthome.infrared.matchkey.IRMatchKeyDialog;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.base.BaseSysDialog;
import com.skyworth.smarthome.infrared.InfraredDeviceDialog;
import com.smarthome.common.utils.Android;
import com.smarthome.common.utils.EmptyUtils;

import java.util.HashMap;
import java.util.Map;


/**
 * Description: 全局Dialog启动控制工具类（Activity改成Dialog） <br>
 * Created by wzh on 2019/4/8 17:31.
 */
public class DialogLauncherUtil {

    public static final String DIALOG_KEY_ADDDEVICE = "dialog_key_adddevice";
    public static final String DIALOG_KEY_PLAYER = "dialog_key_player";
    public static final String DIALOG_KEY_VOICEFEEDBACK = "dialog_key_voicefeedback";
    public static final String DIALOG_KEY_DEVICEMESSAGE = "dialog_key_devicemessage";
    public static final String DIALOG_KEY_APCONFIG = "dialog_key_apconfig";
    public static final String DIALOG_KEY_DISCOVER_NEAR_DEVICES = "dialog_key_discover_near_devices";
    public static final String DIALOG_KEY_INFRARED_DIALOG = "dialog_key_infrared_dialog";
    public static final String DIALOG_KEY_IRConfig = "dialog_key_irconfig";
    public static final String DIALOG_KEY_IR_LEARN = "dialog_key_ir_learn";
    public static final String DIALOG_KEY_ADD_DEVICE_RESULT = "dialog_key_add_device_result";
    public static final String DIALOG_KEY_DOWNLOAD_QRCODE = "dialog_key_download_QrCode";
    public static final String DIALOG_KEY_BIND_JD = "dialog_key_bind_JD";
    public static final String DIALOG_KEY_BIND_THIRD = "dialog_key_bind_third";
    public static final String DIALOG_KEY_SCAN_HELP = "dialog_key_scan_help";
    public static final String DIALOG_KEY_THIRD_LOADING = "dialog_key_third_loading";
    public static final String DIALOG_KEY_THIRD_ACCOUNT = "dialog_key_third_account";
    public static final String DIALOG_KEY_MESSAGE = "dialog_key_message";

    private static Map<String, BaseSysDialog> mDialogCache = new HashMap<>();

    /**
     * 显示添加设备
     */
    public synchronized static void showAddDevice(String origin, int scanTime) {
        Map<String, String> map = new HashMap<>();
        map.put("origin", origin);
        map.put("scanTime", scanTime + "");
        showPage(DIALOG_KEY_ADDDEVICE, map);
    }

    /**
     * 显示视频门铃
     *
     * @param params 地址/标题
     */
    public synchronized static void showPlayer(Map<String, String> params) {
        showPage(DIALOG_KEY_PLAYER, params);
    }

    /**
     * 显示语音反馈卡片
     *
     * @param params
     */
    public synchronized static void showVoiceControlFeedBack(Map<String, String> params) {
        showPage(DIALOG_KEY_VOICEFEEDBACK, params);
    }

    /**
     * 显示消息弹窗
     *
     * @param params
     */
    public synchronized static void showDeviceMessage(Map<String, String> params) {
        showPage(DIALOG_KEY_DEVICEMESSAGE, params);
    }

    /**
     * 显示wifi智能设备自动配网入口
     *
     * @param params
     */
    public synchronized static void showAppConfig(Map<String, String> params) {
        showPage(DIALOG_KEY_APCONFIG, params);
    }

    /**
     * 显示发现附近的设备（所有网）
     *
     * @param params
     */
    public synchronized static void showDiscoverNearDevices(Map<String, String> params) {
        if (mDialogCache.containsKey(DIALOG_KEY_APCONFIG)||mDialogCache.containsKey(DIALOG_KEY_DISCOVER_NEAR_DEVICES)) return;
        showPage(DIALOG_KEY_DISCOVER_NEAR_DEVICES, params);
    }

    /**
     * 显示添加设备
     */
    public synchronized static void showInfraredDialog(Map<String, String> params) {
        showPage(DIALOG_KEY_INFRARED_DIALOG, params);
    }

    /**
     * 显示红外遥控器匹配
     *
     * @param params
     */
    public synchronized static void showIRMatchKeyDialog(Map<String, String> params) {
        showPage(DIALOG_KEY_IRConfig, params);
    }

    /**
     * 显示红外遥控学习
     *
     * @param params
     */
    public synchronized static void showIRLearnDialog(Map<String, String> params) {
        showPage(DIALOG_KEY_IR_LEARN, params);
    }

    /**
     * 显示添加设备接口弹框
     *
     * @param params
     */
    public synchronized static void showAddResultDialog(Map<String, String> params) {
        showPage(DIALOG_KEY_ADD_DEVICE_RESULT, params);
    }

    /**
     * 显示手机小维智联下载弹框
     *
     * @param params
     */
    public synchronized static void showDownloadRrCodeDialog(Map<String, String> params) {
        showPage(DIALOG_KEY_DOWNLOAD_QRCODE, params);
    }

    /**
     * 显示手机小维智联下载弹框
     *
     * @param params
     */
    public synchronized static void showScanHelpGuideDialog(Map<String, String> params) {
        showPage(DIALOG_KEY_SCAN_HELP, params);
    }


    public synchronized static void showThirdAppLoadingDialog(Map<String, String> params) {
        showPage(DIALOG_KEY_THIRD_LOADING, params);
    }


    private static void showPage(final String pageKey, final Map<String, String> params) {
        try {
            if (Utils.isScreenSaverShow()) {
                Log.i("DialogLauncherUtil", "showPage topAct is ScreensaverActivity");
                ThreadManager.getInstance().ioThread(new Runnable() {
                    @Override
                    public void run() {
                        Android.execByRuntime("input keyevent 4");//在显示弹窗之前模拟一个按键，把屏保关掉
                        showPage1(pageKey, params);
                    }
                });
            } else {
                Log.i("DialogLauncherUtil", "showPage topAct empty");
                showPage1(pageKey, params);
            }
        } catch (Exception e) {
            Log.i("DialogLauncherUtil", "showPage is error :" + e.getMessage());
            showPage1(pageKey, params);
        }
    }

    private static void showPage1(final String pageKey, final Map<String, String> params) {

        ThreadManager.getInstance().uiThread(new Runnable() {
            @Override
            public void run() {
                if (mDialogCache.containsKey(pageKey)) {
                    mDialogCache.get(pageKey).showDialog(params);
                } else {
                    BaseSysDialog dialog = null;
                    switch (pageKey) {
                        case DIALOG_KEY_ADDDEVICE:
                            dialog = new AddDeviceDialog();
                            break;
                        case DIALOG_KEY_APCONFIG:
                            dialog = new ApConfigDialog();
                            break;
                        case DIALOG_KEY_DISCOVER_NEAR_DEVICES:
                            dialog = new DiscoverNearDeviceDialog();
                            break;
                        case DIALOG_KEY_INFRARED_DIALOG:
                            dialog = new InfraredDeviceDialog();
                            break;
                        case DIALOG_KEY_IRConfig:
                            dialog = new IRMatchKeyDialog();
                            break;
                        case DIALOG_KEY_IR_LEARN:
                            dialog = new IRLearnDialog();
                            break;
                        case DIALOG_KEY_ADD_DEVICE_RESULT:
                            dialog = new AddDeviceResultDialog();
                            break;
                        case DIALOG_KEY_DOWNLOAD_QRCODE:
                            dialog = new DownloadRrCodeDialog(R.style.transparent_dialog);
                            break;
                        case DIALOG_KEY_SCAN_HELP:
                            dialog = new ScanNearbyDeviceHelpDialog(R.style.transparent_dialog);
                            break;
                        case DIALOG_KEY_THIRD_LOADING:
                            dialog = new ThirdAppLoadingDialog();
                            break;
                    }
                    if (dialog != null) {
                        putDialog(pageKey, dialog);
                        dialog.showDialog(params);
                    }
                }
            }
        });
    }

    public synchronized static void putDialog(String key, BaseSysDialog dialog) {
        mDialogCache.put(key, dialog);
    }

    public synchronized static void removeDialog(String key) {
        if (mDialogCache.containsKey(key)) mDialogCache.remove(key);
    }

    public synchronized static void dismissDialog(final String dialogKey) {
        if (mDialogCache.containsKey(dialogKey)) {
            dismissCacheDialog(dialogKey);
        }
    }

    public synchronized static boolean isShow(String key) {
        return mDialogCache.containsKey(key);
    }


    private static void dismissCacheDialog(String dialogKey) {
        try {
            BaseSysDialog dialog = mDialogCache.get(dialogKey);
            if (EmptyUtils.isNotEmpty(dialog) && dialog.isShowing()) {
                dialog.dismiss();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public synchronized static void dismissOtherDialog(String currentKey) {
        if (currentKey.equals(DIALOG_KEY_THIRD_ACCOUNT)||currentKey.equals(DIALOG_KEY_PLAYER) || currentKey.equals(DIALOG_KEY_MESSAGE) || currentKey.equals(DIALOG_KEY_DOWNLOAD_QRCODE) || currentKey.equals(DIALOG_KEY_BIND_JD) || currentKey.equals(DIALOG_KEY_BIND_THIRD) || currentKey.equals(DIALOG_KEY_SCAN_HELP)){
            return;
        }
        for (String key : mDialogCache.keySet()) {
            dismissDialog(key);
        }
    }

    public static BaseSysDialog getDialog(String key) {
        return mDialogCache.get(key);
    }

}
