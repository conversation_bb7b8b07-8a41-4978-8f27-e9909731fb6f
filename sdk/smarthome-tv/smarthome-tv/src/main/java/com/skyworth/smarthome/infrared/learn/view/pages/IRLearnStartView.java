package com.skyworth.smarthome.infrared.learn.view.pages;

import android.content.Context;
import android.graphics.Color;
import android.text.SpannableStringBuilder;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import android.support.annotation.NonNull;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.util.SpannableStringUtils;
import com.skyworth.util.Util;

import java.util.HashMap;
import java.util.Map;

import static com.skyworth.smarthome.infrared.learn.view.IRLearnViewImpl.PARAMS_KEY_IS_LEARNING;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/7/2 18:24.
 */
public class IRLearnStartView extends BaseIRLearnViews {
    public static final String PARAMS_KEY_CURRENT = "current";
    public static final String PARAMS_KEY_TOTAL = "total";
    public static final String PARAMS_KEY_REMOTE_NAME = "remoteName";
    public static final String PARAMS_KEY_KEY_NAME = "keyName";
    public static final String PARAMS_KEY_IR_DEVICE_NAME = "irDeviceName";

    private TextView mTitle = null;
    private TextView mTip = null;
    private TextView mKeyName = null;
    private View mRemote = null;
    private View mThumb = null;
    private TextView mLearning = null;

    public IRLearnStartView(@NonNull Context context) {
        super(context);
        initTitle();
        initTip();
        initRemoteBg();
        initKeyName();
        initThumb();
    }

    private void initTitle() {
        mTitle = new TextView(getContext());
        mTitle.setTextSize(Util.Dpi(36));
        mTitle.setTextColor(Color.WHITE);
        mTitle.getPaint().setFakeBoldText(true);
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        layoutParams.topMargin = Util.Div(30);
        addView(mTitle, layoutParams);
    }

    private void initTip() {
        mTip = new TextView(getContext());
        mTip.setMaxWidth(Util.Div(586));
        mTip.setTextSize(Util.Dpi(30));
        mTip.setTextAlignment(TEXT_ALIGNMENT_CENTER);
        mTip.setLineSpacing(Util.Div(10), 1);
        LayoutParams layoutParams = new LayoutParams(Util.Div(586), ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        layoutParams.topMargin = Util.Div(84);
        addView(mTip, layoutParams);
    }

    private void initRemoteBg() {
        mRemote = new View(getContext());
        LayoutParams layoutParams = new LayoutParams(Util.Div(240), Util.Div(200));
        layoutParams.topMargin = Util.Div(186);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        addView(mRemote, layoutParams);
    }

    private void initKeyName() {
        mKeyName = new TextView(getContext());
        mKeyName.setTextSize(Util.Dpi(18));
        mKeyName.setGravity(Gravity.CENTER);
        LayoutParams layoutParams = new LayoutParams(Util.Div(80), Util.Div(34));
        layoutParams.leftMargin = Util.Div(307);
        layoutParams.topMargin = Util.Div(260);
        addView(mKeyName, layoutParams);
    }

    private void initThumb() {
        mThumb = new View(getContext());
        LayoutParams layoutParams = new LayoutParams(Util.Div(240), Util.Div(200));
        layoutParams.topMargin = Util.Div(200);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        addView(mThumb, layoutParams);
    }

    @Override
    protected void onShow(Map<String, Object> params) {
        if (params == null || params.size() <= 0) {
            return;
        }
        int current = (int) params.get(PARAMS_KEY_CURRENT);
        int total = (int) params.get(PARAMS_KEY_TOTAL);
        String remoteName = (String) params.get(PARAMS_KEY_REMOTE_NAME);
        String keyName = (String) params.get(PARAMS_KEY_KEY_NAME);
        String irDeviceName = (String) params.get(PARAMS_KEY_IR_DEVICE_NAME);
        boolean isLearning = (boolean) params.get(PARAMS_KEY_IS_LEARNING);

        mTitle.setText(getResources().getString(R.string.ir_learn_start_title, current, total));

        String tip1 = getResources().getString(R.string.ir_learn_start_tip1, remoteName, irDeviceName);
        String tip2 = getResources().getString(R.string.ir_learn_start_tip2);
        SpannableStringBuilder tip = SpannableStringUtils.getBuilder(tip1).setForegroundColor(Color.argb(204, 255, 255, 255)).append(" \"" + keyName + "\" ").setBold().setForegroundColor(Color.WHITE).append(tip2).setForegroundColor(Color.argb(204, 255, 255, 255)).create();
        mTip.setText(tip);

        mKeyName.setText(keyName);

        showBg(isLearning,irDeviceName);
    }

    private void showBg(boolean isLearning,String deviceName) {
        if (isLearning) {
            mKeyName.setTextColor(Color.WHITE);
            mRemote.setBackgroundResource(R.drawable.ir_learn_press_button_bg);
            mThumb.setBackgroundResource(R.drawable.ir_learn_press_thumb_bg);
            showLearning(deviceName);
        } else {
            mKeyName.setTextColor(Color.rgb(12, 163, 187));
            mRemote.setBackgroundResource(R.drawable.ir_learn_un_press_button_bg);
            mThumb.setBackgroundResource(R.drawable.ir_learn_un_press_thumb_bg);
            hideLearning();
        }
    }

    private void showLearning(String deviceName) {
        if (mLearning == null) {
            mLearning = new TextView(getContext());
            mLearning.setText(getResources().getString(R.string.ir_learn_start_learning, deviceName));
            mLearning.setTextSize(Util.Dpi(20));
            mLearning.setTextColor(Color.WHITE);
            LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
            layoutParams.topMargin = Util.Div(355);
            addView(mLearning, layoutParams);
        }
        mLearning.setVisibility(VISIBLE);
    }

    private void hideLearning() {
        if (mLearning != null) {
            mLearning.setVisibility(GONE);
        }
    }

    @Override
    public int getViewWidth() {
        return Util.Div(714);
    }

    @Override
    public int getViewHeight() {
        return Util.Div(400);
    }

    public static Map<String, Object> getParams(int current, int total, String remoteName, String keyName, String irDeviceName) {
        Map<String, Object> map = new HashMap<>(5);
        map.put(PARAMS_KEY_CURRENT, current);
        map.put(PARAMS_KEY_TOTAL, total);
        map.put(PARAMS_KEY_REMOTE_NAME, remoteName);
        map.put(PARAMS_KEY_KEY_NAME, keyName);
        map.put(PARAMS_KEY_IR_DEVICE_NAME, irDeviceName);
        return map;
    }

    @Override
    public String getName() {
        return "IRLearnStartView";
    }
}
