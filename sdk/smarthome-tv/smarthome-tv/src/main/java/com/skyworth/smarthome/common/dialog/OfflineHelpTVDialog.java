package com.skyworth.smarthome.common.dialog;

import android.content.Context;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.TextView;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.base.BaseCommonDialog;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.event.OfflineTVCheckUpdataTipsEvent;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.ui.CommonFocusBox;
import com.skyworth.smarthome.common.util.DialogCommonUtil;
import com.skyworth.smarthome.common.util.SystemProperty;
import com.skyworth.smarthome.common.util.ThreadManager;
import com.skyworth.smarthome.common.util.ViewsBuilder;
import com.skyworth.smarthome.home.smartdevice.controlpanel.thirdapp.appmanager.AppManager;
import com.skyworth.smarthome.home.smartdevice.controlpanel.thirdapp.appmanager.IUpdateListener;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.EmptyUtils;

import org.greenrobot.eventbus.EventBus;

import java.util.List;

/**
 * 本机TV离线帮助dialog
 */
public class OfflineHelpTVDialog extends BaseCommonDialog {
    private final static String TAG = "OfflineHelpTVDialog";
    private final static String PKG_IOT_CHANNEL = "swaiotos.channel.iot";
    private CommonFocusBox mRebingBtn, mCancleBtn;
    private ImageView imageLoading;
    private TextView checkText;
    private OfflineHelpTVDialog.OfflineHelpTVCallBack mOfflineHelpTVCallBack;

    public OfflineHelpTVDialog(@NonNull Context context) {
        this(context, R.style.common_style);
    }

    public OfflineHelpTVDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    @Override
    protected void initParams() {

    }

    public static OfflineHelpTVDialog newInstance(Context context) {
        OfflineHelpTVDialog fragment = new OfflineHelpTVDialog(context, R.style.common_style);
        return fragment;
    }

    public void setOfflineHelpTVCallBack(OfflineHelpTVCallBack offlineHelpTVCallBack) {
        this.mOfflineHelpTVCallBack = offlineHelpTVCallBack;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        View contentView = ViewsBuilder.getOfflineHelpOfTV(getContext());
        setContentView(contentView);
        contentView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                regain();
                dismiss();
            }
        });

        mRebingBtn = findViewById(R.id.offline_help_rebind);
        mCancleBtn = findViewById(R.id.offline_help_cancle);
        imageLoading = findViewById(R.id.offline_help_loading);
        checkText = findViewById(R.id.offline_help_check);
        mCancleBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                regain();
                dismiss();
            }
        });

        mRebingBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                checkupData();
                ThreadManager.getInstance().ioThread(new Runnable() {
                    @Override
                    public void run() {
                            if (AppManager.checkUpdate(mContext, PKG_IOT_CHANNEL)) {
                                //检测已安装iot通道且是最新版本，提示"服务正常",安装出错提示“服务异常，请检查网络后重试”
                                boolean ret = AppManager.downloadAndInstall(mContext, PKG_IOT_CHANNEL, iUpdateListener);
                                regain();
                                EventBus.getDefault().post(new OfflineTVCheckUpdataTipsEvent(ret));
                            } else {
                                regain();
                                EventBus.getDefault().post(new OfflineTVCheckUpdataTipsEvent(true));
                                getDeviceStatus();
                            }
                    }
                }, 1000);
                if (mOfflineHelpTVCallBack != null) {
                    mOfflineHelpTVCallBack.callBack();
                }
            }
        });

        mRebingBtn.requestFocus();
    }

    private void getDeviceStatus() {
        List<DeviceInfo> deviceInfoList = AppData.getInstance().getDeviceInfoList();
        if (EmptyUtils.isNotEmpty(deviceInfoList)) {
            List<DeviceInfo> deviceList = deviceInfoList;
            if (EmptyUtils.isNotEmpty(deviceList)) {
                for (DeviceInfo item : deviceList) {
                    if (item.device_id.equals(SystemProperty.getDeviceId()) && item.online_status == 1) {
                        dismiss();
                    }
                }
            }
        }
//        AiotLibSDK.getDefault().getDeviceImpl().getDeviceList(familyId, new IDevice.IDevicesCallBack() {
//            @Override
//            public void onDevices(int code, String msg, List<DeviceBean> list) {
//                if (code == 0) {
//                    int online = -1;
//                    for (DeviceBean item : list) {
//                        if (item.device_id.equals(SystemProperty.getDeviceId())) {
//                            online = item.online_status;
//                        }
//                    }
//                    if (online == 1) {
//                        dismiss();
//                    }
//                } else {
//                    LogUtil.androidLog("获取设备列表失败");
//                }
//
//            }
//        });
    }

    //检测中
    private void checkupData() {
        checkText.setText(R.string.detect_help_check);
        imageLoading.setVisibility(View.VISIBLE);
        Animation animation = AnimationUtils.loadAnimation(mContext, R.anim.anim_load_roate);
        LinearInterpolator lin = new LinearInterpolator();//设置动画匀速运动
        animation.setInterpolator(lin);
        imageLoading.startAnimation(animation);
    }

    private void regain() {
        //恢复默认
        checkText.setText(R.string.detect_help_rebinding);
        imageLoading.clearAnimation();
        imageLoading.setVisibility(View.INVISIBLE);
    }

    private IUpdateListener iUpdateListener = new IUpdateListener() {
        @Override
        public void onDownloadStart() {
            log("onDownloadStart");
        }

        @Override

        public void onDownloadProcess(int process) {
            log("onDownloadProcess:" + process);
        }

        @Override
        public void onDownloadEnd(boolean res) {
            log("onDownloadEnd:" + res);
        }

        @Override
        public void onInstallStart() {
            log("onInstallStart");
        }

        @Override
        public void onInstallEnd(boolean res, String reason) {
            log("onInstallEnd:" + res + "  " + reason);
        }
    };

    private void log(String msg) {
        CCLog.i(TAG, msg);
    }

    @Override
    public void onStart() {
        super.onStart();
        Window window = getWindow();
        WindowManager.LayoutParams windowParams = window.getAttributes();
        windowParams.width = WindowManager.LayoutParams.MATCH_PARENT;
        windowParams.height = WindowManager.LayoutParams.MATCH_PARENT;
        windowParams.gravity = Gravity.CENTER;
        windowParams.dimAmount = 0.50f;
        windowParams.flags |= WindowManager.LayoutParams.FLAG_DIM_BEHIND;
        window.setAttributes(windowParams);
    }

    public interface OfflineHelpTVCallBack {
        void callBack();
    }

    @Override
    public void show() {
        super.show();
        DialogCommonUtil.putDialog(DialogCommonUtil.DIALOG_KEY_OFFLINE_HELP_WIFI, this);
    }

    @Override
    protected void onDismiss() {
        super.onDismiss();
        regain();
        DialogCommonUtil.removeDialog(DialogCommonUtil.DIALOG_KEY_OFFLINE_HELP_WIFI);
    }
}
