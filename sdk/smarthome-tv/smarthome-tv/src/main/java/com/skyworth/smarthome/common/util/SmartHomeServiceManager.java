package com.skyworth.smarthome.common.util;

import android.app.Instrumentation;
import android.content.Context;
import android.util.Log;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.coocaa.app.core.app.AppCoreApplication;
import com.skyworth.framework.skysdk.util.SkyData;
import com.skyworth.smarthome.common.event.IRLearnStatusEvent;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.voicehandle.SmartHomeAI;
import com.smarthome.common.sal.SalImpl;
import com.tianci.framework.player.SkyPlayerItem;

import org.greenrobot.eventbus.EventBus;

import java.util.Timer;
import java.util.TimerTask;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import swaiotos.sal.SAL;
import swaiotos.sal.hardware.IRLearnListener;
import swaiotos.sal.network.wifi.CCWifiItem;


/**
 * Created by lu on 2018/2/7.
 */

public class SmartHomeServiceManager {
    private static final String TAG = "smarthome";
    private static SmartHomeServiceManager manager = null;
    private static final String TVNAME = "TVNAME";
    private Context mContext;

    private static final int SKY_KEY_POWER = 26;//待机或关机
    private static final int SKY_KEY_VOLUME_MUTE = 164;//静音
    private static final int SKY_KEY_HOME = 3;//酷开主页键
    private static final int SKY_KEY_BACK = 4;//酷开返回键
    private static final int SKY_KEY_UP = 19;//酷开向上键
    private static final int SKY_KEY_DOWN = 20;//酷开向下键
    private static final int SKY_KEY_LEFT = 21;//酷开左键
    private static final int SKY_KEY_RIGHT = 22;//酷开右键
    private static final int SKY_KEY_CENTER = 23;//酷开Center键
    private static final int SKY_KEY_MENU = 82;//酷开菜单键
    private static final int SKY_KEY_CHANNEL_UP = 166;//酷开通道向上
    private static final int SKY_KEY_CHANNEL_DOWN = 167;//酷开通道向下
    private static final int SKY_KEY_TV_INPUT = 178;//酷开TV INPUT键
    private static final int SKY_KEY_VOLUME_UP = 24;//酷开音量+
    private static final int SKY_KEY_VOLUME_DOWN = 25;//酷开音量-


    public synchronized static final void create(Context context) {
        if (manager == null)
            manager = new SmartHomeServiceManager(context);
    }

    public synchronized static final SmartHomeServiceManager getManager() {
        return manager;
    }

    private SmartHomeServiceManager(Context context) {
        mContext = context;
    }



    /**
     * 获取当前播放媒体
     *
     * @return
     * @throws Exception
     */
    public SkyPlayerItem getCurrentPlayerItem() {
        try {
            return SalImpl.getSAL(mContext).getCurrentPlayerItem();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取当前播放媒体进度
     *
     * @return
     * @throws Exception
     */
    public int getCurrentPosition() {
        try {
            return SalImpl.getSAL(mContext).getCurrentPosition();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    /**
     * 暂停播放
     */
    public void pausePlayer() {
        try {
            SalImpl.getSAL(mContext).pausePlayer();
            return;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return;
    }

    public int getVolume() {
        try {
            return SalImpl.getSAL(mContext).getVolume();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return -1;
    }

    public void setVolume(int volume) {
        try {
            SalImpl.getSAL(mContext).setVolume(volume);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean isVolumeMute() {
        try {
            return SalImpl.getSAL(mContext).isVolumeMute();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public void setVolumeMute() {
        try {
//            SalImpl.getSAL(mContext).emulateKey(SkyworthKeyMap.SkyworthKey.SKY_KEY_VOLUME_MUTE);
//            SalImpl.getSAL(mContext).emulateKey(164);
            sendKeyDownUpSync(SKY_KEY_VOLUME_MUTE);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void setPower(boolean on) {
        try {
            LogUtil.androidLog("setPower " + (on ? "on" : "off"));
//            SalImpl.getSAL(mContext).emulateKey(SkyworthKeyMap.SkyworthKey.SKY_KEY_POWER);
//            SalImpl.getSAL(mContext).emulateKey(26);
//            sendKeyDownUpSync(SKY_KEY_POWER);
            if (on) {
                SAL.getModule(mContext, SAL.SYSTEM).setAIScreenMode(true);
            } else {
                SAL.getModule(mContext, SAL.SYSTEM).toStandby();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * KEYCODE_HOME = 1,  主页
     * KEYCODE_BACK = 2,   返回
     * KEYCODE_UP   = 13,  上
     * KEYCODE_DOWN = 14,   下
     * KEYCODE_LEFT = 15,    左
     * KEYCODE_RIGHT = 16,   右
     * KEYCODE_CENTER = 17,   确定
     * KEYCODE_MENU = 23,     菜单
     * SKY_KEY_CHANNEL_UP = 21,
     * SKY_KEY_CHANNEL_DOWN = 22,
     * SKY_KEY_TV_INPUT = 24,
     * SKY_KEY_VOLUME_UP,  18
     * SKY_KEY_VOLUME_DOWN,  19
     * SKY_KEY_VOLUME_MUTE,  20
     * SKY_KEY_SHORT_KEY1 = 850
     * SKY_KEY_SHORT_KEY2 = 851
     * SKY_KEY_SHORT_KEY3 = 852
     **/
    public void sendSkyKey(int keyCode) {
        try {
            int skyKeyCode = keyCode;
            switch (keyCode) {
                case 1:
                    skyKeyCode = SKY_KEY_HOME;
                    break;
                case 2:
                    skyKeyCode = SKY_KEY_BACK;
                    break;
                case 13:
                    skyKeyCode = SKY_KEY_UP;
                    break;
                case 14:
                    skyKeyCode = SKY_KEY_DOWN;
                    break;
                case 15:
                    skyKeyCode = SKY_KEY_LEFT;
                    break;
                case 16:
                    skyKeyCode = SKY_KEY_RIGHT;
                    break;
                case 17:
                    skyKeyCode = SKY_KEY_CENTER;
                    break;
                case 23:
                    skyKeyCode = SKY_KEY_MENU;
                    break;
                case 21:
                    skyKeyCode = SKY_KEY_CHANNEL_UP;
                    break;
                case 22:
                    skyKeyCode = SKY_KEY_CHANNEL_DOWN;
                    break;
                case 24:
                    skyKeyCode = SKY_KEY_TV_INPUT;
                    break;
                case 18:
                    skyKeyCode = SKY_KEY_VOLUME_UP;
                    break;
                case 19:
                    skyKeyCode = SKY_KEY_VOLUME_DOWN;
                    break;
                case 20:
                    skyKeyCode = SKY_KEY_VOLUME_MUTE;
                    break;
            }
            sendKeyDownUpSync(skyKeyCode);
            if ((keyCode == 18) || (keyCode == 19)) {
                AppCoreApplication.Companion.workerThread(500, new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        boolean isMute = SalImpl.getSAL(mContext).isVolumeMute();
                        Log.d("voicecmd", "sendSkyKey  isMute:" + isMute);
                        int mute = isMute ? 1 : 0;
                        SmartHomeAI.respontTVStatus(SmartHomeTvLib.getContext(), "MUTE", 0x80, String.valueOf(mute));
                        return Unit.INSTANCE;
                    }
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void sendKeyDownUpSync(final int key){
        LogUtil.androidLog("sendSkyKey  keycode:" + key);
        AppCoreApplication.Companion.workerThread(100, new Function0<Unit>() {
            @Override
            public Unit invoke() {
                getInst().sendKeyDownUpSync(key);
                return Unit.INSTANCE;
            }
        });
    }

    private SmartHomeAI.STD_RData learnData = null;

    public void updateIRCTL(SmartHomeAI.STD_RData data) {
        learnData = data;
    }

    public SmartHomeAI.STD_RData getLearnData() {
        return learnData;
    }

    public void sendKeyCode(int[] keyCode, boolean flag) {
        try {
            SalImpl.getSAL(mContext).SendInfraredCode(compositeData(keyCode, flag));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void getLearnInfraredCallBack() {
        try {
            startLearnTimer();
            SalImpl.getSAL(mContext).setLearnInfraredCallBack(new IRLearnListener() {
                @Override
                public void onInfraredLearnedFailed(int failed) {
                    if (learnData != null) {
                        cancleTimer();
                        if (failed != 1)
                            return ;
                        learnData.CODE = 2;
                        SmartHomeAI.STD_RData.STD_Result result = new SmartHomeAI.STD_RData.STD_Result();
                        result.KEY_CODE = null;
                        learnData.RESULT = result;
                        String value = JSONObject.toJSONString(learnData);
                        Log.d(TAG, "handle failed value:" + value);
                        notifyIRLearnResult(value);
                        SmartHomeAI.respontIRCTL(SmartHomeTvLib.getContext(), "STD_R", 0x84, value);
                    }
                }

                @Override
                public void onInfraredLearnedSuccess(byte[] bytes) {
                    if (learnData != null) {
                        cancleTimer();
                        SkyData data = new SkyData(bytes);
                        byte[] code = data.getBytes("infraredCode");
                        if (code.length <= 0)
                            return ;
                        short[] shortsCode = Utils.byteToShort(code);
                        Log.d(TAG, "handle success shortsCode:" + JSONArray.toJSONString(shortsCode));
                        SmartHomeAI.STD_RData.STD_Result result = new SmartHomeAI.STD_RData.STD_Result();
                        result.KEY_CODE = shortsCode;
                        learnData.CODE = 1;
                        learnData.RESULT = result;
                        String value = JSONObject.toJSONString(learnData);
                        Log.d(TAG, "handle success value:" + value);
                        notifyIRLearnResult(value);
                        SmartHomeAI.respontIRCTL(SmartHomeTvLib.getContext(), "STD_R", 0x84, value);
                    }
                }
            }, true);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private byte[] compositeData(int[] frameData, boolean isLearn) {
        int length = frameData.length;
        byte[] res = new byte[length + 5];
        res[0] = (byte) (0xED);
        res[1] = (byte) (0x8C);
        int sum = 0;
        if (isLearn) {
            res[2] = (byte) (0x03);//红外学习3
            sum = 0xED + 0x8C + 3;//学习反馈04
        } else {
            res[2] = (byte) (0x01);//红外控制1
            sum = 0xED + 0x8C + length + 1;//学习反馈04
        }
        res[3] = (byte) (length & 0xff);
        for (int i = 0; i < length; i++) {
            byte b = (byte) frameData[i];
            int j = (b >= 0 ? b : (256 + b));
            res[i + 4] = (byte) (j);
            sum = sum + frameData[i];
        }
        sum = sum % 256;
        res[length + 4] = (byte) (255 - sum);
        return res;
    }


//    CCConnecterManager.CCCHandler handler = new CCConnecterManager.CCCHandler() {
//        @Override
//        public byte[] onHandler(String fromtarget, String cmd, byte[] body) {
//            Log.d(TAG, "onHandleEvent === cmd" + cmd);
//            if (cmd.equals(TCSystemCmd.TC_SYSTEM_CMD_INFRARED_LEARNING_SUCCESS.toString())) {//成功
//                if (learnData != null) {
//                    cancleTimer();
//                    SkyData data = new SkyData(body);
//                    byte[] code = data.getBytes("infraredCode");
//                    if (code.length <= 0) return null;
////                    String code_value = JSONObject.toJSONString(code);
////                    Log.d(TAG, "handle success code_value:" + code_value);
////                    TVResponts.STD_RData.STD_Result result = JSONObject.parseObject(code_value, TVResponts.STD_RData.STD_Result.class);
//
//                    short[] shortsCode = Utils.byteToShort(code);
//                    Log.d(TAG, "handle success shortsCode:" + JSONArray.toJSONString(shortsCode));
//
//                    TVResponts.STD_RData.STD_Result result = new TVResponts.STD_RData.STD_Result();
//                    result.KEY_CODE = shortsCode;
//                    learnData.CODE = 1;
//                    learnData.RESULT = result;
//                    String value = JSONObject.toJSONString(learnData);
//                    Log.d(TAG, "handle success value:" + value);
//                    notifyIRLearnResult(value);
//                    TVResponts.respontIRCTL(SmartHomeApplication.getContext(), "STD_R", 0x84, value);
//                }
//            } else if (cmd.equals(TCSystemCmd.TC_SYSTEM_CMD_INFRARED_LEARNING_FAILED.toString())) {//失败
//                if (learnData != null) {
//                    cancleTimer();
//                    int failed = SkyObjectByteSerialzie.toObject(body, Integer.class);
//                    Log.d(TAG, "failed = " + failed);
//                    if (failed != 1) return null;
//                    learnData.CODE = 2;
//                    TVResponts.STD_RData.STD_Result result = new TVResponts.STD_RData.STD_Result();
//                    result.KEY_CODE = null;
//                    learnData.RESULT = result;
//                    String value = JSONObject.toJSONString(learnData);
//                    Log.d(TAG, "handle failed value:" + value);
//                    notifyIRLearnResult(value);
//                    TVResponts.respontIRCTL(SmartHomeApplication.getContext(), "STD_R", 0x84, value);
//                }
//            } else if (TCNetworkBroadcast.TC_NETWORK_BROADCAST_NET_WIFI_EVENT.toString().equals(cmd)) {
//                WifiConnectEvent event = new WifiConnectEvent();
//                event.body = body;
//                event.cmd = cmd;
//                EventBus.getDefault().post(event);
//            } else if (TCNetworkBroadcast.TC_NETWORK_BROADCAST_NET_WIFI_SUPPLICANT_STATE_CHANGED.toString().equals(cmd)) {
//                WifiConnectEvent event = new WifiConnectEvent();
//                event.body = body;
//                event.cmd = cmd;
//                EventBus.getDefault().post(event);
//            } else if (TCSystemDefs.TCSystemBroadcast.TC_SYSTEM_BROADCAST_NOTIFY_ENV_CHANCHED.toString().equalsIgnoreCase(cmd)) {
//                String key = SkyObjectByteSerialzie.toObject(body, String.class);
//                if (TVNAME.equalsIgnoreCase(key)) {
//                    Log.i(TAG, "onHandler: tv name change");
//                    //TODO name change
//                    EventBus.getDefault().post(new DeviceNameChangeEvent());
//                }
//            }
//            return null;
//        }
//
//        @Override
//        public void onResult(String fromtarget, String cmd, byte[] body) {
//
//        }
//    };

    private void notifyIRLearnResult(String json) {
        IRLearnStatusEvent event = new IRLearnStatusEvent();
        event.setData(json);
        EventBus.getDefault().post(event);
    }

    private Timer mTimer = null;

    private void cancleTimer() {
        try {
            if (mTimer != null) {
                Log.d("smarthome", "cacle timer ");
                mTimer.cancel();
                mTimer = null;
            }
        } catch (Exception e) {
        }
    }

    private void startLearnTimer() {
        if (mTimer != null) {
            mTimer.cancel();
            mTimer = null;
        }
        mTimer = new Timer();
        mTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                SmartHomeAI.STD_RData learnData = new SmartHomeAI.STD_RData();
                learnData.CODE = 2;
                SmartHomeAI.STD_RData.STD_Result result = new SmartHomeAI.STD_RData.STD_Result();
                result.KEY_CODE = null;
                learnData.RESULT = result;
                String value = JSONObject.toJSONString(learnData);
                Log.d("smarthome", "cancleIRCTL value:" + value);
                SmartHomeAI.respontIRCTL(SmartHomeTvLib.getContext(), "STD_R", 0x84, value);
            }
        }, 25 * 1000);
    }

    public void connectWifiByDhcp(CCWifiItem wifiAPItem) {
        try {
            SalImpl.getSAL(mContext).connectWifiByDhcp(wifiAPItem);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private Instrumentation inst = null;

    private Instrumentation getInst() {
        if (inst == null) {
            inst = new Instrumentation();
        }
        return inst;
    }
}
