package com.skyworth.smarthome.devices.discover.dialog;

import android.view.Gravity;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.skyworth.smarthome.common.base.BaseSysDialog;
import com.skyworth.smarthome.common.event.RefreshScanNetworkDeviceEvent;
import com.skyworth.smarthome.common.event.RefreshScanWifiDeviceEvent;
import com.skyworth.smarthome.common.event.ScanWifiDeviceEvent;
import com.skyworth.smarthome.common.event.StartDiscoverNearDeviceEvent;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.util.DialogLauncherUtil;
import com.skyworth.smarthome.devices.discover.view.DiscoverNearDevicesView;
import com.skyworth.smarthome.service.model.SmartDeviceDataModel;
import com.skyworth.smarthome.service.push.local.DeviceDataPushUtil;
import com.skyworth.util.Util;
import com.smarthome.common.dataer.DataHelpInfo;
import com.smarthome.common.dataer.LogSDK;
import com.smarthome.common.utils.EmptyUtils;
import com.swaiot.aiotlib.common.entity.DiscoverNetworkDevice;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:发现附近的设备（所有设备）
 * Created by AwenZeng on 2019/4/9
 */
public class DiscoverNearDeviceDialog extends BaseSysDialog {

    private DiscoverNearDevicesView mDiscoverNearDevicesView;
    private List<DiscoverNetworkDevice> mDataList;
    private List<String> mDeviceKeyList;
    private int mDeviceSize;

    @Override
    protected void initParams() {
        mDialogKey = DialogLauncherUtil.DIALOG_KEY_DISCOVER_NEAR_DEVICES;
        EventBus.getDefault().register(this);
    }

    @Override
    protected void initContentView() {
        super.initContentView();
        ImageView bg = new ImageView(mContext);
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(Util.Div(1920), Util.Div(1080));
        mContentView.addView(bg, params);

        mDiscoverNearDevicesView = new DiscoverNearDevicesView(mContext);
        FrameLayout mainLayout = new FrameLayout(mContext);
        FrameLayout.LayoutParams contentParam = new FrameLayout.LayoutParams(Util.Div(660), Util.Div(810));
        contentParam.gravity = Gravity.CENTER_HORIZONTAL|Gravity.CENTER_VERTICAL;
        mainLayout.addView(mDiscoverNearDevicesView, contentParam);
        mContentView.addView(mainLayout);
        //触摸弹框之外的部分关掉弹窗逻辑处理
        mDiscoverNearDevicesView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
//        openAutoDismissDialog();
    }

    @Override
    public void showDialog(Map<String, String> params) {
        super.showDialog(params);
        mDataList = SmartDeviceDataModel.INSTANCE.getNearNetworkDeviceList();
        setDataList(mDataList);
        initDeviceKeyList(mDataList);
    }


    private void setDataList(List<DiscoverNetworkDevice> dataList) {
        if(EmptyUtils.isNotEmpty(dataList)){
            mDiscoverNearDevicesView.show(dataList);
            reportData(dataList);
            DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.DEVICE_LIST, "");
        }
    }

    private void initDeviceKeyList(List<DiscoverNetworkDevice> dataList){
        if(EmptyUtils.isNotEmpty(dataList)){
            mDeviceKeyList = new ArrayList<>();
            for(DiscoverNetworkDevice device: dataList){
                mDeviceKeyList.add(device.device_id);
            }
            mDeviceSize = mDeviceKeyList.size();
        }

    }

    /**
     * 数据统计
     */
    private void reportData(List<DiscoverNetworkDevice> dataList) {
        try {
            for (DiscoverNetworkDevice item : dataList) {
                //数据统计
                Map<String, String> map = new HashMap<>();
                map.put("source", DataHelpInfo.getInstance().getDiscoverDeviceOrigin());
                map.put("device_brand", item.brand_cn);
                map.put("device_name", item.device_name);
                if(EmptyUtils.isNotEmpty(item.deviceInfo)){
                    map.put("device_state", "未配网");
                    map.put("device_SSID", item.device_id);
                }else{
                    map.put("device_id", item.device_id);
                    map.put("device_state", "未绑定");
                }
                LogSDK.submit(LogSDK.EVENT_ID_DISCOVER_DEVICE,map);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void Event(RefreshScanWifiDeviceEvent event) {
      refreshDeviceList();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void Event(RefreshScanNetworkDeviceEvent event) {
        refreshDeviceList();
    }


    /**
     * 刷新设备列表
     */
    private void refreshDeviceList(){
        List<DiscoverNetworkDevice> tempList = SmartDeviceDataModel.INSTANCE.getNearNetworkDeviceList();
        if(EmptyUtils.isNotEmpty(mDataList)&&EmptyUtils.isNotEmpty(tempList)){
            for(DiscoverNetworkDevice device:tempList){
                if(EmptyUtils.isNotEmpty(device.device_id)&&!mDeviceKeyList.contains(device.device_id)){
                    mDataList.add(0,device);
                    mDeviceKeyList.add(device.device_id);
                }
            }
            if(mDeviceSize!=mDeviceKeyList.size()){
                setDataList(mDataList);
                mDeviceSize = mDeviceKeyList.size();
            }
        }
    }

    /**
     * 发送开始搜索附近的设备控制
     *
     * @param isStartDiscover
     */
    private void sendStartDiscoverNearDeviceEvent(boolean isStartDiscover) {
        StartDiscoverNearDeviceEvent event = new StartDiscoverNearDeviceEvent();
        event.setData(isStartDiscover);
        EventBus.getDefault().post(event);
    }

    @Override
    protected void onDismiss() {
        sendStartDiscoverNearDeviceEvent(false);
        AppData.getInstance().setStartScanDevice(false);
        EventBus.getDefault().post(new ScanWifiDeviceEvent(false));
        mDiscoverNearDevicesView.onDestroy();
        EventBus.getDefault().unregister(this);
        super.onDismiss();
    }
}
