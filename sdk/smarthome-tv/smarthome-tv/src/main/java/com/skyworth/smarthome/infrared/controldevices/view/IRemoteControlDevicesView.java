package com.skyworth.smarthome.infrared.controldevices.view;

import android.content.Context;
import android.view.View;

import com.skyworth.smarthome.infrared.controldevices.presenter.IRemoteControlDevicesPresenter;
import com.skyworth.smarthome.infrared.electriclist.model.DeviceTypeListData;

import java.util.List;
import java.util.Map;

/**
 * Describe:
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/4/28
 */
public interface IRemoteControlDevicesView {
    void setParams(Map<String, Object> params);

    void createView(Context context, IRemoteControlDevicesPresenter presenter);

    void refreshUI(List<DeviceTypeListData> list);

    void deleteSuccess(DeviceTypeListData data);

    void deleteFailed();

    void showLoading();

    void hideLoading();

    void requestFocusUI();

    View getView();
}
