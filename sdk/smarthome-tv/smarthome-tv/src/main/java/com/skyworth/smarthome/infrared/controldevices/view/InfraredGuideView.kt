package com.skyworth.smarthome.infrared.controldevices.view

import android.content.Context
import android.graphics.BitmapFactory
import android.graphics.Color
import android.text.TextUtils
import android.view.Gravity
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import com.coocaa.app.core.utils.ioThread
import com.coocaa.app.core.utils.runOnUiThread
import com.skyworth.smarthome.R
import com.skyworth.smarthome.common.util.QRUtils
import com.skyworth.ui.api.widget.CCFocusDrawable
import com.skyworth.util.Util
import org.jetbrains.anko.*

/**
 * Created by fc on 2019/9/11
 * Describe: 红外弹窗view
 */
 class InfraredGuideView(context: Context) : FrameLayout(context) {
    var mQrCodeView: ImageView? = null
    var addBtn: TextView? = null
    var mTitleTv:TextView? = null

    interface OnClickListener {
        fun OnClick()
    }

    var listener: OnClickListener? = null

    fun setOnBtnClickListener(l: OnClickListener) {
        listener = l
    }

    init {
        //标题：
        mTitleTv = textView {
            val params = LayoutParams(wrapContent, wrapContent)
            params.topMargin = Util.Div(28)
            params.gravity = Gravity.CENTER_HORIZONTAL
            layoutParams = params
            textSize = Util.Dpi(36).toFloat()
            textColor = Color.parseColor("#FFFFFF")
            paint.isFakeBoldText = true
            text = "红外遥控"
        }

        textView {
            val params = LayoutParams(wrapContent, wrapContent)
            params.topMargin = Util.Div(84)
            params.gravity = Gravity.CENTER_HORIZONTAL
            layoutParams = params
            textSize = Util.Dpi(26).toFloat()
            textColor = Color.parseColor("#80FFFFFF")
            text = "语音轻松控制老家电"
        }
        imageView {
            val params = LayoutParams(Util.Div(560), Util.Div(1))
            params.topMargin = Util.Div(132)
            params.gravity = Gravity.CENTER_HORIZONTAL
            layoutParams = params
            backgroundColor = Color.parseColor("#FFFFFF")
            alpha = 0.1f
        }

        textView {
            val params = LayoutParams(wrapContent, wrapContent)
            params.topMargin = Util.Div(164)
            params.leftMargin = Util.Div(50)
            layoutParams = params
            textSize = Util.Dpi(30).toFloat()
            textColor = Color.parseColor("#FFFFFF")
            paint.isFakeBoldText = true
            text = "方法一    手机添加"
        }

        imageView {
            val params = LayoutParams(Util.Div(51), Util.Div(24))
            params.topMargin = Util.Div(172)
            params.leftMargin = Util.Div(300)
            layoutParams = params
            background = context.resources.getDrawable(R.drawable.icon_download_recommend)
        }

        textView {
            val params = LayoutParams(Util.Div(560), wrapContent)
            params.topMargin = Util.Div(214)
            params.leftMargin = Util.Div(50)
            layoutParams = params
            textSize = Util.Dpi(24).toFloat()
            textColor = Color.parseColor("#80FFFFFF")
            maxLines = 2
            text = "下载手机APP，登录与电视相同 账号，添加红外设备后，电视将自动同步"
        }

        //白色背景套在外面
        imageView {
            val params = LayoutParams(Util.Div(250), Util.Div(250))
            params.topMargin = Util.Div(304)
            params.gravity = Gravity.CENTER_HORIZONTAL
            layoutParams = params
            backgroundColor = Color.parseColor("#FFFFFF")
        }

        mQrCodeView = imageView {
            val params = LayoutParams(Util.Div(240), Util.Div(240))
            params.topMargin = Util.Div(309)
            params.gravity = Gravity.CENTER_HORIZONTAL
            layoutParams = params
            backgroundColor = Color.parseColor("#FFFFFF")
        }

        imageView {
            val params = LayoutParams(Util.Div(560), Util.Div(1))
            params.topMargin = Util.Div(584)
            params.gravity = Gravity.CENTER_HORIZONTAL
            layoutParams = params
            backgroundColor = Color.parseColor("#FFFFFF")
            alpha = 0.1f
        }

        textView {
            val params = LayoutParams(wrapContent, wrapContent)
            params.topMargin = Util.Div(616)
            params.leftMargin = Util.Div(50)
            layoutParams = params
            textSize = Util.Dpi(30).toFloat()
            textColor = Color.parseColor("#FFFFFF")
            paint.isFakeBoldText = true
            text = "方法二    电视上添加"
        }

        textView {
            val params = LayoutParams(wrapContent, wrapContent)
            params.topMargin = Util.Div(666)
            params.leftMargin = Util.Div(50)
            layoutParams = params
            textSize = Util.Dpi(24).toFloat()
            textColor = Color.parseColor("#80FFFFFF")
            text = "操作较复杂，请按提示耐心操作"
        }

        addBtn = textView {
            val params = LayoutParams(Util.Div(302), Util.Div(60))
            params.topMargin = Util.Div(720)
            params.gravity = Gravity.CENTER_HORIZONTAL
            layoutParams = params
            textSize = Util.Dpi(24).toFloat()
            textColor = Color.parseColor("#000000")
            background = CCFocusDrawable(context).setRadius(Util.Dpi(50).toFloat()).setSolidColor(resources.getColor(R.color.white));
            text = "添加"
            gravity = Gravity.CENTER
            isFocusable = true
            isClickable = true
            setOnClickListener {
                listener?.OnClick()
            }
        }

    }

    fun getFocus() {
        addBtn?.requestFocus()
    }

    fun setTitile(title:String){
        if (!TextUtils.isEmpty(title)) {
            mTitleTv?.setText(title)
        }
    }

    fun showQrCode(qrcontent: String) {
        if (!TextUtils.isEmpty(qrcontent)) {
            ioThread {
                try {
//                    val logo = BitmapFactory.decodeResource(resources, R.drawable.icon_smarthome)
                    val bitmap=QRUtils.createQRImage(qrcontent, Util.Div(275), Util.Div(275),null)
                    if (bitmap != null) {
                        runOnUiThread {
                            mQrCodeView?.setImageBitmap(bitmap)
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        } else {
            mQrCodeView?.setImageBitmap(null)
        }
    }
}