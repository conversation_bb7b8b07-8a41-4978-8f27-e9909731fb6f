package com.skyworth.smarthome.infrared.learn.presenter.steps;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/7/4 10:23.
 */
public class IRLearnStepFinish extends BaseIRLearnStep {
    public static final String STEP_TAG = "finish";

    @Override
    public void create() {
        super.create();
    }

    @Override
    public void run() {
        super.run();
        presenter.stopTimeOutTimer();
        presenter.showLearnFinish();
    }

    @Override
    public boolean input(String msg, Object... params) {
        return false;
    }

    @Override
    public String getTag() {
        return STEP_TAG;
    }

    @Override
    public void destroy() {
        super.destroy();
    }
}
