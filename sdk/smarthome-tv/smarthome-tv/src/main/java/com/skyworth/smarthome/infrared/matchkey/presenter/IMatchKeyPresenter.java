package com.skyworth.smarthome.infrared.matchkey.presenter;

import android.content.Context;

import com.skyworth.smarthome.infrared.matchkey.view.IMatchKeyView;
import com.skyworth.smarthome.infrared.matchkey.model.IMatchKeyModel;

import java.util.Map;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/4/29 16:20.
 */
public interface IMatchKeyPresenter {
    void setListener(IMatchKeyPresenterImpl.IRMatchListener listener);

    void create(Context context, IMatchKeyView view, IMatchKeyModel model);

    void startMatch(Map<String, String> params);

    void startMatchInternal();

    boolean sendIR();

    void handleClick(String key);

    void showReadyView();

    void showSendingView();

    void showConfirmView();

    /**
     * 按键有响应
     */
    void keyResponse();

    /**
     * 按键无响应
     */
    void keyNotResponse();

    boolean saveIRConfig();

    void goConfigSuccess();

    void speechSuccess();

    void showMatchErrorView();

    void launchIRLearn();

    void destroy();
}
