package com.skyworth.smarthome.infrared.electriclist.view;

import android.content.Context;
import android.support.v7.widget.NewRecycleAdapter;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import android.support.annotation.NonNull;

import com.coocaa.app.core.utils.FuncKt;
import com.skyworth.smarthome.infrared.electriclist.model.DeviceTypeListData;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.ui.DialogBg;
import com.skyworth.smarthome.common.ui.EmptyView;
import com.skyworth.smarthome.common.ui.LoadingView;
import com.skyworth.smarthome.infrared.electriclist.presenter.IElectricTypePresenter;
import com.skyworth.ui.newrecycleview.NewRecycleAdapterItem;
import com.skyworth.ui.newrecycleview.NewRecycleLayout;
import com.skyworth.ui.newrecycleview.OnBoundaryListener;
import com.skyworth.ui.newrecycleview.OnItemClickListener;
import com.skyworth.ui.newrecycleview.OnItemFocusChangeListener;
import com.skyworth.util.Util;
import com.smarthome.common.utils.XThemeUtils;

import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * Created by fc on 2019/4/24
 * Describe:
 */
public class ElectricTypeView extends FrameLayout implements IElectircTypeView, OnBoundaryListener {
    private NewRecycleLayout<DeviceTypeListData> mListView = null;
    private NewRecycleAdapter<DeviceTypeListData> mAdapter = null;
    private IElectricTypePresenter mPresenter;
    private EmptyView emptyView;
    private LoadingView loadingView;
    private Context mContext;

    public ElectricTypeView(@NonNull Context context) {
        super(context);
    }


    @Override
    public void createView(Context context, IElectricTypePresenter presenter) {
        mContext = context;
        mPresenter = presenter;
        initView();
    }

    @Override
    public void showList(List<DeviceTypeListData> list) {
        mAdapter = new NewRecycleAdapter<DeviceTypeListData>(list, 2) {

            @Override
            public NewRecycleAdapterItem<DeviceTypeListData> onCreateItem(Object type) {
                return new ElectricTypeItem(mContext);
            }
        };
        mListView.setRecyclerAdapter(mAdapter);
        mListView.setVisibility(VISIBLE);
        mListView.post(new Runnable() {
            @Override
            public void run() {
                mListView.setSelection(0);
            }
        });
    }

    @Override
    public void showLoading() {
        if (loadingView == null) {
            loadingView = new LoadingView(mContext);
            addView(loadingView);
        }
        loadingView.setVisibility(VISIBLE);

    }

    @Override
    public void hideLoading() {
        FuncKt.runOnUiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                if (loadingView != null)
                    loadingView.setVisibility(GONE);
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void showErrorView(String errorMsg, String errorCode) {
        if (emptyView == null) {
            emptyView = new EmptyView(mContext);
            emptyView.setText(errorMsg);
            emptyView.getFocus();
            LayoutParams p = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            p.topMargin = Util.Div(28);
            p.gravity = Gravity.CENTER_HORIZONTAL;
            addView(emptyView, p);
            emptyView.setOnClick(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    mPresenter.loadList();
                }
            });
        }
        emptyView.getFocus();
        emptyView.setVisibility(VISIBLE);
        if (mListView != null)
            mListView.setVisibility(GONE);
    }

    @Override
    public void hideErrorView() {
        if (emptyView != null)
            emptyView.setVisibility(GONE);
    }

    private int mLastFocusPosition = 0;

    @Override
    public void getFocus() {
        if (emptyView != null && emptyView.getVisibility() == VISIBLE)
            emptyView.getFocus();
        else {
            if (mAdapter != null && mAdapter.getData() != null && mAdapter.getData().size() > 0) {
                mListView.post(new Runnable() {
                    @Override
                    public void run() {
                        mListView.setSelection(mLastFocusPosition);
                    }
                });
            }
        }
    }

    @Override
    public View getView() {
        return this;
    }

    private void initView() {
        FrameLayout mLayout = new FrameLayout(mContext);
        LayoutParams layoutParams = new LayoutParams(Util.Div(660), Util.Div(810));
        addView(mLayout, layoutParams);
        mLayout.setBackground(new DialogBg());

        TextView titleView = new TextView(mContext);
        titleView.getPaint().setFakeBoldText(true);
        LayoutParams lp = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        titleView.setTextSize(Util.Dpi(36));
        titleView.setTextColor(XThemeUtils.c_1a());
        lp.topMargin = Util.Div(30);
        lp.gravity = Gravity.CENTER_HORIZONTAL;
        titleView.setText(mContext.getString(R.string.smart_home_select_electric_type));
        mLayout.addView(titleView, lp);

        FrameLayout frameLayout = new FrameLayout(mContext);
        frameLayout.setClipChildren(false);
        frameLayout.setClipToPadding(false);
        lp = new LayoutParams(Util.Div(635), ViewGroup.LayoutParams.MATCH_PARENT);
        lp.gravity = Gravity.CENTER_HORIZONTAL;
        lp.topMargin = Util.Div(92);
        mLayout.addView(frameLayout, lp);

        mListView = new NewRecycleLayout<>(mContext, 3);
        mListView.setItemSpace(Util.Div(30), Util.Div(30));
        mListView.setClipChildren(false);
        mListView.setClipToPadding(false);
        mListView.setmBoundaryListener(this);
        mListView.setmItemFocusChangeListener(onItemFocusChangeListener);
        mListView.setmItemClickListener(onItemClickListener);
        lp = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.MATCH_PARENT);
        lp.bottomMargin = Util.Div(50);
        frameLayout.addView(mListView, lp);

    }

    private OnItemFocusChangeListener onItemFocusChangeListener = new OnItemFocusChangeListener() {
        @Override
        public void focusChange(View v, int position, boolean hasFocus) {
            if (v instanceof ElectricTypeItem) {
                ((ElectricTypeItem) v).onItemFocus(hasFocus,null);
                Util.focusAnimate(v, hasFocus);//放大动画
            }
        }
    };

    private OnItemClickListener onItemClickListener = new OnItemClickListener() {
        @Override
        public void click(View v, int position) {
            //跳转热门列表
            DeviceTypeListData itemData = mAdapter.getItem(position);
            mPresenter.onItemClick(v, itemData, position);
            mLastFocusPosition = position;
        }
    };

    @Override
    public boolean onLeftBoundary(View leaveView, int position) {
        return true;
    }

    @Override
    public boolean onTopBoundary(View leaveView, int position) {
        return true;
    }

    @Override
    public boolean onDownBoundary(View leaveView, int position) {
        return true;
    }

    @Override
    public boolean onRightBoundary(View leaveView, int position) {
        return true;
    }

    @Override
    public boolean onOtherKeyEvent(View v, int position, int keyCode) {
        return false;
    }

}
