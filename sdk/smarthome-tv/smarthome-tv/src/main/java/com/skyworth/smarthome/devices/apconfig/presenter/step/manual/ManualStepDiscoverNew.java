package com.skyworth.smarthome.devices.apconfig.presenter.step.manual;

import com.skyworth.smarthome.devices.apconfig.presenter.step.BaseStep;

import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_CONFIG_EXIT;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_DISCOVER_CONFIRM;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_DISCOVER_EXIT;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_HIDE_DISCOVER;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_SHOW_DISCOVER;

/**
 * Description: <br>
 * Created by <PERSON><PERSON><PERSON>ia<PERSON> on 2019/1/7 21:54.
 */
public class ManualStepDiscoverNew extends BaseStep {
    public static final String STEP_TAG = "manual_discoverNew";

    @Override
    public void run() {
        super.run();
        presenter.showDialog();
        output(STEP_MSG_SHOW_DISCOVER);
    }

    @Override
    public boolean input(String msg, Object... params) {
        switch (msg) {
            case STEP_MSG_DISCOVER_CONFIRM:
                next();
                return true;
            case STEP_MSG_DISCOVER_EXIT:
                output(STEP_MSG_CONFIG_EXIT);
                return true;
        }
        return false;
    }

    @Override
    public String getTag() {
        return STEP_TAG;
    }

    @Override
    public void destroy() {
        output(STEP_MSG_HIDE_DISCOVER);
        super.destroy();
    }

}
