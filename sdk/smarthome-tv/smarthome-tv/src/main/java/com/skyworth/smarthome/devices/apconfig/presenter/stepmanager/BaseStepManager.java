package com.skyworth.smarthome.devices.apconfig.presenter.stepmanager;

import android.content.Context;
import android.os.Looper;
import android.util.Log;

import com.coocaa.app.core.app.AppCoreApplication;

import java.util.ArrayList;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

import static com.skyworth.smarthome.devices.apconfig.ApConfigService.TAG;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/1/7 21:54.
 */
public class BaseStepManager<Presenter> {
    private List<IOneStep> steps = null;
    private int currentStep = 0;
    private IStepListener listener = null;
    private Context context = null;
    private Presenter mPresenter = null;

    public interface IStepListener {
        void onOutput(String msg, Object... params);
    }

    public BaseStepManager(Context context, Presenter presenter) {
        this.context = context;
        mPresenter = presenter;
        steps = new ArrayList<>();
    }

    public void addStep(IOneStep oneStep) {
        oneStep.setContext(context);
        steps.add(oneStep);
        oneStep.setManger(this);
        oneStep.setPresenter(mPresenter);
    }

    public void addStepAt(int index, IOneStep oneStep) {
        oneStep.setContext(context);
        steps.add(index, oneStep);
        oneStep.setManger(this);
        oneStep.setPresenter(mPresenter);
    }

    public void clear() {
        steps.clear();
    }

    public void setListener(IStepListener listener) {
        this.listener = listener;
    }

    public void start() {
        currentStep = 0;
        steps.get(0).create();
        steps.get(0).run();
    }

    public void stop() {
        steps.get(currentStep).destroy();
        currentStep = 0;
    }

    public void next() {
        if (currentStep + 1 < steps.size()) {
            steps.get(currentStep++).destroy();
            steps.get(currentStep).create();
            steps.get(currentStep).run();
        } else if (currentStep + 1 == steps.size()) {
            stop();
        }
    }

    public void previous() {
        if (currentStep - 1 >= 0) {
            steps.get(currentStep--).destroy();
            steps.get(currentStep).create();
            steps.get(currentStep).run();
        }
    }

    public void reload() {
        if (Thread.currentThread() == Looper.getMainLooper().getThread()) {
            AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
                @Override
                public Unit invoke() {
                    steps.get(currentStep).run();
                    return Unit.INSTANCE;
                }
            });
        } else {
            steps.get(currentStep).run();
        }
    }

    public void jumpTo(int index) {
        if (index >= 0 && index < steps.size()) {
            Log.i(TAG, "jumpTo: >>>> " + steps.get(index).getTag() + " step " + index);
            steps.get(currentStep).destroy();
            currentStep = index;
            steps.get(index).create();
            steps.get(index).run();
        } else {
            Log.i(TAG, "jumpTo: fail not match condition");
        }
    }

    public void jumpTo(String tag) {
        String currentTag;
        for (int i = 0; i < steps.size(); i++) {
            currentTag = steps.get(i).getTag();
            Log.i(TAG, "jumpTo: check: " + currentTag);
            if (currentTag.equals(tag)) {
                jumpTo(i);
                return;
            }
        }
        Log.w(TAG, "jumpTo: no step handles tag: " + tag);
    }

    public int getCurrentStepIndex() {
        return currentStep;
    }

    public String getCurrentStepTag() {
        return steps.get(currentStep).getTag();
    }

    public boolean input(String msg, Object... params) {
        if (!steps.get(currentStep).input(msg, params)) {
            Log.i(TAG, "input: " + getCurrentStepTag() + ": no step handles msg: " + msg);
            return false;
        }
        return true;
    }

    public void output(String msg, Object... params) {
        if (listener != null) {
            listener.onOutput(msg, params);
        }
    }

    public int getStepCount() {
        return steps.size();
    }
}
