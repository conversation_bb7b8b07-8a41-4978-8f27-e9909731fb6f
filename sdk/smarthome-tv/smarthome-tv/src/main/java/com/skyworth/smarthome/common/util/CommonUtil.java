package com.skyworth.smarthome.common.util;

import android.content.Context;
import android.content.Intent;

import com.coocaa.operate6_0.model.OnClickData;
import com.smarthome.common.sal.SalImpl;
import com.smarthome.common.utils.Android;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XNetworkDialog;

import static com.coocaa.operate6_0.model.OnClickData.DOWHAT_SEND_BROADCAST;
import static com.coocaa.operate6_0.model.OnClickData.DOWHAT_START_ACTIVITY;
import static com.coocaa.operate6_0.model.OnClickData.DOWHAT_START_SERVICE;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/17
 */
public class CommonUtil {

    private static final String PKG_AI_2 = "com.skyworth.lafite.srtnj.speechserver";//2.0的小维AI包名
    private static final String PKG_AI_3 = "com.skyworth.angel.voice";//3.0的小维AI包名

    public static void click(Context context, OnClickData data) {
        try {
            if (data != null && data.dowhat != null && !data.dowhat.equals("") && !data.dowhat.equals("null")) {
                checkPackage(context, data);
                Intent intent = data.buildIntent(context);
                if (intent != null) {
                    try {
                        switch (data.dowhat) {
                            case DOWHAT_START_ACTIVITY:
                                context.startActivity(intent);
                                break;
                            case DOWHAT_START_SERVICE:
                                context.startService(intent);
                                break;
                            case DOWHAT_SEND_BROADCAST:
                                context.sendBroadcast(intent);
                                break;
                            default:
                                break;
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        if (data.exception != null && data.exception.value != null) {
                            click(context, data.exception.value);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void checkPackage(Context context, OnClickData data) {
        if (EmptyUtils.isNotEmpty(data.packagename)) {
            if (data.packagename.equals(PKG_AI_2) || data.packagename.equals(PKG_AI_3)) {
                String returnPkg;
                if (systemAbove8(context)) {
                    returnPkg = PKG_AI_3;
                } else {
                    returnPkg = PKG_AI_2;
                }
                data.packagename = returnPkg;
                if (EmptyUtils.isNotEmpty(data.exception) && EmptyUtils.isNotEmpty(data.exception.value) && EmptyUtils.isNotEmpty(data.exception.value.params)) {
                    data.exception.value.params.put("id", returnPkg);
                }
            }
        }
    }

    /**
     * 当前系统是否是8.0及以上的
     *
     * @param context
     * @return
     */
    public static boolean systemAbove8(Context context) {
        return getSysVersionCode(context) >= 8;
    }

    /**
     * 获取系统版本号
     *
     * @param context
     * @return 返回单位数：6、7、8
     */
    public static int getSysVersionCode(Context context) {
        int sysVersionCode = 0;
        try {
            String versionCode = String.valueOf(SalImpl.getSAL(context).getVersionCode());
            if (EmptyUtils.isNotEmpty(versionCode) && versionCode.length() > 0) {
                try {
                    sysVersionCode = Integer.parseInt(versionCode.substring(0, 1));
                } catch (NumberFormatException e) {
                    e.printStackTrace();
                }
            }
            CCLog.i("getVersionCode:" + versionCode + "--sysVersionCode:" + sysVersionCode);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sysVersionCode;
    }

    /**
     * 返回网络连接状态，并且显示去连网弹窗
     *
     * @param context
     * @return
     */
    public static boolean isNetConnected(final Context context) {
        if (Android.isNetConnected(context)) {
            return true;
        }
        XNetworkDialog.showConnectNetDialog(context);
        return false;
    }
}
