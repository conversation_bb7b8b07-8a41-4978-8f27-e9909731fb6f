package com.skyworth.smarthome.devices.apconfig.presenter;

import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.wifi.ScanResult;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;

import com.alibaba.fastjson.JSONObject;
import com.coocaa.app.core.app.AppCoreApplication;
import com.skyworth.smarthome.common.model.SPCacheData;
import com.skyworth.smarthome.devices.apconfig.model.ApConfigModelImpl;
import com.skyworth.smarthome.devices.apconfig.model.ApconfigReportDataModel;
import com.skyworth.smarthome.devices.apconfig.model.IApConfigModel;
import com.skyworth.smarthome.devices.apconfig.model.SwitchWifiInfo;
import com.skyworth.smarthome.devices.apconfig.presenter.helpers.ConfirmBindHelper;
import com.skyworth.smarthome.devices.apconfig.presenter.helpers.SpeechHelper;
import com.skyworth.smarthome.devices.apconfig.presenter.helpers.WiFiHelper;
import com.skyworth.smarthome.devices.apconfig.presenter.step.ApConfigStepFactory;
import com.skyworth.smarthome.devices.apconfig.presenter.step.auto.AutoStepBindDevice;
import com.skyworth.smarthome.devices.apconfig.presenter.step.auto.AutoStepConfirmBind;
import com.skyworth.smarthome.devices.apconfig.presenter.step.manual.ManualStepBindDevice;
import com.skyworth.smarthome.devices.apconfig.presenter.step.manual.ManualStepCheckEtherNet;
import com.skyworth.smarthome.devices.apconfig.presenter.step.manual.ManualStepConfigFailed;
import com.skyworth.smarthome.devices.apconfig.presenter.step.manual.ManualStepConfiging;
import com.skyworth.smarthome.devices.apconfig.presenter.step.manual.ManualStepDiscoverNew;
import com.skyworth.smarthome.devices.apconfig.presenter.stepmanager.BaseStepManager;
import com.skyworth.smarthome.devices.apconfig.view.AddDeviceResultView;
import com.skyworth.smarthome.devices.discover.receiver.StartServiceReceiver;
import com.skyworth.smarthome.devices.discover.view.DiscoverNearDevicesView;
import com.skyworth.smarthome.home.custom.panel.DevicePanelPresenter;
import com.skyworth.smarthome.personal.thirdaccount.dialog.third.BindThridAccSysDialog;
import com.skyworth.smarthome.service.model.ISmartHomeModel;
import com.skyworth.smarthome.service.push.local.DeviceDataPushUtil;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.common.bean.DevicePosBean;
import com.skyworth.smarthome.common.bean.ThridAccountHttpBean;
import com.skyworth.smarthome.common.dialog.DownloadRrCodeDialog;
import com.skyworth.smarthome.common.event.ApConfigEvent;
import com.skyworth.smarthome.common.event.StartDiscoverNearDeviceEvent;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.model.UserInfo;
import com.skyworth.smarthome.common.util.DataCacheUtil;
import com.skyworth.smarthome.common.util.LogUtil;
import com.skyworth.smarthome.common.util.NetworkUtils;
import com.skyworth.smarthome.common.util.PollingChecker;
import com.skyworth.smarthome.common.util.Utils;
import com.skyworth.smarthome.devices.apconfig.ApConfigService;
import com.skyworth.smarthome.devices.apconfig.view.IApConfigView;
import com.skyworth.smarthome.service.model.FunctionGoToModel;
import com.skyworth.smarthome.service.model.ISmartDeviceDataModel;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XToast;
import com.swaiot.aiotlib.AiotLibSDK;
import com.swaiot.aiotlib.common.entity.DiscoverNetworkDevice;
import com.swaiot.aiotlib.common.entity.DiscoverWifiDevice;
import com.swaiot.aiotlib.device.IDevice;
import com.swaiot.aiotlib.device.apconfig.module.WifiModule;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

import static com.skyworth.smarthome.common.model.AppConstants.APCONFIG_FAIL_REASON_ACCOUNT_BIND_DEVICE_FAIL;
import static com.skyworth.smarthome.common.model.AppConstants.APCONFIG_FAIL_REASON_APCONFIG_FAIL;
import static com.skyworth.smarthome.common.model.AppConstants.APCONFIG_FAIL_REASON_DEVICE_OTHER_BIND;
import static com.skyworth.smarthome.common.model.AppConstants.APCONFIG_FAIL_REASON_MANUAL_EXIT;
import static com.skyworth.smarthome.common.model.AppConstants.APCONFIG_FAIL_REASON_NOT_LOGIN;
import static com.skyworth.smarthome.devices.apconfig.ApConfigService.DIRECT_TO_STEP_KEY;
import static com.skyworth.smarthome.devices.apconfig.ApConfigService.EXTRA_FLAG_KEY;
import static com.skyworth.smarthome.devices.apconfig.ApConfigService.EXTRA_KEY;
import static com.skyworth.smarthome.devices.apconfig.ApConfigService.EXTRA_KEY_DEVICE_DETAIL;
import static com.skyworth.smarthome.devices.apconfig.ApConfigService.TAG;
import static com.skyworth.smarthome.devices.apconfig.ApConfigService.TYPE_KEY;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/1/7 15:27.
 */
public class ApConfigPresenterImpl implements IApConfigPresenter {
    private Context mContext;
    private IApConfigView mView = null;
    private IApConfigModel mModel = null;
    private WiFiHelper wiFiHelper = null;
    private SpeechHelper speechHelper = null;
    //    private DataSubmitHelper dataSubmitHelper = null;
    private ConfirmBindHelper confirmBindHelper = null;
    private BaseStepManager<ApConfigPresenterImpl> stepManager = null;
    private BindThridAccSysDialog dialog;

    private boolean needResume = false;
    private boolean isApconfigFail = false;//防止多次调用配网失败，提交大数据错误

    /**
     * 配网所有步骤是否完成且成功
     */
    private boolean isAllDone = false;
    private String mConfigType = null;
    private boolean isExiting = false;

    private String requestId = "";
    private String mideaToken = null;

    private List<ThridAccountHttpBean> mThirdAccount = null;
    private DiscoverWifiDevice mDeviceInfo = null;
    private JSONObject deviceMsg;
    private String jumpToTag = null;

    public static final String STEP_MSG_SHOW_DISCOVER = "SHOW_DISCOVER";
    public static final String STEP_MSG_HIDE_DISCOVER = "HIDE_DISCOVER";
    public static final String STEP_MSG_DISCOVER_CONFIRM = "DISCOVER_CONFIRM";
    public static final String STEP_MSG_DISCOVER_EXIT = "DISCOVER_EXIT";
    public static final String STEP_MSG_CONFIG_EXIT = "CONFIG_EXIT";

    public static final String STEP_MSG_ETHERNET_CONFIRM = "ETHERNET_CONFIRM";
    public static final String STEP_MSG_ETHERNET_CONFIRM_SHOW = "ETHERNET_CONFIRM_SHOW";
    public static final String STEP_MSG_ETHERNET_CONFIRM_HIDE = "ETHERNET_CONFIRM_HIDE";

    public static final String STEP_MSG_24GWIFI_HIDE = "24GWIFI_HIDE";
    public static final String STEP_MSG_24GWIFI_SHOW = "24GWIFI_SHOW";
    public static final String STEP_MSG_24GWIFI_CLICK = "24GWIFI_CLICK";

    public static final String STEP_MSG_INPUT_PASSWORD_SHOW = "INPUT_PASSWORD_SHOW";
    public static final String STEP_MSG_INPUT_PASSWORD_HIDE = "INPUT_PASSWORD_HIDE";
    public static final String STEP_MSG_INPUT_PASSWORD_OK = "INPUT_PASSWORD_OK";
    public static final String STEP_MSG_CONFIG_PROGRESS_HIDE = "CONFIG_PROGRESS_HIDE";
    public static final String STEP_MSG_CONFIG_SUCCESS = "CONFIG_SUCCESS";
    public static final String STEP_MSG_CONFIG_FAIL = "CONFIG_FAIL";
    public static final String STEP_MSG_CONFIG_FAILED_SHOW = "CONFIG_FAILED_SHOW";
    public static final String STEP_MSG_CONFIG_FAILED_HIDE = "CONFIG_FAILED_HIDE";
    public static final String STEP_MSG_RECONFIG = "RECONFIG";

    public static final String STEP_MSG_CONFIG_FINISH = "CONFIG_FINISH";
    public static final String STEP_MSG_BIND_FAILED_SHOW = "BIND_FAILED_SHOW";
    public static final String STEP_MSG_BIND_FAILED_HIDE = "BIND_FAILED_HIDE";
    public static final String STEP_MSG_BIND_FAILED_OTHER_BIND = "BIND_FAILED_OTHER_BIND";

    public static final String STEP_MSG_SET_LOCATION_SHOW = "SET_LOCATION_SHOW";
    public static final String STEP_MSG_SET_LOCATION_HIDE = "SET_LOCATION_HIDE";
    public static final String STEP_MSG_SET_LOCATION_OK = "SET_LOCATION_OK";

    public static final String STEP_MSG_CONFIRM_BIND_SHOW = "CONFIRM_BIND_SHOW";

    public static final String STEP_MSG_INPUT_GO_LOGIN = "INPUT_GO_LOGIN";
    public static final String STEP_MSG_INPUT_GO_LOGIN_AND_BIND = "INPUT_GO_LOGIN_AND_BIND";
    public static final String STEP_MSG_INPUT_GO_BIND_MOBILE = "INPUT_GO_BIND_MOBILE";
    public static final String STEP_MSG_INPUT_GO_BIND_MIDEA = "INPUT_GO_BIND_MIDEA";
    public static final String STEP_MSG_INPUT_GO_GET_MIDEA_TOKEN = "INPUT_GO_GET_MIDEA_TOKEN";
    public static final String STEP_MSG_INPUT_CONFIRM_BIND_OK = "CONFIRM_BIND_OK";
    public static final String STEP_MSG_INPUT_CONFIRM_BIND_CANCEL = "CONFIRM_BIND_CANCEL";
    public static final String STEP_MSG_INPUT_SCREEN_SAVER_CLOSE = "SCREEN_SAVER_CLOSE";

    private static final List<Integer> BACK_KEY = new ArrayList<>();
    private static final List<String> TWO_STEP_EXIT_CONFIRM_STEP_TAG = new ArrayList<>();
    private static final List<String> STEP_TAG_NOT_NEED_WIFI = new ArrayList<>();
    private static final List<String> AUTO_CONFIG_SKIP_BACK_KEY = new ArrayList<>();
    private static final List<ComponentName> USER_COMPONENT = new ArrayList<>();

    static {
        BACK_KEY.add(KeyEvent.KEYCODE_BACK);
        BACK_KEY.add(KeyEvent.KEYCODE_ESCAPE);

        TWO_STEP_EXIT_CONFIRM_STEP_TAG.add(ManualStepConfiging.STEP_TAG);
        TWO_STEP_EXIT_CONFIRM_STEP_TAG.add(ManualStepBindDevice.STEP_TAG);
        TWO_STEP_EXIT_CONFIRM_STEP_TAG.add(ManualStepConfigFailed.STEP_TAG);

        STEP_TAG_NOT_NEED_WIFI.add(ManualStepBindDevice.STEP_TAG);
        STEP_TAG_NOT_NEED_WIFI.add(ManualStepDiscoverNew.STEP_TAG);

        AUTO_CONFIG_SKIP_BACK_KEY.add(AutoStepConfirmBind.STEP_TAG);
//        AUTO_CONFIG_SKIP_BACK_KEY.add(AutoStepBindDevice.STEP_TAG);

        USER_COMPONENT.add(new ComponentName("com.tianci.user", "com.tianci.webview.AccountWebActivity"));
        USER_COMPONENT.add(new ComponentName("com.tianci.user", "com.tianci.user.ui.AccountActivity"));
        USER_COMPONENT.add(new ComponentName("com.tianci.user", "com.coocaa.user.ui.AccountActivity"));
    }

    @Override
    public void create(Context context, IApConfigView view, IApConfigModel model) {
        mContext = context;
        mView = view;
        mModel = model;
        wiFiHelper = new WiFiHelper(context);
        speechHelper = new SpeechHelper(context);
//        dataSubmitHelper = new DataSubmitHelper(context);
        confirmBindHelper = new ConfirmBindHelper();
        stepManager = new BaseStepManager<>(mContext, this);
        stepManager.setListener(iStepListener);
        AppData.getInstance().setOpenSoftPan(false);
        regBroadcastReceiver();
        disableDiscoverNear();
    }

    private void regBroadcastReceiver() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
        intentFilter.addAction(StartServiceReceiver.ACTION_EXIT_AD_SCREENSAVER);
        intentFilter.addAction(StartServiceReceiver.ACTION_EXIT_SKYWORTH_SCREENSAVER);
        mContext.registerReceiver(mReceiver, intentFilter);
    }

    @Override
    public void start(Intent intent) {
        if (intent == null) {
            Log.e(TAG, "start: intent == null");
            stopService();
            return;
        }
        String params = intent.getStringExtra(EXTRA_KEY);
        String stepTag = intent.getStringExtra(DIRECT_TO_STEP_KEY);
        String configType = intent.getStringExtra(TYPE_KEY);
        String[] extraFlag = intent.getStringArrayExtra(EXTRA_FLAG_KEY);
        saveExtDeviceInfo(intent.getStringExtra(EXTRA_KEY_DEVICE_DETAIL));
        ApConfigStepFactory.addSteps(stepManager, configType, extraFlag);
//        dataSubmitHelper.setConfigType(configType);
//        dataSubmitHelper.logNetType();
        jumpToTag = stepTag;
        setAllDone(false);
        mConfigType = configType;
        if (TextUtils.isEmpty(stepTag)) {
            startInternal(params);
        } else {
            startAtInternal(params);
        }
//        if (!isManual()) {
//            dataSubmitHelper.logAutoBindStart(getDeviceBrand(), getProduct());
//        }
    }

    private void saveExtDeviceInfo(String json) {
        if (json == null) {
            return;
        }
        Log.i(TAG, "saveExtDeviceInfo: " + json);
        mDeviceInfo = JSONObject.parseObject(json, DiscoverWifiDevice.class);
    }

    private void startInternal(final String json) {
        Log.i(TAG, "startInternal");
        AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                if (!convertParams(json)) {
                    return Unit.INSTANCE;
                }
                stepManager.start();
                return Unit.INSTANCE;
            }
        });
    }

    private void startAtInternal(final String params) {
        final String tag = jumpToTag;
        Log.i(TAG, "startAtInternal: " + tag);
        AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                if (tag.equals(ManualStepBindDevice.STEP_TAG) || tag.equals(AutoStepConfirmBind.STEP_TAG) || tag.equals(AutoStepBindDevice.STEP_TAG)) {
                    deviceMsg = JSONObject.parseObject(params);
                    Log.i(TAG, "startAtInternal: " + deviceMsg);
                } else if (!convertParams(params)) {
                    Log.i(TAG, "startAtInternal: !convertParams()");
                    return Unit.INSTANCE;
                }
                Log.i(TAG, "startAtInternal: jumpTo: manager: " + stepManager + " size: " + stepManager.getStepCount());
                stepManager.jumpTo(tag);
                Log.i(TAG, "startAtInternal: return");
                return Unit.INSTANCE;
            }
        });
    }

    private boolean convertParams(String params) {
        DiscoverWifiDevice deviceInfo = JSONObject.parseObject(params, DiscoverWifiDevice.class);
        if (!checkData(deviceInfo)) {
            return false;
        }
        mDeviceInfo = deviceInfo;
        return true;
    }

    private void disableDiscoverNear() {
        StartDiscoverNearDeviceEvent event = new StartDiscoverNearDeviceEvent();
        event.setData(false);
        EventBus.getDefault().post(event);
    }

    private BroadcastReceiver mReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent == null || intent.getAction() == null) {
                Log.e(TAG, "onReceive: intent == null || action == null");
                return;
            }
            Log.i(TAG, "onReceive: " + intent.getAction());
            switch (intent.getAction()) {
                case ConnectivityManager.CONNECTIVITY_ACTION:
                    AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
                        @Override
                        public Unit invoke() {
                            if (NetworkUtils.isEthernetConnect(mContext) && !STEP_TAG_NOT_NEED_WIFI.contains(stepManager.getCurrentStepTag())) {
                                Log.i(TAG, "onReceive: jumpTo: " + ManualStepCheckEtherNet.STEP_TAG);
                                stepManager.jumpTo(ManualStepCheckEtherNet.STEP_TAG);
                            }
                            return Unit.INSTANCE;
                        }
                    });
                    break;
                case StartServiceReceiver.ACTION_EXIT_SKYWORTH_SCREENSAVER:
                case StartServiceReceiver.ACTION_EXIT_AD_SCREENSAVER:
                    if (!isScreenSaverShow() && !Utils.isPowerOffShow()) {
                        stepManager.input(STEP_MSG_INPUT_SCREEN_SAVER_CLOSE);
                    }
                    break;
                default:
                    break;
            }
        }
    };

    private boolean isScreenSaverShow() {
        int tryTimes = 0;
        while (tryTimes < 10) {
            try {
                if (Utils.isScreenSaverShow()) {
                    Thread.sleep(500);
                    tryTimes++;
                } else {
                    return false;
                }
                Log.i(TAG, "screen saver close check again: " + tryTimes);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        return true;
    }

    private BaseStepManager.IStepListener iStepListener = new BaseStepManager.IStepListener() {
        @Override
        public void onOutput(final String msg, final Object... params) {
            AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
                @Override
                public Unit invoke() {
                    switch (msg) {
                        case STEP_MSG_SHOW_DISCOVER:
                            showDiscover();
                            break;
                        case STEP_MSG_HIDE_DISCOVER:
                            mView.hideDiscoveryDialog();
                            break;
                        case STEP_MSG_ETHERNET_CONFIRM_SHOW:
                            mView.showDisconnectEthernet();
                            break;
                        case STEP_MSG_ETHERNET_CONFIRM_HIDE:
                            mView.hideDisconnectEthernet();
                            break;
                        case STEP_MSG_24GWIFI_HIDE:
                            mView.hide24GWifiList();
                            break;
                        case STEP_MSG_24GWIFI_SHOW:
                            mView.show24GWifiList();
                            break;
                        case STEP_MSG_INPUT_PASSWORD_SHOW:
                            passwordShow(params);
                            break;
                        case STEP_MSG_INPUT_PASSWORD_HIDE:
                            mView.hideInputPassword();
                            break;
                        case STEP_MSG_CONFIG_PROGRESS_HIDE:
                            mView.hideConfiging();
                            break;
                        case STEP_MSG_CONFIG_FAILED_SHOW:
                            showConfigFail();
                            break;
                        case STEP_MSG_CONFIG_FAILED_HIDE:
                            mView.hideConfigFailed();
                            break;
                        case STEP_MSG_CONFIG_FINISH:
                            configFinish();
                            break;
                        case STEP_MSG_BIND_FAILED_SHOW:
                            mView.showBindingResult(false);
                            break;
                        case STEP_MSG_BIND_FAILED_HIDE:
                            mView.hideBindingResult();
                            break;
                        case STEP_MSG_SET_LOCATION_SHOW:
                            showSetDevicePos();
                            break;
                        case STEP_MSG_SET_LOCATION_HIDE:
                            mView.hideSetDevicePos();
                            break;
                        case STEP_MSG_BIND_FAILED_OTHER_BIND:
//                            showToast(mContext.getString(R.string.bind_failed_other_bind));
                            showDownloadPhoneApp("此设备已被他人绑定，您可以通过手机app添加此设备");
                            break;
                        case STEP_MSG_CONFIRM_BIND_SHOW:
                            showBindConfirm();
                            break;
                        case STEP_MSG_INPUT_GO_LOGIN_AND_BIND:
                            showLoginAndBind();
                    }
                    return Unit.INSTANCE;
                }
            });
        }
    };

    private void showDiscover() {
        Log.i(TAG, "showDiscover");
        mView.showDiscoveryDialog(mDeviceInfo.getDeviceDetail().getProduct());
        speechHelper.speechDiscover(mDeviceInfo);
//        dataSubmitHelper.logDiscoverShow(getDeviceName(), mDeviceInfo);
    }

    private void passwordShow(Object[] params) {
        Log.i(TAG, "passwordShow");
        String ssid;
        if (params != null && params[0] != null && params[0] instanceof String) {
            ssid = (String) params[0];
        } else {
            ssid = NetworkUtils.getCurrentWifiSSID(mContext);
        }
        String tipText = "";
        if (params != null && params.length > 1 && params[1] != null && params[1] instanceof String) {
            tipText = (String) params[1];
        }
        mView.showInputPassword(ssid, tipText);
        mView.refreshWifiLoading(null);
    }


    private boolean checkData(DiscoverWifiDevice data) {
        Log.i(TAG, "checkData: ");
        if (data == null) {
            Log.i(TAG, "checkData: data == null");
            return false;
        }
        if (data.getPassword() == null) {
            Log.i(TAG, "checkData: ap password == null");
            return false;
        }
        if (data.getDeviceDetail() == null) {
            Log.i(TAG, "checkData: device detail == null");
            return false;
        }
        if (data.getWifiInfo() == null) {
            Log.i(TAG, "checkData: ap info == null");
            return false;
        }
        Log.i(TAG, "checkData: valid~");
        return true;
    }

    public boolean isCurrentWifiIsAp() {
        String ssid = NetworkUtils.getCurrentWifiSSID(mContext);
        if (ssid == null) {
            ssid = "";
        }
        if (mDeviceInfo != null && mDeviceInfo.getPattern() != null && mDeviceInfo.getDeviceDetail() != null) {
            Pattern patternRule = Pattern.compile(mDeviceInfo.getPattern());
            Matcher matcher = patternRule.matcher(ssid);
            if (matcher.find()) {
                String key = matcher.group(1);
                if (mDeviceInfo.getDeviceDetail().getKey().contains(key)) {
                    Log.i(TAG, "isCurrent24GHzWifi: current wifi is ap: " + ssid);
                    return true;
                }

            }
        }
        return false;
    }

    @Override
    public void discoverDialogClick(Boolean isConfirm) {
        Log.i(TAG, "discoverDialogClick: " + isConfirm);
        final String msg = isConfirm ? STEP_MSG_DISCOVER_CONFIRM : STEP_MSG_DISCOVER_EXIT;
        AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                stepManager.input(msg);
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void checkEtherNetClick() {
        AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                stepManager.input(STEP_MSG_ETHERNET_CONFIRM);
                return Unit.INSTANCE;
            }
        });
        Log.i(TAG, "checkEtherNetClick: ");
    }

    @Override
    public void passwordEntered(String ssid, String password) {
        wiFiHelper.saveWifiPassword(ssid, password);
        AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                try {
                    stepManager.output(STEP_MSG_INPUT_PASSWORD_SHOW, ssid, "正在连网中，请稍后...");
                    SwitchWifiInfo switchWifiInfo = new SwitchWifiInfo();
                    switchWifiInfo.wifiName = ssid;
                    Class<?> cls = Class.forName("android.net.wifi.ScanResult");    //通过类全限定名获取Person的Class类对象
                    ScanResult scanResult = (ScanResult) cls.newInstance();
                    scanResult.BSSID = NetworkUtils.getCurrentWifiBSSID(SmartHomeTvLib.getContext());
                    scanResult.SSID = ssid;
                    scanResult.frequency = NetworkUtils.getWifiManager(SmartHomeTvLib.getContext()).getConnectionInfo().getFrequency();
                    switchWifiInfo.raw = scanResult;
                    connectWifiByDhcp(switchWifiInfo, new WifiModule.WifiConnectCallback() {
                        @Override
                        public void onConnectFail(int code, String msg) {
                            stepManager.output(STEP_MSG_INPUT_PASSWORD_SHOW, switchWifiInfo.wifiName, SmartHomeTvLib.getContext().getString(R.string.apconfig_connect_network_failed));
                        }

                        @Override
                        public void onConnectOk() {
                            stepManager.input(STEP_MSG_INPUT_PASSWORD_OK);
                        }
                    });
//                    }

                } catch (Exception e) {
                    e.printStackTrace();
                }

                return Unit.INSTANCE;
            }
        });
        Log.i(TAG, "passwordEntered: " + password);
    }

    @Override
    public void onResume() {
        Log.i(TAG, "onResume: " + needResume);
        if (needResume) {
            needResume = false;
            AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
                @Override
                public Unit invoke() {
                    stepManager.reload();
                    return Unit.INSTANCE;
                }
            });
        }
    }

    @Override
    public void reConfig() {
        AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                stepManager.input(STEP_MSG_RECONFIG);
                return Unit.INSTANCE;
            }
        });
        Log.i(TAG, "reConfig: ");
//        dataSubmitHelper.logApConfigEndOnClick("重连");
    }

    public void show24GHzWifi() {
        final List<SwitchWifiInfo> showList = wiFiHelper.get24GHzWifiList(mDeviceInfo.getWifiInfo().SSID);
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mView.show24GWifiList(showList);
                return Unit.INSTANCE;
            }
        });
        Log.i(TAG, "show24GHzWifi: ");
    }

    public boolean canGetCurrentWifiPassword() {
        return wiFiHelper.canGetCurrentWifiPassword();
    }

    public boolean isCurrentWifiNeedPassword() {
        return wiFiHelper.isCurrentWifiNeedPassword();
    }


    public void startApConfig() {
        deviceMsg = null;
        mHasShowLoginAndBind = false;
        Log.i(TAG, "startApConfig: mideaToken" + mideaToken);
        ApconfigReportDataModel.reportStartApconfig(mDeviceInfo);
        AppData.getInstance().setApconfigDeviceInfo("");
        AppData.getInstance().setApconfigWifiDevice(null);
        String ssid = NetworkUtils.getCurrentWifiSSID(mContext);
        isApconfigFail = false;
        AiotLibSDK.getDefault().getDeviceImpl().startApconfig(ssid, wiFiHelper.getWifiPassword(ssid), mDeviceInfo.getWifiInfo().SSID, mideaToken, new IDevice.IApconfigCallBack() {

            @Override
            public void onApconfigProgress(int progress, int total, String extra) {
                Log.i(TAG, "onProgressUpdate: step: " + progress + " total: " + total + " extra:" + extra);
                if (isManual()) {
                    float progressPercent = ((float) progress) / total * 100;
                    showProgress((int) progressPercent);
                }
            }


            @Override
            public void onApconfigOk(String result) {
                Log.i(TAG, "onComplete: " + result);
                deviceMsg = JSONObject.parseObject(result);
                stepManager.input(STEP_MSG_CONFIG_SUCCESS);
                AppData.getInstance().setApconfigWifiDevice(mDeviceInfo);
                AppData.getInstance().setApconfigDeviceInfo(deviceMsg.toJSONString());
                ApconfigReportDataModel.reportApconfigResult(mDeviceInfo, "success", "");
                ApconfigReportDataModel.reportApconfigTime(mDeviceInfo, "success");
            }

            @Override
            public void onApconfigFail(int code, String erro) {
                Log.i(TAG, "onError: code: " + code + " msg: " + erro);
                stepManager.input(STEP_MSG_CONFIG_FAIL);
                stepManager.input(STEP_MSG_CONFIG_FAIL);
                if (!isApconfigFail) {
                    ApconfigReportDataModel.reportApconfigTime(mDeviceInfo, "fail");
                    ApconfigReportDataModel.reportApconfigResult(mDeviceInfo, "fail", erro);
                    isApconfigFail = true;
                }
            }

            @Override
            public void onApconfigConnectNetFail(String wifiInfo) {

            }
        });
    }

    private boolean isManual() {
        return ApConfigService.TYPE_MANUAL.equals(mConfigType);
    }

    public void speechConfiging() {
        speechHelper.speechConfiging();
    }

    public void onApConfigComplete() {
        forgetConfiguredAp();
        configSuccessIgnoreCurrentDevice();
    }

    public void onApConfigError() {
        forgetConfiguredAp();
    }

    public void showProgress(final int progress) {
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mView.showConfiging(progress);
                return Unit.INSTANCE;
            }
        });
    }

    public boolean isLogin() {
        boolean result = EmptyUtils.isNotEmpty(UserInfo.getInstance().getUserID());
        Log.i(TAG, "isLogin: " + result);
        return result;
    }

    public boolean isBindMidea() {
        if (mModel == null) {
            Log.e(TAG, "isBindMidea: model == null");
            return false;
        }
        mThirdAccount = mModel.getBindAccount();
        if (mThirdAccount == null || mThirdAccount.size() <= 0) {
            Log.e(TAG, "isBindMidea: third account is empty");
            return false;
        }
        for (ThridAccountHttpBean thridAccountHttpBean : mThirdAccount) {
            if (thridAccountHttpBean.account_type.toLowerCase().equals("midea") && thridAccountHttpBean.bind_status.equals(String.valueOf("1"))) {
                Log.i(TAG, "isBindMidea: true");
                return true;
            }
        }
        Log.i(TAG, "isBindMidea: false");
        return false;
    }

    public String getMideaToken() {
        String token = "";
        if (mModel != null) {
            token = mModel.getMideaToken();
            mideaToken = token;
        }
        Log.i(TAG, "getMideaToken: " + token);
        return token;
    }

    public boolean isBindMobile() {
        boolean result = mModel != null && mModel.hasBindMobilePhone();
        Log.i(TAG, "isBindMobile: " + result);
        return result;
    }

    public void goBindMobile() {
        needResume = true;
        disableDialogFocus();
        mModel.goBindMobile();
    }

    public boolean bindDevice(String location) {
        Log.i(TAG, "bindDevice: " + location);
        JSONObject reply = deviceMsg;
        if (reply == null) {
            Log.i(TAG, "bindDevice: get reply null");
            return false;
        }
        String device_id = getDeviceId();
        Map<String, Object> map = new HashMap<>();
        if (!TextUtils.isEmpty(location)) {
            map.put("location", location);
        }
        map.put("device_id", device_id);
        boolean result;
        if (isMideaDevice()) {
            result = bindMidea(reply, map);
        } else {
            result = bindSkyworth(reply, map);
        }
        Log.i(TAG, "bindDevice: success: " + result);
        setAllDone(result);
        return result;
    }

    private void setAllDone(boolean isAllDone) {
        this.isAllDone = isAllDone;
        if (isAllDone) {
            allDoneRemoveDeviceFromNotBindList();
            //绑定成功后刷新设备列表
            ISmartHomeModel.INSTANCE.getSmartDeviceList(SPCacheData.getFamilyId(mContext));
            ISmartHomeModel.INSTANCE.getFamilyList();
            ISmartHomeModel.INSTANCE.getSceneList();
        }
    }

    private boolean bindSkyworth(final JSONObject reply, Map<String, Object> map) {
        if (mModel == null) {
            return false;
        }
        String protocol_version = reply.getString("protocol_version");
        String module_chip = reply.getString("module_chip");
        String device_name = getDeviceName();
        String product_model = reply.getString("product_model");
        String mac_address = reply.getString("mac_address");
        if (EmptyUtils.isNotEmpty(mDeviceInfo)) {
            reply.put("device_name", mDeviceInfo.getDeviceDetail().getProduct());
            reply.put("brand_cn", mDeviceInfo.getDeviceDetail().getBrand());
        }
        map.put("protocol_version", protocol_version);
        map.put("module_chip", module_chip);
        map.put("device_name", device_name);
        map.put("product_type_id", Integer.parseInt(reply.getString("product_type_id")));
        map.put("product_brand_id", Integer.parseInt(reply.getString("product_brand_id")));
        map.put("product_model", product_model);
        map.put("mac_address", mac_address);
        map.put("family_id", AppData.getInstance().getCurrentFamilyId());
        return mModel.bindDevice(map, new ApConfigModelImpl.IApConfigModelResult() {
            @Override
            public void onResult(String code, final String msg) {
                Log.i(TAG, "bindDevice: code: " + code + " msg: " + msg);
                int codeInt = Integer.parseInt(code);
                if (codeInt != 0) {
                    showToast(msg);
                    ApconfigReportDataModel.reportDeviceBindResult(reply, "fail", APCONFIG_FAIL_REASON_ACCOUNT_BIND_DEVICE_FAIL);
                } else {
                    ApconfigReportDataModel.reportDeviceBindResult(reply, "success", null);
                }
            }
        });
    }

    private boolean bindMidea(JSONObject reply, Map<String, Object> map) {
        if (mModel == null) {
            return false;
        }
        String verification_code = reply.getString("verificationCode");
        map.put("verification_code", verification_code);
        return mModel.bindMidea(map, new ApConfigModelImpl.IApConfigModelResult() {
            @Override
            public void onResult(String code, final String msg) {
                Log.i(TAG, "bindDevice: code: " + code + " msg: " + msg);
                int codeInt = Integer.parseInt(code);
                if (codeInt != 0) {
                    showToast(msg);
                }
            }
        });
    }

    public int getBindStatus() {
        Log.i(TAG, "getBindStatus: ");
        if (isMideaDevice()) {
            Log.i(TAG, "getBindStatus: midea device");
            return 2;
        }
        if (mModel == null) {
            return -1;
        }
        JSONObject reply = deviceMsg;
        if (reply == null) {
            Log.i(TAG, "bindDevice: get reply null");
            return -1;
        }
        if (EmptyUtils.isNotEmpty(mDeviceInfo)) {
            reply.put("device_name", mDeviceInfo.getDeviceDetail().getProduct());
            reply.put("brand_cn", mDeviceInfo.getDeviceDetail().getBrand());
        }
        String device_id = getDeviceId();
        int product_type_id = Integer.parseInt(reply.getString("product_type_id"));
        int product_brand_id = Integer.parseInt(reply.getString("product_brand_id"));
        int result = mModel.getBindStatus(device_id, product_type_id, product_brand_id);
        Log.i(TAG, "getBindStatus: " + result);
        if (result == 1) {
            setAllDone(true);
        } else if (result == 3) {
            ApconfigReportDataModel.reportDeviceBindResult(reply, "fail", APCONFIG_FAIL_REASON_DEVICE_OTHER_BIND);
        }
        return result;
    }

    private void configSuccessIgnoreCurrentDevice() {
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                if (mDeviceInfo == null || mDeviceInfo.getWifiInfo() == null || mDeviceInfo.getWifiInfo().BSSID == null) {
                    LogUtil.androidLog("configSuccessIgnoreCurrentDevice : mDeviceInfo == null or wifiInfo == null or BSSID == null");
                    return Unit.INSTANCE;
                }
                DataCacheUtil.getInstance().putInt(mDeviceInfo.getWifiInfo().BSSID, 1);//成功之后，此设备不再弹出发现设备
                LogUtil.androidLog("设备配网成功，已绑定，不再显示此设备配网弹框：" + mDeviceInfo.getWifiInfo().BSSID + ":" + DataCacheUtil.getInstance().getInt(mDeviceInfo.getWifiInfo().BSSID));
                return Unit.INSTANCE;
            }
        });
    }

    public boolean forceUnBindDevice() {
        Log.i(TAG, "forceUnBindDevice: ");
        if (mModel == null) {
            return false;
        }
        JSONObject reply = deviceMsg;
        if (reply == null) {
            Log.i(TAG, "bindDevice: get reply null");
            return false;
        }
        boolean result = mModel.forceUnBindDevice(getDeviceId());
        Log.i(TAG, "forceUnBindDevice: " + result);
        return result;
    }


    private PollingChecker userChecker = null;
    private int notContainsCount = 0;
    private PollingChecker.IPollingResult checkListener = new PollingChecker.IPollingResult() {
        @Override
        public void onCheck(ComponentName componentName) {
            Log.i(TAG, "onCheck: " + componentName);
            if (componentName == null) {
                enableDialogFocus();
                return;
            }
            if (!USER_COMPONENT.contains(componentName)) {
                notContainsCount++;
            }
            if (notContainsCount >= 3) {
                enableDialogFocus();
                onResume();
                return;
            }
        }
    };

    private void disableDialogFocus() {
        Log.i(TAG, "disable DialogFocus");
        if (userChecker == null) {
            userChecker = new PollingChecker();
        }
        notContainsCount = 0;
        userChecker.start(mContext, checkListener, 3000L, 2000L);
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mView.disableDialogFocus();
                return Unit.INSTANCE;
            }
        });
    }

    private void enableDialogFocus() {
        Log.i(TAG, "enable DialogFocus");
        if (userChecker != null) {
            userChecker.stop();
        }
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mView.enableDialogFocus();
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void goLogin() {
        Log.i(TAG, "goLogin: ");
        needResume = true;
        disableDialogFocus();
        mModel.goToLogin();
    }

    public void goBindMideaAccount(final boolean needCycleCheck) {
        Log.i(TAG, "goBindMideaAccount:");
        if (mThirdAccount == null || mThirdAccount.size() <= 0) {
            Log.e(TAG, "goBindMideaAccount: mThridAccount is empty");
            return;
        }
        ThridAccountHttpBean mideaAccount = null;
        for (final ThridAccountHttpBean thridAccountHttpBean : mThirdAccount) {
            if (thridAccountHttpBean.account_type.toLowerCase().equals("midea")) {
                mideaAccount = thridAccountHttpBean;
                break;
            }
        }
        if (mideaAccount == null) {
            Log.e(TAG, "goBindMideaAccount: no midea account");
            return;
        }
        final ThridAccountHttpBean accountHttpBean = mideaAccount;
        requestId = UUID.randomUUID().toString();
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                dialog = new BindThridAccSysDialog();
                dialog.setParams(accountHttpBean).setOnBindResultListener(new BindThridAccSysDialog.OnBindResultListener() {
                    @Override
                    public void result(boolean result) {
                        Log.i(TAG, "goBindMideaAccount: result: " + result);
                        requestId = "";
                        stepManager.reload();
                    }
                }).showDialog(null);
                if (!needCycleCheck) {
                    dialog.stopPolling();
                }
                return Unit.INSTANCE;
            }
        });
        AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
            private String id = requestId;

            @Override
            public Unit invoke() {
                if (!needCycleCheck) {
                    while (isLoop()) {
                        Log.i(TAG, "goBindMideaAccount: check token Thread: " + Thread.currentThread());
                        try {
                            Thread.sleep(2000);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }
                    if (dialog != null) {
                        dialog.dismiss();
                    }
                }
                return Unit.INSTANCE;
            }

            private boolean isLoop() {
                return TextUtils.isEmpty(getMideaToken()) && !TextUtils.isEmpty(id) && id.equals(requestId);
            }
        });
    }

    @Override
    public void recordIgnoreCurrentDevice() {
        if (mDeviceInfo == null || mDeviceInfo.getWifiInfo() == null) {
            Log.i(TAG, "recordIgnoreCurrentDevice: mDeviceInfo == null");
            return;
        }
        ApConfigEvent event = new ApConfigEvent();
        event.setTypeString(mDeviceInfo.getWifiInfo().BSSID);
        EventBus.getDefault().post(event);
        Log.i(TAG, "recordIgnoreCurrentDevice: ");
    }


    private Long backKeyPressTime = 0L;

    @Override
    public boolean dispatchKeyEvent(KeyEvent keyEvent) {
        Log.i(TAG, "dispatchKeyEvent: " + keyEvent.getKeyCode());
        if (stepManager.input(String.valueOf(keyEvent.getAction()), keyEvent.getKeyCode())) {
            return true;
        }
        String currentStep = stepManager.getCurrentStepTag();
        if (keyEvent.getAction() == KeyEvent.ACTION_DOWN && BACK_KEY.contains(keyEvent.getKeyCode()) && TWO_STEP_EXIT_CONFIRM_STEP_TAG.contains(currentStep)) {
            if (SystemClock.uptimeMillis() - backKeyPressTime > 3000L) {
                backKeyPressTime = SystemClock.uptimeMillis();
                switch (stepManager.getCurrentStepTag()) {
                    case ManualStepConfiging.STEP_TAG:
                        showToast(mContext.getString(R.string.apconfig_exit_config_toast));
                        break;
                    case ManualStepBindDevice.STEP_TAG:
                        showToast(mContext.getString(R.string.apconfig_exit_bind_toast));
                        break;
                    case ManualStepConfigFailed.STEP_TAG:
                        showToast(mContext.getString(R.string.apconfig_exit_config_toast));
                        break;
                }
                return true;
            }
        }

        if (keyEvent.getAction() == KeyEvent.ACTION_DOWN && BACK_KEY.contains(keyEvent.getKeyCode()) && AUTO_CONFIG_SKIP_BACK_KEY.contains(currentStep)) {
            Log.i(TAG, "dispatchKeyEvent: AUTO_CONFIG_SKIP_BACK_KEY");
            return true;
        }
        return false;
    }

    public void forgetConfiguredAp() {
        if (mDeviceInfo == null || mDeviceInfo.getWifiInfo() == null || TextUtils.isEmpty(mDeviceInfo.getWifiInfo().SSID)) {
            Log.i(TAG, "forgetConfiguredAp: == null");
            return;
        }
        String ssid = NetworkUtils.forgetSSID(mContext, mDeviceInfo.getWifiInfo().SSID);
        Log.i(TAG, "forgetConfiguredAp: " + ssid);
    }

    public void setDevicePosClick(String pos) {
        Log.i(TAG, "setDevicePosClick: " + pos);
        stepManager.input(STEP_MSG_SET_LOCATION_OK, pos);
    }

    public void showSetDevicePos() {
        AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                final List<DevicePosBean> posBeans = mModel.getDeviceLocations();
                AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        if (deviceMsg != null) {
                            String deviceName = getDeviceName();
                            Log.i(TAG, "showSetDevicePos: " + deviceName);
                            mView.showSetDevicePos(deviceName, posBeans);
                        } else {
                            Log.i(TAG, "showSetDevicePos: device name is null");
                        }
                        return Unit.INSTANCE;
                    }
                });
                //绑定设备
                int bindStatus = getBindStatus();
                if (bindStatus == 2 || bindStatus == 1) {//未绑定或绑定在当前账号
                    bindDevice("默认房间");
                }
                return Unit.INSTANCE;
            }
        });
    }

    public void updateDevicePos(final String pos) {
        if (mModel == null) {
            return;
        }
        AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                String deviceId = getDeviceId();
                Log.i(TAG, "updateDevicePos: id: " + deviceId + " pos: " + pos);
                mModel.setDeviceLocation(deviceId, pos);
                return Unit.INSTANCE;
            }
        });
    }

    public boolean isMideaDevice() {
        if (mDeviceInfo == null || mDeviceInfo.getWifiInfo() == null || TextUtils.isEmpty(mDeviceInfo.getWifiInfo().SSID)) {
            Log.i(TAG, "isMideaDevice: false");
            return false;
        }
        return mDeviceInfo.getWifiInfo().SSID.toLowerCase().startsWith("midea_");
    }

    @Override
    public void startCurrentDeviceDetail() {
//        String deviceId = getDeviceId();
//        if (TextUtils.isEmpty(deviceId)) {
//            Log.e(TAG, "startCurrentDeviceDetail: deviceid is empty");
//            return;
//        }
//        Map<String, String> params = new HashMap<>();
//        params.put(Constants.KEY_SHOW_TYPE, HomePanelUtil.PAGE_TYPE_DEVICE);
//        params.put(Constants.KEY_SELECT_ID, deviceId);
//        DialogLauncherUtil.showHome(params);
    }

    @Override
    public void connectWifiClick(final SwitchWifiInfo wifiInfo) {
        Log.i(TAG, "connectWifi: " + wifiInfo.wifiName);
        AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                stepManager.input(STEP_MSG_24GWIFI_CLICK, wifiInfo);
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void connectWifiByDhcp(final SwitchWifiInfo switchWifiInfo, WifiModule.WifiConnectCallback callback) {
        wiFiHelper.connectWifiByDhcp(switchWifiInfo, callback);
    }

    @Override
    public void connectWifiByDhcp(final SwitchWifiInfo switchWifiInfo) {
        wiFiHelper.connectWifiByDhcp(switchWifiInfo, null);
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mView.show24GWifiList();
                mView.refreshWifiLoading(switchWifiInfo);
                return Unit.INSTANCE;
            }
        });
    }

    private String getDeviceName() {
        String device_name = "";
        if (mDeviceInfo != null && mDeviceInfo.getDeviceDetail() != null) {
            device_name = mDeviceInfo.getDeviceDetail().getProduct();
        } else if (deviceMsg != null) {
            device_name = deviceMsg.getString("device_name");
        }
        return device_name;
    }

    private String getDeviceId() {
        if (deviceMsg != null) {
            return deviceMsg.getString("device_id");
        }
        return null;
    }

    @Override
    public void showToast(final String text) {
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mView.showToast(text);
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void showNotLogin(final String tag) {
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mView.showNotLogin(tag);
                return Unit.INSTANCE;
            }
        });
    }

    public void hideNotLogin() {
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mView.hideNotLogin();
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void loginClick(final String tag) {
        if (TextUtils.isEmpty(tag)) {
            return;
        }
        hideNotLogin();
        AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                stepManager.input(tag);
                return Unit.INSTANCE;
            }
        });
    }

    public void unbindCurrentDevice() {
        Log.i(TAG, "unbindCurrentDevice");
        mModel.unBindDevice(getDeviceId());
    }

    @Override
    public void onBindConfirmClick(boolean isBind) {
        Log.i(TAG, "onBindConfirmClick: " + isBind);
        String msg = "";
        String result = "";
        if (isBind) {
            msg = STEP_MSG_INPUT_CONFIRM_BIND_OK;
            result = "确定";
        } else {
            msg = STEP_MSG_INPUT_CONFIRM_BIND_CANCEL;
            result = "取消";
        }
        stepManager.input(msg);
//        dataSubmitHelper.logBindingTipsOnClick(result, getDeviceBrand(), getProduct());
    }

    @Override
    public void onLoginAndBindClick() {
        stepManager.input(STEP_MSG_INPUT_GO_LOGIN);
//        dataSubmitHelper.logLoginBindOnClick("点击进入");
    }

    public void showDialog() {
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mView.showDialog();
                return Unit.INSTANCE;
            }
        });
    }

    public void setConfirmHelperFlag(int status) {
        confirmBindHelper.waitForConfirm(deviceMsg.toString(), mDeviceInfo, status);
    }

    public void clearConfirmHelperFlag() {
        confirmBindHelper.onConfirmed(getDeviceId());
    }

    @Override
    public void closeDialog() {
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mView.closeDialog();
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void showDownloadPhoneApp(String erro) {
        if (EmptyUtils.isNotEmpty(mDeviceInfo)) {
            if (mDeviceInfo.getWifiInfo().SSID.contains("midea")) {
                if (EmptyUtils.isNotEmpty(erro)) {
                    XToast.showToast(SmartHomeTvLib.getContext(), "此设备已被他人绑定，您可以通过【美的美居】APP 添加此设备");
                } else {
                    XToast.showToast(SmartHomeTvLib.getContext(), "抱歉配网失败，您可以通过【美的美居】APP 添加此设备");
                }
            } else {
                Map<String, String> params = new HashMap<>();
                if (EmptyUtils.isNotEmpty(erro)) {
                    params.put(DownloadRrCodeDialog.SHOW_SUB_TITLE, erro);
                }
                FunctionGoToModel.INSTANCE.goToAppDownloadGuide(params);
            }
        } else {
            Map<String, String> params = new HashMap<>();
            if (EmptyUtils.isNotEmpty(erro)) {
                params.put(DownloadRrCodeDialog.SHOW_SUB_TITLE, erro);
            }
            FunctionGoToModel.INSTANCE.goToAppDownloadGuide(params);
        }
    }

    private void showBindConfirm() {
        mView.showBindConfirm(getProduct(), getDeviceImage());
    }

    public void hideBindConfirm() {
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mView.hideBindConfirm();
                return Unit.INSTANCE;
            }
        });
    }

    private boolean mHasShowLoginAndBind = false;

    public boolean hasShowLoginAndBind() {
        return mHasShowLoginAndBind;
    }

    private void showLoginAndBind() {
        mHasShowLoginAndBind = true;
        mView.showLoginAndBind(mContext.getString(R.string.apconfig_discover_device_name, getProduct()), getDeviceImage());
    }

    public void hideLoginAndBind() {
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mView.hideLoginAndBind();
                return Unit.INSTANCE;
            }
        });

    }

    private void showConfigFail() {
        speechHelper.speechConfigFailed(getDeviceName());
        mView.showConfigFailed(getDeviceImage());
    }

    private String getProduct() {
        String type = "";
        if (mDeviceInfo != null && mDeviceInfo.getDeviceDetail() != null) {
            type = mDeviceInfo.getDeviceDetail().getProduct();
        } else if (deviceMsg != null) {
            type = deviceMsg.getString("product_type_id");
        }
        return type;
    }

    private String getDeviceImage() {
        String url = "";
        if (mDeviceInfo != null && mDeviceInfo.getDeviceDetail() != null) {
            url = mDeviceInfo.getDeviceDetail().getImg_url();
        }
        return url;
    }

    private String getDeviceBrand() {
        String brand = "";
        if (mDeviceInfo != null && mDeviceInfo.getDeviceDetail() != null) {
            brand = mDeviceInfo.getDeviceDetail().getBrand();
        } else if (deviceMsg != null) {
            brand = deviceMsg.getString("product_brand_id");
        }
        return brand;
    }

    /**
     * 配网绑定成功，语音提示，显示智能化率
     */
    private void configFinish() {
        Log.i(TAG, "configFinish");
        speechHelper.speechConfigSuccess(getDeviceName());
        mView.hideSetDevicePos();
        String id = getDeviceId();
        mView.showSmartRate(id == null ? "" : id);
    }

    public void logAutoBindEnd(boolean isSuccess) {
        String result = isSuccess ? "绑定成功" : "绑定失败";
//        dataSubmitHelper.logAutoBindEnd(result);
    }

    public void recordNotBindAndExit(String reason) {
        recordNotBind(reason);
        logAutoBindEnd(false);
        exitConfig();
    }

    /**
     * 配网绑定成功，设备从未配网列表去掉。
     */
    private void allDoneRemoveDeviceFromNotBindList() {
        Log.i(TAG, "allDoneRemoveDeviceFromNotBindList");
        DiscoverNetworkDevice discoverNetworkDevice = null;
        if (deviceMsg != null) {
            discoverNetworkDevice = JSONObject.parseObject(deviceMsg.toString(), DiscoverNetworkDevice.class);
            if (EmptyUtils.isNotEmpty(mDeviceInfo)) {
                discoverNetworkDevice.device_name = mDeviceInfo.getDeviceDetail().getBrand() + mDeviceInfo.getDeviceDetail().getProduct();
                discoverNetworkDevice.product_type_logo = mDeviceInfo.getDeviceDetail().getImg_url();
            }
        } else {
            ///////////////////////
            /// AIOT 创导者加的! ///
            ///////////////////////
            discoverNetworkDevice = new DiscoverNetworkDevice();
            discoverNetworkDevice.device_id = mDeviceInfo.getWifiInfo().BSSID;
            discoverNetworkDevice.device_name = mDeviceInfo.getDeviceDetail().getBrand() + mDeviceInfo.getDeviceDetail().getProduct();
            discoverNetworkDevice.product_type_logo = mDeviceInfo.getDeviceDetail().getImg_url();
        }
        discoverNetworkDevice.deviceInfo = mDeviceInfo;
        ISmartDeviceDataModel.INSTANCE.removeDiscoverDevice(discoverNetworkDevice);
    }

    /**
     * 标记未绑定设备
     *
     * @param reason
     */
    public void recordNotBind(String reason) {
        Log.i(TAG, "recordNotBind: " + reason);
        DiscoverNetworkDevice discoverNetworkDevice = null;
        if (deviceMsg != null) {
            discoverNetworkDevice = JSONObject.parseObject(deviceMsg.toString(), DiscoverNetworkDevice.class);
            if (EmptyUtils.isNotEmpty(mDeviceInfo)) {
                discoverNetworkDevice.device_name = mDeviceInfo.getDeviceDetail().getBrand() + mDeviceInfo.getDeviceDetail().getProduct();
                discoverNetworkDevice.product_type_logo = mDeviceInfo.getDeviceDetail().getImg_url();
            }
        } else {
            ///////////////////////
            /// AIOT 创导者加的! ///
            ///////////////////////
            discoverNetworkDevice = new DiscoverNetworkDevice();
            discoverNetworkDevice.device_id = mDeviceInfo.getWifiInfo().BSSID;
            discoverNetworkDevice.device_name = mDeviceInfo.getDeviceDetail().getBrand() + mDeviceInfo.getDeviceDetail().getProduct();
            discoverNetworkDevice.product_type_logo = mDeviceInfo.getDeviceDetail().getImg_url();
            discoverNetworkDevice.bind_status = DiscoverNearDevicesView.DEVICE_STATUS_NOT_APCONFIG_NETWORK;
        }
        discoverNetworkDevice.deviceInfo = mDeviceInfo;
        ISmartDeviceDataModel.INSTANCE.addDiscoverDevice(discoverNetworkDevice);
        DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.DEVICE_LIST, "");
        ApconfigReportDataModel.reportDeviceBindResult(deviceMsg, "fail", reason);
    }

    public void logLoginAndBindAfterScreenSaver() {
//        dataSubmitHelper.logLoginAndBindAfterScreenSaver(getDeviceBrand(), getProduct());
    }

    public void logBindAfterScreenSaver() {
//        dataSubmitHelper.logBindAfterScreenSaver(getDeviceBrand(), getProduct());
    }

    /**
     * 对话框关闭回调
     * <p>
     * 如果当前是手动配网流程，若未完成配网，手动关闭了弹框，则记录未绑定
     */
    @Override
    public void onDialogDismiss() {
        Log.i(TAG, "onDialogDismiss: is manual :" + isManual());
        if (isManual()) {
            String result = "";
            if (!isAllDone) {
                String failReason;
                switch (stepManager.getCurrentStepTag()) {
                    case ManualStepConfiging.STEP_TAG:
                        failReason = APCONFIG_FAIL_REASON_APCONFIG_FAIL;
                        break;
                    case ManualStepBindDevice.STEP_TAG:
                        failReason = APCONFIG_FAIL_REASON_ACCOUNT_BIND_DEVICE_FAIL;
                        break;
                    default:
                        failReason = APCONFIG_FAIL_REASON_MANUAL_EXIT;
                }
                recordNotBind(failReason);
                if (!isExiting) {
                    exitConfig();
                }
                result = "失败";
            } else {
                result = "成功";
            }
//            dataSubmitHelper.logManualBindingEnd(result);
        } else {
            if (!isAllDone && !isExiting) {
                String failReason = APCONFIG_FAIL_REASON_MANUAL_EXIT;
                switch (stepManager.getCurrentStepTag()) {
                    case AutoStepBindDevice.STEP_TAG:
                        failReason = APCONFIG_FAIL_REASON_NOT_LOGIN;
                        break;
                }
//                dataSubmitHelper.logAutoBindEnd("绑定失败");
                recordNotBindAndExit(failReason);
            }
        }
    }

    @Override
    public void onDialogAutoDismiss(int reason) {
        Log.i(TAG, "onDialogAutoDismiss: " + reason);
        stepManager.input(STEP_MSG_INPUT_CONFIRM_BIND_CANCEL);
        switch (reason) {
            case AddDeviceResultView.TYPE_DISCOVER_NOT_LOGIN:
//                dataSubmitHelper.logLoginBindOnClick("1min无操作自动关闭");
                break;
            case AddDeviceResultView.TYPE_DISCOVER:
//                dataSubmitHelper.logBindingTipsOnClick("1min无操作自动关闭", getDeviceBrand(), getProduct());
                break;
        }
    }

    /**
     * UI或流程需要退出配网流程
     */
    @Override
    public void exitConfig() {
        Log.i(TAG, "exitConfig");
        isExiting = true;
        recordIgnoreCurrentDevice();
        enableDialogFocus();
        closeDialog();
        forgetConfiguredAp();
        stopService();
        if (EmptyUtils.isNotEmpty(mDeviceInfo)) {
            stopApconfig(mDeviceInfo.getWifiInfo().SSID);
        }
    }

    /**
     * 停止配网
     */
    private void stopApconfig(String deviceSSID) {
        try {
            AiotLibSDK.getDefault().getDeviceImpl().stopApconfig(deviceSSID);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 通知系统停止配网service
     */
    @Override
    public void stopService() {
        Log.i(TAG, "stopService");
        Intent intent = new Intent(SmartHomeTvLib.getContext(), ApConfigService.class);
        mContext.stopService(intent);
    }

    /**
     * service退出，做清理工作
     */
    @Override
    public void destroy() {
        jumpToTag = null;
        isAllDone = false;
        mHasShowLoginAndBind = false;
        if (confirmBindHelper != null) {
            confirmBindHelper.destroy();
        }
        mContext.unregisterReceiver(mReceiver);
        Log.e(TAG, "onDestroy()");
    }
}
