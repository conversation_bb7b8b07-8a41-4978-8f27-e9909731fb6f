package com.skyworth.smarthome.home.base;

import android.content.Context;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.widget.FrameLayout;

import com.skyworth.smarthome.common.util.CommonUtil;
import com.skyworth.util.Util;
import com.smarthome.common.utils.EmptyUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/21
 */
public abstract class BaseSmartListView<T> extends FrameLayout {

    protected IBoundaryCallback mBoundaryCallback;
    protected List<T> mDatas = new ArrayList<>();
    protected List<BaseSmartItemView<T>> mViews = new ArrayList<>();
    protected int mLastFocusPos = -1;//焦点记忆，记录上一次落焦的位置
    protected int mAllSize = 0;
    protected int COLUMN = 4;
    protected int ITEM_W = Util.Div(410 + 10);
    protected int ITEM_H = Util.Div(246 + 10);
    protected int ITEM_SPACE = Util.Div(30);
    protected int CONTENT_TOP_MARGIN = 0;

    public interface IBoundaryCallback {
        boolean onTop(View leaveView);

        boolean onLeft(View leaveView);

        boolean onRight(View leaveView);

        boolean onDown(View leaveView);

        boolean onBackKey(View leaveView);
    }


    public BaseSmartListView(Context context) {
        super(context);
    }

    public void setBoundaryCallback(IBoundaryCallback boundaryCallback) {
        mBoundaryCallback = boundaryCallback;
    }

    public void refreshUI(List<T> datas) {
        removeAllViews();
        addTitle();
        mAllSize = 0;
        mDatas.clear();
        if (EmptyUtils.isNotEmpty(datas)) {
            mDatas.addAll(datas);
            int size = datas.size();
            mAllSize = size;
            for (int i = 0; i < size; i++) {
                createItemView(datas.get(i), i);
            }
        }
        mAllSize += 1;
        createAddItemView(mAllSize - 1);
        if (mLastFocusPos >= 0) {
            resetFocus();
        }
    }

    public void resetFocus() {

    }

    public void refreshItem(T updateData) {

    }

    public void addTitle() {

    }

    public void createItemView(T info, final int pos) {
        BaseSmartItemView<T> itemView = getItemView();
        itemView.refreshUI(info,pos);
        itemView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (!CommonUtil.isNetConnected(getContext())) {
                    return;
                }
                onItemClick(pos);
            }
        });
        itemView.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View view, boolean b) {
                onFocus(view, b, pos);
            }
        });
        mViews.add(itemView);
        create(itemView, pos);
    }

    public void createAddItemView(final int pos) {
        BaseSmartItemView<String> itemView = getAddItemView();
        itemView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (!CommonUtil.isNetConnected(getContext())) {
                    return;
                }
                onAddItemClick();
            }
        });
        itemView.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View view, boolean b) {
                onAddItemFocus(view, b, pos);
            }
        });
        create(itemView, pos);
    }

    private void create(final BaseSmartItemView itemView, final int pos) {
        itemView.setOnKeyListener(new OnKeyListener() {
            @Override
            public boolean onKey(View view, int keyCode, KeyEvent keyEvent) {
                if (keyEvent.getAction() == KeyEvent.ACTION_DOWN) {
                    return onKeyDown(itemView, keyCode, pos);
                }
                return false;
            }
        });
        itemView.setOnTouchListener(new OnTouchListener() {
            @Override
            public boolean onTouch(View view, MotionEvent motionEvent) {
                if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {
                    if (view.isFocusable() && view.isFocusableInTouchMode()) {
                        view.requestFocus();
                    }
                } else if (motionEvent.getAction() == MotionEvent.ACTION_UP) {
                    return view.callOnClick();
                }
                return false;
            }
        });
        LayoutParams params = new LayoutParams(ITEM_W, ITEM_H);
        params.leftMargin = (ITEM_W + ITEM_SPACE) * (getCurrentColumn(pos) - 1);
        params.topMargin = (ITEM_H + ITEM_SPACE) * (getCurrentRow(pos) - 1) + CONTENT_TOP_MARGIN;
        addView(itemView, params);
    }

    protected abstract BaseSmartItemView<T> getItemView();

    protected abstract BaseSmartItemView<String> getAddItemView();

    public void onItemClick(int position) {

    }

    public void onAddItemClick() {

    }

    public void onFocus(View view, boolean b, int position) {
        if (b) {
            mLastFocusPos = position;
        }
    }

    public void onAddItemFocus(View view, boolean b, int position) {
        if (b) {
            mLastFocusPos = position;
        }
    }

    protected int getRowCount() {
        return mAllSize / COLUMN + 1;
    }

    protected int getCurrentRow(int pos) {
        return pos / COLUMN + 1;
    }

    protected int getCurrentColumn(int pos) {
        return (pos + 1) - COLUMN * (getCurrentRow(pos) - 1);
    }

    protected boolean onKeyDown(View view, int keyCode, int pos) {
        switch (keyCode) {
            case KeyEvent.KEYCODE_DPAD_UP:
                if (getCurrentRow(pos) == 1) {
                    mLastFocusPos = -1;
                    return mBoundaryCallback.onTop(view);
                }
                break;
            case KeyEvent.KEYCODE_DPAD_DOWN:
                if (getCurrentRow(pos) == getRowCount()) {
                    mLastFocusPos = -1;
                    return mBoundaryCallback.onDown(view);
                }
                break;
            case KeyEvent.KEYCODE_DPAD_LEFT:
                if (getCurrentColumn(pos) == 1) {
                    return mBoundaryCallback.onLeft(view);
                }
                break;
            case KeyEvent.KEYCODE_DPAD_RIGHT:
                if (getCurrentColumn(pos) == COLUMN || pos == (mAllSize - 1)) {
                    return mBoundaryCallback.onRight(view);
                }
                break;
//            case KeyEvent.KEYCODE_BACK:
//                return mBoundaryCallback.onBackKey(view);
            default:
                break;
        }
        return false;
    }

    public void destroy() {
        try {
            if (getChildCount() > 0) {
                for (int i = 0; i < getChildCount(); i++) {
                    View child = getChildAt(i);
                    if (child instanceof BaseSmartItemView) {
                        ((BaseSmartItemView) child).onDestroy();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
