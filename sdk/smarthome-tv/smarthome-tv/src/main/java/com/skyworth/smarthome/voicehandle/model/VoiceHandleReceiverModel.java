package com.skyworth.smarthome.voicehandle.model;

import android.content.Context;
import android.content.IntentFilter;
import android.os.Build;

import com.skyworth.smarthome.voicehandle.VoiceHandleReceiver;


/**
 * @ClassName: VoiceHandleModel
 * @Author: AwenZeng
 * @CreateDate: 2020/7/9 10:40
 * @Description:
 */
public class VoiceHandleReceiverModel {
    private VoiceHandleReceiver mVoiceHandleReceiver;
    private Context mContext;

    public VoiceHandleReceiverModel(Context context) {
        mContext = context;
        mVoiceHandleReceiver = new VoiceHandleReceiver();
    }

    public void registerVoiceHandleReceiver() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction(VoiceHandleReceiver.ACTION_SMARTHOME_VOICEHANDLER);
            intentFilter.addAction(VoiceHandleReceiver.ACTION_SRTNJ_VOICE_OUTCMD);
            intentFilter.addAction(VoiceHandleReceiver.ACTION_SMARTHOME_HELPHANDLER);
            mContext.registerReceiver(mVoiceHandleReceiver, intentFilter);
        }

    }

    public void unRegisterVoiceHandleReceiver() {
        try {
            mContext.unregisterReceiver(mVoiceHandleReceiver);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
