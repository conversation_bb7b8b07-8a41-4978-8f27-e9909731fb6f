package com.skyworth.smarthome.devices.apconfig.model;

import com.skyworth.smarthome.common.base.IModel;
import com.skyworth.smarthome.common.bean.DevicePosBean;
import com.skyworth.smarthome.common.bean.ThridAccountHttpBean;

import java.util.List;
import java.util.Map;

public interface IApConfigModel extends IModel {
    boolean bindDevice(Map<String, Object> map, ApConfigModelImpl.IApConfigModelResult listener);

    int getBindStatus(String device_id, int product_type_id, int product_brand_id);

    boolean forceUnBindDevice(String deviceId);

    boolean unBindDevice(String deviceId);

    void goToLogin();

    boolean hasBindMobilePhone();

    void goBindMobile();

    List<DevicePosBean> getDeviceLocations();

    boolean setDeviceLocation(String deviceId, String newLocation);

    List<ThridAccountHttpBean> getBindAccount();

    String getMideaToken();

    boolean bindMidea(Map<String, Object> map, ApConfigModelImpl.IApConfigModelResult listener);
}
