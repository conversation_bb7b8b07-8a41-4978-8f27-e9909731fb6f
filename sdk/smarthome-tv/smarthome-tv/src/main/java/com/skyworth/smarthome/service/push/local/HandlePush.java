package com.skyworth.smarthome.service.push.local;


import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.util.ThreadManager;
import com.smarthome.common.utils.EmptyUtils;

import java.util.ArrayList;
import java.util.List;


public class HandlePush implements IHandlerPush {
    private List<IPushListener> listeners = new ArrayList<>();

    @Override
    public synchronized void onArrive(final AppConstants.SSE_PUSH event, final String data) {
        ThreadManager.getInstance().uiThread(new Runnable() {
            @Override
            public void run() {
                for (IPushListener listener : listeners) {
                    listener.onArrive(event, data);
                }
            }
        });
    }

    @Override
    public void regReceiver(IPushListener listener) {
        if (EmptyUtils.isNotEmpty(listener)&&!listeners.contains(listener))
            listeners.add(listener);
    }

    @Override
    public void unRegReceiver(IPushListener listener) {
        if (listeners.contains(listener))
            listeners.remove(listener);
    }
}
