package com.skyworth.smarthome.devices.apconfig.model;

import android.content.Context;

import com.coocaa.app.core.http.HttpServiceManager;
import com.skyworth.smarthome.account.IAppAccountManager;
import com.skyworth.smarthome.common.bean.DevicePosBean;
import com.skyworth.smarthome.common.bean.ThridAccountHttpBean;
import com.skyworth.smarthome.common.http.SmartDevicesHttpService;
import com.smarthome.common.model.SmartBaseData;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

public class ApConfigModelImpl implements IApConfigModel {
    @Override
    public void create(Context context) {

    }

    public interface IApConfigModelResult {
        void onResult(String code, String msg);
    }

    @Override
    public boolean bindDevice(Map<String, Object> map, IApConfigModelResult listener) {
        try {
            Call<SmartBaseData> call = SmartDevicesHttpService.SERVICE.forceBindDevice(map);
            SmartBaseData<String> data = HttpServiceManager.Companion.call(call);
            if (data != null) {
                int code = Integer.valueOf(data.code);
                if (listener != null) {
                    listener.onResult(data.code, data.msg);
                }
                if (code == 0) {
                    return true;
                }
                return false;
            }
        } catch (Exception e) {
        }
        return false;
    }

    @Override
    public int getBindStatus(String device_id, int product_type_id, int product_brand_id) {
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("device_id", device_id);
            map.put("product_type_id", product_type_id);
            map.put("product_brand_id", product_brand_id);
            List<Map<String, Object>> list = new ArrayList<>();
            list.add(map);
            Call<SmartBaseData<List<Map<String, String>>>> call = SmartDevicesHttpService.SERVICE.getBindStatus(list);
            SmartBaseData<List<Map<String, String>>> data = HttpServiceManager.Companion.call(call);
            if (data != null && data.data != null && data.data.size() > 0) {
                Map<String, String> mData = data.data.get(0);
                return Integer.valueOf(mData.get("bind_status"));  //设备绑定状态，1表示自己已绑定，2表示未绑定，3表示他人已绑定
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return -1;
    }

    @Override
    public boolean forceUnBindDevice(String deviceId) {
        try {
            Call<SmartBaseData> call = SmartDevicesHttpService.SERVICE.forceUnBindDevice(deviceId);
            SmartBaseData<String> data = HttpServiceManager.Companion.call(call);
            if (data != null) {
                int code = Integer.valueOf(data.code);
                if (code == 0) {
                    return true;
                }
                return false;
            }
        } catch (Exception e) {
        }
        return false;
    }

    @Override
    public boolean unBindDevice(String deviceId) {
        try {
            Call<SmartBaseData> call = SmartDevicesHttpService.SERVICE.unBindDevice(deviceId);
            SmartBaseData<String> data = HttpServiceManager.Companion.call(call);
            if (data != null) {
                int code = Integer.valueOf(data.code);
                if (code == 0) {
                    return true;
                }
                return false;
            }
        } catch (Exception e) {
        }
        return false;
    }

    @Override
    public void goToLogin() {
        IAppAccountManager.INSTANCE.hasLogin(true);
    }

    @Override
    public boolean hasBindMobilePhone() {
        return IAppAccountManager.INSTANCE.isBindMobile();
    }

    @Override
    public void goBindMobile() {
        IAppAccountManager.INSTANCE.gotoBindMobile();
    }

    @Override
    public List<DevicePosBean> getDeviceLocations() {
        Call<SmartBaseData<List<DevicePosBean>>> call = SmartDevicesHttpService.SERVICE.getDeviceLocations();
        SmartBaseData<List<DevicePosBean>> result = HttpServiceManager.Companion.call(call);
        return result == null ? null : result.data;
    }

    @Override
    public boolean setDeviceLocation(String deviceId, String newLocation) {
        Call<SmartBaseData> call = SmartDevicesHttpService.SERVICE.setDeviceLocation(deviceId,newLocation);
        SmartBaseData result = HttpServiceManager.Companion.call(call);
        return result != null && result.code.equals("0");
    }

    @Override
    public List<ThridAccountHttpBean> getBindAccount() {
        Call<SmartBaseData<List<ThridAccountHttpBean>>> call = SmartDevicesHttpService.SERVICE.getThirdAccountList("MIDEA");
        SmartBaseData<List<ThridAccountHttpBean>> result = HttpServiceManager.Companion.call(call);
        return result == null ? null : result.data;
    }

    @Override
    public String getMideaToken() {
        Call<SmartBaseData> call = SmartDevicesHttpService.SERVICE.getMideaToken();
        SmartBaseData result = HttpServiceManager.Companion.call(call);
        String token = "";
        if (result != null && result.data != null) {
            token = result.data.toString();
        }
        return token;
    }

    @Override
    public boolean bindMidea(Map<String, Object> map, IApConfigModelResult listener) {
        Call<SmartBaseData> call = SmartDevicesHttpService.SERVICE.bindMideaDevice(map);
        SmartBaseData result = HttpServiceManager.Companion.call(call);
        if (result != null && result.code != null && listener != null) {
            listener.onResult(result.code, result.msg);
        }
        return result != null && result.code != null && result.code.equals("0");
    }

    @Override
    public void destroy() {

    }
}
