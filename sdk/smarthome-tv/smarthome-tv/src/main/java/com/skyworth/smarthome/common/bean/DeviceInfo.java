package com.skyworth.smarthome.common.bean;

import com.swaiot.aiotlib.common.entity.DeviceBean;
import com.swaiot.aiotlib.common.entity.DiscoverNetworkDevice;

import java.io.Serializable;

/**
 * @ClassName: DeviceInfo
 * @Author: Awen<PERSON>eng
 * @CreateDate: 2020/6/17 14:57
 * @Description:
 */
public class DeviceInfo extends DeviceBean implements Serializable {
     /**
      * 自定义字段（非网络拉取）
      */
     public boolean is_infrared;//是否是红外设备
     public boolean is_unBind;//是否未绑定
     public boolean isNotConnectedNetwork;//是否没有配网
     public DiscoverNetworkDevice discoverNetworkDevice;//未配网详情

     public void copy(DeviceBean deviceBean){
          this.device_id = deviceBean.device_id;
          this.gateway_id = deviceBean.gateway_id;
          this.familyId = deviceBean.familyId;
          this.device_type_id = deviceBean.product_type_id;
          this.device_icon = deviceBean.device_icon;
          this.device_icon_for_ir = deviceBean.device_icon_for_ir;
          this.product_type_id = deviceBean.product_type_id;
          this.product_type = deviceBean.product_type;
          this.product_brand = deviceBean.product_brand;
          this.product_brand_id = deviceBean.product_brand_id;
          this.product_model = deviceBean.product_model;
          this.module_chip = deviceBean.module_chip;
          this.device_name = deviceBean.device_name;
          this.online_status = deviceBean.online_status;
          this.device_position = deviceBean.device_position;
          this.device_status_desc = deviceBean.device_status_desc;
          this.did = deviceBean.did;
          this.features = deviceBean.features;
          this.is_virtual = deviceBean.is_virtual;
          this.is_new = deviceBean.is_new;
          this.acess_type = deviceBean.acess_type;
          this.report_status = deviceBean.report_status;
          this.voice_tips = deviceBean.voice_tips;
          this.detail_layout = deviceBean.detail_layout;
          this.status_show = deviceBean.status_show;
          this.pasue_start = deviceBean.pasue_start;
     }
}
