package com.skyworth.smarthome.home.smartdevice.controlpanel.model;

import com.alibaba.fastjson.JSONObject;
import com.coocaa.app.core.http.HttpServiceManager;
import com.skyworth.smarthome.service.push.binder.model.AIOTDataPushModel;
import com.skyworth.smarthome.service.push.binder.model.IAiotDataPushModel;
import com.skyworth.smarthome.common.bean.AlarmLogBean;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.event.DevicesStatusChangeEvent;
import com.skyworth.smarthome.common.http.SmartDevicesHttpService;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.util.ThreadManager;
import com.skyworth.smarthome.service.model.ISmartDeviceDataModel;
import com.skyworth.smarthome.service.model.ISmartHomeModel;
import com.skyworth.smarthome.service.model.SmartHomeModel;
import com.skyworth.smarthome.service.push.local.DeviceDataPushUtil;
import com.smarthome.common.model.SmartBaseData;
import com.smarthome.common.utils.EmptyUtils;
import com.swaiot.aiotlib.common.entity.DeviceBean;
import com.swaiot.aiotlib.common.entity.FamilyStatusBean;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: DeviceControlModel
 * @Author: AwenZeng
 * @CreateDate: 2020/6/16 15:57
 * @Description:
 */
public class DeviceControlModel implements IDeviceControlModel {

    @Override
    public void handleDeviceStatusData(String data) {
        IAiotDataPushModel aiotDataPushModel = new AIOTDataPushModel();
        aiotDataPushModel.onHandleDeviceStatusData(data);
    }

    @Override
    public void controlDevice(final Map<String, String> status, final String deviceId) {
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                ISmartHomeModel.INSTANCE.controlDevice(deviceId, status);
            }
        });
    }

    @Override
    public void controlVirtualDevice(Map<String, String> status, String deviceId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("device_id", deviceId);
        jsonObject.put("status", status);
        jsonObject.put(SmartHomeModel.LOCAL_CONTROL, "localControl");
        DevicesStatusChangeEvent event = new DevicesStatusChangeEvent();
        event.setTypeString(jsonObject.toJSONString());
        EventBus.getDefault().post(event);
//        handleVirtualControl(status, deviceId);
    }

    @Override
    public List<AlarmLogBean> requestAlarmLogList(final String gateway_id, final String did) {
        List<AlarmLogBean> logList = new ArrayList<>();
        SmartBaseData<List<AlarmLogBean>> smartBaseData = HttpServiceManager.Companion
                .call(SmartDevicesHttpService.SERVICE.getSensorAlarmLogsList(gateway_id, did, 0, 6));
        if (smartBaseData != null && smartBaseData.data != null) {
            logList = smartBaseData.data;
        }
        return logList;
    }

    /**
     * 虚拟设备控制，推送运行数变化
     *
     * @param status
     * @param deviceId
     */
    private void handleVirtualControl(Map<String, String> status, String deviceId) {
        DeviceInfo deviceInfo = ISmartDeviceDataModel.INSTANCE.getSmartDeviceInfo(deviceId);
        if (EmptyUtils.isNotEmpty(deviceInfo)) {
            try {
                String on_off = getStatus(JSONObject.toJSONString(status), deviceInfo.status_show);
                String homeStatusData = AppData.getInstance().getHomeStatusData();
                if (deviceInfo.online_status == 1 && EmptyUtils.isNotEmpty(homeStatusData)) {
                    FamilyStatusBean familyStatusBean = JSONObject.parseObject(homeStatusData, FamilyStatusBean.class);
                    if (EmptyUtils.isNotEmpty(on_off)) {
                        if (AppConstants.DEVICE_POW_STATUS_ON.equals(on_off)) {
                            if (EmptyUtils.isNotEmpty(deviceInfo.pasue_start)) {
                                String pasue_start = getStatus(JSONObject.toJSONString(status), deviceInfo.pasue_start);
                                if (EmptyUtils.isNotEmpty(pasue_start)) {
                                    if (AppConstants.DEVICE_POW_STATUS_START.equals(pasue_start)) {
                                        familyStatusBean.running_device_count++;
                                        AppData.getInstance().setHomeStatusData(JSONObject.toJSONString(familyStatusBean));
                                    } else if (AppConstants.DEVICE_POW_STATUS_PAUSE.equals(pasue_start)) {
                                        familyStatusBean.running_device_count--;
                                        AppData.getInstance().setHomeStatusData(JSONObject.toJSONString(familyStatusBean));
                                    }
                                }
                            } else {
                                familyStatusBean.running_device_count++;
                                AppData.getInstance().setHomeStatusData(JSONObject.toJSONString(familyStatusBean));
                            }
                        } else if (AppConstants.DEVICE_POW_STATUS_OFF.equals(on_off)) {
                            familyStatusBean.running_device_count--;
                            AppData.getInstance().setHomeStatusData(JSONObject.toJSONString(familyStatusBean));
                        }
                    } else {
                        String pasue_start = getStatus(JSONObject.toJSONString(status), deviceInfo.pasue_start);
                        if (EmptyUtils.isNotEmpty(pasue_start)) {
                            if (AppConstants.DEVICE_POW_STATUS_START.equals(pasue_start)) {
                                familyStatusBean.running_device_count++;
                                AppData.getInstance().setHomeStatusData(JSONObject.toJSONString(familyStatusBean));
                            } else if (AppConstants.DEVICE_POW_STATUS_PAUSE.equals(pasue_start)) {
                                familyStatusBean.running_device_count--;
                                AppData.getInstance().setHomeStatusData(JSONObject.toJSONString(familyStatusBean));
                            }
                        }
                    }
                    DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.HOME_STATUS_INFO, JSONObject.toJSONString(familyStatusBean));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private String getStatus(String status, DeviceBean.StatusShowBean statusShow) {
        if (EmptyUtils.isNotEmpty(statusShow) && EmptyUtils.isNotEmpty(status)) {
            JSONObject reportStatus = JSONObject.parseObject(status);
            JSONObject values = JSONObject.parseObject(statusShow.values);
            if (EmptyUtils.isNotEmpty(values)) {
                String dataField = reportStatus.getString(statusShow.data_field);
                return values.getString(dataField);
            }
        }
        return "";
    }

    @Override
    public boolean isDevicePowerOn(DeviceInfo deviceInfo) {
        try {
            DeviceBean.StatusShowBean statusShow = deviceInfo.status_show;
            if (EmptyUtils.isNotEmpty(statusShow) && EmptyUtils.isNotEmpty(deviceInfo.report_status)) {
                JSONObject status = JSONObject.parseObject(deviceInfo.report_status);
                JSONObject values = JSONObject.parseObject(statusShow.values);
                if (EmptyUtils.isNotEmpty(values)) {
                    String power = status.getString(statusShow.data_field);
                    String powerValue = values.getString(power);
                    if (EmptyUtils.isNotEmpty(powerValue) && powerValue.equals("OFF")) {
                        return false;
                    } else {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }


}
