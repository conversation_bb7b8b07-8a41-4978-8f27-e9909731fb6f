package com.skyworth.smarthome.common.model;

import com.skyworth.smarthome.common.util.DataCacheUtil;
import com.skyworth.smarthome.common.util.DateUtil;
import com.smarthome.common.utils.EmptyUtils;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Describe:用户信息单例
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/1/9
 */
public class UserInfo {
    private static UserInfo instance;
    private String userID;
    private String nick_name;
    private String avatar;
    private String token;
    private String mobilePhone;

    public static UserInfo getInstance() {
        if (instance == null) {
            synchronized (UserInfo.class) {
                if (instance == null) {
                    instance = new UserInfo();
                }
            }
        }
        return instance;
    }

    public String getUserID() {
        return userID;
    }

    public void setUserID(String userID) {
        this.userID = userID;
    }

    public String getNick_name() {
        return nick_name;
    }

    public void setNick_name(String nick_name) {
        this.nick_name = nick_name;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public void clear() {
        setUserID("");
        setToken("");
        setNick_name("");
        setAvatar("");
        setMobilePhone("");
    }

    /**
     * 缓存的UserID是否有效
     * <p>
     * 每隔24小时重置UserID缓存
     *
     * @return true:有效  false:无效
     */
    public boolean isSaveUserIdValid() {
        try {
            if (isSystemTimeRight()) {
                String savedTime = DataCacheUtil.getInstance().getString(DataCacheUtil.KEY_SAVE_USER_ID_TIME, AppConstants.DEFAULT_TIME);
                String savedUserId = DataCacheUtil.getInstance().getString(DataCacheUtil.KEY_SAVE_USER_ID, "");
                if (EmptyUtils.isNotEmpty(savedUserId)) {
                    Date saveDate = DateUtil.formatDate(savedTime, DateUtil.DEFAULT_FORMAT_DATETIME);
                    Date nowDate = DateUtil.formatDate(DateUtil.date2String(System.currentTimeMillis(), DateUtil.DEFAULT_FORMAT_DATETIME), DateUtil.DEFAULT_FORMAT_DATETIME);
                    long between = nowDate.getTime() - saveDate.getTime();
                    if (between > AppConstants.VALID_TIME) {//一天时间
                        return false;
                    } else {
                        return true;
                    }
                }
                return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 粗略检测系统时间是否正确
     *
     * @return
     */
    public boolean isSystemTimeRight() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DateUtil.DEFAULT_FORMAT_DATETIME);
        String sysTime = simpleDateFormat.format(System.currentTimeMillis());
        Date date1 = DateUtil.formatDate(AppConstants.DEFAULT_TIME, DateUtil.DEFAULT_FORMAT_DATETIME);
        Date date2 = DateUtil.formatDate(sysTime, DateUtil.DEFAULT_FORMAT_DATETIME);
        //1.使用Date的compareTo()方法，大于、等于、小于分别返回1、0、-1
        if (date2.compareTo(date1) > 0) {
            return true;
        }
        return false;
    }
}
