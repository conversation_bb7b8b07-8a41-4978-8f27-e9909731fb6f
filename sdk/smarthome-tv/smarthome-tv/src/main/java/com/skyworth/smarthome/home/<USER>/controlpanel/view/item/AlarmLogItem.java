package com.skyworth.smarthome.home.smartdevice.controlpanel.view.item;

import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.bean.AlarmLogBean;
import com.skyworth.ui.api.SkyTextView;
import com.skyworth.ui.newrecycleview.NewRecycleAdapterItem;
import com.skyworth.util.Util;

public class AlarmLogItem extends FrameLayout implements NewRecycleAdapterItem<AlarmLogBean> {
    private TextView tvTime = null;
    private TextView tvDesc = null;
    private View topLine = null;
    private View bottomLine = null;
    private View point = null;
    private FrameLayout mValueLayout = null;
    private int hideBottomLinePosition;

    public AlarmLogItem(Context context) {
        super(context);
        setFocusable(false);
        setFocusableInTouchMode(false);
        initValue();
    }

    private void initValue() {
        mValueLayout = new FrameLayout(getContext());
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, Util.Div(70));
        addView(mValueLayout, layoutParams);

        topLine = new View(getContext());
        topLine.setBackground(getContext().getResources().getDrawable(R.drawable.shape_rect_gary));
        LayoutParams layoutParamsItem = new LayoutParams(Util.Div(2), Util.Div(12));
        layoutParamsItem.leftMargin = Util.Div(55);
        mValueLayout.addView(topLine, layoutParamsItem);

        point = new View(getContext());
        point.setBackground(getContext().getResources().getDrawable(R.drawable.shape_circle_gray));
        layoutParamsItem = new LayoutParams(Util.Div(12), Util.Div(12));
        layoutParamsItem.leftMargin = Util.Div(50);
        layoutParamsItem.topMargin = Util.Div(12);
        mValueLayout.addView(point, layoutParamsItem);

        bottomLine = new View(getContext());
        bottomLine.setBackground(getContext().getResources().getDrawable(R.drawable.shape_rect_gary));
        layoutParamsItem = new LayoutParams(Util.Div(2), Util.Div(46));
        layoutParamsItem.leftMargin = Util.Div(55);
        layoutParamsItem.topMargin = Util.Div(24);
        mValueLayout.addView(bottomLine, layoutParamsItem);

        tvTime = new SkyTextView(getContext());
        tvTime.setTextSize(Util.Dpi(28));
        tvTime.setTextColor(Color.parseColor("#99ffffff"));
        tvTime.setGravity(Gravity.CENTER);
        tvTime.setSingleLine(true);
        layoutParamsItem = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParamsItem.leftMargin = Util.Div(92);
        mValueLayout.addView(tvTime, layoutParamsItem);

        tvDesc = new SkyTextView(getContext());
        tvDesc.setTextSize(Util.Dpi(28));
        tvDesc.setTextColor(Color.parseColor("#ccffffff"));
        tvDesc.setGravity(Gravity.CENTER);
        tvDesc.setSingleLine(true);
        tvDesc.setEllipsize(TextUtils.TruncateAt.END);
        layoutParamsItem = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParamsItem.leftMargin = Util.Div(304);
        mValueLayout.addView(tvDesc, layoutParamsItem);
    }

    @Override
    public View getView() {
        return this;
    }

    @Override
    public void onUpdateData(AlarmLogBean alarmLogBean, int i) {
        if (i == 0) {
            topLine.setVisibility(GONE);
        }
        if (i == hideBottomLinePosition) {
            bottomLine.setVisibility(GONE);
        }
        tvTime.setText(formatTime(alarmLogBean.time));
        matchAlarmText(alarmLogBean);
    }

    @Override
    public void clearItem() {

    }

    @Override
    public void refreshUI() {

    }


    @Override
    public void destroy() {

    }

    private String formatTime(String originString) {
        if (TextUtils.isEmpty(originString)) return "";
        String[] originStrings = originString.split("T");
        String s1 = originStrings[0];
        String s2 = originStrings[1];
        return new StringBuilder().append(s1.substring(s1.length() - 5))
                .append(" ").append(s2.substring(0, 5)).toString();
    }

    public void setHideBottomLinePosition(int hideBottomLinePosition) {
        this.hideBottomLinePosition = hideBottomLinePosition;
    }

    private void matchAlarmText(AlarmLogBean alarmLogBean) {
        StringBuilder logDesc = new StringBuilder();
        switch (alarmLogBean.product_type_id) {
            case 13:
                //13: '门磁'
                if (alarmLogBean.alarm == 0) {
                    logDesc.append(getResources().getString(R.string.control_panel_window_close));
                } else {
                    logDesc.append(getResources().getString(R.string.control_panel_window_open));
                }
                if (alarmLogBean.tamper == 1) {
                    logDesc.append(",").append(getResources().getString(R.string.control_panel_tamper_warn));
                }
                if (alarmLogBean.batery_low == 1) {
                    logDesc.append(",").append(getResources().getString(R.string.control_panel_batery_low_warn));
                }
                break;
            case 16:
                //16: '人体红外感应
                if (alarmLogBean.alarm == 1) {
                    logDesc.append(getResources().getString(R.string.control_panel_someone_passed));
                }
                if (alarmLogBean.tamper == 1) {
                    logDesc.append(",").append(getResources().getString(R.string.control_panel_tamper_warn));
                }
                if (alarmLogBean.batery_low == 1) {
                    logDesc.append(",").append(getResources().getString(R.string.control_panel_batery_low_warn));
                }
                break;
            case 17:
                //17: '温湿度传感器'
                if (alarmLogBean.batery_low == 1) {
                    logDesc.append(",").append(getResources().getString(R.string.control_panel_batery_low_warn));
                }
                break;
            case 18:
                //18一氧化碳报警器
                if (alarmLogBean.alarm == 1) {
                    logDesc.append(getResources().getString(R.string.control_panel_carbon_monoxide_warn));
                }
                if (alarmLogBean.batery_low == 1) {
                    logDesc.append(",").append(getResources().getString(R.string.control_panel_batery_low_warn));
                }
                break;
            case 19:
                //19: '可燃气体报警器'
                if (alarmLogBean.alarm == 1) {
                    logDesc.append(getResources().getString(R.string.control_panel_gas_warn));
                }
                break;
            case 20:
                //20: '烟雾报警器'
                if (alarmLogBean.alarm == 1) {
                    logDesc.append(getResources().getString(R.string.control_panel_smog_warn));
                }
                if (alarmLogBean.batery_low == 1) {
                    logDesc.append(",").append(getResources().getString(R.string.control_panel_batery_low_warn));
                }
                break;
            case 23:
                //23: '水浸传感器'
                if (alarmLogBean.alarm == 1) {
                    logDesc.append(getResources().getString(R.string.control_panel_water_warn));
                }
                if (alarmLogBean.tamper == 1) {
                    logDesc.append(",").append(getResources().getString(R.string.control_panel_tamper_warn));
                }
                if (alarmLogBean.batery_low == 1) {
                    logDesc.append(",").append(getResources().getString(R.string.control_panel_batery_low_warn));
                }
                break;
            case 30:
                //30: '紧急报警按钮'
                if (alarmLogBean.alarm == 1) {
                    logDesc.append(getResources().getString(R.string.control_panel_urgent_warn));
                }
                if (alarmLogBean.batery_low == 1) {
                    logDesc.append(",").append(getResources().getString(R.string.control_panel_batery_low_warn));
                }
                break;
        }
        tvDesc.setText(logDesc.toString());
    }
}
