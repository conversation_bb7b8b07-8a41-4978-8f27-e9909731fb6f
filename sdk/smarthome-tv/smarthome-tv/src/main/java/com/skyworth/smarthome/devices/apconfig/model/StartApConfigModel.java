package com.skyworth.smarthome.devices.apconfig.model;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;
import android.util.Log;

import com.alibaba.fastjson.JSONObject;
import com.coocaa.app.core.app.AppCoreApplication;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.util.DataCacheUtil;
import com.skyworth.smarthome.service.model.FunctionGoToModel;
import com.smarthome.common.dataer.DataHelpInfo;
import com.smarthome.common.dataer.LogSDK;
import com.swaiot.aiotlib.common.entity.DiscoverNetworkDevice;
import com.swaiot.aiotlib.common.entity.DiscoverWifiDevice;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

import static com.skyworth.smarthome.devices.apconfig.ApConfigService.TAG;

/**
 * Describe:
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/2/22
 */
public class StartApConfigModel {

    private Context mContext;
    public static boolean isConfiging = false;

    public StartApConfigModel(Context mContext) {
        this.mContext = mContext;
    }

    public void startApConfig(final String devicesJson) {
        if (isApConfiging()) {
            Log.i(TAG, "onReceive: is configing skip this receive");
            return;
        }
        AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                if (TextUtils.isEmpty(devicesJson)) {
                    Log.i(TAG, "device json is empty");
                    return Unit.INSTANCE;
                }
                List<DiscoverWifiDevice> deviceInfos = JSONObject.parseArray(devicesJson, DiscoverWifiDevice.class);
                reportData(deviceInfos);
                if (deviceInfos.size() <= 0) {
                    Log.i(TAG, "device list size <= 0");
                } else {
                    startConfigPage(mContext, deviceInfos.get(0));
                }
                return Unit.INSTANCE;
            }
        });
    }

    private boolean isApConfiging() {
        return isConfiging;
    }

    /**
     * 上报配网的设备
     * @param deviceInfos
     */
    private void reportData(List<DiscoverWifiDevice> deviceInfos){
        for(DiscoverWifiDevice item:deviceInfos){
            //数据统计
            Map<String, String> map = new HashMap<>();
            map.put("source", DataHelpInfo.getInstance().getDiscoverDeviceOrigin());
            map.put("device_brand", item.getDeviceDetail().getBrand());
            map.put("device_name", item.getDeviceDetail().getProduct());
            map.put("device_state", "未配网");
            map.put("device_SSID", item.getWifiInfo().SSID);
            LogSDK.submit(LogSDK.EVENT_ID_DISCOVER_DEVICE,map);
        }
    }

    private boolean canShowConfigDialog(Context context, String bssid) {
        SharedPreferences sp = context.getSharedPreferences("apconfig", Context.MODE_PRIVATE);
        int count = sp.getInt(bssid, 0);
        Log.i(TAG, "canShowConfigDialog: " + bssid + " " + count);
        return count < 2;
    }

    private void startConfigPage(Context context, DiscoverWifiDevice newDevice) {
        Log.i(TAG, "startConfigPage: auto apconfig");
        if(AppData.getInstance().isOpenScreensaver()){
            DiscoverNetworkDevice discoverNetworkDevice = new DiscoverNetworkDevice();
            discoverNetworkDevice.deviceInfo = newDevice;
            FunctionGoToModel.INSTANCE.goToDeviceConfigAuto(discoverNetworkDevice);
        }else if(DataCacheUtil.getInstance().getInt(DataCacheUtil.KEY_SAVE_ISSHOW_APCONFIG) < 1){
            DiscoverNetworkDevice discoverNetworkDevice = new DiscoverNetworkDevice();
            discoverNetworkDevice.deviceInfo = newDevice;
            FunctionGoToModel.INSTANCE.goToDeviceConfigManualWithDiscoverDialog(discoverNetworkDevice);
            DataCacheUtil.getInstance().putInt(DataCacheUtil.KEY_SAVE_ISSHOW_APCONFIG,1);
        }
        isConfiging = true;
    }

}
