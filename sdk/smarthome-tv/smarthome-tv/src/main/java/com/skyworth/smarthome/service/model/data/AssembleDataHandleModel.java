package com.skyworth.smarthome.service.model.data;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.devices.discover.view.DiscoverNearDevicesView;
import com.smarthome.common.utils.EmptyUtils;
import com.swaiot.aiotlib.common.entity.DiscoverNetworkDevice;
import com.swaiot.aiotlib.common.entity.DiscoverWifiDevice;

/**
 * Describe:组装数据处理
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/9/17
 */
public class AssembleDataHandleModel implements IAssembleDataHandleModel {
    /**
     * 组装首页状态变化推送数据
     *
     * @param status
     * @return
     */
    @Override
    public String assemblePushHomePageData(String status){
//        boolean isHandle = false;//状态变化是否需要处理
//        JSONObject msgJson = JSON.parseObject(status);
//        String targetId = msgJson.getString("device_id");
//        String onlineStatus = msgJson.getString("online_status");
//        if (EmptyUtils.isNotEmpty(onlineStatus) && onlineStatus.equals("0")) {
//            msgJson.put("device_status_des", SmartHomeApplication.getContext().getString(R.string.already_off_online));
//            msgJson.remove("status");
//            return msgJson.toJSONString();
//        } else {
//            DeviceListHttpBean.DeviceDetailData deviceDetail = ISmartDeviceDataModel.INSTANCE.getSmartDeviceInfo(targetId);
//            if (EmptyUtils.isNotEmpty(deviceDetail) && EmptyUtils.isNotEmpty(deviceDetail.home_layout) && EmptyUtils.isNotEmpty(deviceDetail.status)) {
//                JSONObject jsonObject = JSONObject.parseObject(deviceDetail.status);
//                JSONObject statusObject = JSONObject.parseObject(msgJson.getString("status"));
//                for (DeviceListHttpBean.HomePageData item : deviceDetail.home_layout) {
//                    for (Object key : statusObject.keySet().toArray()) {
//                        if (item.data_field.equals(key) || EmptyUtils.isNotEmpty(item.depend) && item.depend.data_field.equals(key) || EmptyUtils.isNotEmpty(item.second_depend) && item.second_depend.data_field.equals(key)) {
//                            isHandle = true;
//                            break;
//                        }
//                    }
//                }
//                if (isHandle) {//处理，修改显示内容
//                    for (Object key : statusObject.keySet().toArray()) {
//                        jsonObject.put((String) key, statusObject.getString((String) key));
//                    }
//                    String deviceStatusDes = assembleHomePageStatusDes(jsonObject.toJSONString(), deviceDetail.home_layout);
//                    msgJson.put("device_status_des", deviceStatusDes);
//                    msgJson.remove("status");
//                    return msgJson.toJSONString();
//                }
//            } else {
//                msgJson.put("device_status_des", SmartHomeApplication.getContext().getString(R.string.online));
//                msgJson.remove("status");
//                return msgJson.toJSONString();
//            }
//        }
        return "";
    }

    @Override
    public DiscoverNetworkDevice assembleApconfigDeviceInfo(DiscoverWifiDevice discoverDeviceInfo) {
        DiscoverNetworkDevice device = new DiscoverNetworkDevice();
        if (EmptyUtils.isNotEmpty(discoverDeviceInfo.getDeviceDetail())) {
            device.device_id = discoverDeviceInfo.getWifiInfo().BSSID;
            device.device_name = discoverDeviceInfo.getDeviceDetail().getBrand() + discoverDeviceInfo.getDeviceDetail().getProduct();
            device.product_type_logo = discoverDeviceInfo.getDeviceDetail().getImg_url();
        } else {
            device.device_id = "device_id";
            device.device_name = SmartHomeTvLib.getContext().getString(R.string.unknow_device);
            device.product_type_logo = "";
        }
        device.bind_status = DiscoverNearDevicesView.DEVICE_STATUS_NOT_APCONFIG_NETWORK;//未配网
        device.deviceInfo = discoverDeviceInfo;
        return device;
    }
}
