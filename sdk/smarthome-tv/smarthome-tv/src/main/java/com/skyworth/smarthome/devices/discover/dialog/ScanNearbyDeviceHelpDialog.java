package com.skyworth.smarthome.devices.discover.dialog;

import android.content.Intent;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.skyworth.smarthome.common.base.BaseSysDialog;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.devices.discover.view.NotScannedHelpView;
import com.skyworth.smarthome.personal.thirdaccount.ThirdAccountActivity;
import com.skyworth.util.Util;

import java.util.Map;

import static com.skyworth.smarthome.common.util.DialogLauncherUtil.DIALOG_KEY_SCAN_HELP;

/**
 * Describe: 扫描附近设备帮助弹框
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/10/9
 */
public class ScanNearbyDeviceHelpDialog extends BaseSysDialog {


    public ScanNearbyDeviceHelpDialog(int themeResId) {
        super(themeResId);
    }

    @Override
    protected void initParams() {
        mDialogKey = DIALOG_KEY_SCAN_HELP;
    }

    @Override
    public void showDialog(Map<String, String> params) {
        super.showDialog(params);
        initUI();
    }


    @Override
    protected void initContentView() {
        super.initContentView();
    }

    private void initUI() {
        FrameLayout mLayout = new FrameLayout(mContext);
        String downloadUrl = AppData.getInstance().getVHomeDownloadUrl();
        NotScannedHelpView notScannedHelpView = new NotScannedHelpView(mContext);
        notScannedHelpView.showQrCode(downloadUrl);
        notScannedHelpView.setOnBtnClickListener(new NotScannedHelpView.OnClickListener() {
            @Override
            public void OnClick() {
                Intent intent = new Intent(getContext(), ThirdAccountActivity.class);
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                getContext().startActivity(intent);
                dismiss();
            }
        });
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Util.Div(1200), Util.Div(520));
        layoutParams.gravity = Gravity.CENTER;
        mLayout.addView(notScannedHelpView, layoutParams);
        mContentView.addView(mLayout, new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT));
        cancelDialogAnimation();
        notScannedHelpView.requestFocus();
        notScannedHelpView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
        openAutoDismissDialog();
    }

    @Override
    protected void onDismiss() {
        super.onDismiss();
    }
}
