package com.skyworth.smarthome.common.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;


import com.skyworth.smarthome.common.base.BaseCommonDialog;
import com.skyworth.smarthome.common.util.DialogCommonUtil;
import com.skyworth.smarthome.service.model.IFunctionGoToModel;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.ui.CommonFocusBox;
import com.skyworth.smarthome.common.util.ViewsBuilder;

/**
 * @ProjectName: NewTV_SmartHome
 * @Package: com.skyworth.smarthome_tv.common.dialog
 * @ClassName: UnBindDialog
 * @Description: 离线帮助-wifi
 * @Author: wangyuehui
 * @CreateDate: 2020/6/9 17:24
 * @UpdateUser: 更新者
 * @UpdateDate: 2020/6/9 17:24
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class OfflineHelpWifiDialog extends BaseCommonDialog {

    private CommonFocusBox mRebingBtn, mCancleBtn;
    private OfflineHelpWifiCallBack mOfflineHelpWifiCallBack;

    public OfflineHelpWifiDialog(@NonNull Context context) {
        this(context, R.style.common_style);
    }

    public OfflineHelpWifiDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    @Override
    protected void initParams() {

    }

    public static OfflineHelpWifiDialog newInstance(Context context) {

        OfflineHelpWifiDialog fragment = new OfflineHelpWifiDialog(context, R.style.common_style);
        return fragment;
    }

    public void setOfflineHelpWifiCallBack(OfflineHelpWifiCallBack offlineHelpWifiCallBack) {
        this.mOfflineHelpWifiCallBack = offlineHelpWifiCallBack;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        View contentView = ViewsBuilder.getOfflineHelpOfWifi(getContext());
        setContentView(contentView);

        contentView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dismiss();
            }
        });

        mRebingBtn = findViewById(R.id.offline_help_rebind);
        mCancleBtn = findViewById(R.id.offline_help_cancle);

        mCancleBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        mRebingBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                IFunctionGoToModel.INSTANCE.goToAddScanSmartDevice("手动");
                if (mOfflineHelpWifiCallBack != null) {
                    mOfflineHelpWifiCallBack.callBack();
                }
                dismiss();
            }
        });

        mRebingBtn.requestFocus();
    }

    @Override
    public void onStart() {
        super.onStart();
        Window window = getWindow();
        WindowManager.LayoutParams windowParams = window.getAttributes();
        windowParams.width = WindowManager.LayoutParams.MATCH_PARENT;
        windowParams.height = WindowManager.LayoutParams.MATCH_PARENT;
        windowParams.gravity = Gravity.CENTER;
        windowParams.dimAmount = 0.50f;
        windowParams.flags |= WindowManager.LayoutParams.FLAG_DIM_BEHIND;
        window.setAttributes(windowParams);
    }

    public interface OfflineHelpWifiCallBack {
        void callBack();
    }

    @Override
    public void show() {
        super.show();
        DialogCommonUtil.putDialog(DialogCommonUtil.DIALOG_KEY_OFFLINE_HELP_WIFI,this);
    }

    @Override
    protected void onDismiss() {
        super.onDismiss();
        DialogCommonUtil.removeDialog(DialogCommonUtil.DIALOG_KEY_OFFLINE_HELP_WIFI);
    }
}
