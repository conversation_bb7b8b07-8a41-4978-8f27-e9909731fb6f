package com.skyworth.smarthome.common.util;

import android.content.ComponentName;
import android.content.Context;

import com.coocaa.app.core.app.AppCoreApplication;
import com.smarthome.common.utils.Android;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/4/16 16:31.
 */
public class PollingChecker {
    private IPollingResult listener = null;
    private Context context = null;
    private boolean isRunning = false;

    public interface IPollingResult {
        void onCheck(ComponentName componentName);
    }

    public void start(Context context, IPollingResult listener, long delay, final long interval) {
        this.context = context;
        this.listener = listener;
        if (isRunning) {
            return;
        }
        isRunning = true;
        AppCoreApplication.Companion.workerThread(delay, new Function0<Unit>() {
            @Override
            public Unit invoke() {
                while (isRunning) {
                    try {
                        check();
                        Thread.sleep(interval);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                return Unit.INSTANCE;
            }
        });
    }

    public void stop() {
        isRunning = false;
    }

    private void check() {
        ComponentName top = Android.getRunningTopActivity(context);
        if (listener != null) {
            listener.onCheck(top);
        }
    }
}
