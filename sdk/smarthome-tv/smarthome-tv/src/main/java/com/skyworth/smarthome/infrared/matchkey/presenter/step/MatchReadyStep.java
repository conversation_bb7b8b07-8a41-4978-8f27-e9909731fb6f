package com.skyworth.smarthome.infrared.matchkey.presenter.step;

import com.skyworth.smarthome.infrared.matchkey.presenter.IMatchKeyPresenter;

import static com.skyworth.smarthome.infrared.matchkey.presenter.IMatchKeyPresenterImpl.HANDLE_KEY_START;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/4/29 18:18.
 */
public class MatchReadyStep extends IRConfigBaseStep<IMatchKeyPresenter> {
    public static final String STEP_TAG = "match_ready";

    @Override
    public void create() {
        super.create();
    }

    @Override
    public void run() {
        super.run();
        presenter.showReadyView();
    }

    @Override
    public boolean input(String msg, Object... params) {
        if (msg != null && msg.equals(HANDLE_KEY_START)) {
            presenter.startMatchInternal();
            return true;
        }
        return false;
    }

    @Override
    public String getTag() {
        return STEP_TAG;
    }

    @Override
    public void destroy() {
        super.destroy();
    }
}
