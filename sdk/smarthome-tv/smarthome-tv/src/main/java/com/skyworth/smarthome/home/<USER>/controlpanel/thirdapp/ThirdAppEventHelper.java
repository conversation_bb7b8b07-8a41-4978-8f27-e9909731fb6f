package com.skyworth.smarthome.home.smartdevice.controlpanel.thirdapp;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: ThirdAppEventHelper
 * @Author: XuZeXiao
 * @CreateDate: 2020-02-10 20:24
 * @Description:
 */
public class ThirdAppEventHelper {
    public static final String VALUE = "value";

    public void onAiStandByStartEvent() {
        onAiStandByEvent(true);
    }

    public void onAiStandByEndEvent() {
        onAiStandByEvent(false);
    }

    private void onAiStandByEvent(boolean value) {
        Map<String, String> params = new HashMap<>(1);
        params.put(VALUE, String.valueOf(value));
        ThirdAppLaunchManager.getInstance().onEvent(ThirdAppLaunchManager.GET_INFO_IS_AI_STAND_BY, params);
    }
}
