package com.skyworth.smarthome.home.smartdevice.controlpanel.view.base;

import android.view.KeyEvent;
import android.view.View;

import com.skyworth.smarthome.home.smartdevice.controlpanel.common.itemdata.BaseControlData;
import com.skyworth.smarthome.home.smartdevice.controlpanel.view.IControlListener;


public interface IControlItem<DATA extends BaseControlData> {
    void show(String status, DATA data);

    void setControlListener(IControlListener listener);

    void onRecycle();

    boolean onKey(View view, int keyCode, KeyEvent event);

    boolean canFocusable();

    void onFocus(boolean hasFocus);

    DATA getData();

    View getView();

    String getType();

    void enable();

    void disable();
}
