package com.skyworth.smarthome.common.bean;

import com.skyworth.smarthome.home.smartdevice.controlpanel.common.itemdata.BaseControlData;

public class AlarmLogBean extends BaseControlData {

    /**
     * product_type_id : 16
     * gateway_id : a2847e18707e237239b17b8234e7e548
     * did : 97c30502008d1500
     * alarm :  感应（报警） 0:正常 1:报警
     * tamper : 防拆报警 0:正常 1:报警
     * batery_low : 电压 0:正常 1:低电压
     * time : 2018-08-07T13:36:53.401886Z
     * log_desc : 红外传感器
     * status : {"INF":0,"AMB":0}
     */

    public int product_type_id;
    public String gateway_id;
    public String did;
    public int alarm;
    public int tamper;
    public int batery_low;
    public String time;
    public String log_desc;
    public StatusBean status;

    public StatusBean getStatus() {
        return status;
    }

    public void setStatus(StatusBean status) {
        this.status = status;
    }

    public static class StatusBean {
        /**
         * INF : 0
         * AMB : 0
         */

        public int INF;
        public int AMB;
    }
}
