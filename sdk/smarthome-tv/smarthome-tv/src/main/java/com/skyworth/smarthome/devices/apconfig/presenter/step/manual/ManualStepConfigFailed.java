package com.skyworth.smarthome.devices.apconfig.presenter.step.manual;

import com.skyworth.smarthome.devices.apconfig.presenter.step.BaseStep;

import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_CONFIG_FAILED_HIDE;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_CONFIG_FAILED_SHOW;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_CONFIG_PROGRESS_HIDE;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_RECONFIG;

/**
 * Description: <br>
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/1/7 21:54.
 */
public class ManualStepConfigFailed extends BaseStep {
    public static final String STEP_TAG = "manual_configFailed";

    @Override
    public void run() {
        super.run();
        output(STEP_MSG_CONFIG_PROGRESS_HIDE);
        output(STEP_MSG_CONFIG_FAILED_SHOW);
    }

    @Override
    public boolean input(String msg, Object... params) {
        switch (msg) {
            case STEP_MSG_RECONFIG:
                jumpTo(ManualCheckMideaStep.STEP_TAG);
                return true;
        }
        return false;
    }

    @Override
    public String getTag() {
        return STEP_TAG;
    }

    @Override
    public void destroy() {
        output(STEP_MSG_CONFIG_FAILED_HIDE);
        output(STEP_MSG_CONFIG_PROGRESS_HIDE);
        super.destroy();
    }
}
