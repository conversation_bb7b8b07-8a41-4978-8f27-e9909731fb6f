package com.skyworth.smarthome.common.util;

import android.text.TextUtils;

import com.coocaa.dataer.tools.CcosProperty;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.smarthome.common.sal.SalImpl;
import com.smarthome.common.utils.EmptyUtils;

import java.lang.reflect.Method;

/**
 * Created by lu on 2018/2/5.
 */

public class SystemProperty {

    public static String get(String key, String defaultValue) {
        String value = defaultValue;
        try {
            Class<?> c = Class.forName("android.os.SystemProperties");
            Method get = c.getMethod("get", String.class);
            value = (String) get.invoke(c, key);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (TextUtils.isEmpty(value)) {
            value = defaultValue;
        }
        return value;
    }

    public static int get(String key, int defaultValue) {
        int value = defaultValue;
        try {
            Class<?> c = Class.forName("android.os.SystemProperties");
            Method get = c.getMethod("get", String.class);
            String tempValue = (String)get.invoke(c, key);
            if(EmptyUtils.isNotEmpty(tempValue)){
                value = Integer.parseInt(tempValue);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return value;
    }

    /**
     * 获取设备ID(电视)
     *
     * @return
     */
    public static String getDeviceId() {
        String deviceId = "";
        try {
            deviceId = SalImpl.getSAL(SmartHomeTvLib.getContext()).getActiveID();

            if(EmptyUtils.isEmpty(deviceId)){
                deviceId = SystemProperty.get("persist.iot.did", "");
            }
            if (EmptyUtils.isEmpty(deviceId)) {
                deviceId = SystemProperty.get("persist.sys.iot.did", "");
            }
            return deviceId;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return deviceId;
    }


    /**
     * 是否AOD息屏
     * third.get.aod_wakelock  0 为开机状态，1为关机状态
     * @return
     */
    public static boolean isAodDevice() {
        try {
            int aodWakelock = SystemProperty.get("third.get.aod_wakelock", 0);
            return aodWakelock == 1 ? true : false;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }


    /**
     * 是否红外设备
     * @return
     */
    public static boolean isInfraredDevice() {
        try {
            int hwVaule = SystemProperty.get("ro.skyconfig.hw.iot_rc", 0);
            return hwVaule == 1 ? true : false;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 是否是卖场模式
     * @return
     */
    public static boolean isStoreMode() {
        return CcosProperty.isStoreMode();
    }

    /**
     * 是否是OLED电视
     * @return
     */
    public static boolean isOLED() {
        return SalImpl.getSAL(SmartHomeTvLib.getContext()).isOLED();
    }

    /**
     * 是否AI待机
     */
    public static boolean isAistandbymode() {
        String mode = SystemProperty.get("third.get.aistandbymode", "");
        if (!TextUtils.isEmpty(mode) && !mode.equals("0")) {
            return true;
        }
        return false;
    }

}
