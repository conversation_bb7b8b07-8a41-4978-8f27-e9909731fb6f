package com.skyworth.smarthome.common.model;

import android.content.Context;

import com.smarthome.common.utils.Constants;
import com.smarthome.common.utils.config.ConfigFactory;

/**
 * @Description: SharedPreferences 缓存数据接口
 * @Author: wzh
 * @CreateDate: 2020/7/1
 */
public class SPCacheData {

    /**
     * 缓存家庭id
     *
     * @param context
     * @param familyId
     */
    public static void setFamilyId(Context context, String familyId) {
        try {
            ConfigFactory.get(context, Constants.SP_NAME_SMARTHOME).put(Constants.SP_KEY_FAMILY_ID, familyId);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String getFamilyId(Context context) {
        try {
            return ConfigFactory.get(context, Constants.SP_NAME_SMARTHOME).get(Constants.SP_KEY_FAMILY_ID, "");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 设置本机设备id，用于判断过滤本机设备不显示的逻辑
     *
     * @param context
     * @param screenId
     */
    public static void setScreenId(Context context, String screenId) {
        try {
            ConfigFactory.get(context, Constants.SP_NAME_SMARTHOME).put(Constants.SP_KEY_SCREEN_ID, screenId);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取本机设备id
     *
     * @param context
     * @return
     */
    public static String getScreenId(Context context) {
        try {
            return ConfigFactory.get(context, Constants.SP_NAME_SMARTHOME).get(Constants.SP_KEY_SCREEN_ID, "");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    /******************************************************************** 主页版面数据缓存 ************************************************************************************/
    private final static String SP_NAME_MAIN_CACHE = "main_cache_data";

    /**
     * 缓存主页导航tags数据
     *
     * @param context
     * @param homeTags json串
     */
    public static void setHomeTags(Context context, String homeTags) {
        try {
            ConfigFactory.get(context, SP_NAME_MAIN_CACHE).put("home_tag", homeTags);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取当前缓存的主页导航tags数据
     *
     * @param context
     * @return
     */
    public static String getHomeTags(Context context) {
        try {
            return ConfigFactory.get(context, SP_NAME_MAIN_CACHE).get("home_tag", "");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 缓存版面数据，tagId为key
     *
     * @param context
     * @param tagId
     * @param data
     */
    public static void setMainData(Context context, String tagId, String data) {
        try {
            ConfigFactory.get(context, SP_NAME_MAIN_CACHE).put(tagId, data);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据tagId获取版面数据
     *
     * @param context
     * @param tagId
     * @return
     */
    public static String getMainData(Context context, String tagId) {
        try {
            return ConfigFactory.get(context, SP_NAME_MAIN_CACHE).get(tagId, "");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }
}
