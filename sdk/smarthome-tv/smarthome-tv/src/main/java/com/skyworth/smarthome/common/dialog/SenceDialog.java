package com.skyworth.smarthome.common.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.view.Gravity;
import android.view.Window;
import android.view.WindowManager;


import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.util.ViewsBuilder;

/**
 * @ProjectName: NewTV_SmartHome
 * @Package: com.skyworth.smarthome_tv.common.dialog
 * @ClassName: UnBindDialog
 * @Description: java类作用描述
 * @Author: wangyuehui
 * @CreateDate: 2020/6/9 17:24
 * @UpdateUser: 更新者
 * @UpdateDate: 2020/6/9 17:24
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class SenceDialog extends Dialog {

    public SenceDialog(@NonNull Context context) {
        this(context, R.style.common_style);
    }

    public SenceDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    public static SenceDialog newInstance(Context context) {

        SenceDialog fragment = new SenceDialog(context, R.style.common_style);
        return fragment;
    }


    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(ViewsBuilder.getGuideSence(getContext()));

    }

    @Override
    public void onStart() {
        super.onStart();
        Window window = getWindow();
        WindowManager.LayoutParams windowParams = window.getAttributes();
        windowParams.width = WindowManager.LayoutParams.MATCH_PARENT;
        windowParams.height = WindowManager.LayoutParams.MATCH_PARENT;
        windowParams.gravity = Gravity.CENTER;
        windowParams.dimAmount = 0.50f;
        windowParams.flags |= WindowManager.LayoutParams.FLAG_DIM_BEHIND;
        window.setAttributes(windowParams);
    }

}
