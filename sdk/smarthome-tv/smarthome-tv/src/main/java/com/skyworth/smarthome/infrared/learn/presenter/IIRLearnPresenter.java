package com.skyworth.smarthome.infrared.learn.presenter;

import android.view.KeyEvent;

import com.skyworth.smarthome.infrared.learn.view.IIRLearnView;
import com.skyworth.smarthome.common.base.IPresenter;
import com.skyworth.smarthome.infrared.learn.model.IIRLearnModel;

import java.util.Map;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/7/2 14:43.
 */
public interface IIRLearnPresenter extends IPresenter<IIRLearnView, IIRLearnModel> {
    void setParams(Map<String, String> params);

    void iAmReady();

    void retry();

    void next();

    void exit();

    boolean loadLearnList();

    boolean createIRChildDevice();

    void showReady();

    void showLearnStart();

    void showLearning();

    boolean startLearnStatus();

    void startTimeOutCheck();

    void stopTimeOutTimer();

    void showLearnFail();

    void showLearnFinish();

    void showLearnSuccess();

    boolean saveLearntKey(int[] code);

    boolean isLearnFinish();

    void showLoading();

    void hideLoading();

    void showError(String msg);

    boolean handleKeyEvent(KeyEvent event);
}
