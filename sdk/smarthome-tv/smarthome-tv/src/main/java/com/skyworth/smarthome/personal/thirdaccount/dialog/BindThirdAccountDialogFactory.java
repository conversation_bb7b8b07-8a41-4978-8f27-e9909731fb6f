package com.skyworth.smarthome.personal.thirdaccount.dialog;

import android.content.Context;

import com.skyworth.smarthome.personal.thirdaccount.dialog.jd.BindJDAccDialog;
import com.skyworth.smarthome.personal.thirdaccount.dialog.third.BindThridAccDialog;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.model.AppConstants;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/6/5 15:24.
 */
public class BindThirdAccountDialogFactory {
    public static final IBindThirdAccountDialog create(String type, Context context) {
        if (AppConstants.SUPPORT_ACCOUNT_TYPE.SMART_JD.toString().equals(type)) {
            return new BindJDAccDialog(context, R.style.transparent_dialog);
        } else {
            return new BindThridAccDialog(context, R.style.transparent_dialog);
        }
    }
}
