package com.skyworth.smarthome.service;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;

import com.alibaba.fastjson.JSONObject;
import com.skyworth.smarthome.common.event.AccountChangeEvent;
import com.skyworth.smarthome.common.event.AodDeviceInfoChangeEvent;
import com.skyworth.smarthome.common.event.ApConfigEvent;
import com.skyworth.smarthome.common.event.DeviceInfoUptateEvent;
import com.skyworth.smarthome.common.event.DeviceNameChangeEvent;
import com.skyworth.smarthome.common.event.DevicesStatusChangeEvent;
import com.skyworth.smarthome.common.event.DiscoverDeviceEvent;
import com.skyworth.smarthome.common.event.DiscoverNearDeviceEvent;
import com.skyworth.smarthome.common.event.ScanWifiDeviceEvent;
import com.skyworth.smarthome.common.event.StartDiscoverNearDeviceEvent;
import com.skyworth.smarthome.common.event.UpdateNearbyDeviceListEvent;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.model.SPCacheData;
import com.skyworth.smarthome.common.util.DataCacheUtil;
import com.skyworth.smarthome.common.util.DialogLauncherUtil;
import com.skyworth.smarthome.common.util.LogUtil;
import com.skyworth.smarthome.common.util.SystemProperty;
import com.skyworth.smarthome.common.util.ThreadManager;
import com.skyworth.smarthome.common.util.Utils;
import com.skyworth.smarthome.devices.apconfig.model.StartApConfigModel;
import com.skyworth.smarthome.devices.discover.model.DiscoverDeviceModel;
import com.skyworth.smarthome.service.binder.ConnectPoolBinder;
import com.skyworth.smarthome.service.binder.SmartHomeAodPushBinder;
import com.skyworth.smarthome.service.model.IAIOTModel;
import com.skyworth.smarthome.service.model.IFunctionGoToModel;
import com.skyworth.smarthome.service.model.IReportTvStatusModel;
import com.skyworth.smarthome.service.model.ISmartDeviceDataModel;
import com.skyworth.smarthome.service.model.ISmartHomeModel;
import com.skyworth.smarthome.service.model.ReportTvStatusModel;
import com.skyworth.smarthome.service.push.binder.AIOTPushServiceCallbackBinder;
import com.skyworth.smarthome.service.push.local.DeviceDataPushUtil;
import com.skyworth.smarthome.voicehandle.model.VoiceHandleReceiverModel;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.account.IAppAccountManager;
import com.smarthome.common.dataer.DataHelpInfo;
import com.smarthome.common.dataer.LogSDK;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XToast;
import com.swaiot.aiotlib.AiotLibSDK;
import com.swaiot.aiotlib.common.entity.DiscoverNetworkDevice;
import com.swaiot.aiotlib.common.entity.DiscoverWifiDevice;
import com.swaiot.aiotlib.service.keep.KeepAliveHelperService;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.HashMap;




/**
 * Describe:智慧家庭服务
 * Created by AwenZeng on 2019/1/30
 */
public class SmartHomeService extends Service {
    private ConnectPoolBinder mConnectPoolBinder;
    private StartApConfigModel mStartApConfigModel;
    private DiscoverDeviceModel mDiscoverDeviceModel;
    private IReportTvStatusModel mReportTvStatusModel;
    private SmartHomeAodPushBinder mSmartHomeAodPushBinder;
    private VoiceHandleReceiverModel mVoiceHandleReceiverModel;
    private AIOTPushServiceCallbackBinder mAiotPushServiceCallbackBinder;
    private boolean isStarted = false;

    @Override
    public void onCreate() {
        init();
    }

    private void init() {
        LogUtil.androidLog("SmartHomeService初始化");
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
        KeepAliveHelperService.keep(this, 100);
        IAppAccountManager.INSTANCE.registerAccountReceiver();
        IAppAccountManager.INSTANCE.getAccountInfo(false);
        mStartApConfigModel = new StartApConfigModel(this);
        mDiscoverDeviceModel = new DiscoverDeviceModel();
        mConnectPoolBinder = new ConnectPoolBinder();
        mSmartHomeAodPushBinder = mConnectPoolBinder.getSmartHomeAodPushBinder();
        mAiotPushServiceCallbackBinder = new AIOTPushServiceCallbackBinder();
        mReportTvStatusModel = new ReportTvStatusModel(this);
        mVoiceHandleReceiverModel = new VoiceHandleReceiverModel(this);
        mVoiceHandleReceiverModel.registerVoiceHandleReceiver();
        mReportTvStatusModel.startReport();
        bindAiotServcie();
        submitServiceStart();
        IAIOTModel.INSTANCE.getAppConfig();
    }

    private void submitServiceStart() {
        LogSDK.submit(LogSDK.EVENT_SERVICE_START, new HashMap<String, String>());
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        return START_STICKY;
    }

    @Override
    public IBinder onBind(Intent intent) {
        return mConnectPoolBinder;
    }

    private void bindAiotServcie() {
        if (isStarted) {
            return;
        }
        AiotLibSDK.getDefault().init(SmartHomeTvLib.getContext(), AiotLibSDK.Platform.TV, new AiotLibSDK.InitListener() {
            @Override
            public void success() {
                isStarted = true;
                LogUtil.androidLog("bindService success.");
                AppData.getInstance().setServiceInit(true);
                AiotLibSDK.getDefault().setReceiveListener(mAiotPushServiceCallbackBinder);
                AiotLibSDK.getDefault().getAccountImp().onInitAccountInfo(JSONObject.toJSONString(IAppAccountManager.INSTANCE.getAccountInfo(false)));
                ThreadManager.getInstance().uiThread(new Runnable() {
                    @Override
                    public void run() {
                        ISmartHomeModel.INSTANCE.getSmartDeviceList(SPCacheData.getFamilyId(SmartHomeTvLib.getContext()));
                        ISmartHomeModel.INSTANCE.getFamilyList();
                        ISmartHomeModel.INSTANCE.getSceneList();
                        EventBus.getDefault().post(new DeviceNameChangeEvent());
                    }
                }, 1500);

            }

            @Override
            public void fail() {
                LogUtil.androidLog("bindService fail.");
            }
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void Event(AccountChangeEvent event) {
        LogUtil.androidLog("AccountChangeEvent~~~~~");
        AiotLibSDK.getDefault().getAccountImp().onAccountChange(IAppAccountManager.INSTANCE.hasLogin(false), JSONObject.toJSONString(IAppAccountManager.INSTANCE.getAccountInfo(false)));
        if (IAppAccountManager.INSTANCE.hasLogin(false)) {
            String apconfigDeviceInfo = AppData.getInstance().getApconfigDeviceInfo();
            if (EmptyUtils.isNotEmpty(apconfigDeviceInfo)) {
                DiscoverNetworkDevice discoverNetworkDevice = JSONObject.parseObject(apconfigDeviceInfo, DiscoverNetworkDevice.class);
                DiscoverWifiDevice discoverWifiDevice = AppData.getInstance().getApconfigWifiDevice();
                if (EmptyUtils.isNotEmpty(discoverWifiDevice)) {
                    discoverNetworkDevice.device_name = discoverWifiDevice.getDeviceDetail().getProduct();
                    IFunctionGoToModel.INSTANCE.goToDeviceBind(discoverNetworkDevice);
                }
                AppData.getInstance().setApconfigDeviceInfo("");
                AppData.getInstance().setApconfigWifiDevice(null);
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void Event(AodDeviceInfoChangeEvent event) {
        LogUtil.androidLog("EventBus-AodDeviceInfoChangeEvent");
        if (SystemProperty.isAodDevice()) {
            mSmartHomeAodPushBinder.sendDeviceChangeMsg(event.event, event.data);
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void Event(ScanWifiDeviceEvent event) {
        LogUtil.androidLog("ScanWifiDeviceEvent:" + event.isStartScan);
        if (event.isStartScan) {
            AiotLibSDK.getDefault().getDeviceImpl().startDiscoverWifiDevice();
        } else {
            AiotLibSDK.getDefault().getDeviceImpl().stopDiscoverWifiDevice();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void Event(StartDiscoverNearDeviceEvent event) {
        LogUtil.androidLog("是否开始附近已配网设备扫描：" + event.getData());
        if ((boolean) event.getData()) {
            AiotLibSDK.getDefault().getDeviceImpl().startDiscoverNetworkDevice();
        } else {
            AiotLibSDK.getDefault().getDeviceImpl().stopDiscoverNetworkDevice();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void Event(DeviceInfoUptateEvent event) {
        LogUtil.androidLog("设备信息更新推送");
        DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.DEVICE_LIST, "");
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void Event(DevicesStatusChangeEvent event) {
        LogUtil.androidLog("DevicesStatusChangeEvent");
        DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.DEVICE_STATUS, event.getTypeString());
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void Event(DeviceNameChangeEvent event) {
        LogUtil.androidLog("Check TV deviceName");
        if (IAppAccountManager.INSTANCE.hasLogin(false)) {
            ISmartHomeModel.INSTANCE.updateSelfTvDeviceInfo();
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void Event(DiscoverDeviceEvent event) {
        LogUtil.androidLog("发现设备");
        if (EmptyUtils.isNotEmpty(event.getTypeString()) && !SystemProperty.isAodDevice()) {
            mStartApConfigModel.startApConfig(event.getTypeString());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void Event(ApConfigEvent event) {
        LogUtil.androidLog("配网返回");
        if (EmptyUtils.isNotEmpty(event.getTypeString())) {
            mDiscoverDeviceModel.handleApConfigExit(event.getTypeString());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void Event(DiscoverNearDeviceEvent event) {
        LogUtil.androidLog("发现已配网未绑定设备");
        if (!SystemProperty.isAodDevice()) {
            if (EmptyUtils.isEmpty(AppData.getInstance().getDiscoverUnConfigNetDeviceList()) && EmptyUtils.isEmpty(AppData.getInstance().getDiscoverUnbindDeviceList())) {
                DialogLauncherUtil.dismissDialog(DialogLauncherUtil.DIALOG_KEY_ADDDEVICE);
                Utils.speech(SmartHomeTvLib.getContext(), SmartHomeTvLib.getContext().getString(R.string.discover_devices_voice_tip));
                XToast.showToast(SmartHomeTvLib.getContext(), SmartHomeTvLib.getContext().getString(R.string.not_have_no_apconfig_device));
            } else {
                DialogLauncherUtil.showDiscoverNearDevices(null);
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void Event(UpdateNearbyDeviceListEvent event) {
        LogUtil.androidLog("更新附近的设备数据");
        ISmartDeviceDataModel.INSTANCE.addNearbyDeviceToDeviceList();
    }


    @Override
    public void onDestroy() {
        LogUtil.androidLog("SmartHomeService onDestroy");
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        KeepAliveHelperService.stopForeground(this);
        AppData.getInstance().setServiceInit(false);
        AiotLibSDK.getDefault().destroy();
        mReportTvStatusModel.clear();
        mVoiceHandleReceiverModel.unRegisterVoiceHandleReceiver();
        IAppAccountManager.INSTANCE.unRegisterAccountReceiver();
    }
}
