package com.skyworth.smarthome.personal.unbinddevice;

import android.content.Context;
import android.util.Log;

import com.skyworth.smarthome.common.base.mvp.BasePresenter;
import com.skyworth.smarthome.service.push.local.DeviceDataPushUtil;
import com.skyworth.smarthome.service.push.local.IHandlerPush;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.service.model.ISmartDeviceDataModel;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.EmptyUtils;

/**
 * @ProjectName: NewTV_SmartHome
 * @Package: com.skyworth.smarthome_tv.personal.unbinddevice
 * @ClassName: UnBindPresenter
 * @Description: java类作用描述
 * @Author: wangyuehui
 * @CreateDate: 2020/6/5 19:02
 * @UpdateUser: 更新者
 * @UpdateDate: 2020/6/5 19:02
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class UnBindPresenter extends BasePresenter<UnbindContract.View> implements UnbindContract.Presenter {
    private static final String TAG = UnBindPresenter.class.getSimpleName();
    private String mCurrentDeviceList = "";//给打补丁（相同数据同一时间会推多次，此处做一下拦截）
    private IHandlerPush.IPushListener iPushListener = new IHandlerPush.IPushListener() {

        @Override
        public void onArrive(AppConstants.SSE_PUSH event, String data) {
            Log.i(TAG, "PersonCenterPresenter iPushListener: ----------event:" + event);
            //数据变化的回调
            if (event == AppConstants.SSE_PUSH.DEVICE_LIST) {//设备列表变化
                if (EmptyUtils.isNotEmpty(mCurrentDeviceList) && EmptyUtils.isNotEmpty(data) && mCurrentDeviceList.equals(data)) {
                    CCLog.i("PersonCenterPresenter", "onArrive: event:" + event + " -- same data, return...");
                    return;
                }
                mCurrentDeviceList = data;
                if (getView() != null && getView().isActive()) {
                    getView().queryBindDevices(ISmartDeviceDataModel.INSTANCE.getCacheSmartDeviceList());
                }
            }
        }
    };

    public UnBindPresenter(UnbindContract.View view) {
        attachView(view);
        view.setPresenter(this);
    }

    @Override
    public void init(Context context) {
        //获取设备列表
        if (getView() != null && getView().isActive()) {
            getView().queryBindDevices(ISmartDeviceDataModel.INSTANCE.getCacheSmartDeviceList());
        }
        if (iPushListener != null) {
            DeviceDataPushUtil.getPush().regReceiver(iPushListener);
        }
    }

    @Override
    public void detachView() {
        super.detachView();
        if (iPushListener != null) {
            DeviceDataPushUtil.getPush().unRegReceiver(iPushListener);
        }

    }
}
