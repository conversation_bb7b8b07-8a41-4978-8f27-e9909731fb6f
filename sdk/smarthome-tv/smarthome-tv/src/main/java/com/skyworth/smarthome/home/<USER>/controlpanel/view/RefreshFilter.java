package com.skyworth.smarthome.home.smartdevice.controlpanel.view;

import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.util.Log;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/11/19 15:47.
 */
public class RefreshFilter {
    private static final int REQUEST_MAX_REMAIN_TIME = 2000;
    private static byte[] lock = new byte[0];
    private List<ControlCommand> controlCommands = new ArrayList<>();
    private static final String TAG = "RefreshFilter";
    private Handler mHandler = null;
    private Runnable commandDeleter = new Runnable() {
        @Override
        public void run() {
            int size = controlCommands.size();
            Log.i(TAG, "delete size: " + size);
            if (size <= 0) {
                return;
            }
            synchronized (lock){
                long current = SystemClock.uptimeMillis();
                Iterator<ControlCommand> iterator = controlCommands.iterator();
                ControlCommand next = null;
                while (iterator.hasNext()) {
                    next = iterator.next();
                    if (current - next.time > REQUEST_MAX_REMAIN_TIME) {
                        Log.i(TAG, "delete: " + next);
                        iterator.remove();
                    }
                }
            }
            if (!controlCommands.isEmpty()) {
                mHandler.postDelayed(this, 500);
            }
        }
    };

    public static class ControlCommand {
        private String key;
        private Object value;
        private long time;

        public ControlCommand(String key, Object value) {
            this.key = key;
            this.value = value;
            time = SystemClock.uptimeMillis();
        }

        public long getTime() {
            return time;
        }

        @Override
        public String toString() {
            return "key: " + key + " value: " + value + " time: " + time;
        }

        @Override
        public boolean equals(Object o) {
            if (o == null) {
                return false;
            }
            if (!(o instanceof ControlCommand)) {
                return false;
            }
            return RefreshFilter.equals(key, ((ControlCommand) o).key) && RefreshFilter.equals(value, ((ControlCommand) o).value);
        }
    }

    public RefreshFilter() {
        mHandler = new Handler(Looper.getMainLooper());
    }

    public static boolean equals(Object a, Object b) {
        return (a == null) ? (b == null) : a.equals(b);
    }

    public void addNewControlCommand(ControlCommand controlCommand) {
        Log.i(TAG, "addNewControlCommand: " + controlCommand);
        controlCommands.add(controlCommand);
        startDelete();
    }

    public boolean needRefresh(List<ControlCommand> newCommands) {
        if (newCommands == null || newCommands.isEmpty()) {
            return false;
        }
        boolean needRefresh = true;
        for (ControlCommand newCommand : newCommands) {
            if (controlCommands.contains(newCommand)) {
                Log.i(TAG, "needRefresh: remove" + newCommand);
                int index = controlCommands.indexOf(newCommand);
                if (index >= 0) {
                    controlCommands.remove(index);
                }
                needRefresh = false;
            }
        }
        Log.i(TAG, "needRefresh: " + needRefresh);
        return needRefresh;
    }

    private void startDelete() {
        Log.i(TAG, "startDelete");
        mHandler.removeCallbacks(commandDeleter);
        mHandler.post(commandDeleter);
    }

    private void stopDelete() {
        Log.i(TAG, "stopDelete");
        mHandler.removeCallbacks(commandDeleter);
    }

    public void destroy() {
        controlCommands.clear();
        stopDelete();
    }
}
