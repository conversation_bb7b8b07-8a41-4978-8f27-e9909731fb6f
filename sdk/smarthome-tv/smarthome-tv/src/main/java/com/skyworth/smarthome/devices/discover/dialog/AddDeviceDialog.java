package com.skyworth.smarthome.devices.discover.dialog;

import android.content.DialogInterface;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.skyworth.smarthome.common.base.BaseSysDialog;
import com.skyworth.smarthome.common.event.DiscoverNearDeviceEvent;
import com.skyworth.smarthome.common.event.ScanWifiDeviceEvent;
import com.skyworth.smarthome.common.event.StartDiscoverNearDeviceEvent;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.devices.discover.view.NearbyDeviceView;
import com.smarthome.common.utils.EmptyUtils;

import org.greenrobot.eventbus.EventBus;

import java.util.Map;

import static com.skyworth.smarthome.common.util.DialogLauncherUtil.DIALOG_KEY_ADDDEVICE;

/**
 * Description: 添加发现设备 <br>
 * Created by wzh on 2019/4/9 15:10.
 */
public class AddDeviceDialog extends BaseSysDialog {

    private NearbyDeviceView mView;

    private MsgHandler mMsgHandler;

    public static final int SCAN_TIME = 30 * 1000;//扫描时间为30秒

    public AddDeviceDialog() {
        super();
    }

    @Override
    protected void initParams() {
        mDialogKey = DIALOG_KEY_ADDDEVICE;
    }

    @Override
    public void showDialog(Map<String, String> params) {
        super.showDialog(params);
        mMsgHandler = new MsgHandler(mContext.getMainLooper());
        mMsgHandler.sendMessageDelayed(new Message(), SCAN_TIME);
        sendStartDiscoverNearDeviceEvent(true);
        EventBus.getDefault().post(new ScanWifiDeviceEvent(true));
        clearCache();
        AppData.getInstance().setStartScanDevice(true);
    }

    @Override
    protected void initContentView() {
        super.initContentView();
        mView = new NearbyDeviceView(mContext);
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.gravity = Gravity.CENTER_HORIZONTAL | Gravity.CENTER_VERTICAL;
        mContentView.addView(mView, params);

        //触摸弹框之外的部分关掉弹窗逻辑处理
        mView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });

        //主动退出的时候，不需要再次弹出扫描二维码结果弹框
        setOnKeyListener(new OnKeyListener() {
            @Override
            public boolean onKey(DialogInterface dialogInterface, int i, KeyEvent keyEvent) {
                if (keyEvent.getAction() == KeyEvent.ACTION_DOWN) {
                    if (keyEvent.getKeyCode() == KeyEvent.KEYCODE_BACK || keyEvent.getKeyCode() == KeyEvent.KEYCODE_HOME) {
                        sendStartDiscoverNearDeviceEvent(false);
                        AppData.getInstance().setStartScanDevice(false);
                        EventBus.getDefault().post(new ScanWifiDeviceEvent(false));
                        return false;
                    }
                }
                return false;
            }
        });
    }

    /**
     * 清理掉智能设备的控制缓存及附近已配网设备的缓存
     */
    private void clearCache() {
        //清理掉缓存中的数据
        AppData.getInstance().setDiscoverUnConfigNetDeviceList(null);
        AppData.getInstance().setDiscoverUnbindDeviceList(null);
        AppData.getInstance().setDiscoverNetworkDevice(null);
//        DataHelpInfo.getInstance().setDiscoverDeviceOrigin("手动点击发现设备按钮");
    }

    /**
     * 发送开始搜索附近的设备控制
     *
     * @param isStartDiscover
     */
    private void sendStartDiscoverNearDeviceEvent(boolean isStartDiscover) {
        StartDiscoverNearDeviceEvent event = new StartDiscoverNearDeviceEvent();
        event.setData(isStartDiscover);
        EventBus.getDefault().post(event);
    }

    private class MsgHandler extends Handler {

        public MsgHandler(Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(Message msg) {
            EventBus.getDefault().post(new DiscoverNearDeviceEvent());
            sendStartDiscoverNearDeviceEvent(false);
            EventBus.getDefault().post(new ScanWifiDeviceEvent(false));
            dismiss();
        }
    }

    @Override
    protected void onDismiss() {
        if (EmptyUtils.isNotEmpty(mMsgHandler)) {
            mMsgHandler.removeCallbacksAndMessages(null);
        }
        if (mView != null)
            mView.destroy();
        super.onDismiss();
    }
}
