package com.skyworth.smarthome.service.push.binder.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.common.event.OtherAccountAuthResultEvent;
import com.skyworth.smarthome.home.smartdevice.controlpanel.thirdapp.ThirdAppLaunchManager;

import org.greenrobot.eventbus.EventBus;

/**
 * Describe:其他推送Model
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/5/13
 */
public class OtherPushModel implements IOtherPushModel {

    public static final String ACOUNT_TYPE_OTHER_AUTH = "other";

    public static final String ACOUNT_TYPE_JD = "jd";


    @Override
    public void otherAccountLoginSucceed(boolean success) {
        sendAuthResultEvent(ACOUNT_TYPE_OTHER_AUTH, success);
    }

    @Override
    public void jdAccountLoginSucceed() {
        sendAuthResultEvent(ACOUNT_TYPE_JD, true);
    }

    @Override
    public void jdAccountLoginFailed() {
        sendAuthResultEvent(ACOUNT_TYPE_JD, false);
    }


    @Override
    public void startOtherApp(String data) {
        try{
            JSONObject jsonObject = JSON.parseObject(data);
            String pkgName = jsonObject.getString("app_package");
            String startParam = jsonObject.getString("start_param");
            ThirdAppLaunchManager.getInstance().handleStartThirdApp(SmartHomeTvLib.getContext(),pkgName,startParam);
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    @Override
    public void notifyOtherApp(String data) {
        try{
            JSONObject jsonObject = JSON.parseObject(data);
            String pkgName = jsonObject.getString("app_package");
            String startParam = jsonObject.getString("message_content");
            ThirdAppLaunchManager.getInstance().handleStartThirdApp(SmartHomeTvLib.getContext(),pkgName,startParam);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * 推送账号授权事件
     *
     * @param type
     * @param result
     */
    private void sendAuthResultEvent(String type, boolean result) {
        OtherAccountAuthResultEvent event = new OtherAccountAuthResultEvent();
        event.setAuthAccount(type);
        event.setResult(result);
        EventBus.getDefault().post(event);
    }
}
