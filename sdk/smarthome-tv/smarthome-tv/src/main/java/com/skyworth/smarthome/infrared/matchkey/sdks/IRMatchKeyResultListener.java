package com.skyworth.smarthome.infrared.matchkey.sdks;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/9/2 16:13.
 */
public interface IRMatchKeyResultListener {
    /**
     * 匹配成功，返回遥控器id
     *
     * @param remoteId
     */
    void onMatchSuccess(String remoteId);

    /**
     * 匹配失败，没有符合的遥控器
     */
    void onMatchFail();

    /**
     * 测试一个新的按键
     */
    void onMatchNextKey();

    /**
     * 匹配出错
     *
     * @param msg 出错原因
     */
    void onError(String msg);
}
