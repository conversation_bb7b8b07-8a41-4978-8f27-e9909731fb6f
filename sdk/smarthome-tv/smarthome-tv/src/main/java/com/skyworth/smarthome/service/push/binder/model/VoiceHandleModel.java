package com.skyworth.smarthome.service.push.binder.model;

import android.content.Intent;

import com.alibaba.fastjson.JSONObject;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.bean.MessageBean;
import com.skyworth.smarthome.common.bean.VoiceCommandPushData;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.util.ThreadManager;
import com.skyworth.smarthome.message.MessageDialog;
import com.skyworth.smarthome.service.model.IAIOTModel;
import com.skyworth.smarthome.service.model.ISecondScreenModel;
import com.skyworth.smarthome.service.model.ISmartDeviceDataModel;
import com.smarthome.common.dataer.LogSDK;
import com.smarthome.common.utils.Constants;
import com.smarthome.common.utils.EmptyUtils;
import com.swaiot.aiotlib.common.entity.SceneBean;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: VoiceHandleModel
 * @Author: AwenZeng
 * @CreateDate: 2020/5/13 17:29
 * @Description: 语音处理
 */
public class VoiceHandleModel implements IVoiceHandleModel {

    @Override
    public void handleVoiceCommand(String data) {
        VoiceCommandPushData voiceCommandPushData = JSONObject.parseObject(data, VoiceCommandPushData.class);
        ISecondScreenModel.INSTANCE.sendVoiceMsg(voiceCommandPushData);
        MessageBean messageBean = new MessageBean();
        switch (voiceCommandPushData.command_type) {
            case AppConstants.VOICE_COMMAND_CONTROL_DEVICE:
                DeviceInfo deviceBean = ISmartDeviceDataModel.INSTANCE.getSmartDeviceInfo(voiceCommandPushData.command_data.device_id);
                messageBean.type = AppConstants.MESSAGE_TYPE.DEVICE_VOICE_CONTROL_RESULT;
                if(EmptyUtils.isNotEmpty(deviceBean)){
                    if(deviceBean.is_new){
                        IAIOTModel.INSTANCE.cancelNewDeviceMark(deviceBean.device_id,null);
                    }
                    messageBean.imgUrl = deviceBean.device_icon;
                    messageBean.location = deviceBean.device_position;
                    messageBean.voiceTips = deviceBean.voice_tips;
                    reportVoiceControlDeviceData(deviceBean,voiceCommandPushData.control_type,voiceCommandPushData.command_data.result_code<100?"fail":"success");
                }
                messageBean.content = voiceCommandPushData.command_data.result_tips;
                showMessageDialog(messageBean);
                break;
            case AppConstants.VOICE_COMMAND_EXECUTE_SCENE:
                messageBean.type = AppConstants.MESSAGE_TYPE.SCENE_VOICE_CONTROL_RESULT;
                messageBean.content = voiceCommandPushData.command_data.result_tips;
                showMessageDialog(messageBean);
                SceneBean sceneBean = ISmartDeviceDataModel.INSTANCE.getSceneInfo(voiceCommandPushData.command_data.scene_id);
                if(sceneBean.is_new){
                    IAIOTModel.INSTANCE.cancelNewSceneMark(sceneBean.scene_id,null);
                }
                break;
            case AppConstants.VOICE_COMMAND_QUERY_DEVICE_LIST:
                Intent intent = new Intent("com.smarthome.action.HOME");
                intent.putExtra(Constants.KEY_DEVICE_ID, "null");
                SmartHomeTvLib.getContext().startActivity(intent);
                break;
            case AppConstants.VOICE_COMMAND_CONTROL_MULTI_DEVICE:
                break;
            case AppConstants.VOICE_COMMAND_OTHER:
                break;
        }
    }

    public void showMessageDialog(final MessageBean messageBean){
        ThreadManager.getInstance().uiThread(new Runnable() {
            @Override
            public void run() {
                MessageDialog dialog = new MessageDialog();
                dialog.showDialog(messageBean);
            }
        });
    }

    /**
     * 上报语音控制设备大数据
     * @param deviceInfo
     * @param controlContent
     * @param result
     */
    private void reportVoiceControlDeviceData(DeviceInfo deviceInfo, String controlContent, String result){
        try {
            if(EmptyUtils.isEmpty(deviceInfo))
                return;
            //数据统计
            Map<String, String> map = new HashMap<>();
            map.put("voice_control","语音控制");
            map.put("control_detail", controlContent);
            map.put("device_id",deviceInfo.device_id);
            map.put("device_brand",deviceInfo.product_brand);
            map.put("device_name",deviceInfo.device_name);
            map.put("control_result",result);
            map.put("if_infrared",deviceInfo.is_infrared?"yes":"no");
            LogSDK.submit(LogSDK.EVENT_ID_DEVICE_CONTROL,map);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
