package com.skyworth.smarthome.home.smartdevice.devicelist;

import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.widget.PopupWindow;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.ui.VoiceTipsView;
import com.skyworth.smarthome.common.util.ThreadManager;
import com.skyworth.util.Util;
import com.smarthome.common.utils.EmptyUtils;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/17
 */
public class DeviceItemView extends BaseDeviceItemView {

    private PopupWindow mPopupWindow;
    private int mPosition;
    private VoiceTipsView mVoiceTipsView;
    private UiRunnable uiRunnable;

    public DeviceItemView(Context context) {
        super(context);
        initPopWindow();
    }

    @Override
    public void refreshUI(DeviceInfo data, int position) {
        super.refreshUI(data,position);
        mPosition = position;
    }

    @Override
    public void onFocusChange(View view, boolean hasFocus) {
        super.onFocusChange(view, hasFocus);
        //增加语音提示
        if(EmptyUtils.isNotEmpty(mData.voice_tips)&&mData.online_status == 1){
            showPopWindow(view,hasFocus);
        }else{
            if (mPopupWindow.isShowing()) {
                mPopupWindow.dismiss();
            }
        }
    }

    private void initPopWindow(){
        if (mPopupWindow == null) {
            mVoiceTipsView = new VoiceTipsView(getContext());
            mPopupWindow = new PopupWindow(mVoiceTipsView, Util.Div(356), Util.Div(60));
            mPopupWindow.setOutsideTouchable(false);
        }
        if (mPopupWindow.isShowing()) {
            mPopupWindow.dismiss();
        }
    }

    private void showPopWindow(final View view,boolean hasFocus){
        if(hasFocus){
            uiRunnable = new UiRunnable(view);
            ThreadManager.getInstance().uiThread(uiRunnable,350);
        }else{
            if(EmptyUtils.isNotEmpty(mVoiceTipsView)){
                mVoiceTipsView.stopFlipping();
            }
            if(EmptyUtils.isNotEmpty(mPopupWindow)){
                mPopupWindow.dismiss();
            }
            if(EmptyUtils.isNotEmpty(uiRunnable)){
                ThreadManager.getInstance().removeUiThread(uiRunnable);
                uiRunnable = null;
            }
        }
    }

    class UiRunnable implements Runnable{

        View view = null;
        public UiRunnable(View _view) {
            view = _view;
        }

        @Override
        public void run() {
            if(mPopupWindow.isShowing())
                return;
            mVoiceTipsView.startWithList(mData.voice_tips);
            int width = mPopupWindow.getWidth();
            int[] xy = new int[2];
            view.getLocationInWindow(xy);
            if(mPosition != 0){
                mPopupWindow.showAtLocation(view, Gravity.NO_GRAVITY,
                        xy[0] + Util.Div(13) + (view.getWidth() - width) / 2, xy[1] - Util.Div(75));
            }else{
                mPopupWindow.showAtLocation(view, Gravity.NO_GRAVITY,
                        xy[0]  + Util.Div(13) + (view.getWidth() - width) / 2, xy[1] + view.getHeight() + Util.Div(35));
            }
            mPopupWindow.setAnimationStyle(R.style.popupWindowAnim);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mPopupWindow.isShowing()) {
            mPopupWindow.dismiss();
        }
    }
}
