package com.skyworth.smarthome.personal.thirdaccount.dialog.jd;

import android.content.Context;
import android.content.DialogInterface;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.skyworth.smarthome.common.base.BaseCommonDialog;
import com.skyworth.smarthome.common.bean.ThridAccountHttpBean;
import com.skyworth.smarthome.personal.thirdaccount.dialog.AccountQrCodeView;
import com.skyworth.smarthome.personal.thirdaccount.dialog.IBindThirdAccountDialog;
import com.skyworth.util.Util;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XToast;


/**
 * 绑定京东账号
 * Description: <br>
 * Created by XuZexiao on 2019/6/5 15:16.
 */
public class BindJDAccDialog extends BaseCommonDialog implements IBindThirdAccountDialog<BindJDAccDialog> {
    private BindJDAccPresenter mPresenter;
    private OnBindResultListener mListener;
    private boolean mBindSuccess = false;//是否绑定成功的返回值
    private AccountQrCodeView qrCodeView = null;

    public BindJDAccDialog(Context context, int themeResId) {
        super(context, themeResId);
        initUI();
    }

    @Override
    protected void initParams() {
//        mDialogKey = DIALOG_KEY_BIND_JD;
    }

    private void initUI() {
        FrameLayout mLayout = new FrameLayout(mContext);
        qrCodeView = new AccountQrCodeView(mContext);
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Util.Div(870), Util.Div(440));
        layoutParams.gravity = Gravity.CENTER;
        mLayout.addView(qrCodeView, layoutParams);
        mContentView.addView(mLayout, new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT));
        cancelDialogAnimation();
        setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                if (mPresenter != null) {
                    mPresenter.stopPolling();
                }
                if (mListener != null) {
                    mListener.result(mBindSuccess);
                }
                if (mPresenter != null) {
                    mPresenter.cancelGetJDQR();
                    mPresenter.stopWaitForQRExpire();
                }
            }
        });
        if (mPresenter == null) {
            initPresenter();
        }
        mPresenter.getJDQR();
    }

    private void initPresenter() {
        mPresenter = new BindJDAccPresenter(new OnBindResultListener() {
            @Override
            public void result(boolean result) {
                mBindSuccess = result;
                dismiss();
            }
        }, this, mContext);
    }

    @Override
    public BindJDAccDialog setParams(ThridAccountHttpBean data) {
        if (EmptyUtils.isNotEmpty(qrCodeView)) {
            qrCodeView.showQRcode(data.auth_url);
            qrCodeView.setData(data);
        }
        return this;
    }

    public void showQRCode(String qr) {
        if (qrCodeView != null) {
            qrCodeView.showQRcode(qr);
        }
    }

    public void post(Runnable runnable, long delay) {
        qrCodeView.postDelayed(runnable, delay);
    }

    public void cancelPost(Runnable runnable) {
        qrCodeView.removeCallbacks(runnable);
    }

    public void showToast(String txt) {
        XToast.showToast(mContext, txt);
    }

    @Override
    public BindJDAccDialog stopPolling() {
        if (mPresenter == null) {
            initPresenter();
        }
        if (mPresenter != null) {
            mPresenter.cancelGetJDQR();
            mPresenter.stopWaitForQRExpire();
        }
        mPresenter.stopPolling();
        return this;
    }

    @Override
    public BindJDAccDialog setOnBindResultListener(OnBindResultListener listener) {
        mListener = listener;
        return this;
    }

    @Override
    public void show() {
        super.show();
    }

}
