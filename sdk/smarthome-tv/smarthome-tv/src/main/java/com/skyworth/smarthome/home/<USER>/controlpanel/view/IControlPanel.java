package com.skyworth.smarthome.home.smartdevice.controlpanel.view;

import android.view.View;

import com.skyworth.smarthome.common.bean.DeviceInfo;

public interface IControlPanel {
    /**
     * 显示数据
     */
    void show(DeviceInfo deviceDetailData, boolean isLocal);

    View getView();

    /**
     * 设置操作回调
     *
     * @param listener
     */
    void setControlListener(IControlListener listener);

    /**
     * 获取当前显示的设备id
     *
     * @return
     */
    String getCurrentDeviceId();

    /**
     * 第一项落焦
     */
    void requestFocusAtFirstItem();

    void destroy();
}
