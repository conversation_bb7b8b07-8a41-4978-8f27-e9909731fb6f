package com.skyworth.smarthome.common.dialog.view;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import android.support.annotation.NonNull;

import com.coocaa.app.core.utils.FuncKt;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.ui.DialogBg;
import com.skyworth.smarthome.common.util.QRUtils;
import com.skyworth.util.Util;
import com.smarthome.common.utils.EmptyUtils;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * Description: AI在家  下载二维码View<br>
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/2/21 18:13.
 */
public class DownloadPhomeAppQrCodeView extends LinearLayout {

    private TextView mTitleV;
    private TextView mSubTitleV;
    private LinearLayout mQrCodeLayout;
    private ImageView mQrCodeView;
    private String mCurrentQrContent = "";

    public DownloadPhomeAppQrCodeView(@NonNull Context context) {
        super(context);
        setBackground(new DialogBg());

        setOrientation(VERTICAL);
        setGravity(Gravity.CENTER_HORIZONTAL);
        mTitleV = new TextView(context);
        mTitleV.setTextColor(context.getResources().getColor(R.color.white));
        mTitleV.setTextSize(Util.Dpi(44));
        mTitleV.getPaint().setFakeBoldText(true);
        mTitleV.setGravity(Gravity.CENTER_HORIZONTAL);
        mTitleV.setText(context.getString(R.string.scene_ai_download));
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.topMargin = Util.Div(70);
        addView(mTitleV, params);

        mSubTitleV = new TextView(context);
        mSubTitleV.setTextColor(context.getResources().getColor(R.color.white_60));
        mSubTitleV.setGravity(Gravity.CENTER_HORIZONTAL);
        mSubTitleV.setTextSize(Util.Dpi(32));
        mSubTitleV.setText(context.getString(R.string.scene_ai_more));
        params = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.topMargin = Util.Div(16);
        addView(mSubTitleV, params);

        mQrCodeLayout = new LinearLayout(context);
        mQrCodeLayout.setBackgroundColor(Color.WHITE);
        mQrCodeLayout.setGravity(Gravity.CENTER);
        params = new LayoutParams(Util.Div(320), Util.Div(320));
        params.topMargin = Util.Div(34);
        addView(mQrCodeLayout, params);

        mQrCodeView = new ImageView(context);
        params = new LayoutParams(Util.Div(300), Util.Div(300));
        mQrCodeLayout.setPadding(Util.Div(10),Util.Div(10),Util.Div(10),Util.Div(10));
        mQrCodeLayout.addView(mQrCodeView, params);
    }


    /**
     * 设置副标题
     * @param content
     */
    public void setTitile(String titile){
        if(EmptyUtils.isNotEmpty(titile)){
            mTitleV.setText(titile);
        }
    }

    /**
     * 设置副标题
     * @param content
     */
    public void setSubTitile(String subTitile){
        if(EmptyUtils.isNotEmpty(subTitile)){
            mSubTitleV.setText(subTitile);
        }
    }


    public void showQrCode(final String qrcontent, final int logoID) {
        if (!TextUtils.isEmpty(qrcontent)) {
            if (mCurrentQrContent.equals(qrcontent)) {
                return;
            }
            mCurrentQrContent = qrcontent;
            FuncKt.ioThread(new Function0<Unit>() {
                @Override
                public Unit invoke() {
                    try {
                        Bitmap logo = BitmapFactory.decodeResource(getResources(),logoID);
                        final Bitmap bitmap = QRUtils.createQRImage(qrcontent, Util.Div(400), Util.Div(400), logo);
                        if (bitmap != null) {
                            FuncKt.runOnUiThread(new Function0<Unit>() {
                                @Override
                                public Unit invoke() {
                                    mQrCodeView.setImageBitmap(bitmap);
                                    return Unit.INSTANCE;
                                }
                            });
                        } else Log.i("AIAtHomeQrCodeView", "-------showQrCode: bitmap is null");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    return Unit.INSTANCE;
                }
            });
        } else {
            mQrCodeView.setImageBitmap(null);
            Log.i("AIAtHomeQrCodeView", "-------showQrCode: QRContent is null");
        }
    }

}
