package com.skyworth.smarthome.devices.apconfig.view;

import android.content.Context;
import android.graphics.Color;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.ui.DialogBg;
import com.skyworth.ui.api.widget.SimpleFocusDrawable;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;
import com.smarthome.common.utils.EmptyUtils;


/**
 * Description:添加设备结果View
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/1/8 10:04.
 */
public class AddDeviceResultView extends LinearLayout implements View.OnClickListener {

    private Context mContext;
    private TextView mTitileTv;
    private View mIconImg;
    private TextView mTipsTv;
    private RelativeLayout mBottomLayout;
    private Button mOkBtn;
    private Button mCancelBtn;
    private ImageView mRecommendImg;
    private int mType;
    private HelpHandler mHandler;

    public static final int TYPE_DISCOVER = 0;//发现设备
    public static final int TYPE_DISCOVER_NOT_LOGIN = 1;//发现设备未登录
    public static final int TYPE_DISCOVER_DISAPPEAR = 2;//发现设备已消失
    public static final int TYPE_BIND_FAILED = 3;//绑定失败

    public static final String VIEW_TAG_OK = "ok";
    public static final String VIEW_TAG_CANCEL = "cancel";
    private SimpleFocusDrawable mOkFocusBg;
    private SimpleFocusDrawable mCancelFocusBg;

    private AddDeviceResultCallBack mAddDeviceResultCallBack;

    public interface AddDeviceResultCallBack {
        void onOkClick(int type);

        void onCancelClick(int type);

        void onHideView(int type);
    }

    private class HelpHandler extends Handler {

        public HelpHandler(Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(Message msg) {
            if (EmptyUtils.isNotEmpty(mAddDeviceResultCallBack)) {
                mAddDeviceResultCallBack.onHideView(mType);
            }
        }
    }


    public AddDeviceResultView(Context context) {
        super(context);
        mContext = context;
        initUI();
    }

    private void initUI() {
        setBackground(new DialogBg());
        setOrientation(LinearLayout.VERTICAL);

        mOkFocusBg = new SimpleFocusDrawable(getContext()).setRadius(Util.Div(10));
        mCancelFocusBg = new SimpleFocusDrawable(getContext()).setRadius(Util.Div(10));

        mTitileTv = new TextView(mContext);
        mTitileTv.setTextSize(Util.Dpi(36));
        mTitileTv.setTextColor(mContext.getResources().getColor(R.color.color_wihte));
        mTitileTv.getPaint().setFakeBoldText(true);
        mTitileTv.setGravity(Gravity.CENTER);
        LayoutParams params = new LayoutParams(Util.Div(614), ViewGroup.LayoutParams.WRAP_CONTENT);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        params.topMargin = Util.Div(80);
        addView(mTitileTv, params);

        mIconImg = ImageLoader.getLoader().getView(mContext);
        LayoutParams imgParams = new LayoutParams(Util.Div(110), Util.Div(110));
        imgParams.gravity = Gravity.CENTER_HORIZONTAL;
        imgParams.topMargin = Util.Div(15);
        addView(mIconImg, imgParams);


        mTipsTv = new TextView(mContext);
        mTipsTv.setTextSize(Util.Dpi(32));
        mTipsTv.setTextColor(Color.parseColor("#aaFFFFFF"));
        mTipsTv.setGravity(Gravity.CENTER);
        LayoutParams tipsParams = new LayoutParams(Util.Div(614), ViewGroup.LayoutParams.WRAP_CONTENT);
        tipsParams.gravity = Gravity.CENTER_HORIZONTAL;
        tipsParams.topMargin = Util.Div(20);
        addView(mTipsTv, tipsParams);

        mBottomLayout = new RelativeLayout(mContext);
        LayoutParams bottomParams = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, Util.Div(90));
        bottomParams.topMargin = Util.Div(20);
        bottomParams.gravity = Gravity.CENTER_HORIZONTAL;
        addView(mBottomLayout, bottomParams);


        LinearLayout bottomBtnLayout = new LinearLayout(mContext);
        bottomBtnLayout.setOrientation(HORIZONTAL);
        bottomBtnLayout.setGravity(Gravity.CENTER_HORIZONTAL);
        LayoutParams bottomBtnParams = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        mBottomLayout.addView(bottomBtnLayout, bottomBtnParams);

        mOkBtn = new Button(mContext);
        mOkBtn.setTextSize(Util.Dpi(32));
        mOkBtn.setPadding(0, 0, 0, 0);
        mOkBtn.setFocusable(true);
        mOkBtn.setTextColor(Color.parseColor("#000000"));
        mOkBtn.setBackground(mOkFocusBg);
        if (android.os.Build.VERSION.SDK_INT >= 21) {
            mOkBtn.setStateListAnimator(null);
        }
        LayoutParams okBtnParam = new LayoutParams(Util.Div(292), Util.Div(90));
        okBtnParam.gravity = Gravity.CENTER_HORIZONTAL;
        bottomBtnLayout.addView(mOkBtn, okBtnParam);

        mCancelBtn = new Button(mContext);
        mCancelBtn.setTextSize(Util.Dpi(32));
        mCancelBtn.setPadding(0, 0, 0, 0);
        mCancelBtn.setTextColor(mContext.getResources().getColor(R.color.color_wihte));
        mCancelBtn.setBackground(mCancelFocusBg);
        if (android.os.Build.VERSION.SDK_INT >= 21) {
            mCancelBtn.setStateListAnimator(null);
        }
        LayoutParams cancelBtnParam = new LayoutParams(Util.Div(292), Util.Div(90));
        cancelBtnParam.leftMargin = Util.Div(20);
        bottomBtnLayout.addView(mCancelBtn, cancelBtnParam);


        mRecommendImg = new ImageView(mContext);
        mRecommendImg.setImageResource(R.drawable.icon_download_recommend);
        RelativeLayout.LayoutParams recommendParams = new RelativeLayout.LayoutParams(Util.Div(51), Util.Div(24));
        recommendParams.addRule(RelativeLayout.ALIGN_PARENT_RIGHT);
        recommendParams.addRule(RelativeLayout.CENTER_VERTICAL);
        recommendParams.rightMargin = Util.Div(58);
        mBottomLayout.addView(mRecommendImg, recommendParams);
        mRecommendImg.setVisibility(GONE);

        mOkBtn.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                mOkFocusBg.setFocus(hasFocus);
                mCancelFocusBg.setFocus(!hasFocus);
                if (hasFocus) {
                    mOkBtn.setTextColor(Color.parseColor("#000000"));
                    mCancelBtn.setTextColor(Color.parseColor("#aaFFFFFF"));
                } else {
                    mCancelBtn.setTextColor(Color.parseColor("#000000"));
                    mOkBtn.setTextColor(Color.parseColor("#aaFFFFFF"));
                }
            }
        });
        mOkBtn.setOnClickListener(this);
        mCancelBtn.setOnClickListener(this);
        mOkBtn.setTag(VIEW_TAG_OK);
        mCancelBtn.setTag(VIEW_TAG_CANCEL);
    }

    public void updateUI(int type, String titleStr, String imgUrl) {
        mType = type;
        if (EmptyUtils.isNotEmpty(titleStr)) {
            mTitileTv.setText(titleStr);
        }
        switch (type) {
            case TYPE_DISCOVER:
                mTipsTv.setText(mContext.getResources().getString(R.string.add_device_result_tips_01));
                mOkBtn.setText(mContext.getResources().getString(R.string.add_device_bind));
                mCancelBtn.setText(mContext.getResources().getString(R.string.Cancel));
                mCancelBtn.setVisibility(VISIBLE);
                break;
            case TYPE_DISCOVER_NOT_LOGIN:
                mTipsTv.setText(mContext.getResources().getString(R.string.add_device_result_tips_01));
                mOkBtn.setText(mContext.getResources().getString(R.string.login_and_bind));
                mCancelBtn.setVisibility(GONE);
                break;
            case TYPE_DISCOVER_DISAPPEAR:
                mTipsTv.setText(mContext.getResources().getString(R.string.add_device_result_tips_02));
                mOkBtn.setText(mContext.getResources().getString(R.string.got_it));
                mCancelBtn.setVisibility(GONE);
                break;
            case TYPE_BIND_FAILED:
                mTipsTv.setText(mContext.getResources().getString(R.string.add_device_result_tips_02));
                mOkBtn.setText(mContext.getResources().getString(R.string.add_device_reconnect));
                mCancelBtn.setText(mContext.getResources().getString(R.string.user_mobile_phone_bind));
                mCancelBtn.setVisibility(VISIBLE);
                mRecommendImg.setVisibility(VISIBLE);
                break;
        }

        mHandler = new HelpHandler(mContext.getMainLooper());
        mHandler.sendEmptyMessageDelayed(0, AppConstants.DIALOG_DISMISS_TIME);

        Uri imgUri;
        if (EmptyUtils.isNotEmpty(imgUrl)) {
            imgUri = Uri.parse(imgUrl);
            ImageLoader.getLoader().with(mContext).resize(Util.Div(110), Util.Div(110)).setScaleType(ImageView.ScaleType.FIT_XY).setPlaceHolder(R.drawable.device_default_icon).load(imgUri).into(mIconImg);
        } else {
            mIconImg.setBackgroundResource(R.drawable.device_default_icon);
        }

        post(new Runnable() {
            @Override
            public void run() {
                mOkBtn.requestFocus();
            }
        });
    }

    public void setAddDeviceResultCallBack(AddDeviceResultCallBack addDeviceResultCallBack) {
        this.mAddDeviceResultCallBack = addDeviceResultCallBack;
    }

    @Override
    public void onClick(View v) {
        if (mHandler != null) {
            mHandler.removeMessages(0);
        }
        switch ((String) v.getTag()) {
            case VIEW_TAG_OK:
                if (EmptyUtils.isNotEmpty(mAddDeviceResultCallBack)) {
                    mAddDeviceResultCallBack.onOkClick(mType);
                }
                break;
            case VIEW_TAG_CANCEL:
                if (EmptyUtils.isNotEmpty(mAddDeviceResultCallBack)) {
                    mAddDeviceResultCallBack.onCancelClick(mType);
                }
                break;
        }
    }
}
