package com.skyworth.smarthome.personal.thirdaccount.view;

import android.view.View;

import com.skyworth.smarthome.common.bean.ThridAccountHttpBean;
import com.skyworth.smarthome.personal.thirdaccount.presenter.IThirdAccountPresenter;

import java.util.List;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/9
 */
public interface IThirdAccountView {
    View getView();

    void setPresenter(IThirdAccountPresenter presenter);

    void refreshUI(List<ThridAccountHttpBean> dataList);

    void refreshAccountStatus(int postiion, String status);

    void showToast(String msg);

    void showLoading();

    void hideLoading();

    void showErrorView(String errorMsg);

    void hideErrorView();

    void destroy();
}
