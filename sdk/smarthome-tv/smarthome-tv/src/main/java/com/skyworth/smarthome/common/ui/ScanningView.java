package com.skyworth.smarthome.common.ui;


import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Point;
import android.os.Handler;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.skyworth.smarthome.R;
import com.skyworth.util.Util;

import java.util.LinkedList;
import java.util.Queue;
import java.util.Random;

/**
 * 扫描中view
 * mScanningView = new ScanningView(getContext());
 * FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
 * layoutParams.gravity = Gravity.CENTER;
 * addView(mScanningView, layoutParams);
 * <p>
 * 开始动画
 * mScanningView.startAnim();
 * <p>
 * 设置中间icon
 * mScanningView.setCenterIcon(R.drawable.scan_icon);
 * <p>
 * 设置当前百分比和百分比后面的文字，-1时仅半分比不显示
 * mScanningView.setProgress(-1, getResources().getString(R.string.tvmgr_space_clean_scanning));
 * <p>
 * 设置第二行文字
 * mScanningView.setSecondLine("text");
 * <p>
 * 销毁Activity别忘记停止动画
 * mScanningView.stopAnim();
 */
public class ScanningView extends FrameLayout {
    private FrameLayout mRotateLayout = null;
    private ImageView mCenterIcon = null;
    private TextView mFirstLine = null;
    private TextView mSecondLine = null;
    private View mRotateView = null;
    private int newPointsPerSecond = 2;
    private long nextNewPointTime = 0;
    private Queue<View> mDotCache = null;
    private Queue<View> mCircleCache = null;

    private ObjectAnimator mRotateAnim = null;
    private int mNewCircleInterval = 1000;
    private Handler mHandle = null;
    private Runnable mCircleRunnable = null;
    private static final int CIRCLE_RADIUS = Util.Div(190);
    private static final int CENTER_CIRCLE_RADIUS = Util.Div(130);

    public ScanningView(Context context) {
        super(context);
        initView();
    }

    private void initView() {
        mHandle = new Handler();
        mDotCache = new LinkedList<>();
        mCircleCache = new LinkedList<>();
        mRotateLayout = new FrameLayout(getContext());
        LayoutParams layoutParams = new LayoutParams(Util.Div(380), Util.Div(380));
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        addView(mRotateLayout, layoutParams);

        mRotateView = new View(getContext());
        mRotateView.setBackgroundResource(R.drawable.scaning_rotate);
        layoutParams = new LayoutParams(Util.Div(380), Util.Div(380));
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        mRotateLayout.addView(mRotateView, layoutParams);

    }

    /**
     * 开始动画
     */
    public void startAnim() {
        if (mRotateAnim != null && mRotateAnim.isRunning()) return;
        startRotateAnim();
        startCircleAnim();
    }

    private void startRotateAnim() {
        mRotateAnim = ObjectAnimator.ofFloat(mRotateView, "rotation", 0f, 360f);
        mRotateAnim.setRepeatCount(ValueAnimator.INFINITE);
        mRotateAnim.setDuration(2000);
        mRotateAnim.setInterpolator(null);
        mRotateAnim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                int currentAngle = ((Number) animation.getAnimatedValue()).intValue();
                generateDotPos(currentAngle);
            }
        });
        mRotateAnim.start();
    }

    private void generateDotPos(int currentAngle) {
        long now = SystemClock.uptimeMillis();
        if (now < nextNewPointTime) return;
        Random random = new Random();
        nextNewPointTime = now + 1000 / newPointsPerSecond + random.nextInt(500) - 250;
        Point newPoint = new Point();
        int r = random.nextInt(CIRCLE_RADIUS - CENTER_CIRCLE_RADIUS - Util.Div(5)) + CENTER_CIRCLE_RADIUS;
        double angleInRadius = Math.toRadians(currentAngle);
        newPoint.x = (int) (CIRCLE_RADIUS + r * Math.sin(angleInRadius));
        newPoint.y = (int) (CIRCLE_RADIUS - r * Math.cos(angleInRadius));
        generateDot(newPoint.x, newPoint.y);
    }

    private void startCircleAnim() {
        mCircleRunnable = new Runnable() {
            @Override
            public void run() {
                generateCircle();
                mHandle.postDelayed(this, mNewCircleInterval);
            }
        };
        mHandle.postDelayed(mCircleRunnable, mNewCircleInterval);
    }

    /**
     * 结束动画
     */
    public void stopAnim() {
        if (mRotateAnim != null) mRotateAnim.cancel();
        if (mHandle != null) mHandle.removeCallbacks(mCircleRunnable);
    }

    /**
     * 设置中间icon
     *
     * @param centerIcon
     */
    public void setCenterIcon(int centerIcon) {
        if (mCenterIcon == null) {
            mCenterIcon = new ImageView(getContext());
            LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            layoutParams.gravity = Gravity.CENTER;
            mRotateLayout.addView(mCenterIcon, layoutParams);
        }
        mCenterIcon.setImageResource(centerIcon);
    }

    /**
     * 设置第一行文字
     *
     * @param progress 百分比
     * @param text     文字
     */
    public void setProgress(int progress, String text) {
        if (mFirstLine == null) {
            mFirstLine = new TextView(getContext());
            mFirstLine.setTextSize(Util.Dpi(36));
            mFirstLine.setTextColor(Color.WHITE);
            mFirstLine.getPaint().setFakeBoldText(true);
            LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
            layoutParams.topMargin = Util.Div(552);
            addView(mFirstLine, layoutParams);
        }
        StringBuilder builder = new StringBuilder();
        if (progress >= 0) {
            builder.append(progress);
            builder.append("% ");
        }
        builder.append(text);
        mFirstLine.setText(builder.toString());
    }

    /**
     * 设置第二行文字
     *
     * @param text
     */
    public void setSecondLine(String text) {
        if (mSecondLine == null) {
            mSecondLine = new TextView(getContext());
            mSecondLine.setTextSize(Util.Dpi(28));
//            mSecondLine.setTextColor(XThemeUtils.c_3());
            mSecondLine.setSingleLine();
            mSecondLine.setEllipsize(TextUtils.TruncateAt.END);
            LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
            layoutParams.topMargin = Util.Div(618);
            addView(mSecondLine, layoutParams);
        }
        mSecondLine.setText(text);
    }

    private void generateCircle() {
        View circleTmp = mCircleCache.poll();
        if (circleTmp == null) {
            circleTmp = new View(getContext());
            circleTmp.setBackgroundResource(R.drawable.scaning_circle);
            LayoutParams layoutParams = new LayoutParams(Util.Div(380), Util.Div(380));
            layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
            circleTmp.setLayoutParams(layoutParams);
        }
        final View circle = circleTmp;
        mRotateLayout.addView(circle);

        ObjectAnimator scaleX = ObjectAnimator.ofFloat(circle, "scaleX", 0.5f, 1.0f);
        scaleX.setDuration(2000);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(circle, "scaleY", 0.5f, 1.0f);
        scaleY.setDuration(2000);
        ObjectAnimator alpha1 = ObjectAnimator.ofFloat(circle, "alpha", 0f, 1f);
        alpha1.setDuration(1600);
        ObjectAnimator alpha2 = ObjectAnimator.ofFloat(circle, "alpha", 1f, 0f);
        alpha2.setDuration(400);
        alpha2.setStartDelay(1600);
        AnimatorSet set = new AnimatorSet();
        set.setInterpolator(null);
        set.playTogether(scaleX, scaleY, alpha1, alpha2);
        set.start();
        set.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                mRotateLayout.removeView(circle);
                mCircleCache.add(circle);
            }
        });
    }

    private void generateDot(int x, int y) {
        View dotTmp = mDotCache.poll();
        if (dotTmp == null) {
            dotTmp = new View(getContext());
            dotTmp.setBackgroundResource(R.drawable.scaning_dot);
            LayoutParams layoutParams = new LayoutParams(Util.Div(10), Util.Div(10));
            dotTmp.setLayoutParams(layoutParams);
        }
        LayoutParams layoutParams = (LayoutParams) dotTmp.getLayoutParams();
        layoutParams.leftMargin = x;
        layoutParams.topMargin = y;
        final View dotView = dotTmp;
        mRotateLayout.addView(dotView);

        ObjectAnimator alphaAnim1 = ObjectAnimator.ofFloat(dotView, "alpha", 0f, 1f);
        alphaAnim1.setDuration(300);
        ObjectAnimator alphaAnim2 = ObjectAnimator.ofFloat(dotView, "alpha", 1f, 0f);
        alphaAnim2.setDuration(700);
        alphaAnim2.setStartDelay(300);

        AnimatorSet set = new AnimatorSet();
        set.playTogether(alphaAnim1, alphaAnim2);
        set.setInterpolator(null);
        set.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                mRotateLayout.removeView(dotView);
                mDotCache.add(dotView);
            }
        });
        set.start();
    }
}
