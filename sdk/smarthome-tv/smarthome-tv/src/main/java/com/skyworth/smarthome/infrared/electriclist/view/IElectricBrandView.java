package com.skyworth.smarthome.infrared.electriclist.view;

import android.content.Context;
import android.view.View;

import com.skyworth.smarthome.infrared.electriclist.model.ElectricBrandData;
import com.skyworth.smarthome.infrared.electriclist.presenter.IElectricBrandPresenter;

import java.util.List;

/**
 * Created by fc on 2019/4/26
 * Describe:
 */
public interface IElectricBrandView {

    /**
     * view 初始化
     *
     * @param context
     * @param presenter
     */
    void createView(Context context, IElectricBrandPresenter presenter);

    void setReLoadParams(String deviceID,String type_id, String brand, List<String> hotList);

    void showHotBrandList(List<ElectricBrandData> list);

    void showCharacterList(List<String> list);

    void showBrandList(List<ElectricBrandData> list);

    void showLoading();

    void hideLoading();

    void getFocus();

    void showErrorView(String errorMsg, String errorCode);

    void hideErrorView();


    View getView();
}
