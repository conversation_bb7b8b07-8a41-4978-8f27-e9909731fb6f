package com.skyworth.smarthome.infrared.matchkey.view;

import android.content.Context;
import android.view.View;

import com.skyworth.smarthome.infrared.matchkey.presenter.IMatchKeyPresenter;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/4/29 16:20.
 */
public interface IMatchKeyView {
    void create(Context context, IMatchKeyPresenter presenter);

    void showReadyView(String titleText, String tipText, String buttonText);

    void showSendingView();

    void showConfirmView(int step, int totalStep, String tipText, String tipTextLine2);

    void showMatchErrorView();

    void showMatchSuccessView(String deviceId);

    View getView();

    void destroy();
}
