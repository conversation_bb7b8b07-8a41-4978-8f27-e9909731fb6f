package com.skyworth.smarthome.home.smartdevice.controlpanel.model;

import com.skyworth.smarthome.common.bean.AlarmLogBean;
import com.skyworth.smarthome.common.bean.DeviceInfo;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: IDeviceControl
 * @Author: <PERSON>wen<PERSON><PERSON>
 * @CreateDate: 2020/6/16 15:56
 * @Description:
 */
public interface IDeviceControlModel {

    /**
     * 处理设备状态数据
     *
     * @param data
     */
    void handleDeviceStatusData(String data);

    /**
     * 设备开关是否打开
     *
     * @param deviceInfo
     * @return
     */
    boolean isDevicePowerOn(DeviceInfo deviceInfo);

    /**
     * 控制设备
     *
     * @param status
     * @param deviceId
     */
    void controlDevice(Map<String, String> status, String deviceId);

    /**
     * 控制虚拟设备
     *
     * @param status
     * @param deviceId
     */
    void controlVirtualDevice(Map<String, String> status, String deviceId);

    /**
     * 获取传感器感应记录
     *
     * @param gateway_id 网关id
     * @param did        子设备id
     */
    List<AlarmLogBean> requestAlarmLogList(String gateway_id, String did);
}
