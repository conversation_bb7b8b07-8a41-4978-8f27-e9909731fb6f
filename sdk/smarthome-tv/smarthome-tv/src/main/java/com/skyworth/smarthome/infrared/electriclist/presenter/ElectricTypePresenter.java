package com.skyworth.smarthome.infrared.electriclist.presenter;

import android.view.View;

import com.coocaa.app.core.http.HttpServiceManager;
import com.coocaa.app.core.utils.FuncKt;
import com.skyworth.smarthome.infrared.controldevices.view.IShowRemoteControl;
import com.skyworth.smarthome.infrared.electriclist.model.DeviceTypeListData;
import com.skyworth.smarthome.infrared.electriclist.view.IElectircTypeView;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.common.http.SmartDevicesHttpService;
import com.skyworth.smarthome.infrared.manager.IREntry;
import com.smarthome.common.model.SmartBaseData;
import com.smarthome.common.utils.XToast;

import java.util.HashMap;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import retrofit2.Call;

/**
 * Created by fc on 2019/4/25
 * Describe:
 */
public class ElectricTypePresenter implements IElectricTypePresenter {
    private IElectircTypeView electircTypeView;
    private IShowRemoteControl mShowRemoteControl;
    private String mDeviceId = "";
    private String mDeviceName = "";
    private String mDeviceTypeId = "";


    @Override
    public void loadList() {
        electircTypeView.hideErrorView();
        electircTypeView.showLoading();
        FuncKt.ioThread(new Runnable() {
            @Override
            public void run() {
                Call<SmartBaseData<List<DeviceTypeListData>>> call = SmartDevicesHttpService.SERVICE.getDeviceTypeList();
                SmartBaseData baseData = HttpServiceManager.Companion.call(call);
                electircTypeView.hideLoading();
                if (baseData != null && baseData.code.equals("0")) {
                    final List<DeviceTypeListData> dataList = ((List<DeviceTypeListData>) baseData.data);
                    FuncKt.runOnUiThread(new Function0<Unit>() {
                        @Override
                        public Unit invoke() {
                            electircTypeView.showList(dataList);
                            return null;
                        }
                    });
                } else {
                    FuncKt.runOnUiThread(new Function0<Unit>() {
                        @Override
                        public Unit invoke() {
                            electircTypeView.showErrorView("网络状态错误", "");
                            return null;
                        }
                    });
                }
            }
        });

    }

    @Override
    public void onItemClick(View view, DeviceTypeListData itemData, int position) {
        if (itemData == null) return;
        if (itemData.name.equals("空调")) {
            XToast.showToast(SmartHomeTvLib.getContext(), "空调暂不支持此功能");
            return;
        }
        if (mShowRemoteControl != null) {
            HashMap<String, Object> map = new HashMap<String, Object>();
            map.put(IREntry.DEVICE_KEY, mDeviceId);
            map.put(IREntry.DEVICE_NAME_KEY, mDeviceName);
            map.put(IREntry.DEVICE_TYPE_ID_KEY, mDeviceTypeId);
            map.put("id", itemData.id);
            map.put("typeName", itemData.name);
            map.put("hotList", itemData.hot_brands_cn);
            map.put(IREntry.KK_TYPE_ID, itemData.kk_type_id);
            map.put(IREntry.SDK_PRIORITY, itemData.code_type);
            mShowRemoteControl.showElectricBrandView(map);
        }

//        Map<String, String> params = new HashMap<String, String>();
//        params.put("device_name", itemData.name);
//        LoggerImpl.Companion.onEvent(DataConstants.EVENT_INFRARED_SELECT_DEVICE_TYPE_ONCLICK, params);
    }

    @Override
    public void setDeviceId(String deviceId) {
        mDeviceId = deviceId;
    }

    @Override
    public void setDeviceName(String deviceName) {
        mDeviceName = deviceName;
    }

    @Override
    public void setDeviceTypeId(String id) {
        mDeviceTypeId = id;
    }

    @Override
    public void setView(IElectircTypeView view) {
        electircTypeView = view;
    }

    @Override
    public void setIShowRemoteControl(IShowRemoteControl showRemoteControl) {
        mShowRemoteControl = showRemoteControl;
    }
}
