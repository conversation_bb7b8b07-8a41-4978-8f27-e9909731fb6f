package com.skyworth.smarthome.infrared.learn.view;

import android.content.Context;
import android.util.Log;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.widget.FrameLayout;

import com.skyworth.smarthome.infrared.learn.view.pages.BaseIRLearnViews;
import com.skyworth.smarthome.infrared.learn.view.pages.IRLearnFailView;
import com.skyworth.smarthome.infrared.learn.view.pages.IRLearnFinishView;
import com.skyworth.smarthome.infrared.learn.view.pages.IRLearnReadyView;
import com.skyworth.smarthome.infrared.learn.view.pages.IRLearnStartView;
import com.skyworth.smarthome.infrared.learn.view.pages.IRLearnSuccessView;
import com.skyworth.smarthome.infrared.learn.presenter.IIRLearnPresenter;
import com.skyworth.util.Util;

import java.lang.reflect.Constructor;
import java.util.HashMap;
import java.util.Map;

import static android.view.View.GONE;
import static com.skyworth.smarthome.infrared.learn.IRLearnDialog.TAG;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/7/2 14:43.
 */
public class IRLearnViewImpl implements IIRLearnView {
    private IIRLearnPresenter mPresenter = null;
    private FrameLayout mLayout = null;
    private Map<Class, BaseIRLearnViews> views = new HashMap<>();
    private BaseIRLearnViews currentShowView = null;
    private static final int MARGIN = Util.Div(40);
    private Context mContext = null;
    public static final String PARAMS_KEY_IS_LEARNING = "isLearning";
    private IDialogListener mDialogListener = null;

    @Override
    public void setDialogListener(IDialogListener listener) {
        mDialogListener = listener;
    }

    @Override
    public void create(Context context, IIRLearnPresenter presenter) {
        mPresenter = presenter;
        mContext = context;
        mLayout = new FrameLayout(mContext);
    }

    @Override
    public void showReady(String remoteName) {
        Map<String, Object> params = IRLearnReadyView.getParams(remoteName);
        showView(IRLearnReadyView.class, params);
    }

    @Override
    public void showLearnStart(int current, int total, String remoteName, String keyName, String irDeviceName) {
        Map<String, Object> params = IRLearnStartView.getParams(current, total, remoteName, keyName, irDeviceName);
        params.put(PARAMS_KEY_IS_LEARNING, false);
        showView(IRLearnStartView.class, params);
    }

    @Override
    public void showLearning(int current, int total, String remoteName, String keyName, String irDeviceName) {
        Map<String, Object> params = IRLearnStartView.getParams(current, total, remoteName, keyName, irDeviceName);
        params.put(PARAMS_KEY_IS_LEARNING, true);
        showView(IRLearnStartView.class, params);
    }

    @Override
    public void showLearnSuccess(String keyName, int seconds) {
        Map<String, Object> params = IRLearnSuccessView.getParams(keyName, seconds);
        showView(IRLearnSuccessView.class, params);
    }

    @Override
    public void showLearnFail() {
        showView(IRLearnFailView.class, null);
    }

    @Override
    public void showLearnFinish(String deviceId) {
        Map<String, Object> params = IRLearnFinishView.getParams(deviceId);
        showView(IRLearnFinishView.class, params);
    }

    private void showView(Class clazz, Map<String, Object> params) {
        BaseIRLearnViews view = null;
        if (views.containsKey(clazz)) {
            view = views.get(clazz);
        } else {
            view = createView(clazz);
        }
        if (currentShowView != null && currentShowView != view && currentShowView.getVisibility() != GONE) {
            currentShowView.setVisibility(GONE);
            Log.i(TAG, "showView: hide: " + currentShowView.getName());
        }
        if (view == null) {
            return;
        }
        view.show(params);
        if (view.getVisibility() != View.VISIBLE) {
            view.setVisibility(View.VISIBLE);
            Log.i(TAG, "showView: show: " + view.getName());
        }
        currentShowView = view;
    }

    private BaseIRLearnViews createView(Class clazz) {
        BaseIRLearnViews view = null;
        try {
            Constructor constructor = clazz.getConstructor(Context.class);
            view = (BaseIRLearnViews) constructor.newInstance(mContext);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (view == null) {
            return null;
        }
        Log.i(TAG, "createView: " + view.getName());
        view.setPresenter(mPresenter);
        FrameLayout.LayoutParams l = new FrameLayout.LayoutParams(view.getViewWidth(), view.getViewHeight());
        l.gravity = Gravity.CENTER;
        mLayout.addView(view, l);
        views.put(clazz, view);
        return view;
    }

    @Override
    public void showLoading() {
//        XToast.showToast(mContext, "showLoading");
    }

    @Override
    public void hideLoading() {
//        XToast.showToast(mContext, "hideLoading");
    }

    @Override
    public void showError(String msg) {
//        XToast.showToast(mContext, msg);
    }

    @Override
    public boolean handleKeyEvent(KeyEvent keyEvent) {
        return mPresenter != null && mPresenter.handleKeyEvent(keyEvent);
    }

    @Override
    public void dismiss() {
        if (mDialogListener != null) {
            mDialogListener.onDismiss();
        }
    }

    @Override
    public View getView() {
        return mLayout;
    }

    @Override
    public void destroy() {
        mLayout.removeAllViews();
        views.clear();
    }
}
