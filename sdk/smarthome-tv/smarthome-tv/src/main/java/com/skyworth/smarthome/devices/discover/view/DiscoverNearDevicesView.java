package com.skyworth.smarthome.devices.discover.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.support.v7.widget.NewRecycleAdapter;
import android.support.v7.widget.OrientationHelper;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;


import com.alibaba.fastjson.JSONObject;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.ui.DialogBg;
import com.skyworth.smarthome.common.ui.SmallScanningView;
import com.skyworth.smarthome.common.util.LogUtil;
import com.skyworth.smarthome.service.model.IFunctionGoToModel;
import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.ui.newrecycleview.NewRecycleAdapterItem;
import com.skyworth.ui.newrecycleview.NewRecycleLayout;
import com.skyworth.ui.newrecycleview.OnItemClickListener;
import com.skyworth.ui.newrecycleview.OnItemFocusChangeListener;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;
import com.smarthome.common.dataer.LogSDK;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XThemeUtils;
import com.swaiot.aiotlib.common.entity.DiscoverNetworkDevice;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 发现附近的设备
 */
public class DiscoverNearDevicesView extends FrameLayout implements OnItemFocusChangeListener, OnItemClickListener {
    private Context mContext;
    private SmallScanningView mScanningView;//扫描View
    private List<DiscoverNetworkDevice> mDataList;
    private NewRecycleLayout<DiscoverNetworkDevice> mRecycleLayout;
    private NewRecycleAdapter<DiscoverNetworkDevice> mAdapter;
    public static final String DEVICE_STATUS_NOT_APCONFIG_NETWORK = "0";//未配网
    public static final String DEVICE_STATUS_ADDED = "1";//已添加
    public static final String DEVICE_STATUS_UNBIND = "2";//未绑定
    public static final String DEVICE_STATUS_BINDED = "3";//已绑定

    public DiscoverNearDevicesView(Context context) {
        super(context);
        setFocusable(true);
        mContext = context;
        setBackground(new DialogBg());

        LinearLayout titleLayout = new LinearLayout(context);
        titleLayout.setOrientation(LinearLayout.HORIZONTAL);
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        addView(titleLayout, params);

        mScanningView = new SmallScanningView(context);
        mScanningView.setCenterIcon(R.drawable.scanning_wifi);
        params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.topMargin = Util.Div(5);
        titleLayout.addView(mScanningView, params);
        startAnim();

        TextView title = new TextView(context);
        title.setText(context.getString(R.string.near_devices));
        title.setTextColor(Color.WHITE);
        title.setTextSize(Util.Dpi(32));
        title.getPaint().setFakeBoldText(true);
        title.setGravity(Gravity.CENTER_VERTICAL);
        params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT,Util.Div(102));
        params.leftMargin = Util.Div(4);
        titleLayout.addView(title, params);


        mRecycleLayout = new NewRecycleLayout<>(context);
        mRecycleLayout.setOrientation(OrientationHelper.VERTICAL);
        mRecycleLayout.setSpanCount(1);
        mRecycleLayout.setmItemFocusChangeListener(this);
        mRecycleLayout.setmItemClickListener(this);
        mRecycleLayout.setClipChildren(false);
        mRecycleLayout.setClipToPadding(false);
        params = new LayoutParams(Util.Div(580), ViewGroup.LayoutParams.MATCH_PARENT);
        params.leftMargin = Util.Div(40);
        params.rightMargin = Util.Div(40);
        params.bottomMargin= Util.Div(14);
        params.topMargin = Util.Div(80);
        addView(mRecycleLayout, params);
    }



    public void show(List<DiscoverNetworkDevice> devices) {
        if (devices.size() <= 0) {
            return;
        }
        mDataList = devices;
        if(EmptyUtils.isEmpty(mAdapter)) {
            mAdapter = new NewRecycleAdapter<DiscoverNetworkDevice>(mDataList, 1) {

                @Override
                public NewRecycleAdapterItem<DiscoverNetworkDevice> onCreateItem(Object type) {
                    return new DeviceInfoItem(mContext);
                }
            };
            mRecycleLayout.setRecyclerAdapter(mAdapter);
            mRecycleLayout.setSelection(0);
        }else{
            mAdapter.refreshUI(mDataList);
//            mRecycleLayout.notifyDataSetChanged();
        }
    }

    @Override
    public void click(View v, int position) {
        DiscoverNetworkDevice item = mAdapter.getData().get(position);
        if(item.bind_status.equals(DEVICE_STATUS_UNBIND)){//未绑定的时候，才会弹
            LogUtil.androidLog("绑定设备："+ JSONObject.toJSONString(item));
            IFunctionGoToModel.INSTANCE.goToDeviceBind(item);
        }else if(item.bind_status.equals(DEVICE_STATUS_NOT_APCONFIG_NETWORK)){
            IFunctionGoToModel.INSTANCE.goToDeviceConfigManual(item);
            reportClickData(item);
        }
    }

    private void reportClickData(DiscoverNetworkDevice item){
        try {
            //数据统计
            Map<String, String> map = new HashMap<>();
            map.put("device_SSID", item.device_id);
            map.put("device_brand", item.brand_cn);
            map.put("device_name",item.device_name);
            LogSDK.submit(LogSDK.EVENT_ID_NO_NETWORK_BUTTON,map);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void focusChange(View v, int position, boolean hasFocus) {
        ((DeviceInfoItem) v).onFocusChange(v, hasFocus);
    }

    public void startAnim() {
        mScanningView.startAnim();
    }

    public void stopAnim() {
        mScanningView.stopAnim();
    }

    public void onDestroy() {
        stopAnim();
    }

    private class DeviceInfoItem extends FrameLayout implements NewRecycleAdapterItem<DiscoverNetworkDevice> {
        private TextView mDeviceNameTv;
        private View mDeviceIconView;
        private TextView mBindingStatusTv;
        private FrameLayout mItemLayout;
        private Drawable mUnFocusBg;
        private CCFocusDrawable mFocusBg;
        private PopupWindow mPopupWindow;

        public DeviceInfoItem( Context context) {
            super(context);
            setFocusable(true);
            mItemLayout = new FrameLayout(mContext);
            LayoutParams itemParam = new LayoutParams(Util.Div(580), Util.Div(90));
            itemParam.topMargin = Util.Div(20);

            mDeviceIconView =  ImageLoader.getLoader().getView(mContext);
            LayoutParams deviceIconParam = new LayoutParams(Util.Div(60), Util.Div(60));
            deviceIconParam.gravity = Gravity.CENTER_VERTICAL;
            deviceIconParam.leftMargin = Util.Div(20);
            mItemLayout.addView(mDeviceIconView, deviceIconParam);


            mDeviceNameTv = new TextView(getContext());
            mDeviceNameTv.setTextSize(Util.Dpi(28));
            mDeviceNameTv.setTextColor(Color.WHITE);
            mDeviceNameTv.setGravity(Gravity.LEFT);
            LayoutParams deviceNameParam = new LayoutParams(Util.Div(196), ViewGroup.LayoutParams.WRAP_CONTENT);
            deviceNameParam.gravity = Gravity.CENTER_VERTICAL;
            deviceNameParam.leftMargin = Util.Div(90);
            mItemLayout.addView(mDeviceNameTv, deviceNameParam);

            mBindingStatusTv = new TextView(getContext());
            mBindingStatusTv.setTextSize(Util.Dpi(20));
            mBindingStatusTv.setTextColor(Color.WHITE);
            mBindingStatusTv.setGravity(Gravity.CENTER);
            mBindingStatusTv.setBackgroundResource(R.drawable.adapter_item_bind_status_bg);
            LayoutParams bindingStatusParam = new LayoutParams(Util.Div(92), Util.Div(36));
            bindingStatusParam.gravity = Gravity.CENTER_VERTICAL|Gravity.RIGHT;
            bindingStatusParam.rightMargin = Util.Div(30);
            mItemLayout.addView(mBindingStatusTv, bindingStatusParam);

            mFocusBg = new CCFocusDrawable(getContext()).setRadius(Util.Dpi(10)).setSolidColor(getResources().getColor(R.color.white));
            mUnFocusBg = XThemeUtils.getDrawable(Color.parseColor("#19CCCCCC"), 0, 0, Util.Div(8));
            mItemLayout.setBackground(mUnFocusBg);
            addView(mItemLayout,itemParam);
//            initPopWindow();
        }
//美的活动提示屏蔽
//        private void initPopWindow(){
//            if (mPopupWindow == null) {
//                View  view = new View(getContext());
//                view.setBackgroundResource(R.drawable.pop_media_bg);
//                mPopupWindow = new PopupWindow(view, Util.Div(220), Util.Div(52));
//                mPopupWindow.setOutsideTouchable(false);
//            }
//            if (mPopupWindow.isShowing()) {
//                mPopupWindow.dismiss();
//            }
//        }

        public void onFocusChange(View v, boolean hasFocus) {
            mBindingStatusTv.setSelected(hasFocus);
            mDeviceNameTv.setSelected(hasFocus);
            if (hasFocus) {
                mDeviceNameTv.setTextColor(Color.parseColor("#000000"));
                mBindingStatusTv.setTextColor(Color.parseColor("#000000"));
                mItemLayout.setBackground(mFocusBg);
//美的活动提示屏蔽
//                int width = mPopupWindow.getWidth();
//                int[] xy = new int[2];
//                mBindingStatusTv.getLocationInWindow(xy);
//                mPopupWindow.showAtLocation(mBindingStatusTv, Gravity.NO_GRAVITY,
//                            xy[0] - Util.Div(35)+ (mBindingStatusTv.getWidth() - width) / 2, xy[1] - Util.Div(60));
            } else {
                mDeviceNameTv.setTextColor(Color.parseColor("#ccFFFFFF"));
                mBindingStatusTv.setTextColor(Color.parseColor("#ccFFFFFF"));
                mItemLayout.setBackground(mUnFocusBg);
//美的活动提示屏蔽
//                if(EmptyUtils.isNotEmpty(mPopupWindow)){
//                    mPopupWindow.dismiss();
//                }
            }
        }

        @Override
        public View getView() {
            return this;
        }

        @Override
        public void onUpdateData(DiscoverNetworkDevice data, int position) {
            String imgUrl = data.product_type_logo;
            Uri imgUri;
            if(EmptyUtils.isNotEmpty(imgUrl)){
                imgUri = Uri.parse(data.product_type_logo);
                ImageLoader.getLoader().with(mContext).resize(Util.Div(60), Util.Div(60))
                        .setScaleType(ImageView.ScaleType.FIT_XY).setPlaceHolder(R.drawable.device_default_icon).load(imgUri).into(mDeviceIconView);
            }else{
                mDeviceIconView.setBackgroundResource(R.drawable.device_default_icon);
            }
            mDeviceNameTv.setText(data.device_name);
            switch (data.bind_status){
                case DEVICE_STATUS_NOT_APCONFIG_NETWORK:
                    mBindingStatusTv.setText(mContext.getString(R.string.un_apconfig_network));
                    break;
                case DEVICE_STATUS_ADDED:
                    mBindingStatusTv.setText(mContext.getString(R.string.added));
                    break;
                case DEVICE_STATUS_UNBIND:
                    mBindingStatusTv.setText(mContext.getString(R.string.unbind));
                    break;
                case DEVICE_STATUS_BINDED:
                    mBindingStatusTv.setText(mContext.getString(R.string.binded));
                    break;
            }
        }

        @Override
        public void clearItem() {

        }

        @Override
        public void refreshUI() {

        }

        @Override
        public void destroy() {

        }
    }
}
