package com.skyworth.smarthome.home.smartdevice.controlpanel.view.item;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.DashPathEffect;
import android.graphics.Paint;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.skyworth.smarthome.home.smartdevice.controlpanel.common.itemdata.GroupDisplayControlData;
import com.skyworth.smarthome.home.smartdevice.controlpanel.view.base.BaseControlItem;
import com.skyworth.util.Util;
import com.smarthome.common.utils.EmptyUtils;

import java.util.LinkedList;
import java.util.Queue;

public class GroupDisplayLeftItem extends BaseControlItem<GroupDisplayControlData> {
    public static final String TYPE = "display";
    private LinearLayout mLayout = null;
    private LinearLayout mLayout2 = null;
    private Queue<View> mRecycled = null;
    private int mUnitCount = 0;
    private Paint mDividerPaint = null;

    public GroupDisplayLeftItem(Context context) {
        super(context);
        setWillNotDraw(false);
        setLayerType(LAYER_TYPE_SOFTWARE, null);
        mRecycled = new LinkedList<>();
        initMLayout();
    }

    private void initMLayout() {
        mLayout = new LinearLayout(getContext());
        mLayout.setOrientation(LinearLayout.HORIZONTAL);
        addView(mLayout, new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, Util.Div(88)));
    }


    @Override
    protected void refreshUI() {
        if (mData == null || EmptyUtils.isEmpty(mData.items)) {
            logi("mData is empty");
            return;
        }
        if (mData.items.size() <= 2) {
            refresh3();
        } else {
            refresh4();
        }
        mUnitCount = mData.items.size();
    }

    private void refresh3() {
        hideLayout2();
        recycleLayoutItems(mLayout);
        for (int i = 0; i < mData.items.size(); i++) {
            addItemToLayout(mLayout, mData.items.get(i), i);
        }
    }

    private void refresh4() {
        showLayout2();
        recycleLayoutItems(mLayout);
        recycleLayoutItems(mLayout2);
        for (int i = 0; i < mData.items.size();i++){
            if (i < 2){
                addItemToLayout(mLayout, mData.items.get(i), i);
            }else {
                addItemToLayout(mLayout2, mData.items.get(i), i-2);
            }
        }
    }

    private void addItemToLayout(LinearLayout layout, GroupDisplayControlData.GroupItem item, int index) {
        TextUnit textUnit = getUnit();
        textUnit.show(getUpText(item), item.title);
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(getUnitWidth(), Util.Div(88));
        layoutParams.leftMargin = Util.Div(30);
        layoutParams.rightMargin = Util.Div(30);
        if (textUnit.getParent() == null) {
            layout.addView(textUnit, layoutParams);
        } else {
            textUnit.setLayoutParams(layoutParams);
        }
    }

    private void showLayout2() {
        if (mLayout2 == null) {
            initLayout2();
        }
        if (mLayout2.getParent() == null) {
            LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, Util.Div(88));
            layoutParams.topMargin = Util.Div(60);
            if (mData.items.size() >= 4){
                layoutParams.gravity = Gravity.LEFT;
                layoutParams.leftMargin = Util.Div(17);
            }else {
                layoutParams.leftMargin = Util.Div(0);
            }
            addView(mLayout2, layoutParams);
        }
        mLayout2.setVisibility(VISIBLE);
    }

    private void hideLayout2() {
        if (mLayout2 != null) {
            mLayout2.setVisibility(GONE);
            recycleLayoutItems(mLayout2);
            removeView(mLayout2);
        }
    }

    private void recycleLayoutItems(ViewGroup layout) {
        if (layout == null || layout.getChildCount() <= 0) {
            return;
        }
        for (int i = layout.getChildCount() - 1; i >= 0; i--) {
            recycleView(layout.getChildAt(i));
        }
        layout.removeAllViews();
    }

    private void initLayout2() {
        mLayout2 = new LinearLayout(getContext());
        mLayout2.setOrientation(LinearLayout.HORIZONTAL);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (mData == null || mData.items == null) {
            return;
        }
        if (mDividerPaint == null) {
            initPaint();
        }
        float y1 = 0;
        float y2 = getHeight();
        float x  = getWidth() / 2f;
        switch (mData.items.size()) {
            case 2:
                canvas.drawLine(x, y1 + Util.Div(30), x, y2 - Util.Div(30), mDividerPaint);
                break;
            case 3:
                canvas.drawLine(x, y1 + Util.Div(30), x, y2 - Util.Div(30), mDividerPaint);
                canvas.drawLine(Util.Div(50), getHeight() / 2f, getWidth() - Util.Div(50), getHeight() / 2f, mDividerPaint);
                break;
            case 4:
                canvas.drawLine(x, y1 + Util.Div(30), x, y2 - Util.Div(30), mDividerPaint);
                canvas.drawLine(Util.Div(50), getHeight() / 2f, getWidth() - Util.Div(50), getHeight() / 2f, mDividerPaint);
                break;
        }
    }

    private void initPaint() {
        DashPathEffect dashPathEffect = new DashPathEffect(new float[]{Util.Div(0), Util.Div(0)}, 0);
        mDividerPaint = new Paint();
        mDividerPaint.setColor(Color.parseColor("#33D8D8D8"));
        mDividerPaint.setAntiAlias(true);
        mDividerPaint.setStyle(Paint.Style.STROKE);
        mDividerPaint.setStrokeWidth(Util.Div(1));
        mDividerPaint.setPathEffect(dashPathEffect);
    }

    private TextUnit getUnit() {
        TextUnit textUnit = (TextUnit) mRecycled.poll();
        if (textUnit == null) {
            textUnit = new TextUnit(getContext());
        }
        return textUnit;
    }

    private int getUnitWidth() {
        if (mData == null || mData.items == null || mData.items.size() <= 0) {
            return 0;
        }
        if (mData.items.size() == 1) {
            mLayout.setGravity(Gravity.CENTER);
            return Util.Div(190);
        } else if (mData.items.size() == 2) {
            mLayout.setGravity(Gravity.CENTER);
            return Util.Div(210);
        } else if (mData.items.size() == 3) {
            mLayout.setGravity(Gravity.CENTER);
            return Util.Div(210);
        } else {
            mLayout.setGravity(Gravity.CENTER);
            return Util.Div(210);
        }
    }

    private void recycleView(View view) {
        if (view == null) {
            return;
        }
        if (view instanceof TextUnit) {
            mRecycled.add(view);
        }
    }

    private String getUpText(GroupDisplayControlData.GroupItem item) {
        if (item == null || TextUtils.isEmpty(item.transfer)) {
            return "";
        }
        String text = null;
        if (item.transfer != null && item.transfer.toLowerCase().equals(Boolean.TRUE.toString())) {
            Object key = mStatus.get(item.data_field);
            if (key == null || item.values == null) {
                return "";
            }
            text = item.values.get(key.toString());
        } else {
            Object object = mStatus.get(item.data_field);
            if (object != null) {
                if (item.title.equals("剩余时间")){
                    if (mStatus.get("POW_S").equals("0")){
                        text = "0分钟";
                    }else{
                        int h = Integer.parseInt(mStatus.get("RE_H").toString());
                        text = (Integer.parseInt(object.toString())+h) + item.unit;
                    }
                }else {
                    text = object.toString() + item.unit;
                }
            }
        }
        if (text == null) {
            text = "";
        }
        return text;
    }

    @Override
    public void onRecycle() {

    }

    @Override
    public String getType() {
        return TYPE;
    }

    @Override
    public void enable() {
        setAlpha(1);
        setFocusable(false);
    }

    @Override
    public boolean canFocusableDefault() {
        return false;
    }

    public static class TextUnit extends LinearLayout {
        private TextView mUp = null;
        private TextView mDown = null;

        public TextUnit(Context context) {
            super(context);
            setOrientation(HORIZONTAL);
            setGravity(Gravity.CENTER_VERTICAL);
        }

        public void show(String textUp, String textDown) {

            if (mDown == null) {
                initDown();
            }
            if (mUp == null) {
                initUp();
            }
            if (!mDown.getText().equals(textDown)) {
                mDown.setText(textDown);
            }
            if (!mUp.getText().equals(textUp)) {
                mUp.setText(textUp);
                mUp.setSelected(true);
            }
        }

        public void initUp() {
            mUp = new TextView(getContext());
            mUp.setTextSize(Util.Dpi(26));
            mUp.setTextColor(Color.parseColor("#FFFFFFFF"));
            mUp.setSingleLine();
            mUp.setMaxEms(5);
            mUp.setEllipsize(TextUtils.TruncateAt.MARQUEE);
            mUp.setMarqueeRepeatLimit(-1);
            LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            addView(mUp, layoutParams);
        }

        public void initDown() {
            mDown = new TextView(getContext());
            mDown.setTextSize(Util.Dpi(26));
            mDown.setTextColor(Color.parseColor("#99FFFFFF"));
            mDown.setSingleLine();
            mDown.setEllipsize(TextUtils.TruncateAt.MARQUEE);
            mDown.setMarqueeRepeatLimit(-1);
            LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            layoutParams.weight=Util.Div(1);
            layoutParams.width =Util.Div(0);
            addView(mDown, layoutParams);
        }
    }
}
