package com.skyworth.smarthome.personal.thirdaccount.dialog.jd;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.coocaa.app.core.http.HttpServiceManager;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.bean.JDQRBean;
import com.skyworth.smarthome.common.event.OtherAccountAuthResultEvent;
import com.skyworth.smarthome.common.http.SmartDevicesHttpService;
import com.skyworth.smarthome.common.util.ThreadManager;
import com.skyworth.smarthome.personal.thirdaccount.dialog.IBindThirdAccountDialog;
import com.smarthome.common.model.SmartBaseData;
import com.smarthome.common.sal.SalImpl;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import static com.skyworth.smarthome.service.push.binder.model.OtherPushModel.ACOUNT_TYPE_JD;


/**
 * Description: <br>
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/6/5 15:16.
 */
public class BindJDAccPresenter {
    private final static String TAG = "BindJDPresenter";
    private IBindThirdAccountDialog.OnBindResultListener mListener;
    private boolean stopPolling = false;
    private JDQRBean jdqrBean = null;
    private BindJDAccDialog mView = null;
    private Context mContext = null;

    public BindJDAccPresenter(IBindThirdAccountDialog.OnBindResultListener listener, BindJDAccDialog view, Context context) {
        mListener = listener;
        mView = view;
        mContext = context;
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void Event(OtherAccountAuthResultEvent event) {
        String type = event.getAuthAccount();
        if (type == null || !type.equals(ACOUNT_TYPE_JD)) {
            return;
        }
        Log.i(TAG, "Event: " + event.isResult());
        if (event.isResult()) {
            if (!stopPolling) {
                stopPolling();
                if (mListener != null) {
                    mListener.result(true);
                } else {
                    cancelGetJDQR();
                    stopWaitForQRExpire();
                }
            }
        } else {
            stopWaitForQRExpire();
            showFailedToast();
//            getJDQR();
        }
    }

    public void stopPolling() {
        stopPolling = true;
        try {
            unregisterPush();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void getJDQR() {
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                String deviceId = SalImpl.getSAL(mContext).getActiveID();
                final SmartBaseData<JDQRBean> data = HttpServiceManager.Companion.call(SmartDevicesHttpService.SERVICE.getJDQR(deviceId));
                if (data == null || data.data == null) {
                    Log.e(TAG, "getJDQR: data == null");
                    mListener.result(false);
                    showFailedToast();
                    return;
                }
                jdqrBean = data.data;
                Log.i(TAG, "getJDQR: " + jdqrBean.qrcode);
                waitForQRExpire();
                ThreadManager.getInstance().uiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (mView != null) {
                            mView.showQRCode(jdqrBean.qrcode);
                        }
                    }
                });
            }
        });
    }

    private void showFailedToast() {
        ThreadManager.getInstance().uiThread(new Runnable() {
            @Override
            public void run() {
                if (mView != null) {
                    mView.showToast(mContext.getResources().getString(R.string.account_bind_qr_failed));
                }
            }
        });
    }

    private Runnable expireRunnable = new Runnable() {
        @Override
        public void run() {
            Log.i(TAG, "expireRunnable");
            getJDQR();
        }
    };

    private void waitForQRExpire() {
        if (mView != null) {
            mView.post(expireRunnable, jdqrBean.expire_in * 1000);
        }
    }

    public void stopWaitForQRExpire() {
        Log.i(TAG, "stopWaitForQRExpire");
        if (mView != null) {
            mView.cancelPost(expireRunnable);
        }
    }

    public void cancelGetJDQR() {
        Log.i(TAG, "cancelGetJDQR");
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                if (jdqrBean == null || TextUtils.isEmpty(jdqrBean.qrcode)) {
                    return;
                }
                SmartBaseData data = HttpServiceManager.Companion.call(SmartDevicesHttpService.SERVICE.cancelGetJDQR(jdqrBean.qrcode));
                if (data == null) {
                    Log.i(TAG, "cancelGetJDQR: fail");
                }
                Log.i(TAG, "cancelGetJDQR: success");
            }
        });
    }


    private void unregisterPush() {
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }
}
