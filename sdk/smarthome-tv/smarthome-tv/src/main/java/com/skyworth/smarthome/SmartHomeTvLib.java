package com.skyworth.smarthome;

import android.content.Context;
import android.content.Intent;
import android.os.Build;

import com.coocaa.operate6_0.utils.AndroidUtil;
import com.coocaa.operate6_0.utils.SupportUtil;
import com.skyworth.smarthome.common.http.SmartHomeHttpConfig;
import com.skyworth.smarthome.common.model.SPCacheData;
import com.skyworth.smarthome.common.util.LogUtil;
import com.skyworth.smarthome.common.util.SmartHomeServiceManager;
import com.skyworth.smarthome.common.util.ThreadManager;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;
import com.smarthome.common.dataer.LogSDK;
import com.smarthome.common.sal.SalImpl;
import com.smarthome.common.utils.Android;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.XThemeUtils;
import com.swaiot.aiotlib.AIOTLib;
import com.swaiot.aiotlib.AiotLibSDK;
import com.swaiot.aiotlib.common.util.SystemProperty;

import java.util.HashMap;
import java.util.Map;

/**
 *
 */
public class SmartHomeTvLib {

    public static Context mContext;


    public static void init(Context context) {
        mContext = context;
        try {
            CCLog.init("SmartHomeApp");//日志打印，代替Log.i();
            SalImpl.getSAL(mContext);
            if (Android.isMainProcess(context)) {//主进程才初始化
                LogUtil.androidLog("Main app process");
                XThemeUtils.init(mContext);//主题适配工具初始化
                Util.instence(mContext);//像素适配工具初始化
                LogSDK.init(mContext);//日志采集初始化
                AndroidUtil.isTouchMode = true;
                ImageLoader.getLoader().init(mContext);//图片加载库初始化
                SupportUtil.setAppContext(mContext);//瀑布流运营框架初始化
                SmartHomeServiceManager.create(mContext);
                AIOTLib.getDefault().startAiotService(mContext);//aiot数据服务启动
                SPCacheData.setScreenId(mContext, SalImpl.getSAL(mContext).getActiveID());//缓存本机设备id
                startSmartHomeService();
            } else {
                LogUtil.androidLog("Aiotlib process");
                ThreadManager.getInstance().uiThread(new Runnable() {
                    @Override
                    public void run() {
                        AIOTLib.getDefault().setDeviceName(SalImpl.getSAL(mContext).getDeviceName());
                    }
                }, 1000);
                AIOTLib.getDefault().init(getContext(), AiotLibSDK.Platform.TV, SmartHomeHttpConfig.APP_KEY, SmartHomeHttpConfig.SECRET, getHeaderMap());//aiot进程初始化
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 启动主进程服务
     */
    public static void startSmartHomeService() {
        Intent intent = new Intent();
        intent.setPackage(mContext.getPackageName());
        intent.setAction("com.skyworth.smarthome_tv.SmartHomeService");
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            mContext.startForegroundService(intent);
        } else {
            mContext.startService(intent);
        }
    }

    public static void stopSmartHomeService() {
        Intent intent = new Intent();
        intent.setPackage(mContext.getPackageName());
        intent.setAction("com.skyworth.smarthome_tv.SmartHomeService");
        mContext.stopService(intent);
    }


    public static Context getContext() {
        return mContext;
    }


    /**
     * Header参数
     *
     * @return
     */
    private static Map<String, String> getHeaderMap() {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("MAC", SalImpl.getSAL(mContext).getMAC());
        headerMap.put("cModel", SalImpl.getSAL(mContext).getDeviceModel());
        headerMap.put("cChip", SalImpl.getSAL(mContext).getDeviceChip());
        headerMap.put("cSize", "");
        headerMap.put("cResolution", Util.getDisplayWidth() + "*" + Util.getDisplayHeight());
        headerMap.put("cTcVersion", SalImpl.getSAL(mContext).getVersionName());
        headerMap.put("cFMode", SalImpl.getSAL(mContext).getDeviceModel());
        headerMap.put("emmcid", "");
        headerMap.put("product_type_id", "1");
        headerMap.put("product_brand_id", "1");
        headerMap.put("product_model", getSelfTvModel());
        headerMap.put("activation_id", SalImpl.getSAL(mContext).getActiveID());
        headerMap.putAll(SalImpl.getSAL(getContext()).getCommonHeader());
        return headerMap;
    }

    private static String getSelfTvModel() {
        String hwVaule = SystemProperty.get("ro.skyconfig.hw.iot_rc", "0");
        String swVaule = SystemProperty.get("ro.skyconfig.sw.mesh", "0");

        if (hwVaule.equals("0") && swVaule.equals("0")) {
            return "SmartTV";
        } else if (hwVaule.equals("1") && swVaule.equals("0")) {
            return "SmartTV-AIot10";
        } else if (hwVaule.equals("0") && swVaule.equals("1")) {
            return "SmartTV-AIot20";
        } else if (hwVaule.equals("1") && swVaule.equals("1")) {
            return "SmartTV-AIot30";
        }
        return "SmartTV";
    }

}
