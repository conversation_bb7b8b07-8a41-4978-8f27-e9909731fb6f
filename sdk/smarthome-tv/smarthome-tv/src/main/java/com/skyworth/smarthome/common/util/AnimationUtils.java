package com.skyworth.smarthome.common.util;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.view.View;
import android.view.animation.DecelerateInterpolator;

import com.skyworth.util.Util;

import java.util.ArrayList;
import java.util.List;

/**
 * Description: <br>
 * Created by wzh on 2019/3/18 14:00.
 */
public class AnimationUtils {

    public static void startAlpha(View view, long duration) {
        startAlpha(view, duration, null, 0, 1);
    }

    public static void startAlpha(View view, long duration, AnimatorListenerAdapter animatorListenerAdapter, float... values) {
        ObjectAnimator a = ObjectAnimator.ofFloat(view, "alpha", values);
        a.setDuration(duration);
        a.start();
        if (animatorListenerAdapter != null)
            a.addListener(animatorListenerAdapter);
    }

    public static void startRotation(View view, long duration, float... values) {
        ObjectAnimator a = ObjectAnimator.ofFloat(view, "rotation", values);
        a.setDuration(duration);
        a.start();
    }

    public interface IOperationScrollAnim {
        void startAnim();

        void endAnim();
    }

    /**
     * 同时动画移动多个View
     *
     * @param views
     * @param direction 方向
     */
    public static void scrollViews(List<View> views, String direction, long duration, int length, final IOperationScrollAnim iadScrollAnim) {
        List<Animator> animators = new ArrayList<>();
        AnimatorSet set = new AnimatorSet();
        if (views.size() <= 0) return;
        for (View view : views) {
            ObjectAnimator scrollAnimator = null;
            float y = view.getTranslationY();
            switch (direction) {
                case "up":
                    scrollAnimator = ObjectAnimator.ofFloat(view, "translationY", y, y - Util.Div(length));
                    break;
                case "down":
                    scrollAnimator = ObjectAnimator.ofFloat(view, "translationY", y, y + Util.Div(length));
                    break;
            }
            if (scrollAnimator != null) {
                scrollAnimator.setInterpolator(new DecelerateInterpolator());
                scrollAnimator.setDuration(duration);
                animators.add(scrollAnimator);
            }
        }
        set.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                if (iadScrollAnim != null) {
                    iadScrollAnim.endAnim();
                }
            }

            @Override
            public void onAnimationStart(Animator animation) {
                super.onAnimationStart(animation);
                if (iadScrollAnim != null) {
                    iadScrollAnim.startAnim();
                }
            }
        });
        set.playTogether(animators);
        set.start();
    }
}
