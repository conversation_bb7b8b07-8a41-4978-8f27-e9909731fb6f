package com.skyworth.smarthome.home.smartdevice.controlpanel.view;

import android.content.Context;

import com.skyworth.smarthome.home.smartdevice.controlpanel.view.base.BaseControlItem;
import com.skyworth.smarthome.home.smartdevice.controlpanel.view.item.ButtonItem;
import com.skyworth.smarthome.home.smartdevice.controlpanel.view.item.ProgressItem;
import com.skyworth.smarthome.home.smartdevice.controlpanel.view.item.SwitchItem;
import com.skyworth.smarthome.home.smartdevice.controlpanel.view.item.ToggleItem;

import java.util.ArrayList;
import java.util.List;

public class ControlWidgetFactory {
    private static List<String> SUPPORT_WIDGET_TYPE = new ArrayList<>();

    static {
        SUPPORT_WIDGET_TYPE.add(SwitchItem.TYPE);
        SUPPORT_WIDGET_TYPE.add(ProgressItem.TYPE);
        SUPPORT_WIDGET_TYPE.add(ButtonItem.TYPE);
        SUPPORT_WIDGET_TYPE.add(ToggleItem.TYPE);
    }

    public static BaseControlItem getItem(String type, Context context) {
        switch (type) {
            case SwitchItem.TYPE:
                return new SwitchItem(context);
//            case GroupDisplayItem.TYPE:
//                return new GroupDisplayItem(context);
            case ProgressItem.TYPE:
                return new ProgressItem(context);
            case ButtonItem.TYPE:
                return new ButtonItem(context);
            case ToggleItem.TYPE:
                return new ToggleItem(context);
        }
        return null;
    }

    public static boolean isSupport(String type) {
        return SUPPORT_WIDGET_TYPE.contains(type);
    }
}
