package com.skyworth.smarthome.common.http;

import com.skyworth.smarthome.common.bean.WeathBean;
import com.skyworth.smarthome.common.bean.CityBean;
import com.skyworth.smarthome.common.bean.RedirectHttpBean;
import com.smarthome.common.model.SmartBaseData;

import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Query;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/10
 */
public interface SmartHomeHttpMethod {

    @GET("smarthome/v1/panel-config/get-by-name")
    Call<SmartBaseData<RedirectHttpBean>> getRedirect(@Query("appkey") String appkey,
                                                      @Query("time") String time,
                                                      @Query("uid") String uid,
                                                      @Query("ak") String ak,
                                                      @Query("vuid") String vuid,
                                                      @Query("name") String name,
                                                      @Query("sign") String sign);

    @GET("devicemgr/v1/weather/weather-info")
    Call<SmartBaseData<WeathBean>> getWeatherInfo(@Query("appkey") String appkey,
                                                  @Query("time") String time,
                                                  @Query("uid") String uid,
                                                  @Query("ak") String ak,
                                                  @Query("vuid") String vuid,
                                                  @Query("city") String name,
                                                  @Query("sign") String sign);

    @GET("https://wifimodule.doubimeizhi.com/ip/ip-city")
    Call<SmartBaseData<CityBean>> getCityInfo(@Query("appkey") String appkey,
                                              @Query("time") String time,
                                              @Query("uid") String uid,
                                              @Query("ak") String ak,
                                              @Query("vuid") String vuid,
                                              @Query("sign") String sign);
}
