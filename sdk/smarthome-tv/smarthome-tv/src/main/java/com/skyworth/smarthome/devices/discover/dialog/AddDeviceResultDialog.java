package com.skyworth.smarthome.devices.discover.dialog;

import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.alibaba.fastjson.JSONObject;
import com.skyworth.smarthome.common.base.BaseSysDialog;
import com.skyworth.smarthome.common.util.DialogLauncherUtil;
import com.skyworth.smarthome.devices.apconfig.view.AddDeviceResultView;
import com.skyworth.smarthome.service.model.ISmartDeviceDataModel;
import com.skyworth.util.Util;
import com.smarthome.common.utils.EmptyUtils;
import com.swaiot.aiotlib.common.entity.DiscoverNetworkDevice;

import java.util.Map;

import static com.skyworth.smarthome.common.util.DialogLauncherUtil.DIALOG_KEY_ADDDEVICE;


/**
 * Describe:
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/10/9
 */
public class AddDeviceResultDialog extends BaseSysDialog {


    private DiscoverNetworkDevice mDevice;

    public static final String KEY_DEVICEINFO = "device_info";

    public AddDeviceResultDialog() {
        super();
    }

    @Override
    protected void initParams() {
        mDialogKey = DIALOG_KEY_ADDDEVICE;
    }

    @Override
    public void showDialog(Map<String, String> params) {
        super.showDialog(params);
        String deviceJson = params.get(KEY_DEVICEINFO);
        if(EmptyUtils.isNotEmpty(deviceJson)){
            mDevice = JSONObject.parseObject(deviceJson, DiscoverNetworkDevice.class);
        }
        initUI();
    }


    @Override
    protected void initContentView() {
        super.initContentView();
    }

    private void initUI() {

        ImageView bg = new ImageView(mContext);
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(Util.Div(1920), Util.Div(1080));
        mContentView.addView(bg, params);

        FrameLayout mLayout = new FrameLayout(mContext);
        AddDeviceResultView addDeviceResultView = new AddDeviceResultView(mContext);
        addDeviceResultView.updateUI(AddDeviceResultView.TYPE_DISCOVER_DISAPPEAR, mDevice.device_name + " 已失去连接",
                mDevice.product_type_logo);
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Util.Div(714), Util.Div(460));
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL|Gravity.CENTER_VERTICAL;
        mLayout.addView(addDeviceResultView, layoutParams);
        addDeviceResultView.setAddDeviceResultCallBack(new AddDeviceResultView.AddDeviceResultCallBack() {
            @Override
            public void onOkClick(int type) {
                DialogLauncherUtil.dismissDialog(DialogLauncherUtil.DIALOG_KEY_ADD_DEVICE_RESULT);
                ISmartDeviceDataModel.INSTANCE.removeDiscoverDevice(mDevice);
            }

            @Override
            public void onCancelClick(int type) {
            }

            @Override
            public void onHideView(int type) {
            }
        });
        //触摸弹框之外的部分关掉弹窗逻辑处理
        addDeviceResultView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
        mContentView.addView(mLayout, new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT));
        openAutoDismissDialog();
    }

}
