package com.skyworth.smarthome.devices.apconfig.presenter.step.auto;

import com.coocaa.app.core.app.AppCoreApplication;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.util.NetworkUtils;
import com.skyworth.smarthome.common.util.Utils;
import com.skyworth.smarthome.devices.apconfig.presenter.helpers.ConfirmBindHelper;
import com.skyworth.smarthome.devices.apconfig.presenter.step.BaseStep;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_INPUT_GO_LOGIN;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_INPUT_GO_LOGIN_AND_BIND;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_INPUT_SCREEN_SAVER_CLOSE;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/9/17 11:25.
 */
public class AutoStepBindDevice extends BaseStep {
    public static final String STEP_TAG = "auto_bind_device";
    private boolean isScreenShowWhenLogin = false;

    @Override
    public void run() {
        super.run();
        int retryTimes = 0;
        while (!NetworkUtils.checkNetWorkCanUse() && retryTimes < 5) {
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            retryTimes++;
            logi("check net times: " + retryTimes);
        }
        if (!presenter.isLogin()) {
            if (Utils.isScreenSaverShow()) {
                presenter.setConfirmHelperFlag(ConfirmBindHelper.UnConfirmData.STATUS_NOT_LOGIN);
                logi("not login wait for screen close");
                isScreenShowWhenLogin = true;
                return;
            }
            presenter.showDialog();
            presenter.clearConfirmHelperFlag();
            output(STEP_MSG_INPUT_GO_LOGIN_AND_BIND);
            submitLoginAndBind();
            return;
        }
        if (!presenter.isBindMobile()) {
            presenter.recordNotBindAndExit(AppConstants.APCONFIG_FAIL_REASON_NOT_BIND_MOBILE);
            return;
        }
        bind();
    }

    private void submitLoginAndBind() {
        if (isScreenShowWhenLogin) {
            isScreenShowWhenLogin = false;
            presenter.logLoginAndBindAfterScreenSaver();
        }
    }

    private void bind() {
        AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                int bindStatus = presenter.getBindStatus();
                if (bindStatus == 1) {
                    next();
                } else if (bindStatus == 2) {
                    if (presenter.bindDevice(null)) {
                        next();
                    } else {
                        presenter.recordNotBindAndExit(AppConstants.APCONFIG_FAIL_REASON_ACCOUNT_BIND_DEVICE_FAIL);
                    }
                } else if (bindStatus == 3) {
                    presenter.recordNotBindAndExit(AppConstants.APCONFIG_FAIL_REASON_DEVICE_OTHER_BIND);
                }
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public boolean input(String msg, Object... params) {
        if (STEP_MSG_INPUT_GO_LOGIN.equals(msg)) {
            presenter.hideLoginAndBind();
            presenter.goLogin();
            return true;
        } else if (STEP_MSG_INPUT_SCREEN_SAVER_CLOSE.equals(msg)) {
            int tryTimes = 0;
            while (Utils.isScreenSaverShow() && tryTimes < 10) {
                try {
                    Thread.sleep(500);
                    tryTimes++;
                    logi("screen saver close check again: " + tryTimes);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
            reload();
            return true;
        }
        return false;
    }

    @Override
    public String getTag() {
        return STEP_TAG;
    }

    @Override
    public void destroy() {
        super.destroy();
        isScreenShowWhenLogin = false;
    }
}
