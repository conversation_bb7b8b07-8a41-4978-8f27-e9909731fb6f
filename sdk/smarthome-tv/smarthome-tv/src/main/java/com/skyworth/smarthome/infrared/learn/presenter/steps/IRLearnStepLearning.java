package com.skyworth.smarthome.infrared.learn.presenter.steps;

import com.coocaa.app.core.app.AppCoreApplication;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

import static com.skyworth.smarthome.infrared.learn.presenter.IRLearnPresenterImpl.INPUT_LEARN_FAILED;
import static com.skyworth.smarthome.infrared.learn.presenter.IRLearnPresenterImpl.INPUT_LEARN_SUCCESS;

/**
 * Description: <br>
 * Created by <PERSON><PERSON>ex<PERSON><PERSON> on 2019/7/4 10:20.
 */
public class IRLearnStepLearning extends BaseIRLearnStep {
    public static final String STEP_TAG = "learning";

    @Override
    public void create() {
        super.create();
    }

    @Override
    public void run() {
        super.run();
        presenter.showLearning();
    }

    @Override
    public boolean input(String msg, final Object... params) {
        if (INPUT_LEARN_SUCCESS.equals(msg)) {
            AppCoreApplication.Companion.ioThread(new Function0<Unit>() {
                @Override
                public Unit invoke() {
                    if (!presenter.saveLearntKey((int[]) params[0])) {
                        jumpTo(IRLearnStepFail.STEP_TAG);
                    }
                    if (presenter.isLearnFinish()) {
                        jumpTo(IRLearnStepFinish.STEP_TAG);
                    } else {
                        jumpTo(IRLearnStepSuccess.STEP_TAG);
                    }
                    return Unit.INSTANCE;
                }
            });
            return true;
        } else if (INPUT_LEARN_FAILED.equals(msg)) {
            jumpTo(IRLearnStepFail.STEP_TAG);
            return true;
        }
        return false;
    }

    @Override
    public String getTag() {
        return STEP_TAG;
    }

    @Override
    public void destroy() {
        super.destroy();
    }
}
