package com.skyworth.smarthome.home.smartdevice.controlpanel.view.item;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;


import com.skyworth.smarthome.home.smartdevice.controlpanel.common.itemdata.SwitchControlData;
import com.skyworth.smarthome.home.smartdevice.controlpanel.view.base.BaseControlItem;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.util.Utils;
import com.skyworth.util.Util;

import java.util.HashMap;
import java.util.Map;

public class SwitchItem extends BaseControlItem<SwitchControlData> {
    public static final String TYPE = "switch";
    private TextView mTitle = null;
    private TextView mValue = null;
    private View mLeftArrow = null;
    private View mRightArrow = null;
    private View mLeftArrowClick = null;
    private View mRightArrowClick = null;
    private FrameLayout mValueLayout = null;

    public SwitchItem(Context context){
        super(context);
        initTitleBg();
        initValue();
        initText();
        initListener();
    }

    //右边
    private void initValue() {
        mValueLayout = new FrameLayout(getContext());
        mValueLayout.setBackground(mTitleBgUnFocus);
        mValueLayout.setMinimumWidth(Util.Div(200));
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, Util.Div(50));
        layoutParams.gravity = Gravity.CENTER_VERTICAL | Gravity.RIGHT;
        layoutParams.rightMargin = Util.Div(20);
        addView(mValueLayout, layoutParams);

        mLeftArrow = new View(getContext());
        mLeftArrow.setBackgroundResource(R.drawable.arrow_left);
        LayoutParams layoutParamsItem = new LayoutParams(Util.Div(11), Util.Div(22));
        layoutParamsItem.gravity = Gravity.LEFT|Gravity.CENTER_VERTICAL;
        layoutParamsItem.leftMargin = Util.Div(20);
        mValueLayout.addView(mLeftArrow, layoutParamsItem);

        mLeftArrowClick = new View(getContext());
        mLeftArrowClick.setBackgroundResource(R.color.translucent);
        layoutParamsItem = new LayoutParams(Util.Div(50), Util.Div(50));
        layoutParamsItem.gravity = Gravity.LEFT;
        mValueLayout.addView(mLeftArrowClick, layoutParamsItem);

        mValue = new TextView(getContext());
        mValue.setTextSize(Util.Dpi(28));
        mValue.setTextColor(Color.argb(204, 255, 255, 255));
        mValue.setGravity(Gravity.CENTER);
        mValue.setPadding(Util.Div(32),0,Util.Div(32),0);
        mValue.setMinWidth(Util.Div(120));
        mValue.setSingleLine(true);
        layoutParamsItem = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParamsItem.gravity = Gravity.CENTER;
        mValueLayout.addView(mValue, layoutParamsItem);

        mRightArrow = new View(getContext());
        mRightArrow.setBackgroundResource(R.drawable.arrow_right);
        layoutParamsItem = new LayoutParams(Util.Div(11), Util.Div(22));
        layoutParamsItem.gravity = Gravity.RIGHT|Gravity.CENTER_VERTICAL;
        layoutParamsItem.rightMargin = Util.Div(20);
        mValueLayout.addView(mRightArrow, layoutParamsItem);

        mRightArrowClick = new View(getContext());
        mRightArrowClick.setBackgroundResource(R.color.translucent);
        layoutParamsItem = new LayoutParams(Util.Div(50), Util.Div(50));
        layoutParamsItem.gravity = Gravity.RIGHT;
        mValueLayout.addView(mRightArrowClick, layoutParamsItem);
    }

    //左边标题
    private void initText() {
        mTitle = new TextView(getContext());
        mTitle.setTextSize(Util.Dpi(32));
        mTitle.setTextColor(Color.parseColor("#aaFFFFFF"));
        mTitle.setGravity(Gravity.CENTER);
        mTitle.getPaint().setFakeBoldText(true);
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.leftMargin = Util.Div(20);
        layoutParams.gravity = Gravity.CENTER_VERTICAL;
        addView(mTitle, layoutParams);
    }

    private void initListener(){
        mLeftArrowClick.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if(isEnable){
                    onKeyLeft();
                }
            }
        });

        mRightArrowClick.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if(isEnable){
                    onKeyRight();
                }
            }
        });
    }

    @Override
    protected void refreshUI() {
        logi("refreshUI:");
        if (mData == null) {
            loge("refreshUI: mData == null");
            return;
        }
        mTitle.setText(mData.title);
        String value = mStatus.getString(mData.data_field);
        String showText = mData.values.get(value);
        logi("refreshUI: " + showText);
        if (showText != null && !showText.equals(mValue.getText().toString())) {
            mValue.setText(showText);
        }
        if (showText == null) {
            mValue.setText(R.string.control_panel_not_selected);
        }
    }

    @Override
    public void onRecycle() {

    }

    @Override
    public void onFocus(boolean hasFocus) {
        super.onFocus(hasFocus);
        if (hasFocus) {
            mTitle.setTextColor(Color.parseColor("#000000"));
            mValue.setTextColor(Color.parseColor("#cc000000"));
            mValue.getPaint().setFakeBoldText(true);
            mValueLayout.setBackground(mTitleBgFocus);
            mLeftArrow.setBackgroundResource(R.drawable.arrow_left_focus);
            mRightArrow.setBackgroundResource(R.drawable.arrow_right_focus);
        } else {
            mTitle.setTextColor(Color.parseColor("#aaFFFFFF"));
            mValue.setTextColor(Color.parseColor("#ccFFFFFF"));
            mValue.getPaint().setFakeBoldText(false);
            mValueLayout.setBackground(mTitleBgUnFocus);
            mLeftArrow.setBackgroundResource(R.drawable.arrow_left);
            mRightArrow.setBackgroundResource(R.drawable.arrow_right);
        }
    }

    @Override
    public boolean onKeyLeft() {
        if (mData == null) {
            loge("onKeyLeft: mData == null");
            return true;
        }
        String currentValue = mStatus.getString(mData.data_field);
        Object[] keys = mData.values.keySet().toArray();
        String newValue;
        if (keys[0].equals(currentValue)) {
            newValue = keys[keys.length - 1].toString();
        } else {
            int currentIndex = Utils.getArrayIndex(keys, currentValue);
            newValue = currentIndex - 1 >= 0 ? keys[currentIndex - 1].toString() : keys[keys.length - 1].toString();
        }
        logi("onKeyLeft: " + mData.data_field + " " + newValue);
        Map<String, String> status = new HashMap<>();
        status.put(mData.data_field, newValue);
        controlDevice(status);
        return true;
    }

    @Override
    public boolean onKeyRight() {
        if (mData == null) {
            logi("onKeyRight: mData == null");
            return true;
        }
        String currentValue = mStatus.getString(mData.data_field);
        Object[] keys = mData.values.keySet().toArray();
        String newValue;
        if (keys[keys.length - 1].equals(currentValue)) {
            newValue = keys[0].toString();
        } else {
            int currentIndex = Utils.getArrayIndex(keys, currentValue);
            newValue = currentIndex + 1 < keys.length ? keys[currentIndex + 1].toString() : keys[0].toString();
        }
        logi("onKeyRight: " + mData.data_field + " " + newValue);
        Map<String, String> status = new HashMap<>();
        status.put(mData.data_field, newValue);
        controlDevice(status);
        return true;
    }

    @Override
    public void enable() {
        super.enable();
        mLeftArrowClick.setFocusable(true);
        mRightArrowClick.setFocusable(true);
    }

    @Override
    public void disable() {
        super.disable();
        mLeftArrowClick.setFocusable(false);
        mRightArrowClick.setFocusable(false);
    }

    @Override
    public String getType() {
        return TYPE;
    }

    @Override
    public boolean canFocusable() {
        return isEnable;
    }

    @Override
    public boolean canFocusableDefault() {
        return true;
    }
}
