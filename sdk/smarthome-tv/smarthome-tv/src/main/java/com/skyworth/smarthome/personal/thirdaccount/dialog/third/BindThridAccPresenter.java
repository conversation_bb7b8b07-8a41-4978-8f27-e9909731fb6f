package com.skyworth.smarthome.personal.thirdaccount.dialog.third;

import android.util.Log;

import com.skyworth.smarthome.common.event.OtherAccountAuthResultEvent;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * Description: <br>
 * Created by wzh on 2019/3/21 12:39.
 */
public class BindThridAccPresenter {

    private final static String TAG = "BindTPresenter";
    private BindThridAccDialog.OnBindResultListener mListener;
    private boolean stopPolling = false;

    public BindThridAccPresenter(BindThridAccDialog.OnBindResultListener listener) {
        mListener = listener;
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    public void pollingAccountResult(final String account_type) {
//        stopPolling = false;
//        FuncKt.workerThread(2000, new Function0<Unit>() {//2秒轮询一次
//            @Override
//            public Unit invoke() {
//                if (stopPolling) return Unit.INSTANCE;//未避免在两秒内退出了此页面
//                final List<ThridAccountHttpBean> thridAccountList = ISmartHomeModel.INSTANCE.getThirdAccountList();
//                if (thridAccountList != null && thridAccountList.size() > 0) {
//                    for (ThridAccountHttpBean data : thridAccountList) {
//                        if (data.account_type.equals(account_type)) {
//                            if (data.bind_status.equals("1")) {//表示绑定上了,就不再轮询
//                                stopPolling = true;
//                                Log.i(TAG, "pollingAccountResult: bind success");
//                                if (mListener != null) mListener.result(true);
//                                break;
//                            }
//                        }
//                    }
//                }
//                Log.i(TAG, "pollingAccountResult: stopPolling:" + stopPolling);
//                if (!stopPolling) pollingAccountResult(account_type);//轮询查询绑定状态
//                return Unit.INSTANCE;
//            }
//        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void Event(OtherAccountAuthResultEvent event) {
        Log.i(TAG, "Event: stopPolling:" + stopPolling);
        if (!stopPolling) {
            if (mListener != null) {
                mListener.result(true);
            }
            stopPolling();
        }
    }

    public void stopPolling() {
        stopPolling = true;
        try {
            unregisterPush();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void unregisterPush() {
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }
}
