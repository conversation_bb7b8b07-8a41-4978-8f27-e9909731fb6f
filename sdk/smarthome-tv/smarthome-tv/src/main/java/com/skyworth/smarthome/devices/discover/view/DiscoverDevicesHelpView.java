package com.skyworth.smarthome.devices.discover.view;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.skyworth.smarthome.R;
import com.skyworth.util.Util;

/**
 * Description:发现设备帮助View <br>
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/1/31 10:04.
 */
public class DiscoverDevicesHelpView extends LinearLayout {

    public DiscoverDevicesHelpView(Context context) {
        super(context);
        setOrientation(LinearLayout.VERTICAL);
        initView(context);
    }

    private void initView(Context context){
        TextView mTitileTv = new TextView(context);
        mTitileTv.setText(context.getString(R.string.discover_devices_help_titile));
        mTitileTv.setTextSize(Util.Dpi(48));
//        mTitileTv.setTextColor(XThemeUtils.c_1());
        mTitileTv.getPaint().setFakeBoldText(true);
        mTitileTv.setGravity(Gravity.CENTER);
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        params.topMargin =  Util.Div(90);
        addView(mTitileTv, params);

        TextView mContentTv = new TextView(context);
        mContentTv.setTextSize(Util.Dpi(36));
        mContentTv.setTextColor(Color.parseColor("#CDD2D8"));
        mContentTv.setGravity(Gravity.LEFT);
        mContentTv.setLineSpacing(Util.Dpi(8),1);
        mContentTv.setText(context.getString(R.string.discover_devices_help_tips));
        LayoutParams contentParams = new LayoutParams(Util.Div(1540), ViewGroup.LayoutParams.WRAP_CONTENT);
        contentParams.gravity = Gravity.CENTER_HORIZONTAL;
        contentParams.topMargin = Util.Div(60);
        addView(mContentTv, contentParams);
    }
}
