package com.skyworth.smarthome.common.util;

import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.drawable.GradientDrawable;
import android.os.Build;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.Window;
import android.view.WindowManager;

import com.skyworth.smarthome.R;
import com.skyworth.util.Util;

/**
 * @ProjectName: NewTV_SmartHome
 * @Package: com.skyworth.smarthome_tv.common.uitl
 * @ClassName: DialogsBuilder
 * @Description: 生成各种dialog
 * @Author: wangyuehui
 * @CreateDate: 2020/6/9 16:34
 * @UpdateUser: 更新者
 * @UpdateDate: 2020/6/9 16:34
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class DialogsBuilder {
    /**
     *
     * 系统dialog：解绑对话框
     *
     * */
    public static Dialog showUnbindDialog(Context context) {

        final AlertDialog dialog = new AlertDialog.Builder(context).create();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M){//6.0+
            dialog.getWindow().setType(
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
        }else {
            dialog.getWindow().setType(
                    WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
        }
        dialog.setCanceledOnTouchOutside(true);
        dialog.show();
        Window window = dialog.getWindow();
        WindowManager.LayoutParams params = dialog.getWindow().getAttributes();

        params.width =  Util.Div(714);
        params.height =  Util.Div(400);
        params.gravity = Gravity.CENTER;
        window.setAttributes(params);
        window.setContentView(ViewsBuilder.getUnBindDialogView(context));

        //设置背景
        GradientDrawable rootViewBackGroudDrawable = new GradientDrawable();
        rootViewBackGroudDrawable.setShape(GradientDrawable.RECTANGLE);
        rootViewBackGroudDrawable.setColor(context.getResources().getColor(R.color.color_444444));
        rootViewBackGroudDrawable.setCornerRadius(Util.Div(16));
        window.setBackgroundDrawable(rootViewBackGroudDrawable);

        dialog.setOnKeyListener(new DialogInterface.OnKeyListener() {
            @Override
            public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                if (event.getKeyCode() == KeyEvent.KEYCODE_BACK || event.getKeyCode() == KeyEvent.KEYCODE_HOME) {
                    dialog.dismiss();
                    return true;
                }
                return false;
            }
        });

        return dialog;
    }
}
