package com.skyworth.smarthome.infrared.learn.view.pages;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import android.support.annotation.NonNull;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.devices.apconfig.view.SmartRateMessageView;
import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.util.Util;

import java.util.HashMap;
import java.util.Map;

import static com.skyworth.smarthome.devices.apconfig.view.SmartRateMessageView.TYPE_INFRARED;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/7/2 18:25.
 */
public class IRLearnFinishView extends BaseIRLearnViews {
    private static final String DEVICE_ID = "deviceId";
    private SmartRateMessageView smartRateMessageView = null;
    private TextView mButton;

    public IRLearnFinishView(@NonNull Context context) {
        super(context);
        smartRateMessageView = new SmartRateMessageView(context, TYPE_INFRARED);
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        layoutParams.topMargin = Util.Div(60);
        addView(smartRateMessageView, layoutParams);

        mButton = new TextView(getContext());
        mButton.setBackground(new CCFocusDrawable(getContext()).setRadius(Util.Dpi(16)).setSolidColor(getResources().getColor(R.color.white)));
        mButton.setTextColor(Color.parseColor("#000000"));
        mButton.setTextSize(Util.Dpi(32));
        mButton.setFocusableInTouchMode(true);
        mButton.setFocusable(true);
        mButton.setText("知道了");
        mButton.setTypeface(Typeface.DEFAULT_BOLD);
        mButton.setGravity(Gravity.CENTER);
        mButton.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mPresenter != null) {
                    mPresenter.exit();
                }
            }
        });
        layoutParams = new LayoutParams(Util.Div(614), Util.Div(90));
        layoutParams.topMargin = Util.Div(256);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        addView(mButton, layoutParams);
    }

    @Override
    protected void onShow(Map<String, Object> params) {
        if (params == null || params.size() <= 0) {
            return;
        }
        String deviceId = (String) params.get("device");
        smartRateMessageView.setDeviceId(deviceId);
        smartRateMessageView.setTitle(getResources().getString(R.string.ir_learn_finish_tip));
        smartRateMessageView.updateUI();
        smartRateMessageView.setVisibility(View.VISIBLE);
    }

    @Override
    public int getViewWidth() {
        return Util.Div(714);
    }

    @Override
    public int getViewHeight() {
        return Util.Div(400);
    }

    public static Map<String, Object> getParams(String deviceId) {
        Map<String, Object> params = new HashMap<>(1);
        params.put(DEVICE_ID, deviceId);
        return params;
    }

    @Override
    public String getName() {
        return "IRLearnFinishView";
    }
}
