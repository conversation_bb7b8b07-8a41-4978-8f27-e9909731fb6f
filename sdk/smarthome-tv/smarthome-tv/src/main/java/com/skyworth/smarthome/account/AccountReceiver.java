package com.skyworth.smarthome.account;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.skyworth.smarthome.common.model.UserInfo;
import com.skyworth.smarthome.common.util.LogUtil;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.smarthome.common.account.AccountManager;
import com.smarthome.common.utils.EmptyUtils;

/**
 * @ClassName: AccountReceiver
 * @Author: AwenZeng
 * @CreateDate: 2020/9/4 19:11
 * @Description:
 */
public class AccountReceiver extends BroadcastReceiver {
    private static long lastClickTime = 0L;
    private static final long CLICK_INTERVAL = 100;
    public static final String ACCOUNT_CHANGED = "com.tianci.user.account_changed";
    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        LogUtil.androidLog("acount change action：" + action);
        if (action != null && action.equals(ACCOUNT_CHANGED)) {
            if (isQucikClick() && isAccountChanged()) {
                LogUtil.androidLog("账号变化");
                IAppAccountManager.INSTANCE.getAccountInfo(true);
            }
        }
    }

    private boolean isQucikClick() {
        long time = System.currentTimeMillis();
        if ((time - lastClickTime) > CLICK_INTERVAL) {
            lastClickTime = time;
            return true;
        }
        lastClickTime = time;
        return false;
    }

    /**
     * 检测账号是否变化
     *
     * @return
     */
    private boolean isAccountChanged() {
        String userID = UserInfo.getInstance().getUserID();
        String openID = AccountManager.getManager(SmartHomeTvLib.getContext()).getOpenId();
        if (EmptyUtils.isEmpty(openID) || EmptyUtils.isEmpty(userID)) {
            return true;
        }
        if (!userID.equals(openID)) {
            return true;
        }
        return false;
    }
}
