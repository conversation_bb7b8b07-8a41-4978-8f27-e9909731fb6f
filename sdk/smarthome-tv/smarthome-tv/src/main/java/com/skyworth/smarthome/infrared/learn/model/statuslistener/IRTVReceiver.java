package com.skyworth.smarthome.infrared.learn.model.statuslistener;

import android.util.Log;

import com.skyworth.smarthome.common.event.IRLearnStatusEvent;
import com.skyworth.smarthome.infrared.learn.model.IIRLearnModel;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.util.Map;

import static com.skyworth.smarthome.infrared.learn.IRLearnDialog.TAG;

/**
 * Description: <br>
 * Created by <PERSON><PERSON>exiao on 2019/7/5 16:48.
 */
public class IRTVReceiver implements ILearnStatusReceiver {
    private IIRLearnModel model = null;

    @Override
    public void startListen(IIRLearnModel model, Map<String, Object> params) {
        this.model = model;
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Subscribe
    public void onEvent(IRLearnStatusEvent event) {
        Log.i(TAG, "onEvent: IRTVReceiver");
        String json = event.getData().toString();
        if (model != null) {
            model.checkLearnResult(json);
        }
    }

    @Override
    public void stopListen() {
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }
}
