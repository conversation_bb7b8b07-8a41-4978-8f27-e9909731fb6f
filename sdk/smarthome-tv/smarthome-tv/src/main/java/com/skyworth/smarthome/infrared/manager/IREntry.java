package com.skyworth.smarthome.infrared.manager;

import android.content.Context;
import android.util.Log;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.widget.FrameLayout;
import android.support.annotation.NonNull;

import com.skyworth.smarthome.infrared.controldevices.presenter.IRemoteControlDevicesPresenter;
import com.skyworth.smarthome.infrared.controldevices.presenter.RemoteControlDevicesPresenter;
import com.skyworth.smarthome.infrared.controldevices.view.IShowRemoteControl;
import com.skyworth.smarthome.infrared.controldevices.view.RemoteControlDevicesView;
import com.skyworth.smarthome.infrared.electriclist.presenter.ElectricBrandPresenter;
import com.skyworth.smarthome.infrared.electriclist.presenter.ElectricTypePresenter;
import com.skyworth.smarthome.infrared.electriclist.presenter.IElectricBrandPresenter;
import com.skyworth.smarthome.infrared.electriclist.presenter.IElectricTypePresenter;
import com.skyworth.smarthome.infrared.electriclist.view.ElectricBrandView;
import com.skyworth.smarthome.infrared.electriclist.view.ElectricTypeView;
import com.skyworth.smarthome.infrared.electriclist.view.IElectircTypeView;
import com.skyworth.smarthome.infrared.electriclist.view.IElectricBrandView;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.util.Utils;
import com.skyworth.util.Util;
import com.smarthome.common.utils.EmptyUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Stack;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/4/30 18:19.
 */
public class IREntry extends FrameLayout implements IIREntry, IShowRemoteControl {
    private IIRControl iirControl;
    private IViewStackManager viewStackManager = null;
    private _RemoteControlDevicesView mRemoteControlDevicesView;
    private _ElectircTypeView mElectricTypeView;
    private _ElectricBrandView mElectricBrandView;
    public static final String DEVICE_KEY = "device_id";
    public static final String DEVICE_NAME_KEY = "device_name";
    public static final String DEVICE_TYPE_ID_KEY = "device_type_id";
    public static final String KK_TYPE_ID = "kk_type_id";
    public static final String SDK_PRIORITY = "sdk_priority";
    public static final String KK_BRAND_ID = "kk_brand_id";
    private Map<String, Object> mParams = new HashMap<>();
    private static List<OnKeyListener> listeners = new ArrayList<>();

    public static void regOnKeyListener(OnKeyListener listener) {
        if (!listeners.contains(listener)) {
            listeners.add(listener);
        }
    }

    public static void unRegOnKeyListener(OnKeyListener listener) {
        listeners.remove(listener);
    }

    public interface EachView {
        String getName();

        void show(Map<String, Object> map);

        void hide();
    }

    public class _RemoteControlDevicesView implements EachView {
        RemoteControlDevicesView remoteControlDevicesView = null;
        IRemoteControlDevicesPresenter presenter;

        @Override
        public void show(Map<String, Object> map) {
            if (EmptyUtils.isEmpty(remoteControlDevicesView)) {
                remoteControlDevicesView = new RemoteControlDevicesView(getContext());
                remoteControlDevicesView.setParams(mParams);
                presenter = new RemoteControlDevicesPresenter();
                remoteControlDevicesView.createView(getContext(), presenter);
                presenter.setView(remoteControlDevicesView);
                presenter.setShowAddInfraredDevice(IREntry.this);
                Object deviceIdValue = map == null ? null : map.get(DEVICE_KEY);
                Object deviceNameValue = map == null ? null : map.get(DEVICE_NAME_KEY);
                Object deviceTypeId = map == null ? null : map.get(DEVICE_TYPE_ID_KEY);
                presenter.setDeviceId(deviceIdValue == null ? "" : deviceIdValue.toString());
                presenter.setDeviceName(deviceNameValue == null ? "" : deviceNameValue.toString());
                presenter.setDeviceTypeId(deviceTypeId == null ? "" : deviceTypeId.toString());
                presenter.loadData();
            }
            if (remoteControlDevicesView.getView().getParent() == null) {
                LayoutParams params = new LayoutParams(Util.Div(660), Util.Div(810));
                params.gravity = Gravity.CENTER;
                addView(remoteControlDevicesView.getView(), params);
            }
            if (remoteControlDevicesView.getView().getVisibility() == View.GONE) {
                remoteControlDevicesView.requestFocusUI();
            }
            remoteControlDevicesView.getView().setVisibility(VISIBLE);
        }

        @Override
        public void hide() {
            if (EmptyUtils.isNotEmpty(remoteControlDevicesView)) {
                remoteControlDevicesView.getView().setVisibility(View.GONE);
            }
        }

        @Override
        public String getName() {
            return "RemoteControlDevicesView";
        }
    }

    public class _ElectircTypeView implements EachView {
        IElectircTypeView electircTypeView = null;
        IElectricTypePresenter presenter = null;

        @Override
        public void show(Map<String, Object> map) {
            if (EmptyUtils.isEmpty(electircTypeView)) {
                electircTypeView = new ElectricTypeView(getContext());
                presenter = new ElectricTypePresenter();
                electircTypeView.createView(getContext(), presenter);
                presenter.setView(electircTypeView);
                presenter.setIShowRemoteControl(IREntry.this);
                LayoutParams params = new LayoutParams(Util.Div(660), Util.Div(810));
                params.gravity = Gravity.CENTER;
                addView(electircTypeView.getView(), params);
                presenter.loadList();
                Object deviceIdValue = map == null ? null : map.get(DEVICE_KEY);
                Object deviceNameValue = map == null ? null : map.get(DEVICE_NAME_KEY);
                Object deviceTypeId = map == null ? null : map.get(DEVICE_TYPE_ID_KEY);
                presenter.setDeviceId(deviceIdValue == null ? "" : deviceIdValue.toString());
                presenter.setDeviceName(deviceNameValue == null ? "" : deviceNameValue.toString());
                presenter.setDeviceTypeId(deviceTypeId == null ? "" : deviceTypeId.toString());
            }
            electircTypeView.getView().setVisibility(View.VISIBLE);
            electircTypeView.getFocus();
            logShow();
        }

        private void logShow() {
//            Map<String, String> params = new HashMap<>();
//            String origin;
//            if (DeviceUtil.isIRAI(String.valueOf(mParams.get(IREntry.DEVICE_TYPE_ID_KEY)))) {
//                origin = DataConstants.KEY_ENTER_INFRARED_ORIGIN_IRAI;
//            } else {
//                origin = DataConstants.KEY_ENTER_INFRARED_ORIGIN_IRTV;
//            }
//            String from;
//            if ("voice".equals(HomeDialogParams.getOneTimeParamsStringValue(Constants.KEY_ENTER_INFRARED_FROM))) {
//                from = DataConstants.KEY_ENTER_INFRARED_FROM_VOICE;
//            } else {
//                from = DataConstants.KEY_ENTER_INFRARED_FROM_CLICK;
//            }
//            params.put("origin", origin);
//            params.put("entry_style", from);
//            LoggerImpl.Companion.onEvent(DataConstants.EVENT_INFRARED_SELECT_DEVICE_TYPE_SHOW, params);
        }

        @Override
        public void hide() {
            if (EmptyUtils.isNotEmpty(electircTypeView)) {
                electircTypeView.getView().setVisibility(View.GONE);
            }
        }

        @Override
        public String getName() {
            return "ElectircTypeView";
        }
    }

    public class _ElectricBrandView implements EachView {
        IElectricBrandView electricBrandView = null;
        IElectricBrandPresenter presenter = null;

        @Override
        public String getName() {
            return "IElectricBrandView";
        }

        @Override
        public void show(Map<String, Object> map) {
            if (EmptyUtils.isEmpty(electricBrandView)) {
                electricBrandView = new ElectricBrandView(getContext());
                presenter = new ElectricBrandPresenter();
                presenter.setParams(mParams);
                electricBrandView.createView(getContext(), presenter);
                presenter.setView(electricBrandView);
                LayoutParams params = new LayoutParams(Util.Div(660), Util.Div(810));
                params.gravity = Gravity.CENTER;
                addView(electricBrandView.getView(), params);
            }
            electricBrandView.getView().setVisibility(View.VISIBLE);
//            electricBrandView.getFocus();
            List<String> hotList = null;
            try {
                hotList = (List<String>) map.get("hotList");
            } catch (Exception e) {
                e.printStackTrace();
            }
            Object deviceIdValue = map == null ? null : map.get(DEVICE_KEY);
            Object deviceNameValue = map == null ? null : map.get(DEVICE_NAME_KEY);
            Object deviceTypeId = map == null ? null : map.get(DEVICE_TYPE_ID_KEY);
            presenter.setDeviceId(deviceIdValue == null ? "" : deviceIdValue.toString());
            presenter.setDeviceName(deviceNameValue == null ? "" : deviceNameValue.toString());
            presenter.setDeviceTypeId(deviceTypeId == null ? "" : deviceTypeId.toString());
            presenter.loadList((String) map.get("id"), (String) map.get("typeName"), hotList);
//            LoggerImpl.Companion.onEvent(DataConstants.EVENT_INFRARED_SELECT_DEVICE_BRAND_SHOW);
        }

        @Override
        public void hide() {
            if (EmptyUtils.isNotEmpty(electricBrandView)) {
                electricBrandView.getView().setVisibility(View.GONE);
            }
        }
    }

    public interface IViewStackManager {
        void onKeyBack();

        void showView(EachView view, Map<String, Object> map);

        void destroy();
    }

    public class ViewStackManagerImpl implements IViewStackManager {
        private Stack<EachView> viewStack = new Stack<>();

        @Override
        public void onKeyBack() {
            if (viewStack.size() == 0) {
                iirControl.exitIR();
            } else if (viewStack.size() == 1) {
                EachView view = viewStack.pop();
                Log.i("irconfig", "onKeyBack: " + view.getName() + " hide");
                view.hide();
                iirControl.exitIR();
            } else {
                EachView view = viewStack.pop();
                Log.i("irconfig", "onKeyBack: " + view.getName() + " hide");
                view.hide();
                view = viewStack.peek();
                Log.i("irconfig", "onKeyBack: " + view.getName() + " show");
                view.show(null);
            }
        }

        @Override
        public void showView(EachView view, Map<String, Object> map) {
            mParams.putAll(map);
            Log.i("irconfig", "showView: " + Utils.printMap(mParams));
            if (!viewStack.isEmpty()) {
                EachView pre = viewStack.peek();
                Log.i("irconfig", "onKeyBack: " + pre.getName() + " hide");
                pre.hide();
            }
            viewStack.push(view);
            Log.i("irconfig", "onKeyBack: " + view.getName() + " show");
            view.show(map);
        }

        @Override
        public void destroy() {
            viewStack.clear();
        }
    }

    public interface IIRControl {
        void exitIR();
    }

    public IREntry(@NonNull Context context, IIRControl listener) {
        super(context);
        iirControl = listener;
        viewStackManager = new ViewStackManagerImpl();
    }

    @Override
    public void show(Map<String, Object> params) {
        showRemoteControlDevices(params);
        boolean isShowAddIR = params.containsKey(AppConstants.KEY_ENTER_INFRARED_ADD) && String.valueOf(params.get(AppConstants.KEY_ENTER_INFRARED_ADD)).equals(Boolean.TRUE.toString());
        Log.i("irconfig", "isShowAddIR: " + isShowAddIR);
        if (isShowAddIR) {
            showElectricTypeView(params);
        }
    }


    @Override
    public void showRemoteControlDevices(Map<String, Object> params) {
        if (mRemoteControlDevicesView == null) {
            mRemoteControlDevicesView = new _RemoteControlDevicesView();
        }
        viewStackManager.showView(mRemoteControlDevicesView, params);
    }

    @Override
    public void showElectricTypeView(Map<String, Object> map) {
        if (mElectricTypeView == null) {
            mElectricTypeView = new _ElectircTypeView();
        }
        viewStackManager.showView(mElectricTypeView, map);
    }

    @Override
    public void showElectricBrandView(Map<String, Object> map) {
        if (mElectricBrandView == null) {
            mElectricBrandView = new _ElectricBrandView();
        }
        viewStackManager.showView(mElectricBrandView, map);
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (dispatchKeyListener(event)) {
            return true;
        }
        if (event.getAction() == KeyEvent.ACTION_DOWN && event.getKeyCode() == KeyEvent.KEYCODE_BACK) {
            if (viewStackManager != null) {
                viewStackManager.onKeyBack();
                return true;
            }
        }
        return super.dispatchKeyEvent(event);
    }

    private boolean dispatchKeyListener(KeyEvent event) {
        if (listeners == null || listeners.size() <= 0) {
            return false;
        }
        for (OnKeyListener listener : listeners) {
            boolean res = listener.onKey(this, event.getKeyCode(), event);
            if (res) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void onDestroy() {
        if (viewStackManager != null) {
            viewStackManager.destroy();
        }
        listeners.clear();
        mRemoteControlDevicesView = null;
    }

    @Override
    public View getView() {
        return this;
    }
}
