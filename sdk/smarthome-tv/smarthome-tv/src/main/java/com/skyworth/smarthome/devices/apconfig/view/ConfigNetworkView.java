package com.skyworth.smarthome.devices.apconfig.view;

import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.ui.DialogBg;
import com.skyworth.util.Util;

/**
 * Description: 设备配网中-View<br>
 * Created by wzh on 2019/1/7 17:20.
 */
public class ConfigNetworkView extends FrameLayout {

    private ImageView mRotateView;
    private TextView mProgressView;
    private ObjectAnimator mRotateAnimation = null;

    public ConfigNetworkView(Context context) {
        super(context);
        setBackground(new DialogBg());

        mRotateView = new ImageView(context);
        mRotateView.setBackgroundResource(R.drawable.sh_confignet_loading);
        LayoutParams params = new LayoutParams(Util.Div(140), Util.Div(140));
        params.gravity = Gravity.CENTER_HORIZONTAL;
        params.topMargin = Util.Div(60);
        addView(mRotateView, params);

        ImageView wifiIcon = new ImageView(context);
        wifiIcon.setBackgroundResource(R.drawable.sh_confignet_wifi);
        params = new LayoutParams(Util.Div(140), Util.Div(140));
        params.gravity = Gravity.CENTER_HORIZONTAL;
        params.topMargin = Util.Div(60);
        addView(wifiIcon, params);

        mProgressView = new TextView(context);
        mProgressView.setText(context.getString(R.string.apconfig_confignetwork));
        mProgressView.setTextSize(Util.Dpi(36));
        mProgressView.setTextColor(Color.parseColor("#FFFFFF"));
        mProgressView.getPaint().setFakeBoldText(true);
        params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.topMargin = Util.Div(244);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        addView(mProgressView, params);

        TextView tips = new TextView(context);
        tips.setText(context.getString(R.string.apconfig_confignetwork_tip));
        tips.setTextSize(Util.Dpi(32));
        tips.setTextColor(Color.parseColor("#aaFFFFFF"));
        params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.topMargin = Util.Div(306);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        addView(tips, params);

        startAnimation();
    }

    private void startAnimation() {
        mRotateAnimation = ObjectAnimator.ofFloat(mRotateView, "rotation", 0f, 360f);
        mRotateAnimation.setInterpolator(null);
        mRotateAnimation.setRepeatCount(ValueAnimator.INFINITE);
        mRotateAnimation.setDuration(2000);
        mRotateAnimation.start();
    }

    private void stopAnimation() {
        if (mRotateAnimation != null) mRotateAnimation.cancel();
    }

    public void refreshUI(int progress) {
        if (mRotateAnimation != null && !mRotateAnimation.isRunning()) mRotateAnimation.start();
        StringBuilder builder = new StringBuilder("设备配网中 ").append(progress).append("%");
        mProgressView.setText(builder.toString());
    }

    public void destroy() {
        stopAnimation();
    }
}
