package com.skyworth.smarthome.personal.thirdaccount.dialog.third;

import android.content.Context;
import android.content.DialogInterface;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.skyworth.smarthome.common.base.BaseCommonDialog;
import com.skyworth.smarthome.common.bean.ThridAccountHttpBean;
import com.skyworth.smarthome.personal.thirdaccount.dialog.AccountQrCodeView;
import com.skyworth.smarthome.personal.thirdaccount.dialog.IBindThirdAccountDialog;
import com.skyworth.util.Util;
import com.smarthome.common.utils.EmptyUtils;

/**
 * Description: 绑定第三方账号二维码<br>
 * Created by wzh on 2019/3/21 12:37.
 */
public class BindThridAccDialog extends BaseCommonDialog implements IBindThirdAccountDialog<BindThridAccDialog> {

    private BindThridAccPresenter mPresenter;
    private OnBindResultListener mListener;
    private AccountQrCodeView qrCodeView;
    private boolean mBindSccuss = false;//是否绑定成功的返回值
    private boolean stopPolling = false;

    public BindThridAccDialog(Context context, int themeResId) {
        super(context, themeResId);
        initUI();
    }

    @Override
    protected void initParams() {
    }

    private void initUI() {
        FrameLayout mLayout = new FrameLayout(mContext);
        qrCodeView = new AccountQrCodeView(mContext);
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Util.Div(870), Util.Div(440));
        layoutParams.gravity = Gravity.CENTER;
        mLayout.addView(qrCodeView, layoutParams);
        mContentView.addView(mLayout, new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT));
        cancelDialogAnimation();

        setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                BindThridAccDialog.super.onDismiss();
                if (mPresenter != null) mPresenter.stopPolling();
                if (mListener != null) mListener.result(mBindSccuss);
            }
        });
        if (mPresenter == null)
            initPresenter();
    }

    private void initPresenter() {
        mPresenter = new BindThridAccPresenter(new OnBindResultListener() {
            @Override
            public void result(boolean result) {
                mBindSccuss = true;
                dismiss();
            }
        });
    }

    @Override
    public BindThridAccDialog setParams(ThridAccountHttpBean data) {
        if (EmptyUtils.isNotEmpty(data.auth_url)) {
            qrCodeView.showQRcode(data.auth_url);
            qrCodeView.setData(data);
        }
        return this;
    }

    public BindThridAccDialog stopPolling() {
        stopPolling = true;
        if (mPresenter == null) initPresenter();
        mPresenter.stopPolling();
        return this;
    }

    public BindThridAccDialog setOnBindResultListener(OnBindResultListener listener) {
        mListener = listener;
        return this;
    }

    @Override
    public void show() {
        super.show();
    }
}
