package com.skyworth.smarthome.common.util;

import android.text.TextUtils;

import java.io.BufferedReader;
import java.io.DataInputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/4/23 17:33.
 */
public class WiFiPassword {
    /**
     * 通过读取系统prop:persist.sys.wifipassword获取当前连接的wifi密码，但有时此密码是错误的。
     * 在低版本系统上无此字段。
     * @return
     */
    public static String getCurrentWifiPassword() {
//        return SystemProperties.get("persist.sys.wifipassword");
        return "";
    }

    /**
     * 通过Android通用的方法来获取已连接过的wifi名称和密码
     *
     * @return
     */
    public static Map<String, String> getWifiPassWord() {
        Map<String, String> result = new HashMap<>();
        Process process = null;
        DataInputStream dataInputStream = null;
        StringBuilder wifiConf = new StringBuilder();
        try {
            process = Runtime.getRuntime().exec("cat /data/misc/wifi/*.conf");
            dataInputStream = new DataInputStream(process.getInputStream());
            InputStreamReader inputStreamReader = new InputStreamReader(dataInputStream, "UTF-8");
            BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
            String line = null;
            while ((line = bufferedReader.readLine()) != null) {
                wifiConf.append(line);
            }
            bufferedReader.close();
            inputStreamReader.close();
            process.waitFor();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (dataInputStream != null) {
                    dataInputStream.close();
                }
                process.destroy();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        Pattern network = Pattern.compile("network=\\{([^\\}]+)\\}", Pattern.DOTALL);
        Matcher networkMatcher = network.matcher(wifiConf.toString());
        while (networkMatcher.find()) {
            String networkBlock = networkMatcher.group();
            Pattern ssid = Pattern.compile("ssid=\"([^\"]+)\"");
            Matcher ssidMatcher = ssid.matcher(networkBlock);

            if (ssidMatcher.find()) {
                Pattern psk = Pattern.compile("psk=\"([^\"]+)\"");
                Matcher pskMatcher = psk.matcher(networkBlock);
                if (pskMatcher.find()) {
                    result.put(ssidMatcher.group(1), pskMatcher.group(1));
                } else {
                    result.put(ssidMatcher.group(1), "");
                }
            }
        }
        return result;
    }

    /**
     * 隐藏password中间部分为星号
     *
     * @param password
     * @return
     */
    public static String hidePassword(String password) {
        if (TextUtils.isEmpty(password)) {
            return "";
        }
        if (password.length() == 1) {
            return "*";
        }
        if (password.length() == 2) {
            return password.substring(0, 1) + "*";
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < password.length(); i++) {
            if (i == 0) {
                stringBuilder.append(password.charAt(i));
                continue;
            }
            if (i == password.length() - 1) {
                stringBuilder.append(password.charAt(i));
                continue;
            }
            stringBuilder.append("*");
        }
        return stringBuilder.toString();
    }

}
