package com.skyworth.smarthome.common.dialog;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.RelativeLayout;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import com.skyworth.smarthome.service.model.IFunctionGoToModel;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.util.ViewsBuilder;
import com.smarthome.common.dataer.DataHelpInfo;
import com.smarthome.common.dataer.LogSDK;

/**
 * @ProjectName: NewTV_SmartHome
 * @Package: com.skyworth.smarthome_tv.common.dialog
 * @ClassName: UnBindDialog
 * @Description: java类作用描述
 * @Author: wangyuehui
 * @CreateDate: 2020/6/9 17:24
 * @UpdateUser: 更新者
 * @UpdateDate: 2020/6/9 17:24
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class DeviceGuideDialog extends Dialog {

    private RelativeLayout mScheme2Layout, mScheme3Layout;
    private DeviceGuideallBack mDeviceGuideallBack;

    public DeviceGuideDialog(@NonNull Context context) {
        this(context, R.style.common_style);
    }

    public DeviceGuideDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    public static DeviceGuideDialog newInstance(Context context) {

        DeviceGuideDialog fragment = new DeviceGuideDialog(context, R.style.common_style);
        return fragment;
    }

    public void setDeviceGuideallBack(DeviceGuideallBack deviceGuideallBack) {
        mDeviceGuideallBack = deviceGuideallBack;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(ViewsBuilder.getGuideAddDevices(getContext()));


        mScheme2Layout = findViewById(R.id.guide_add_devices_sheme_2);
        mScheme3Layout = findViewById(R.id.guide_add_devices_sheme_3);

        mScheme2Layout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                IFunctionGoToModel.INSTANCE.goToAddScanSmartDevice("手动");
                DataHelpInfo.getInstance().setDiscoverDeviceOrigin("手动");
                LogSDK.submit(LogSDK.EVENT_ID_ADD_DEVICE, "source", "智能设备tab点击");
            }
        });

        mScheme3Layout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getContext().startActivity(new Intent("com.smarthome.action.THRID_ACCOUNT"));
            }
        });
    }

    @Override
    public void onStart() {
        super.onStart();
        Window window = getWindow();
        WindowManager.LayoutParams windowParams = window.getAttributes();
        windowParams.width = WindowManager.LayoutParams.MATCH_PARENT;
        windowParams.height = WindowManager.LayoutParams.MATCH_PARENT;
        windowParams.gravity = Gravity.CENTER;
        windowParams.dimAmount = 0.50f;
        windowParams.flags |= WindowManager.LayoutParams.FLAG_DIM_BEHIND;
        window.setAttributes(windowParams);
    }

    public interface DeviceGuideallBack {
        void callBackToAddDevice();

        void callBackToRelate();
    }
}
