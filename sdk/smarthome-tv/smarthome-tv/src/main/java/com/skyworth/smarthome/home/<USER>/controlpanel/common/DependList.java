package com.skyworth.smarthome.home.smartdevice.controlpanel.common;

import android.text.TextUtils;
import android.util.Log;

import com.alibaba.fastjson.JSONObject;
import com.skyworth.smarthome.home.smartdevice.controlpanel.view.ControlPanelView;
import com.skyworth.smarthome.common.util.Utils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class DependList implements Serializable {
    public List<Depend> depend;
    public static class Depend implements Serializable {
        public static final String TYPE_ENABLE = "enable";
        public static final String TYPE_TEXT_COLOR = "textcolor";
        public static final String CONDITION_EQ = "eq";
        public static final String CONDITION_LT = "it";
        public static final String CONDITION_GT = "gt";
        public static final String CONDITION_NE = "ne";
        public static final String CONDITION_GE = "ge";
        public static final String CONDITION_LE = "le";

        public String type;
        public String data_field;
        public String condition;
        public String compare_value;
        public String normal_color;
        public String color;
    }

    public static <T extends Comparable> boolean compare(T src, T target, String condition) {
        Log.i(ControlPanelView.TAG, "compare: " + src + " " + target + " " + condition);
        if (src == null || target == null || condition == null) {
            return false;
        }
        int result = src.compareTo(target);
        switch (condition) {
            case Depend.CONDITION_EQ:
                return result == 0;
            case Depend.CONDITION_LT:
                return result < 0;
            case Depend.CONDITION_GT:
                return result > 0;
            case Depend.CONDITION_NE:
                return result != 0;
            case Depend.CONDITION_GE:
                return result == 0 || result > 0;
            case Depend.CONDITION_LE:
                return result == 0 || result < 0;
        }
        return false;
    }

    public static BaseDependData dependResultFactory(String type) {
        BaseDependData resultData = null;
        switch (type) {
            case Depend.TYPE_ENABLE:
                resultData = new EnableDependData();
                break;
            case Depend.TYPE_TEXT_COLOR:
                resultData = new TextColorDependData();
                break;
        }
        return resultData;
    }

    public abstract static class BaseDependData {
        public int result;
        public static final int RESULT_NO_FIELD = 0;
        public static final int RESULT_COMPARE_SUCCESS = 1;
        public static final int RESULT_COMPARE_FAILED = 2;

        public abstract String getType();

        protected abstract void internalCheck(JSONObject status, Depend depend);

        public void check(JSONObject status, Depend depend) {
            if (depend == null || depend.type == null || !depend.type.equals(getType()) || TextUtils.isEmpty(depend.data_field) || depend.compare_value == null) {
                result = RESULT_NO_FIELD;
                Log.i(ControlPanelView.TAG, "checkDepend: RESULT_NO_FIELD1");
                return;
            }
            if (status == null) {
                Log.i(ControlPanelView.TAG, "checkDepend: RESULT_NO_FIELD2");
                result = RESULT_NO_FIELD;
                return;
            }
            Object currentValue;
            currentValue = status.get(depend.data_field);
            if (currentValue == null) {
                Log.i(ControlPanelView.TAG, "checkDepend: RESULT_NO_FIELD3");
                result = RESULT_NO_FIELD;
                return;
            }
            if (compare(currentValue, depend.compare_value, depend.condition)) {
                result = RESULT_COMPARE_SUCCESS;
            } else {
                result = RESULT_COMPARE_FAILED;
            }
            internalCheck(status, depend);
        }

        private boolean compare(Object src, Object target, String condition) {
            Comparable srcCmp = null;
            Comparable targetCmp = null;
            if (src instanceof Comparable) {
                srcCmp = (Comparable) src;
            }
            if (target instanceof Comparable) {
                targetCmp = (Comparable) target;
            }
            if (srcCmp == null || targetCmp == null) {
                return false;
            }
            if (Utils.isNumeric(src.toString()) && Utils.isNumeric(target.toString())) {
                return DependList.compare(new BigDecimal(src.toString()), new BigDecimal(target.toString()), condition);
            } else {
                return DependList.compare(srcCmp.toString(), targetCmp.toString(), condition);
            }
        }

        public int getResult() {
            return result;
        }
    }


    public static class EnableDependData extends BaseDependData {
        public boolean isEnable = false;

        @Override
        protected void internalCheck(JSONObject status, Depend depend) {

        }

        @Override
        public String getType() {
            return Depend.TYPE_ENABLE;
        }
    }

    public static class TextColorDependData extends BaseDependData {
        public String normal_color;
        public String color;

        @Override
        protected void internalCheck(JSONObject status, Depend depend) {
            normal_color = depend.normal_color;
            color = depend.color;
        }

        @Override
        public String getType() {
            return Depend.TYPE_TEXT_COLOR;
        }
    }
}
