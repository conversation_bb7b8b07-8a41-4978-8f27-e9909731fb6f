package com.skyworth.smarthome.devices.apconfig.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Rect;
import android.text.InputType;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.inputmethod.InputMethodManager;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.util.DataCacheUtil;
import com.skyworth.smarthome.devices.apconfig.presenter.IApConfigPresenter;
import com.skyworth.ui.api.widget.SimpleFocusDrawable;
import com.skyworth.util.Util;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XThemeUtils;

/**
 * Description: 配网中-获取异常-View<br>
 * Created by wzh on 2019/1/7 18:05.
 */
public class ConfigNetworkErrView extends FrameLayout {
    private TextView mWifiTitle, mErrorTip;
    private EditText mPwdEditText;
    private CheckBox mRecordPwdCB;
    private TextView mConfirmButton;
    private View mEditFocusView;
    private InputMethodManager mInputMethodManager = null;
    private IApConfigPresenter mPresenter;
    private String wifiName = "";
    private SimpleFocusDrawable mConfirmFocusBg;

    public ConfigNetworkErrView(Context context) {
        super(context);
        initUI();
        initListener();
    }

    private void initUI() {
        setBackgroundColor(Color.argb(230, 0, 0, 0));

        mWifiTitle = new TextView(getContext());
        mWifiTitle.setTextSize(Util.Dpi(36));
        mWifiTitle.setTextColor(Color.parseColor("#999999"));
        mWifiTitle.getPaint().setFakeBoldText(true);
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.topMargin = Util.Div(350);
        params.leftMargin = Util.Div(585);
        addView(mWifiTitle, params);

        mErrorTip = new TextView(getContext());
        mErrorTip.setTextSize(Util.Dpi(24));
        mErrorTip.setTextColor(Color.parseColor("#FF5E5B"));
        mErrorTip.getPaint().setFakeBoldText(true);
        mErrorTip.setVisibility(GONE);
        params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.topMargin = Util.Div(402);
        params.leftMargin = Util.Div(585);
        addView(mErrorTip, params);

        mPwdEditText = new EditText(getContext());
        mPwdEditText.setFocusable(true);
        mPwdEditText.setFocusableInTouchMode(true);
        mPwdEditText.setBackgroundColor(XThemeUtils.c_b_8());
        mPwdEditText.setTextColor(Color.parseColor("#FFFFFF"));
        mPwdEditText.setTextSize(Util.Dpi(48));
        mPwdEditText.setGravity(Gravity.CENTER);
        mPwdEditText.setInputType(InputType.TYPE_TEXT_FLAG_MULTI_LINE);
        params = new LayoutParams(Util.Div(750), Util.Div(90));
        params.topMargin = Util.Div(436);
        params.leftMargin = Util.Div(585);
        addView(mPwdEditText, params);


        mEditFocusView = new View(getContext());
        mEditFocusView.setBackground(XThemeUtils.getDrawable(0, Color.parseColor("#FFFFFF"), Util.Div(4), 0));
        params = new LayoutParams(Util.Div(750), Util.Div(90));
        params.topMargin = Util.Div(436);
        params.leftMargin = Util.Div(585);
        addView(mEditFocusView, params);
        mEditFocusView.setVisibility(GONE);


        mRecordPwdCB = new CheckBox(getContext());
        mRecordPwdCB.setFocusable(true);
        mRecordPwdCB.setFocusableInTouchMode(true);
        mRecordPwdCB.setButtonDrawable(R.drawable.checkbox_select);
        mRecordPwdCB.setTextColor(Color.parseColor("#aaFFFFFF"));
        mRecordPwdCB.setText("记住密码");
        mRecordPwdCB.setGravity(Gravity.CENTER_VERTICAL);
        mRecordPwdCB.setCompoundDrawablePadding(Util.Div(10));
        mRecordPwdCB.setChecked(true);
        params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, Util.Div(32));
        params.topMargin = Util.Div(550);
        params.leftMargin = Util.Div(585);
        addView(mRecordPwdCB, params);


        mConfirmFocusBg = new SimpleFocusDrawable(getContext()).setRadius(Util.Div(10));

        mConfirmButton = new TextView(getContext());
        mConfirmButton.setFocusable(true);
        mConfirmButton.setFocusableInTouchMode(true);

        mConfirmButton.setBackground(mConfirmFocusBg);
        mConfirmButton.setText(getContext().getString(R.string.confirm));
        mConfirmButton.setGravity(Gravity.CENTER);
        mConfirmButton.setTextSize(Util.Dpi(32));
        mConfirmButton.setTextColor(Color.parseColor("#aaFFFFFF"));
        mConfirmButton.getPaint().setFakeBoldText(true);
        params = new LayoutParams(Util.Div(750), Util.Div(90));
        params.topMargin = Util.Div(600);
        params.leftMargin = Util.Div(585);
        addView(mConfirmButton, params);
    }

    public void setWifiName(String name) {
        wifiName = name;
        mWifiTitle.setText(getContext().getString(R.string.apconfig_network_err_input, this.wifiName));
    }

    public void setRedTipText(String tipText) {
        mErrorTip.setText(tipText);
    }

    private void initListener() {
        mPwdEditText.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                showInputKeyboard(true);
            }
        });
        mPwdEditText.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    mEditFocusView.setVisibility(VISIBLE);
                    showInputKeyboard(true);
                } else {
                    mEditFocusView.setVisibility(GONE);
                    showInputKeyboard(false);
                }
            }
        });
//向下直接跳转到确定按钮，解决部分机型输入法焦点被抢问题（注释掉相关代码）
//        mPwdEditText.setOnKeyListener(new OnKeyListener() {
//            @Override
//            public boolean onKey(View v, int keyCode, KeyEvent event) {
//                if(event.getAction() == KeyEvent.ACTION_DOWN){
//                    switch (keyCode) {
//                        case KeyEvent.KEYCODE_DPAD_DOWN:
//                            mRecordPwdCB.requestFocus();
//                            return true;
//                    }
//                }
//                return false;
//            }
//        });
        mRecordPwdCB.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean isCheck) {
                showCheckBox(mRecordPwdCB.isFocused(), isCheck);
            }
        });
        mRecordPwdCB.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View view, boolean hasFocus) {
                showCheckBox(hasFocus, mRecordPwdCB.isChecked());
            }
        });
        mConfirmButton.setOnKeyListener(new OnKeyListener() {
            @Override
            public boolean onKey(View v, int keyCode, KeyEvent event) {
                if(event.getAction() == KeyEvent.ACTION_DOWN){
                    switch (keyCode) {
                        case KeyEvent.KEYCODE_DPAD_UP:
                            mRecordPwdCB.requestFocus();
                            return true;
                        case KeyEvent.KEYCODE_DPAD_LEFT:
                        case KeyEvent.KEYCODE_DPAD_RIGHT:
                        case KeyEvent.KEYCODE_DPAD_DOWN:
                            return true;
                    }
                }
                return false;
            }
        });
        mConfirmButton.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mPresenter != null) {
                    mPresenter.passwordEntered(wifiName, mPwdEditText.getText().toString());
                }
                if (mRecordPwdCB.isChecked()) {
                    DataCacheUtil.getInstance().putString(wifiName, mPwdEditText.getText().toString());
                } else {
                    DataCacheUtil.getInstance().putString(wifiName, "");
                }

            }
        });
        mConfirmButton.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                mConfirmFocusBg.setFocus(hasFocus);
                if (hasFocus) {
                    mConfirmButton.setTextColor(Color.parseColor("#000000"));
                } else {
                    mConfirmButton.setTextColor(Color.parseColor("#aaFFFFFF"));
                }
            }
        });
    }

    private void showInputKeyboard(boolean flag) {
        AppData.getInstance().setOpenSoftPan(flag);
        if (flag) {
            if (mInputMethodManager == null) {
                mInputMethodManager = (InputMethodManager) getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
            }
            if (mInputMethodManager != null) {
                mInputMethodManager.toggleSoftInput(0, InputMethodManager.SHOW_FORCED);
            }
        } else {
            if (mInputMethodManager != null) {
                mInputMethodManager.hideSoftInputFromWindow(mPwdEditText.getWindowToken(), 0); //强制隐藏键盘
            }
        }
    }


    public void setPresenter(IApConfigPresenter presenter) {
        mPresenter = presenter;
    }

    public void connectIsError(boolean b) {
        mErrorTip.setVisibility(b ? VISIBLE : INVISIBLE);
    }


    private void showCheckBox(boolean hasFocus, boolean isCheck) {
        if (isCheck) {
            if (hasFocus) {
                mRecordPwdCB.setButtonDrawable(R.drawable.checkbox_select_focus);
            } else {
                mRecordPwdCB.setButtonDrawable(R.drawable.checkbox_select);
            }
        } else {
            if (hasFocus) {
                mRecordPwdCB.setButtonDrawable(R.drawable.checkbox_unselect_focus);
            } else {
                mRecordPwdCB.setButtonDrawable(R.drawable.checkbox_unselect);
            }
        }
    }

    public void setFocus() {
        if (mPwdEditText != null) {
            post(new Runnable() {
                @Override
                public void run() {
                    String password = DataCacheUtil.getInstance().getString(wifiName, "");
                    if (EmptyUtils.isEmpty(password)) {
                        mPwdEditText.setText("");
                        mPwdEditText.requestFocus();
                    } else {
                        mPwdEditText.setText(password);
                        mConfirmButton.requestFocus();
                    }
                }
            });
        }
    }

}
