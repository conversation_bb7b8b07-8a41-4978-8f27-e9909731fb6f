package com.skyworth.smarthome.common.dialog;


import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.util.Contants;
import com.skyworth.smarthome.common.util.ViewsBuilder;

/**
 * @ProjectName: NewTV_SmartHome
 * @Package: com.skyworth.smarthome_tv.common.dialog
 * @ClassName: UnBindDialog
 * @Description: java类作用描述
 * @Author: wangyuehui
 * @CreateDate: 2020/6/9 17:24
 * @UpdateUser: 更新者
 * @UpdateDate: 2020/6/9 17:24
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class UnBindDialog extends Dialog {

    private TextView mUnBindSureBtn,mUnBindCancelBtn;
    private String mDeviceId;

    public UnBindDialog(@NonNull Context context) {
        super(context);
    }

    public UnBindDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    public void setmDeviceId(String mDeviceId) {
        this.mDeviceId = mDeviceId;
    }

    public static UnBindDialog newInstance(Context context, String deviceId) {

        Bundle args = new Bundle();
        args.putString(Contants.COOCAA_INTENT_CURRENT_DEVICE_ID,deviceId);

        UnBindDialog fragment = new UnBindDialog(context, R.style.common_style);
        fragment.setmDeviceId(deviceId);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(ViewsBuilder.getUnBindDialogView(getContext()));

        mUnBindSureBtn = findViewById(R.id.unbind_devices_btn_sure);
        mUnBindCancelBtn = findViewById(R.id.unbind_devices_btn_cancle);

        mUnBindCancelBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        mUnBindSureBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
    }

    @Override
    public void onStart() {
        super.onStart();
        Window window = getWindow();
        WindowManager.LayoutParams windowParams = window.getAttributes();
        windowParams.dimAmount = 0.50f;
        windowParams.flags |= WindowManager.LayoutParams.FLAG_DIM_BEHIND;
        window.setAttributes(windowParams);
    }
}
