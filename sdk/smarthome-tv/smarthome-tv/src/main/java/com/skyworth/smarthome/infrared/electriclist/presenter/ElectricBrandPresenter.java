package com.skyworth.smarthome.infrared.electriclist.presenter;

import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import com.coocaa.app.core.http.HttpServiceManager;
import com.coocaa.app.core.utils.FuncKt;
import com.skyworth.smarthome.infrared.electriclist.model.DeviceBrandListData;
import com.skyworth.smarthome.infrared.electriclist.model.ElectricBrandData;
import com.skyworth.smarthome.infrared.electriclist.view.IElectricBrandView;
import com.skyworth.smarthome.common.event.CloseInfraredDialogEvent;
import com.skyworth.smarthome.common.http.SmartDevicesHttpService;
import com.skyworth.smarthome.common.util.PinYinUtil;
import com.skyworth.smarthome.infrared.learn.IRLearnDialog;
import com.skyworth.smarthome.infrared.manager.IREntry;
import com.skyworth.smarthome.infrared.matchkey.IRMatchKeyDialog;
import com.smarthome.common.model.SmartBaseData;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import retrofit2.Call;

/**
 * Created by fc on 2019/4/26
 * Describe:
 */
public class ElectricBrandPresenter implements IElectricBrandPresenter {
    private IElectricBrandView mView;
    private String mId, mTypeName;
    private String mDeviceId = "";
    private String mDeviceName = "";
    private String mDeviceTypeId = "";
    private Map<String, Object> mParams = null;

    @Override
    public void loadList(final String type_id, final String typeName, final List<String> hotList) {
        mId = type_id;
        mTypeName = typeName;
        mView.hideErrorView();
        mView.showLoading();
        mView.setReLoadParams(mDeviceId,mId, mTypeName, hotList);
        FuncKt.ioThread(new Runnable() {
            @Override
            public void run() {
                Call<SmartBaseData<List<DeviceBrandListData>>> call = SmartDevicesHttpService.SERVICE.getDeviceBrandList(mId);
                final SmartBaseData baseData = HttpServiceManager.Companion.call(call);
                mView.hideLoading();
                if (baseData != null && baseData.code.equals("0")) {
                    FuncKt.runOnUiThread(new Function0<Unit>() {
                        @Override
                        public Unit invoke() {
                            //展示热门列表
                            final List<DeviceBrandListData> dataList = ((List<DeviceBrandListData>) baseData.data);
                            addPinYin(dataList);
                            if (hotList != null && hotList.size() > 0) {
                                if (hotList.size() > 8) {
                                    List<String> first8List = new ArrayList<>();
                                    for (int i = 0; i < 8; i++) {
                                        first8List.add(hotList.get(i));
                                    }
                                    mView.showHotBrandList(convertHostListToElectricBrandData(first8List, dataList));
                                } else {
                                    mView.showHotBrandList(convertHostListToElectricBrandData(hotList, dataList));
                                }
                            } else {
                                mView.showErrorView("暂无相关数据", "");
                                Log.i("ElectricBrandView", "hotList no data: ");
                                return Unit.INSTANCE;
                            }
                            if (dataList != null && dataList.size() > 0) {
                                //组装字母列表
                                String firstChar = "*";
                                List<String> charList = new ArrayList<>();
                                for (int i = 0; i < dataList.size(); i++) {
                                    if (!checkCharacter(dataList.get(i).pinyin)) {
                                        dataList.get(i).pinyin = "#";
                                    }
                                    String nextChar = dataList.get(i).pinyin.substring(0, 1);
                                    if (!nextChar.equals(firstChar)) {//去重
                                        charList.add(nextChar);
                                        firstChar = nextChar;
                                    }
                                }
                                mView.showCharacterList(charList);
//                                Log.i("ElectricBrandView", "showCharacterList: " + JSONObject.toJSONString(charList));

                                //组装右侧数据
                                List<ElectricBrandData> brandDataArrayList = new ArrayList<>();
                                for (int i = 0; i < charList.size(); i++) {
                                    ElectricBrandData titleData = new ElectricBrandData();
                                    titleData.character = charList.get(i);
                                    brandDataArrayList.add(titleData);

                                    for (int j = 0; j < dataList.size(); j++) {
                                        if (!checkCharacter(dataList.get(i).pinyin)) {
                                            dataList.get(i).pinyin = "#";
                                        }
                                        String charater = dataList.get(j).pinyin.substring(0, 1);
                                        if (charater.equals(charList.get(i))) {
                                            ElectricBrandData brandData = new ElectricBrandData();
                                            brandData.character = charList.get(i);
                                            brandData.electricName = dataList.get(j).name_cn;
                                            brandData.brand_id_kk = dataList.get(j).brand_id_kk;
                                            brandData.hxd_has = dataList.get(j).hxd_has;
                                            brandDataArrayList.add(brandData);
                                        }
                                    }

                                    //解析一个标题下面品牌信息的背景样式
                                    int lastTitleIndex = 0;
                                    for (int x = 0; x < brandDataArrayList.size(); x++) {
                                        if (TextUtils.isEmpty(brandDataArrayList.get(x).electricName)) { //过滤标题item
//                                            Log.i("ElectricBrandView", "标题的下标 " + x);
                                            brandDataArrayList.get(x).uiType = 0;
                                            if (x - lastTitleIndex > 2) {  //过滤一个标题下只有一个品牌的情况
                                                brandDataArrayList.get(lastTitleIndex + 1).uiType = 1;
                                                brandDataArrayList.get(x - 1).uiType = 3;
//                                                //中间的四个直角
//                                                for (int z = lastTitleIndex + 2; z < i - 1; z++) {
//                                                    brandDataArrayList.get(z).uiType = 2;
//                                                }
                                            } else {
                                                brandDataArrayList.get(lastTitleIndex + 1).uiType = 0;
                                            }
                                            lastTitleIndex = x;
                                        }
                                    }
                                }
                                mView.showBrandList(brandDataArrayList);
//                                Log.i("ElectricBrandView", "loadList: " + JSONObject.toJSONString(brandDataArrayList));
                            } else {
                                mView.showErrorView("暂无相关数据", "");
                                Log.i("ElectricBrandView", "allBrand no data: ");
                            }
                            return Unit.INSTANCE;
                        }
                    });

                } else {
                    FuncKt.runOnUiThread(new Function0<Unit>() {
                        @Override
                        public Unit invoke() {
                            mView.showErrorView("网络状态错误", "");
                            return Unit.INSTANCE;
                        }
                    });
                }
            }
        });

    }

    private List<ElectricBrandData> convertHostListToElectricBrandData(List<String> hotList, List<DeviceBrandListData> allBrand) {
        List<ElectricBrandData> result = new ArrayList<>();
        Map<String, DeviceBrandListData> cache = getAllBrandCache(hotList, allBrand);
        for (String s : hotList) {
            DeviceBrandListData brand = cache.get(s);
            if (brand == null) {
                continue;
            }
            ElectricBrandData item = new ElectricBrandData();
            item.electricName = s;
            item.hxd_has = brand.hxd_has;
            item.brand_id_kk = brand.brand_id_kk;
            result.add(item);
        }
        return result;
    }

    private Map<String, DeviceBrandListData> getAllBrandCache(List<String> hotList, List<DeviceBrandListData> allBrand) {
        Map<String, DeviceBrandListData> result = new HashMap<>();
        for (String s : hotList) {
            DeviceBrandListData select = null;
            for (DeviceBrandListData deviceBrandListData : allBrand) {
                if (s.equals(deviceBrandListData.name_cn)) {
                    select = deviceBrandListData;
                    break;
                }
            }
            if (select != null) {
                result.put(s, select);
            }
        }
        return result;
    }

    private void addPinYin(List<DeviceBrandListData> list) {
        for (DeviceBrandListData deviceBrandListData : list) {
            deviceBrandListData.pinyin = PinYinUtil.toPinYin(deviceBrandListData.name_cn).toUpperCase();
        }
        Collections.sort(list, new Comparator<DeviceBrandListData>() {
            @Override
            public int compare(DeviceBrandListData lhs, DeviceBrandListData rhs) {
                return lhs.pinyin.compareTo(rhs.pinyin);
            }
        });
    }

    @Override
    public void setDeviceName(String deviceName) {
        mDeviceName = deviceName;
    }

    @Override
    public void setDeviceId(String deviceId) {
        mDeviceId = deviceId;
    }

    @Override
    public void setDeviceTypeId(String mDeviceTypeId) {
        this.mDeviceTypeId = mDeviceTypeId;
    }

    @Override
    public void onItemClick(View view, ElectricBrandData realBrand, int position) {
        String kkTypeId = getValueFromParams(IREntry.KK_TYPE_ID);
        String prioritySDK = getValueFromParams(IREntry.SDK_PRIORITY);
        IRMatchKeyDialog.launch(mDeviceId, mDeviceName, mDeviceTypeId, mTypeName, mId, realBrand.electricName, kkTypeId, String.valueOf(realBrand.brand_id_kk), String.valueOf(realBrand.hxd_has), prioritySDK);
        EventBus.getDefault().post(new CloseInfraredDialogEvent());
//        Map<String, String> params = new HashMap<String, String>();
//        params.put("device_name", realBrand.electricName);
//        LoggerImpl.Companion.onEvent(DataConstants.EVENT_INFRARED_SELECT_DEVICE_BRAND_ONCLICK, params);
    }

    private String getValueFromParams(String key) {
        Object o = mParams.get(key);
        return o == null ? "" : o.toString();
    }

    @Override
    public void onClickIrLearn() {
        IRLearnDialog.launch(mDeviceId, mDeviceName, mDeviceTypeId, mTypeName, mId);
        EventBus.getDefault().post(new CloseInfraredDialogEvent());
    }

    public boolean checkCharacter(String firstrData) {
        if (TextUtils.isEmpty(firstrData)) {
            return false;
        }
        char c = firstrData.charAt(0);
        if (((c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z'))) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public void setView(IElectricBrandView view) {
        mView = view;
    }

    @Override
    public void setParams(Map<String, Object> params) {
        mParams = params;
    }
}
