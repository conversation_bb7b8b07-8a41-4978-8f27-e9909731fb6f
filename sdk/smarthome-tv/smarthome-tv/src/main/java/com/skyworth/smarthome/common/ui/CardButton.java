package com.skyworth.smarthome.common.ui;

import android.content.Context;
import android.graphics.Color;
import android.support.v7.widget.AppCompatTextView;
import android.view.Gravity;
import android.view.View;


import com.skyworth.ui.api.widget.SimpleFocusDrawable;
import com.skyworth.util.Util;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/3/21 17:04.
 */
public class CardButton extends AppCompatTextView {
    private SimpleFocusDrawable mFocusBg;
    private View.OnFocusChangeListener onExtFocusChangeListener = null;
    private View.OnFocusChangeListener onFocusChangeListener = new View.OnFocusChangeListener() {
        @Override
        public void onFocusChange(View v, boolean hasFocus) {
            if (onExtFocusChangeListener != null) {
                onExtFocusChangeListener.onFocusChange(v, hasFocus);
            }
            mFocusBg.setFocus(hasFocus);
            if (hasFocus) {
                setTextColor(Color.parseColor("#000000"));
                getPaint().setFakeBoldText(true);
            } else {
                setTextColor(Color.parseColor("#aaFFFFFF"));
                getPaint().setFakeBoldText(false);
            }
        }
    };

    public CardButton(Context context) {
        super(context);
        setOnFocusChangeListener(onFocusChangeListener);
        initView();
    }

    public void setExtOnFocusChangeListener(View.OnFocusChangeListener onFocusChangeListener) {
        onExtFocusChangeListener = onFocusChangeListener;
    }

    private void initView() {
        mFocusBg =  new SimpleFocusDrawable(getContext()).setRadius(Util.Div(10));
        setGravity(Gravity.CENTER);
        setTextSize(Util.Dpi(32));
        setTextColor(Color.WHITE);
        setPadding(0, 0, 0, 0);
        setFocusable(true);
        setFocusableInTouchMode(true);
        setBackground(mFocusBg);
    }
}
