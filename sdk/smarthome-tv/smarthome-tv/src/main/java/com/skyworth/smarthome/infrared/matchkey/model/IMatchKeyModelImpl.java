package com.skyworth.smarthome.infrared.matchkey.model;

import com.coocaa.app.core.http.HttpServiceManager;
import com.skyworth.smarthome.common.bean.IRAddDeviceData;
import com.skyworth.smarthome.common.http.SmartDevicesHttpService;
import com.smarthome.common.model.SmartBaseData;

import java.util.Map;

import retrofit2.Call;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/4/29 18:37.
 */
public class IMatchKeyModelImpl implements IMatchKeyModel {

    @Override
    public boolean sendIRCode(Map<String, Object> params) {
        Call<SmartBaseData> call = SmartDevicesHttpService.SERVICE.irSendCode(params);
        SmartBaseData result = HttpServiceManager.Companion.call(call);
        return result != null && result.code.equals("0");
    }

    @Override
    public IRAddDeviceData addNewIRDevice(Map<String, Object> params) {
        Call<SmartBaseData<IRAddDeviceData>> call = SmartDevicesHttpService.SERVICE.irDeviceOpt(1, params);
        SmartBaseData<IRAddDeviceData> result = HttpServiceManager.Companion.call(call);
        return result == null ? null : result.data;
    }

    @Override
    public void destroy() {

    }
}
