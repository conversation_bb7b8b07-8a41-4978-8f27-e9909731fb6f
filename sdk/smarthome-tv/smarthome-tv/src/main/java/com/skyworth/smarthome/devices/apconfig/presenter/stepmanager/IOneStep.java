package com.skyworth.smarthome.devices.apconfig.presenter.stepmanager;

import android.content.Context;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/1/7 21:54.
 */
public interface IOneStep<Presenter> {
    void create();

    void run();

    boolean input(String msg, Object... params);

    void setManger(BaseStepManager<Presenter> manger);

    void setPresenter(Presenter presenter);

    void setContext(Context context);

    String getTag();

    void destroy();
}
