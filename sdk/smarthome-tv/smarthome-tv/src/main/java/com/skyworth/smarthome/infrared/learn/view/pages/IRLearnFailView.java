package com.skyworth.smarthome.infrared.learn.view.pages;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import android.support.annotation.NonNull;

import com.skyworth.smarthome.R;
import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.util.Util;
import com.smarthome.common.utils.XThemeUtils;

import java.util.Map;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/7/2 18:25.
 */
public class IRLearnFailView extends BaseIRLearnViews {
    private TextView mRetryButton = null;

    public IRLearnFailView(@NonNull Context context) {
        super(context);
        initTitle();
        initRetry();
        initNext();
    }

    private void initTitle() {
        TextView title = new TextView(getContext());
        title.setText(R.string.ir_learn_fail_title);
        title.setTextColor(Color.WHITE);
        title.setTextSize(Util.Dpi(36));
        title.getPaint().setFakeBoldText(true);
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        layoutParams.topMargin = Util.Div(99);
        addView(title, layoutParams);
    }

    private void initRetry() {
        mRetryButton = new TextView(getContext());
        mRetryButton.setText(R.string.ir_learn_fail_retry);
        mRetryButton.setTextSize(Util.Dpi(32));
        mRetryButton.setTextColor(Color.parseColor("#aaFFFFFF"));
        mRetryButton.setGravity(Gravity.CENTER);
        mRetryButton.setFocusable(true);
        mRetryButton.setFocusableInTouchMode(true);
        final Drawable drawable = XThemeUtils.getDrawable(Color.parseColor("#19FFFFFF"), 0, Util.Div(2), Util.Div(16));
        mRetryButton.setBackground(drawable);
        mRetryButton.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    mRetryButton.setBackground(new CCFocusDrawable(getContext()).setRadius(Util.Dpi(16)).setSolidColor(getResources().getColor(R.color.white)));
                    mRetryButton.setTextColor(Color.parseColor("#000000"));
                    mRetryButton.getPaint().setFakeBoldText(true);
                } else {
                    mRetryButton.setBackground(drawable);
                    mRetryButton.setTextColor(Color.parseColor("#aaFFFFFF"));
                    mRetryButton.getPaint().setFakeBoldText(false);
                }
            }
        });
        mRetryButton.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mPresenter != null) {
                    mPresenter.retry();
                }
            }
        });
        LayoutParams layoutParams = new LayoutParams(Util.Div(292), Util.Div(90));
        layoutParams.leftMargin = Util.Div(50);
        layoutParams.topMargin = Util.Div(260);
        addView(mRetryButton, layoutParams);
    }

    private void initNext() {
        final TextView next = new TextView(getContext());
        next.setText(R.string.ir_learn_fail_next);
        next.setTextSize(Util.Dpi(32));
        next.setTextColor(Color.parseColor("#aaFFFFFF"));
        next.setGravity(Gravity.CENTER);
        next.setFocusable(true);
        next.setFocusableInTouchMode(true);
        final Drawable drawable = XThemeUtils.getDrawable(Color.parseColor("#19FFFFFF"), 0, Util.Div(2), Util.Div(16));
        next.setBackground(drawable);
        next.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    next.setBackground(new CCFocusDrawable(getContext()).setRadius(Util.Dpi(16)).setSolidColor(getResources().getColor(R.color.white)));
                    next.setTextColor(Color.parseColor("#000000"));
                    next.getPaint().setFakeBoldText(true);
                } else {
                    next.setBackground(drawable);
                    next.setTextColor(Color.parseColor("#aaFFFFFF"));
                    next.getPaint().setFakeBoldText(false);
                }
            }
        });
        next.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mPresenter != null) {
                    mPresenter.next();
                }
            }
        });
        LayoutParams layoutParams = new LayoutParams(Util.Div(292), Util.Div(90));
        layoutParams.leftMargin = Util.Div(372);
        layoutParams.topMargin = Util.Div(260);
        addView(next, layoutParams);
    }

    @Override
    protected void onShow(Map<String, Object> params) {
        if (mRetryButton != null) {
            mRetryButton.requestFocus();
        }
    }

    @Override
    public int getViewWidth() {
        return Util.Div(714);
    }

    @Override
    public int getViewHeight() {
        return Util.Div(400);
    }

    @Override
    public String getName() {
        return "IRLearnFailView";
    }
}
