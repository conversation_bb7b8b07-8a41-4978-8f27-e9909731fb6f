package com.skyworth.smarthome.common.bean;

import com.swaiot.aiotlib.common.entity.DeviceBean;
import com.swaiot.aiotlib.common.entity.DiscoverNetworkDevice;

import java.io.Serializable;
import java.util.List;

/**
 * Description: 设备列表接口数据结构<br>
 * Created by wzh on 2019/1/31 10:47.
 */
public class DeviceListHttpBean implements Serializable {
    public List<DeviceDetailData> device_list;//设备列表
    public String support_count;//智能化率

    public static class DeviceDetailData implements Serializable {
        public String device_id;//设备id
        public String parent_id;//红外宝设备id
        public String device_type_id;//设备品类id
        public String brand;//设备品牌
        public String brand_cn;//设备品牌中文名
        public String device_icon;//设备图标
        public String device_icon_for_ir;//红外电视图标
        public String device_name;//设备名称
        public String online_status;//设备在线状态1：在线 0：不在线
        public List<String> device_tags;//设备标签列表
        public String status;//设备状态表，代表设备当前状态----字段不固定，直接下发json
        public List<String> voice_tips;//设备语音引导句
        public String list_layout;//列表Item的ui控件信息
        public String detail_layout;//设备详情ui控件信息
        public int unbind_disabled;//改设备是否支持解绑：为1时表示不支持解绑
        public DeviceBean.StatusShowBean status_desc;//虚拟控制开关状态描述
        public String gateway_id;//网关设备的唯一id
        public String child_did;//网关下当前子设备的唯一id

        /**
         * 自定义字段（非网络拉取）
         */
        public boolean is_infrared;//是否是红外设备
        public boolean is_unBind;//是否未绑定
        public boolean isNotConnectedNetwork;//是否没有配网
        public DiscoverNetworkDevice discoverNetworkDevice;//未配网详情

    }
}
