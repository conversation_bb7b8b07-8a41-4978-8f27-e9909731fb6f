package com.skyworth.smarthome.devices.apconfig.presenter.step;

import com.skyworth.smarthome.devices.apconfig.ApConfigService;
import com.skyworth.smarthome.devices.apconfig.presenter.step.manual.ManualStepConfigFailed;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl;

import java.util.HashMap;
import java.util.Map;

import static com.skyworth.smarthome.common.model.AppConstants.APCONFIG_FAIL_REASON_MANUAL_EXIT;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/1/7 21:54.
 */
public abstract class BaseStep extends com.skyworth.smarthome.devices.apconfig.presenter.stepmanager.BaseStep<ApConfigPresenterImpl> {
    protected static final int RETRY_TIMES = 2;
    protected boolean isDialogShowTwice = false;
    protected Map<String, Integer> dialogShowTimes = new HashMap<>();

    protected int getDialogShowTimes(String tag) {
        Object object = dialogShowTimes.get(tag);
        return object == null ? 0 : (int) object;
    }

    protected boolean checkBackTwice(String tag) {
        int count = getDialogShowTimes(tag);
        logi("checkBackTwice: " + count);
        if (count >= RETRY_TIMES || isDialogShow(2) && count == 1) {
            loge("retry times more than " + RETRY_TIMES);
            presenter.recordNotBind(APCONFIG_FAIL_REASON_MANUAL_EXIT);
            jumpTo(ManualStepConfigFailed.STEP_TAG);
            return true;
        }
        return false;
    }

    protected void recordDialog(String tag) {
        int count = getDialogShowTimes(tag);
        dialogShowTimes.put(tag, ++count);
    }

    protected boolean isDialogShow(int times) {
        if (dialogShowTimes.isEmpty()) {
            return false;
        }
        for (Integer integer : dialogShowTimes.values()) {
            if (integer >= times) {
                return true;
            }
        }
        return false;
    }

    protected void tipBackTwice(String tag) {
        if (getDialogShowTimes(tag) == 2) {
            presenter.showToast(context.getResources().getString(R.string.apconfig_exit_config_toast));
        }
    }

    protected void showDialog(String tag) {
        logi("showDialog: " + tag);
        if (checkBackTwice(tag)) {
            return;
        }
        if (!isDialogShow(1)) {
            logi("showNotLogin");
            presenter.showNotLogin(tag);
        } else {
            input(tag);
        }
        recordDialog(tag);
        tipBackTwice(tag);
    }

    @Override
    protected String getLogTag() {
        return ApConfigService.TAG;
    }
}
