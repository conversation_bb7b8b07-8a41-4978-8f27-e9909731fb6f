package com.skyworth.smarthome.home.custom.panel;

import android.content.Context;
import android.util.Log;
import android.view.View;

import com.alibaba.fastjson.JSONObject;
import com.coocaa.app.core.utils.FuncKt;
import com.coocaa.operate6_0.model.Container;
import com.skyworth.smarthome.home.smartdevice.devicelist.DeviceListView;
import com.skyworth.smarthome.service.push.local.DeviceDataPushUtil;
import com.skyworth.smarthome.service.push.local.IHandlerPush;
import com.skyworth.smarthome.common.bean.DevcieStatusBean;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.home.custom.BaseCustomPresenter;
import com.skyworth.smarthome.service.model.ISmartDeviceDataModel;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.EmptyUtils;

import java.util.ArrayList;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * @Description: 设备列表自定义Panel
 * @Author: wzh
 * @CreateDate: 2020/6/17
 */
public class DevicePanelPresenter extends BaseCustomPresenter {

    private DeviceListView mDeviceListView;
    private List<DeviceInfo> mDeviceList = new ArrayList<>();
    private String mCurrentDeviceList = "";//给打补丁（相同数据同一时间会推多次，此处做一下拦截）

    private IHandlerPush.IPushListener iPushListener = new IHandlerPush.IPushListener() {

        @Override
        public void onArrive(AppConstants.SSE_PUSH event, final String data) {
            //数据变化的回调
            switch (event) {
                case DEVICE_STATUS://设备状态变化
                    CCLog.i("DevicePanelPresenter", "onArrive: ----------event:" + event);
                    if (EmptyUtils.isEmpty(data)) {
                        return;
                    }
                    FuncKt.runOnUiThread(new Function0<Unit>() {
                        @Override
                        public Unit invoke() {
                            notifyDeviceItemChanged(data);
                            return Unit.INSTANCE;
                        }
                    });
                    break;
                case DEVICE_LIST://设备列表变化
                    CCLog.i("DevicePanelPresenter", "onArrive: ----------event:" + event);
                    if (EmptyUtils.isNotEmpty(mCurrentDeviceList) && EmptyUtils.isNotEmpty(data) && mCurrentDeviceList.equals(data)) {
                        CCLog.i("DevicePanelPresenter", "onArrive: event:" + event + " -- same data, return...");
                        return;
                    }
                    mCurrentDeviceList = data;
                    refreshUI(ISmartDeviceDataModel.INSTANCE.getCacheSmartDeviceList());
                    break;
                default:
                    break;
            }
        }
    };

    private void notifyDeviceItemChanged(String data) {
        DevcieStatusBean devcieStatusBean = JSONObject.parseObject(data, DevcieStatusBean.class);
        if (devcieStatusBean != null) {
            for (DeviceInfo info : mDeviceList) {
                if (info.device_id.equals(devcieStatusBean.device_id)) {
                    if (EmptyUtils.isNotEmpty(devcieStatusBean.device_name)) {
                        info.device_name = devcieStatusBean.device_name;
                    }
                    if (EmptyUtils.isNotEmpty(devcieStatusBean.online_status)) {
                        info.online_status = Integer.parseInt(devcieStatusBean.online_status);
                    }
                    if (EmptyUtils.isNotEmpty(devcieStatusBean.device_status_desc)) {
                        info.device_status_desc = devcieStatusBean.device_status_desc;
                    }
                    if (EmptyUtils.isNotEmpty(devcieStatusBean.status)) {
                        JSONObject reportStatus = JSONObject.parseObject(info.report_status);
                        JSONObject updateStatus = JSONObject.parseObject(devcieStatusBean.status);
                        Object[] updateStatusArr = updateStatus.keySet().toArray();
                        if (EmptyUtils.isNotEmpty(updateStatusArr)) {
                            for (Object key : updateStatusArr) {
                                reportStatus.put((String) key, updateStatus.getString((String) key));
                            }
                        }
                        info.report_status = reportStatus.toJSONString();
                    }
                    CCLog.i("DevicePanelPresenter refreshItem:" + data);
                    mDeviceListView.refreshItem(info);
                    break;
                }
            }
        }
    }

    public DevicePanelPresenter(Context context) {
        super(context);
        mDeviceListView = new DeviceListView(context);
        mDeviceListView.setBoundaryCallback(this);
        DeviceDataPushUtil.getPush().regReceiver(iPushListener);
    }

    @Override
    public View getView() {
        return mDeviceListView;
    }

    @Override
    public void setContainer(Container container) {
        super.setContainer(container);
        refreshUI(ISmartDeviceDataModel.INSTANCE.getCacheSmartDeviceList());
    }

    private void refreshUI(final List<DeviceInfo> list) {
        FuncKt.runOnUiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mDeviceList.clear();
                if (EmptyUtils.isNotEmpty(list)) {
                    mDeviceList.addAll(list);
                }
                mDeviceListView.refreshUI(mDeviceList);
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void onPause() {
        super.onPause();
        mDeviceListView.onPause();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mDeviceListView.destroy();
        DeviceDataPushUtil.getPush().unRegReceiver(iPushListener);
    }
}
