package com.skyworth.smarthome.personal.unbinddevice;

import android.content.Context;

import com.skyworth.smarthome.common.base.mvp.IBasePresenter;
import com.skyworth.smarthome.common.base.mvp.IBaseView;
import com.skyworth.smarthome.common.bean.DeviceInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2020/3/27
 * @describe
 */
public interface UnbindContract {

    interface View extends IBaseView<Presenter> {
        void queryBindDevices(List<DeviceInfo> deviceBeans);
    }

    interface Presenter extends IBasePresenter<View> {
        void init(Context context);

    }
}
