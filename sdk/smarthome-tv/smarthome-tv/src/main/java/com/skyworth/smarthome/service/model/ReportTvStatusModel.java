package com.skyworth.smarthome.service.model;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;

import com.coocaa.app.core.app.AppCoreApplication;
import com.coocaa.app.core.http.HttpServiceManager;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.common.http.SmartDevicesHttpService;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.util.LogUtil;
import com.skyworth.smarthome.common.util.SmartHomeServiceManager;
import com.skyworth.smarthome.common.util.SystemProperty;
import com.skyworth.smarthome.common.util.Utils;
import com.skyworth.smarthome.voicehandle.SmartHomeAI;
import com.smarthome.common.model.SmartBaseData;
import com.smarthome.common.utils.EmptyUtils;

import java.util.HashMap;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * Describe:电视状态上报
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/7/2
 */
public class ReportTvStatusModel implements IReportTvStatusModel {

    private final static String TAG = "ReportStatus";

    private Context mContext;

    private ReportHandler mHandler;

    private final int KEY_START_REPROT_TVSTATUS = 0;//开始上报电视状态

    private final int DELAY_TIME = 30*60*1000;//30秒

    private class ReportHandler extends Handler {

        public ReportHandler(Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(Message msg) {
            reportTvStatus();
            LogUtil.androidLog(TAG,"----------Report TV Status---------");
            mHandler.sendEmptyMessageDelayed(KEY_START_REPROT_TVSTATUS,DELAY_TIME);
        }
    }

    public ReportTvStatusModel(Context context) {
        this.mContext = context;
        mHandler = new ReportHandler(context.getMainLooper());
    }


    @Override
    public void startReport() {
        reportTvStatus();
        mHandler.sendEmptyMessage(KEY_START_REPROT_TVSTATUS);
    }

    @Override
    public void reportTvStatus() {
        AppCoreApplication.Companion.workerThread(100, new Function0<Unit>() {
            @Override
            public Unit invoke() {
                reportTVDeviceStatus();
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void reportPow_S() {
        boolean isAISleep = Utils.isAistandbymode();
        String pow_s = isAISleep ? "0" : "1";
        if(EmptyUtils.isEmpty(AppData.getInstance().getPow_s())||!AppData.getInstance().getPow_s().equals(pow_s)){
            SmartHomeAI.respontTVStatus(SmartHomeTvLib.getContext(), "POW_S", 0x80, pow_s);
            AppData.getInstance().setPow_s(pow_s);
            LogUtil.androidLog(TAG,"TV POW_S："+pow_s);
            Map<String,Object> map = new HashMap<>();
            map.put("POW_S",pow_s);
        }
    }

    @Override
    public void reportMute() {
        boolean isMute = SmartHomeServiceManager.getManager().isVolumeMute();
        String mute = isMute ? "1" : "0";
        if(EmptyUtils.isEmpty(AppData.getInstance().getMute())||!AppData.getInstance().getMute().equals(mute)){
            SmartHomeAI.respontTVStatus(SmartHomeTvLib.getContext(), "MUTE", 0x80, mute);
            AppData.getInstance().setMute(mute);
            LogUtil.androidLog(TAG,"TV MUTE：" + mute);
            Map<String,Object> map = new HashMap<>();
            map.put("MUTE",mute);
        }
    }

    @Override
    public void reportVolume() {
        int volume = SmartHomeServiceManager.getManager().getVolume();
        String volumeValue = String.valueOf(volume);
        if(EmptyUtils.isEmpty(AppData.getInstance().getVolume())||!AppData.getInstance().getVolume().equals(volumeValue)){
            SmartHomeAI.respontTVStatus(SmartHomeTvLib.getContext(), "VOL", 0x80,volumeValue);
            AppData.getInstance().setVolume(volumeValue);
            LogUtil.androidLog(TAG,"TV Volume：" + volumeValue);
            Map<String,Object> map = new HashMap<>();
            map.put("VOL",volumeValue);
        }
    }

    private void reportTVDeviceStatus(){
        boolean isAISleep = Utils.isAistandbymode();
        int pow_s = isAISleep ? 0 : 1;
        boolean isMute = SmartHomeServiceManager.getManager().isVolumeMute();
        int mute = isMute ? 1 : 0;
        int volume = SmartHomeServiceManager.getManager().getVolume();
        Map<String,Object> statusMap = new HashMap<>();
        statusMap.put("POW_S",pow_s);
        statusMap.put("MUTE",mute);
        statusMap.put("VOL",volume);
        String devcieID = SystemProperty.getDeviceId();
        SmartBaseData<String> baseData = HttpServiceManager.Companion.call(SmartDevicesHttpService.SERVICE.updateDeviceStatus(devcieID,statusMap));
    }

    @Override
    public void clear(){
        if(EmptyUtils.isNotEmpty(mHandler)){
            mHandler.removeCallbacksAndMessages(null);
        }
    }

}
