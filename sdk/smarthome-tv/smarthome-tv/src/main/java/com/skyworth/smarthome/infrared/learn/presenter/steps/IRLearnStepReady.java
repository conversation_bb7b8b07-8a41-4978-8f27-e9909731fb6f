package com.skyworth.smarthome.infrared.learn.presenter.steps;

import com.coocaa.app.core.app.AppCoreApplication;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

import static com.skyworth.smarthome.infrared.learn.presenter.IRLearnPresenterImpl.INPUT_I_AM_READY;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/7/4 10:12.
 */
public class IRLearnStepReady extends BaseIRLearnStep {
    private static final String STEP_TAG = "ready";

    @Override
    public void create() {
        super.create();
    }

    @Override
    public void run() {
        super.run();
        presenter.showReady();
    }

    @Override
    public boolean input(String msg, Object... params) {
        if (INPUT_I_AM_READY.equals(msg)) {
            AppCoreApplication.Companion.ioThread(new Function0<Unit>() {
                @Override
                public Unit invoke() {
                    presenter.showLoading();
                    if (!presenter.loadLearnList()) {
                        loadError();
                        return Unit.INSTANCE;
                    }
                    presenter.hideLoading();
                    next();
                    return Unit.INSTANCE;
                }
            });
            return true;
        }
        return false;
    }

    private void loadError() {
        presenter.showError("数据加载失败");
    }

    @Override
    public String getTag() {
        return STEP_TAG;
    }

    @Override
    public void destroy() {
        super.destroy();
    }
}
