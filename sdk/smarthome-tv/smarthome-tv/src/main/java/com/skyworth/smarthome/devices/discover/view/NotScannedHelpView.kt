package com.skyworth.smarthome.devices.discover.view

import android.content.Context
import android.graphics.BitmapFactory
import android.graphics.Color
import android.text.TextUtils
import android.view.Gravity
import android.widget.ImageView
import com.coocaa.app.core.utils.ioThread
import com.coocaa.app.core.utils.runOnUiThread
import com.skyworth.smarthome.R
import com.skyworth.smarthome.common.util.QRUtils
import com.skyworth.ui.api.SkyBaseDialogView
import com.skyworth.ui.api.widget.CCFocusDrawable
import com.skyworth.util.Util
import com.smarthome.common.utils.XThemeUtils
import org.jetbrains.anko.*


/**
 * Created by fc on 2019/9/10
 * Describe:扫描不到的弹窗的 帮助view
 */
class NotScannedHelpView(context: Context) : SkyBaseDialogView(context) {
    var mQrCodeView: ImageView? = null

    interface OnClickListener {
        fun OnClick()
    }

    var listener: OnClickListener? = null

    fun setOnBtnClickListener(l: OnClickListener) {
        listener = l
    }

    init {
        layoutParams = LayoutParams(Util.Div(1200), Util.Div(520))
        background = XThemeUtils.getDrawable(Color.parseColor("#454649"), 0, 0, Util.Div(14))
        alpha = 0.96f

        //标题：
        textView {
            val params = LayoutParams(wrapContent, wrapContent)
            params.topMargin = Util.Div(60)
            params.gravity = Gravity.CENTER_HORIZONTAL
            layoutParams = params
            textSize = Util.Dpi(36).toFloat()
            textColor = Color.parseColor("#999999")
            paint.isFakeBoldText = true
            text = "扫描不到要添加的设备怎么办？"
        }
        imageView {
            val params = LayoutParams(Util.Div(32), Util.Div(32))
            params.topMargin = Util.Div(180)
            params.leftMargin = Util.Div(60)
            layoutParams = params
            background = context.resources.getDrawable(R.drawable.not_scan_help_one)
        }

        textView {
            val params = LayoutParams(wrapContent, wrapContent)
            params.topMargin = Util.Div(178)
            params.leftMargin = Util.Div(112)
            layoutParams = params
            textSize = Util.Dpi(28).toFloat()
            textColor = Color.parseColor("#999999")
            text = "查看说明书确保设备进入配网模式。"
        }

        imageView {
            val params = LayoutParams(Util.Div(32), Util.Div(32))
            params.topMargin = Util.Div(253)
            params.leftMargin = Util.Div(60)
            layoutParams = params
            background = context.resources.getDrawable(R.drawable.not_scan_help_two)
        }

        textView {
            val params = LayoutParams(Util.Div(460), wrapContent)
            params.topMargin = Util.Div(248)
            params.leftMargin = Util.Div(112)
            layoutParams = params
            maxLines = 3
            textSize = Util.Dpi(28).toFloat()
            textColor = Color.parseColor("#999999")
            text = "确保电视连接WiFi并检查网络，网络异常无法发现设备。"
        }

        imageView {
            val params = LayoutParams(Util.Div(32), Util.Div(32))
            params.topMargin = Util.Div(363)
            params.leftMargin = Util.Div(60)
            layoutParams = params
            background = context.resources.getDrawable(R.drawable.not_scan_help_three)
        }

        textView {
            val params = LayoutParams(Util.Div(460), wrapContent)
            params.topMargin = Util.Div(358)
            params.leftMargin = Util.Div(112)
            layoutParams = params
            maxLines = 3
            textSize = Util.Dpi(28).toFloat()
            textColor = Color.parseColor("#999999")
            text = "设备已被他人绑定，如果不清楚谁连接了设备，可查看说明书进行重置。"
        }

        imageView {
            val params = LayoutParams(Util.Div(2), Util.Div(272))
            params.topMargin = Util.Div(176)
            params.leftMargin = Util.Div(594)
            layoutParams = params
            backgroundColor = Color.parseColor("#999999")
            alpha = 0.3f
        }

        textView {
            val params = LayoutParams(wrapContent, wrapContent)
            params.topMargin = Util.Div(178)
            params.leftMargin = Util.Div(626)
            layoutParams = params
            textSize = Util.Dpi(28).toFloat()
            textColor = Color.parseColor("#999999")
            text = "通过账户授权控制其他品牌设备"
        }

        textView {
            val params = LayoutParams(Util.Div(90), Util.Div(40))
            params.topMargin = Util.Div(174 + 5)
            params.leftMargin = Util.Div(1050)
            layoutParams = params
            textSize = Util.Dpi(18).toFloat()
            textColor = Color.parseColor("#000000")
            background = CCFocusDrawable(context).setRadius(Util.Dpi(50).toFloat()).setSolidColor(resources.getColor(R.color.white));

            text = "去看看"
            gravity = Gravity.CENTER
            isFocusable = true
            isClickable = true
            setOnClickListener {
                listener?.OnClick()
                logScanOnClick()
            }
        }

        textView {
            val params = LayoutParams(wrapContent, wrapContent)
            params.topMargin = Util.Div(344)
            params.leftMargin = Util.Div(676)
            layoutParams = params
            textSize = Util.Dpi(28).toFloat()
            textColor = Color.parseColor("#999999")
            text = "扫码下载手机APP"
        }

        imageView {
            val params = LayoutParams(Util.Div(51), Util.Div(24))
            params.topMargin = Util.Div(346 + 5)
            params.leftMargin = Util.Div(907 - 5)
            layoutParams = params
            background = context.resources.getDrawable(R.drawable.icon_download_recommend)
        }

        //白色背景套在外面
        imageView {
            val params = LayoutParams(Util.Div(170), Util.Div(170))
            params.topMargin = Util.Div(273)
            params.leftMargin = Util.Div(970)
            layoutParams = params
            backgroundColor = Color.parseColor("#FFFFFF")
        }

        mQrCodeView = imageView {
            val params = LayoutParams(Util.Div(158), Util.Div(158))
            params.topMargin = Util.Div(279)
            params.leftMargin = Util.Div(976)
            layoutParams = params
            backgroundColor = Color.parseColor("#FFFFFF")
        }

    }

    private fun logScanOnClick() {
//        val map = HashMap<String, String>()
//        map["Button"] = "去看看"
//        LoggerImpl.onEvent(EVENT_LAN_SCANNING_ONCLICK, map)
    }

    fun showQrCode(qrcontent: String) {
        if (!TextUtils.isEmpty(qrcontent)) {
            ioThread {
                try {
//                    val logo = BitmapFactory.decodeResource(resources, R.drawable.icon_smarthome)
                    val bitmap = QRUtils.createQRImage(qrcontent, Util.Div(158), Util.Div(158), null)
                    if (bitmap != null) {
                        runOnUiThread {
                            mQrCodeView?.setImageBitmap(bitmap)
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        } else {
            mQrCodeView?.setImageBitmap(null)
        }
    }
}