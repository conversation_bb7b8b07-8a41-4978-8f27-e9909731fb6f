package com.skyworth.smarthome.home.base;

import android.content.Context;
import android.graphics.Rect;
import android.view.View;
import android.widget.FrameLayout;

import com.skyworth.util.Util;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/21
 */
public abstract class BaseSmartItemView<T> extends FrameLayout {

    protected T mData;
    protected final static int FOCUS_W = Util.Div(16);

    public BaseSmartItemView(Context context) {
        super(context);
        setFocusable(true);
        setFocusableInTouchMode(true);
        setPressed(true);
    }

    public void refreshUI(T data,int position) {
        mData = data;
    }

    @Override
    protected void onFocusChanged(boolean gainFocus, int direction, Rect previouslyFocusedRect) {
        super.onFocusChanged(gainFocus, direction, previouslyFocusedRect);
        onFocusChange(this, gainFocus);
    }

    public void onFocusChange(View v, boolean b) {

    }

    public void onDestroy() {

    }
}
