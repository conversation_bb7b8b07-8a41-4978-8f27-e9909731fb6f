package com.skyworth.smarthome.infrared.matchkey.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import android.support.annotation.NonNull;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.ui.DialogBg;
import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.util.Util;
import com.smarthome.common.utils.XThemeUtils;

import static android.graphics.Paint.ANTI_ALIAS_FLAG;
import static android.graphics.Paint.UNDERLINE_TEXT_FLAG;
import static com.skyworth.smarthome.infrared.matchkey.presenter.IMatchKeyPresenterImpl.HANDLE_KEY_CONFIRM_AGAIN;
import static com.skyworth.smarthome.infrared.matchkey.presenter.IMatchKeyPresenterImpl.HANDLE_KEY_CONFIRM_NO;
import static com.skyworth.smarthome.infrared.matchkey.presenter.IMatchKeyPresenterImpl.HANDLE_KEY_CONFIRM_YES;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/4/30 14:17.
 */
public class MatchConfirmView extends FrameLayout {
    private TextView title = null;
    private TextView tip = null;
    private TextView tipLine2 = null;
    private OnClickListener onClickListener = null;
    private Drawable buttonFocusBg = null;
    private Drawable buttonUnFocusBg = null;
    private TextView buttonLeft = null;

    public MatchConfirmView(@NonNull Context context, OnClickListener onClickListener) {
        super(context);
        this.onClickListener = onClickListener;
        initBg();
        initView();
    }

    private void initBg() {
        setBackground(new DialogBg());
        buttonFocusBg = new CCFocusDrawable(getContext()).setRadius(Util.Dpi(16)).setSolidColor(getResources().getColor(R.color.white));
        buttonUnFocusBg = XThemeUtils.getDrawable(Color.parseColor("#19FFFFFF"), 0, Util.Div(2), Util.Div(16));
    }

    private void initView() {
        title = new TextView(getContext());
        title.setTextSize(Util.Dpi(36));
        title.setTextColor(Color.WHITE);
        title.getPaint().setFakeBoldText(true);
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.topMargin = Util.Div(40);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        addView(title, layoutParams);

        tip = new TextView(getContext());
        tip.setTextSize(Util.Dpi(32));
        tip.setTextColor(Color.parseColor("#aaFFFFFF"));
        tip.getPaint().setFakeBoldText(true);
        tip.setGravity(Gravity.CENTER);
        tip.setLines(2);
        layoutParams = new LayoutParams(Util.Div(614), ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.topMargin = Util.Div(96);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        addView(tip, layoutParams);

        buttonLeft = createButton();
        buttonLeft.setTag(HANDLE_KEY_CONFIRM_YES);
        buttonLeft.setOnClickListener(onClickListener);
        buttonLeft.setText(R.string.ir_config_confirm_yes);
        layoutParams = new LayoutParams(Util.Div(292), Util.Div(90));
        layoutParams.topMargin = Util.Div(226);
        layoutParams.leftMargin = Util.Div(50);
        addView(buttonLeft, layoutParams);

        TextView buttonRight = createButton();
        buttonRight.setTag(HANDLE_KEY_CONFIRM_NO);
        buttonRight.setOnClickListener(onClickListener);
        buttonRight.setText(R.string.ir_config_confirm_no);
        layoutParams = new LayoutParams(Util.Div(292), Util.Div(90));
        layoutParams.topMargin = Util.Div(226);
        layoutParams.leftMargin = Util.Div(372);
        addView(buttonRight, layoutParams);

        final TextView retry = new TextView(getContext());
        retry.setFocusableInTouchMode(true);
        retry.setFocusable(true);
        retry.setGravity(Gravity.CENTER);
        retry.setTag(HANDLE_KEY_CONFIRM_AGAIN);
        retry.setTextSize(Util.Dpi(20));
        retry.setTextColor(Color.WHITE);
        retry.getPaint().setFlags(UNDERLINE_TEXT_FLAG | ANTI_ALIAS_FLAG);
        retry.setText(R.string.ir_config_confirm_not_sure);
        retry.setOnClickListener(onClickListener);
        retry.setPadding(0, 0, 0, 0);
        retry.setBackground(null);
        retry.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    retry.setTextColor(Color.parseColor("#000000"));
                    retry.setBackground(buttonFocusBg);
                    retry.getPaint().setFlags(ANTI_ALIAS_FLAG);
                } else {
                    retry.setTextColor(Color.WHITE);
                    retry.setBackground(null);
                    retry.getPaint().setFlags(UNDERLINE_TEXT_FLAG | ANTI_ALIAS_FLAG);
                }
            }
        });
        layoutParams = new LayoutParams(Util.Div(200), Util.Div(60));
        layoutParams.topMargin = Util.Div(325);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        addView(retry, layoutParams);

    }

    private TextView createButton() {
        final TextView button = new TextView(getContext());
        button.setFocusableInTouchMode(true);
        button.setFocusable(true);
        button.setGravity(Gravity.CENTER);
        button.setTextSize(Util.Dpi(32));
        button.setTextColor(Color.WHITE);
        button.setBackground(buttonUnFocusBg);
        button.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    button.setTextColor(Color.parseColor("#000000"));
                    button.setBackground(buttonFocusBg);
                } else {
                    button.setTextColor(Color.parseColor("#aaFFFFFF"));
                    button.setBackground(buttonUnFocusBg);
                }
            }
        });
        return button;
    }

    public void show(int step, int totalStep, String tipTextLine1, String tipTextLine2) {
        title.setText(getResources().getString(R.string.ir_config_match_key, step, totalStep));
        tip.setText(tipTextLine1+","+tipTextLine2);
        post(new Runnable() {
            @Override
            public void run() {
                buttonLeft.requestFocus();
            }
        });
    }
}
