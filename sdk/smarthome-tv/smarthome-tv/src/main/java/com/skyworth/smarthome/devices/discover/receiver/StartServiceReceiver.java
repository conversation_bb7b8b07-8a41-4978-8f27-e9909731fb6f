package com.skyworth.smarthome.devices.discover.receiver;

import android.app.ActivityManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import com.coocaa.app.core.app.AppCoreApplication;
import com.skyworth.smarthome.devices.apconfig.presenter.helpers.ConfirmBindHelper;
import com.skyworth.smarthome.home.smartdevice.controlpanel.thirdapp.ThirdAppEventHelper;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.util.DataCacheUtil;
import com.skyworth.smarthome.common.util.LogUtil;
import com.skyworth.smarthome.service.SmartHomeService;
import com.skyworth.smarthome.service.model.ISmartHomeModel;
import com.skyworth.smarthome.voicehandle.SmartHomeAI;

import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;


/**
 * Describe:开机广播监听
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/1/3
 */
public class StartServiceReceiver extends BroadcastReceiver {
    private Context mContext;
    public static final String ACTION_BOOT_COMPLETED = "android.intent.action.BOOT_COMPLETED";
    public static final String ACTION_BOOT_COMPLETED_TC = "android.intent.action.BOOT_COMPLETED.TC";
    public static final String ACTION_BOOT_COMPLETED_RESUME = "com.skyworth.broadcast.standby.quick.resume";
    public static final String ACTION_SCREEN_SOUND_OPEN = "com.skyworth.broadcast.screensound.open";
    public static final String ACTION_SCREEN_SOUND_CLOSE = "com.skyworth.broadcast.screensound.close";
    public static final String ACTION_START_SCREENSAVER = "com.tianci.ad.start_screensaver";
    public static final String ACTION_EXIT_AD_SCREENSAVER = "com.tianci.ad.exist_screensaver";
    public static final String ACTION_EXIT_SKYWORTH_SCREENSAVER = "com.skyworth.screensaverexit";
    //恢复出厂设置开始广播
    public static final String SKY_BCT_RECOVERY_START = "com.skyworth.broadcast.recovery.start";

    @Override
    public void onReceive(Context context, Intent intent) {
        mContext = context;
        String action = intent.getAction();
        LogUtil.androidLog("接收广播Action:" + action);
        if (TextUtils.isEmpty(action))
            return;
        switch (action) {
            case ACTION_SCREEN_SOUND_CLOSE: {
                AppData.getInstance().setCloseScreen(true);
                SmartHomeAI.respontTVStatus(context, "POW_S", 0x80, "0");
                new ThirdAppEventHelper().onAiStandByStartEvent();
                break;
            }
            case ACTION_SCREEN_SOUND_OPEN: {
                AppData.getInstance().setCloseScreen(false);
                AppData.getInstance().setOpenScreensaver(false);
                SmartHomeAI.respontTVStatus(context, "POW_S", 0x80, "1");
                DataCacheUtil.getInstance().putInt(DataCacheUtil.KEY_SAVE_ISSHOW_APCONFIG, 0);
                startOnlyService();
//                checkApconfigData();
                new ThirdAppEventHelper().onAiStandByEndEvent();
                break;
            }
            case ACTION_START_SCREENSAVER:
                LogUtil.androidLog("屏保打开");
                AppData.getInstance().setOpenScreensaver(true);
                break;
            case ACTION_EXIT_SKYWORTH_SCREENSAVER:
            case ACTION_EXIT_AD_SCREENSAVER:
                LogUtil.androidLog("屏保关闭");
                AppData.getInstance().setOpenScreensaver(false);
                break;
            case ACTION_BOOT_COMPLETED:
            case ACTION_BOOT_COMPLETED_RESUME:
            case ACTION_BOOT_COMPLETED_TC: {
                DataCacheUtil.getInstance().putInt(DataCacheUtil.KEY_SAVE_ISSHOW_APCONFIG, 0);
                startOnlyService();
                SmartHomeAI.respontTVStatus(context, "POW_S", 0x80, "1");
//                checkApconfigData();
                break;
            }
            case SKY_BCT_RECOVERY_START://系统恢复出厂设置
                forceUnBindDevices();
                break;
            default:
                startOnlyService();
                break;
        }
    }


    /**
     * 仅仅启动服务
     */
    private void startOnlyService() {
        boolean isRunning = isServiceRunning(SmartHomeService.class.getName());
        if (!isRunning) {//发现服务进程已被杀，重新启动进程
            mContext.startService(new Intent(mContext, SmartHomeService.class));
        }
    }

    /**
     * 强制解绑设备
     */
    private void forceUnBindDevices(){
        AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                ISmartHomeModel.INSTANCE.forceUnBindDevices();
                return Unit.INSTANCE;
            }
        });
    }

    /**
     * 检查配网数据，若有配网确定弹出，则徐弹出
     */
    private void checkApconfigData(){
        AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                new ConfirmBindHelper().bootupCheckUnConfirm();
                return Unit.INSTANCE;
            }
        });
    }

    /**
     * 判断服务是否在运行
     * @param serviceName
     * @return
     */
    public boolean isServiceRunning(String serviceName) {
        boolean isWork = false;
        ActivityManager myAM = (ActivityManager) mContext.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningServiceInfo> myList = myAM.getRunningServices(100);
        if (myList.size() <= 0) {
            return false;
        }
        for (int i = 0; i < myList.size(); i++) {
            String mName = myList.get(i).service.getClassName().toString();
            if (mName.equals(serviceName)) {
                isWork = true;
                break;
            }
        }
        return isWork;
    }

}
