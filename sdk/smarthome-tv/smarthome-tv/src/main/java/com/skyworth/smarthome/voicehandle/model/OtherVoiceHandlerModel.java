package com.skyworth.smarthome.voicehandle.model;

import android.content.Intent;

import com.coocaa.app.core.app.AppCoreApplication;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.common.util.SystemProperty;
import com.skyworth.smarthome.service.model.IFunctionGoToModel;
import com.skyworth.smarthome.voicehandle.SmartHomeAI;
import com.smarthome.common.dataer.DataHelpInfo;
import com.smarthome.common.dataer.LogSDK;
import com.smarthome.common.utils.EmptyUtils;
import com.swaiot.aiotlib.common.util.LogUtil;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * Describe:其他语音处理Model
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/8/20
 */
public class OtherVoiceHandlerModel implements IVoiceHandlerModel, IOtherVoiceHandlerModel {

    private static final String VOICE_CMD_INFRARED_LIST = "1";//红外遥控器列表
    private static final String VOICE_CMD_INFRARED_ADD = "2";//添加红外设备
    private static final String VOICE_CMD_SMARTHOME_LIST = "3";//智能设备列表
    private static final String VOICE_CMD_ADD_DEVICE = "4";//添加智能设备

    @Override
    public void handleCmd(Intent intent) {
        String voicecmd = intent.getStringExtra("openPage");
        LogUtil.androidLog("语音处理：" + voicecmd);
        if (EmptyUtils.isNotEmpty(voicecmd)) {
            switch (voicecmd) {
                case VOICE_CMD_ADD_DEVICE:
                    goToAddSmartDevice();
                    break;
                case VOICE_CMD_SMARTHOME_LIST:
                    goToSmartDeviceList();
                    break;
                case VOICE_CMD_INFRARED_ADD:
                    goToAddInfraredDevice();
                    break;
                case VOICE_CMD_INFRARED_LIST:
                    goToInfraredDeviceList();
                    break;
            }
        }
    }

    public void handleCmd(String voiceCmd) {
        LogUtil.androidLog("语音处理：" + voiceCmd);
        if (EmptyUtils.isNotEmpty(voiceCmd)) {
            switch (voiceCmd) {
                case VOICE_CMD_ADD_DEVICE:
                    goToAddSmartDevice();
                    break;
                case VOICE_CMD_SMARTHOME_LIST:
                    goToSmartDeviceList();
                    break;
                case VOICE_CMD_INFRARED_ADD:
                    goToAddInfraredDevice();
                    break;
                case VOICE_CMD_INFRARED_LIST:
                    goToInfraredDeviceList();
                    break;
            }
        }
    }

    @Override
    public void goToInfraredDeviceList() {
        if (SystemProperty.isInfraredDevice()) {
            IFunctionGoToModel.INSTANCE.goToInfraredDeviceList("voice",null);
        } else {
            AppCoreApplication.Companion.workerThread(2000, new Function0<Unit>() {
                @Override
                public Unit invoke() {
                    SmartHomeAI.playVoiceTTS(SmartHomeTvLib.getContext(), "当前电视暂不支持红外控制");
                    return Unit.INSTANCE;
                }
            });

        }
    }

    @Override
    public void goToAddInfraredDevice() {
        if (SystemProperty.isInfraredDevice()) {
            IFunctionGoToModel.INSTANCE.goToAddInfraredDevice("voice");
        } else {
            AppCoreApplication.Companion.workerThread(2000, new Function0<Unit>() {
                @Override
                public Unit invoke() {
                    SmartHomeAI.playVoiceTTS(SmartHomeTvLib.getContext(), "当前电视暂不支持红外控制");
                    return Unit.INSTANCE;
                }
            });
        }
    }

    @Override
    public void goToSmartDeviceList() {
        Intent intent = new Intent("com.smarthome.action.HOME");
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        SmartHomeTvLib.getContext().startActivity(intent);
    }

    @Override
    public void goToAddSmartDevice() {
        DataHelpInfo.getInstance().setDiscoverDeviceOrigin("语音");
        LogSDK.submit(LogSDK.EVENT_ID_ADD_DEVICE, "source", "语音呼出");
        IFunctionGoToModel.INSTANCE.goToAddScanSmartDevice("voice");
    }
}
