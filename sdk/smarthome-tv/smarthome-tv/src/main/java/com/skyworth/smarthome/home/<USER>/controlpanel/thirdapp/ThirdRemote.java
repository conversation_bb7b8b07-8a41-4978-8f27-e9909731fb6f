package com.skyworth.smarthome.home.smartdevice.controlpanel.thirdapp;

import android.content.Context;
import android.content.ServiceConnection;

import com.skyworth.smarthome.third.IThirdLaunch;


/**
 * @ClassName: ThirdRemote
 * @Author: Xu<PERSON>eXiao
 * @CreateDate: 2019-12-20 09:57
 * @Description:
 */
public class ThirdRemote {
    public String packageName;
    public IThirdLaunch remote;
    public ServiceConnection connection;

    public ThirdRemote(String packageName, IThirdLaunch remote, ServiceConnection connection) {
        this.packageName = packageName;
        this.remote = remote;
        this.connection = connection;
    }

    public void unBind(Context context) {
        context.unbindService(connection);
        connection = null;
        remote = null;
    }
}
