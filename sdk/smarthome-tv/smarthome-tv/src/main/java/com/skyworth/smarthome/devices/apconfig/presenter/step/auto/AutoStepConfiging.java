package com.skyworth.smarthome.devices.apconfig.presenter.step.auto;

import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.devices.apconfig.presenter.step.BaseStep;

import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_CONFIG_FAIL;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_CONFIG_SUCCESS;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/9/17 11:24.
 */
public class AutoStepConfiging extends BaseStep {
    public static final String STEP_TAG = "auto_configing";

    @Override
    public void run() {
        super.run();
        if (presenter.canGetCurrentWifiPassword() || !presenter.isCurrentWifiNeedPassword()) {
            presenter.startApConfig();
        } else {
            presenter.recordNotBindAndExit(AppConstants.APCONFIG_FAIL_REASON_GET_WIFI_PASS_ERROR);
        }
    }

    @Override
    public boolean input(String msg, Object... params) {
        if (STEP_MSG_CONFIG_SUCCESS.equals(msg)) {
            presenter.onApConfigComplete();
            next();
            return true;
        } else if (STEP_MSG_CONFIG_FAIL.equals(msg)) {
            presenter.onApConfigError();
            presenter.recordNotBindAndExit(AppConstants.APCONFIG_FAIL_REASON_APCONFIG_FAIL);
            return true;
        }
        return false;
    }

    @Override
    public String getTag() {
        return STEP_TAG;
    }

    @Override
    public void destroy() {
        super.destroy();
    }
}
