package com.skyworth.smarthome.common.util;

import android.content.Context;

/**
 * @ProjectName: NewTV_SmartHome
 * @Package: com.skyworth.smarthome_tv.common.uitl
 * @ClassName: ToastUtil
 * @Description: java类作用描述
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2020/6/15 17:23
 * @UpdateUser: 更新者
 * @UpdateDate: 2020/6/15 17:23
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class ToastUtil {

    private static ToastStyle toastStyle;


    public static void showToast(Context context,String msg,int duration,int[] location) {
        if (toastStyle != null) {
            toastStyle.hide();
        }

        toastStyle = new ToastStyle(context,msg,location);
        toastStyle.show(duration);
    }
}
