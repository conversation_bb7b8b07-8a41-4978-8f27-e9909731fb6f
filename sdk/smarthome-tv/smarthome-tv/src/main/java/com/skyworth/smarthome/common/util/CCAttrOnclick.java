package com.skyworth.smarthome.common.util;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.net.Uri;
import android.os.RemoteException;
import android.support.v4.content.LocalBroadcastManager;
import android.text.TextUtils;
import android.util.Log;

import com.coocaa.app.core.app.AppCoreApplication;
import com.skyworth.smarthome.home.smartdevice.controlpanel.thirdapp.ThirdAppLaunchManager;
import com.smarthome.common.utils.JObject;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * Created by lu on 16-3-2.
 */
public class CCAttrOnclick extends JObject {

    public static final String DOWHAT_START_ACTIVITY = "startActivity";
    public static final String DOWHAT_START_SERVICE = "startService";
    public static final String DOWHAT_SEND_BROADCAST = "sendBroadcast";
    public static final String DOWHAT_SEND_INTERNALBROADCAST = "sendInternalBroadcast";
    public static final String DOWHAT_THIRD_APP = "thirdApp";

    public static final String BYWHAT_ACTION = "action";
    public static final String BYWHAT_CLASS = "class";
    public static final String BYWHAT_URI = "uri";

    public String packagename;
    public int versioncode;
    public String dowhat;
    public String bywhat;
    public String byvalue;
    public String data;
    public Map<String, String> params;
    public CCAttrException<CCAttrOnclick> exception;

    public CCAttrOnclick setPackagename(String packagename) {
        this.packagename = packagename;
        return this;
    }

    public CCAttrOnclick setException(CCAttrException<CCAttrOnclick> exception) {
        this.exception = exception;
        return this;
    }

    public CCAttrOnclick setParams(Map<String, String> params) {
        this.params = params;
        return this;
    }

    public CCAttrOnclick setVersioncode(int versioncode) {
        this.versioncode = versioncode;
        return this;
    }

    public CCAttrOnclick setDowhat(String dowhat) {
        this.dowhat = dowhat;
        return this;
    }

    public CCAttrOnclick setByvalue(String byvalue) {
        this.byvalue = byvalue;
        return this;
    }

    public CCAttrOnclick setBywhat(String bywhat) {
        this.bywhat = bywhat;
        return this;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public static CCAttrOnclick create(String packageName) {
        CCAttrOnclick data = new CCAttrOnclick();
        data.setPackagename(packageName);
        return data;
    }

    public String getDowhat() {
        return dowhat;
    }

    public String getBywhat() {
        return bywhat;
    }

    public String getByvalue() {
        return byvalue;
    }

    public Map<String, String> getParams() {
        return params;
    }

    public CCAttrException<CCAttrOnclick> getException() {
        return exception;
    }

    public int getVersioncode() {
        return versioncode;
    }

    public String getPackagename() {
        return packagename;
    }

    public CCAttrOnclick build() {

        return this;
    }


    public Intent buildIntent(Context c) {
        Intent intent = null;
        if (bywhat != null && !bywhat.equals("") && !bywhat.equals("null")) {
            intent = new Intent();
            if (!TextUtils.isEmpty(packagename)) {
                if (packagename.equals("com.coocaa.x.app.mall")) {
                    packagename = "com.coocaa.mall";
                }
                intent.setPackage(packagename);
            }
            if (bywhat.equals(BYWHAT_ACTION) && !TextUtils.isEmpty(byvalue)) {
                intent.setAction(byvalue);
                if (!TextUtils.isEmpty(data)) {
                    intent.setData(Uri.parse(data));
                }
            } else if (bywhat.equals(BYWHAT_CLASS) && !TextUtils.isEmpty(byvalue)) {
                intent.setClassName(packagename, byvalue);
                if (!TextUtils.isEmpty(data)) {
                    intent.setData(Uri.parse(data));
                }
            } else if (bywhat.equals(BYWHAT_URI) && !TextUtils.isEmpty(byvalue)) {
                intent.setData(Uri.parse(byvalue));
            } else {
                byvalue = getLauncherActivity(c, packagename);
                intent.setClassName(packagename, byvalue);
            }
            if (params != null && params.size() > 0) {
                Iterator iterator = params.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry entry = (Map.Entry) iterator.next();
                    intent.putExtra((String) entry.getKey(), (String) entry.getValue());
                }
            }
        }
        return intent;
    }

    private static String getLauncherActivity(Context context, String packageName) {
        PackageManager pm = context.getPackageManager();
        Intent intent = new Intent(Intent.ACTION_MAIN, null);
        intent.addCategory(Intent.CATEGORY_LAUNCHER);
        intent.setPackage(packageName);
        List<ResolveInfo> resolveInfo = pm.queryIntentActivities(intent, PackageManager.GET_DISABLED_COMPONENTS);
        if (resolveInfo != null && resolveInfo.size() > 0) {
            ResolveInfo info = resolveInfo.get(0);
            return info.activityInfo.name;
        }
        return "";
    }

    public static boolean start(final Context c, final CCAttrOnclick onclick) {
        if (handleThirdApp(c, onclick)) {
            return true;
        }
        Intent intent = onclick.buildIntent(c);
        if (intent == null || c.getPackageManager().queryIntentActivities(intent, PackageManager.MATCH_ALL).size() <= 0) {
            Log.i("CCOnclick", "onClick: intent == null");
            return false;
        }
        if (onclick.getDowhat() != null && !onclick.getDowhat().equals("") && !onclick.getDowhat().equals("null")) {
            if (intent != null) {
                try {
                    if (onclick.getDowhat().equals(CCAttrOnclick.DOWHAT_START_ACTIVITY)) {
                        c.startActivity(intent);
                        return true;
                    } else if (onclick.getDowhat().equals(CCAttrOnclick.DOWHAT_START_SERVICE)) {
                        c.startService(intent);
                        return true;
                    } else if (onclick.getDowhat().equals(CCAttrOnclick.DOWHAT_SEND_BROADCAST)) {
                        c.sendBroadcast(intent);
                        return true;
                    } else if (onclick.getDowhat().equals(CCAttrOnclick.DOWHAT_SEND_INTERNALBROADCAST)) {
                        LocalBroadcastManager.getInstance(c).sendBroadcast(intent);
                        return true;
                    } else if (onclick.getDowhat().equals("start_video")) {

                    } else if (onclick.getDowhat().equals("start_web")) {

                    } else if (onclick.getDowhat().equals("start_list")) {

                    } else if (onclick.getDowhat().equals("start_image")) {

                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    if (onclick.getException() != null && onclick.getException().getValue() != null) {
                        return start(c, onclick.getException().getValue());
                    }
                }
            }
        }
        return false;
    }
    private static boolean handleThirdApp(final Context c, final CCAttrOnclick onclick) {
        if (onclick != null && onclick.getDowhat() != null && onclick.getDowhat().equals(CCAttrOnclick.DOWHAT_THIRD_APP)) {
            AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
                @Override
                public Unit invoke() {
                    try {
                        ThirdAppLaunchManager.getInstance().handleStartThirdApp(c, onclick.getPackagename(), onclick.getData());
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                    return Unit.INSTANCE;
                }
            });
            return true;
        }
        return false;
    }
}
