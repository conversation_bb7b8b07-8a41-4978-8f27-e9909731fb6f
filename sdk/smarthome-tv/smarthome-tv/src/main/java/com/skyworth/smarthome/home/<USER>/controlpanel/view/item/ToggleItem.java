package com.skyworth.smarthome.home.smartdevice.controlpanel.view.item;

import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.home.smartdevice.controlpanel.common.itemdata.ToggleControlData;
import com.skyworth.smarthome.home.smartdevice.controlpanel.view.ControlPanelView;
import com.skyworth.smarthome.home.smartdevice.controlpanel.view.base.BaseControlItem;
import com.skyworth.util.Util;

import java.util.HashMap;
import java.util.Map;


/**
 * Description: <br>
 * Created by XuZexiao on 2019/3/13 20:48.
 */
public class ToggleItem extends BaseControlItem<ToggleControlData> {
    public static final String TYPE = "toggle";
    private TextView mValue = null;
    private ImageView mToggleView = null;
    private boolean isOpen = false;

    public ToggleItem(Context context) {
        super(context);
        initValue();
        initToggleView();
    }

    private void initValue() {
        mValue = new TextView(getContext());
        mValue.setTextSize(Util.Dpi(32));
        mValue.setTextColor(Color.parseColor("#aaFFFFFF"));
        mValue.getPaint().setFakeBoldText(true);
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.leftMargin = Util.Div(20);
        layoutParams.gravity = Gravity.CENTER_VERTICAL;
        addView(mValue, layoutParams);
    }

    private void initToggleView() {
        mToggleView = new ImageView(getContext());
        LayoutParams layoutParams = new LayoutParams(Util.Div(68), Util.Div(36));
        layoutParams.gravity = Gravity.CENTER_VERTICAL | Gravity.RIGHT;
        layoutParams.rightMargin = Util.Div(20);
        addView(mToggleView, layoutParams);
    }

    @Override
    protected void refreshUI() {
        if (mData == null || mData.data_field == null) {
            Log.i(ControlPanelView.TAG, "refreshUI: data is null");
            return;
        }
        mValue.setText(mData.title);
        Object value = mStatus.get(mData.data_field);
        if (value == null) {
            Log.i(ControlPanelView.TAG, "refreshUI: value == null");
            return;
        }
        if (mData.values != null && !mData.values.containsKey(value.toString())) {
            value = mData.default_value;
        }
        String currentValue = value == null ? "" : value.toString();
        if (!TextUtils.isEmpty(mData.open_value) && mData.open_value.equals(currentValue)) {
            Log.i(ControlPanelView.TAG, "refreshUI: open");
            isOpen = true;
            mToggleView.setBackground(getContext().getResources().getDrawable(R.drawable.switch_on));
        } else if(!TextUtils.isEmpty(mData.close_value) && mData.close_value.equals(currentValue)) {
            Log.i(ControlPanelView.TAG, "refreshUI: close");
            isOpen = false;
            if(isFocused()){
                mToggleView.setBackground(getContext().getResources().getDrawable(R.drawable.switch_off_focus));
            }else{
                mToggleView.setBackground(getContext().getResources().getDrawable(R.drawable.switch_off));
            }
        }
    }

    @Override
    public boolean canFocusable() {
        return isEnable;
    }

    @Override
    public boolean onKeyLeft() {
        handleClick();
        return true;
    }

    @Override
    public boolean onKeyRight() {
        handleClick();
        return true;
    }

    @Override
    public void onClick() {
        super.onClick();
        if(isEnable){
            handleClick();
        }
    }

    private void handleClick() {
        if (mData == null || mData.values == null || mData.values.size() <= 0) {
            Log.i(ControlPanelView.TAG, "handleClick: data is null");
            return;
        }
        Object[] array = mData.values.keySet().toArray();
        if (array.length < 2) {
            Log.i(ControlPanelView.TAG, "handleClick: array is empty");
            return;
        }
        Object currentValue = mStatus.get(mData.data_field);
        if (currentValue == null) {
            Log.i(ControlPanelView.TAG, "handleClick: currentValue == null");
            return;
        }
        String newValue;
        if (currentValue.toString().equals(array[0])) {
            newValue = array[1].toString();
        } else {
            newValue = array[0].toString();
        }
        logi("handleClick: newValue " + newValue);
        if (mListener != null) {
            Map<String, String> map = new HashMap<>();
            map.put(mData.data_field, newValue);
            controlDevice(map);
        }
    }

    @Override
    public void onFocus(boolean hasFocus) {
        super.onFocus(hasFocus);
        if (hasFocus) {
            mValue.setTextColor(Color.parseColor("#000000"));
            mValue.getPaint().setFakeBoldText(true);
            if(!isOpen){
                mToggleView.setBackground(getContext().getResources().getDrawable(R.drawable.switch_off_focus));
            }
        } else {
            mValue.setTextColor(Color.parseColor("#aaFFFFFF"));
            mValue.getPaint().setFakeBoldText(false);
            if(!isOpen){
                mToggleView.setBackground(getContext().getResources().getDrawable(R.drawable.switch_off));
            }
        }
    }

    @Override
    public boolean canFocusableDefault() {
        return true;
    }

    @Override
    public void onRecycle() {

    }

    @Override
    public String getType() {
        return TYPE;
    }

}
