package com.skyworth.smarthome.devices.apconfig.presenter.helpers;

import android.content.Context;
import android.util.Log;

import com.coocaa.app.core.app.AppCoreApplication;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.voicehandle.SmartHomeAI;
import com.swaiot.aiotlib.common.entity.DiscoverWifiDevice;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

import static com.skyworth.smarthome.devices.apconfig.ApConfigService.TAG;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/9/20 14:34.
 */
public class SpeechHelper {

    private Context mContext = null;

    public SpeechHelper(Context context) {
        mContext = context;
    }

    public void speechDiscover(DiscoverWifiDevice mDeviceInfo) {
        Log.i(TAG, "speechDiscover");
        SmartHomeAI.playVoiceTTS(mContext, mContext.getString(R.string.apconfig_voice_discover, mDeviceInfo.getDeviceDetail().getProduct()));
    }

    public void speechConfiging() {
        Log.i(TAG, "speechConfiging");
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                SmartHomeAI.playVoiceTTS(mContext, mContext.getString(R.string.apconfig_voice_config));
                return Unit.INSTANCE;
            }
        });
    }

    public void speechConfigSuccess(String name) {
        Log.i(TAG, "speechConfigSuccess");
        SmartHomeAI.playVoiceTTS(mContext, mContext.getString(R.string.apconfig_voice_success, name));
    }

    public void speechConfigFailed(String name) {
        Log.i(TAG, "speechConfigFailed");
        SmartHomeAI.playVoiceTTS(mContext, mContext.getString(R.string.apconfig_voice_failed, name));
    }
}
