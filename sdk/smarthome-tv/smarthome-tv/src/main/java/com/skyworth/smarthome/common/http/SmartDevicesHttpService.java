package com.skyworth.smarthome.common.http;


import android.util.Log;

import com.alibaba.fastjson.JSONObject;
import com.coocaa.app.core.http.HttpServiceManager;
import com.skyworth.smarthome.common.bean.AlarmLogBean;
import com.skyworth.smarthome.common.bean.DeviceControlData;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.bean.DevicePosBean;
import com.skyworth.smarthome.common.bean.GatewayDeviceBean;
import com.skyworth.smarthome.common.bean.IRAddDeviceData;
import com.skyworth.smarthome.common.bean.IRLearnKeyBean;
import com.skyworth.smarthome.common.bean.IRMatchKeyData;
import com.skyworth.smarthome.common.bean.JDQRBean;
import com.skyworth.smarthome.common.bean.SceneListBean;
import com.skyworth.smarthome.common.bean.SceneListWithConditionDevice;
import com.skyworth.smarthome.common.bean.ThridAccountHttpBean;
import com.skyworth.smarthome.infrared.electriclist.model.DeviceBrandListData;
import com.skyworth.smarthome.infrared.electriclist.model.DeviceTypeListData;
import com.smarthome.common.model.SmartBaseData;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

/**
 * Created by Eric on 2018/4/18.
 */

public class SmartDevicesHttpService extends HttpServiceManager<SmartDevicesHttpMethod> {

    public static final SmartDevicesHttpService SERVICE = new SmartDevicesHttpService(
            SmartHomeHttpConfig.DEVICE_SERVER);

    private static final int TIMEOUT = 5; // 5 seconds

    private SmartDevicesHttpService() {
        this(SmartHomeHttpConfig.DEVICE_SERVER);
    }

    private SmartDevicesHttpService(String server) {
        super(SmartHomeHttpConfig.getServer(server), SmartHomeHttpConfig.SMARTHOME_HEADER_LOADER,
                TIMEOUT, TIMEOUT, TIMEOUT);
    }

    @Override
    protected Class<SmartDevicesHttpMethod> getServiceClazz() {
        return SmartDevicesHttpMethod.class;
    }

    /**
     * 设备控制
     *
     * @param body
     * @return
     */
    public Call<SmartBaseData<DeviceControlData>> deviceControl(Map<String, Object> body) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        return getHttpsService().deviceControl(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                SmartHomeHttpConfig.getSign(map),
                body);
    }

    /**
     * 场景控制
     *
     * @param cmd
     * @param scene_id
     * @return
     */
    public Call<SmartBaseData> sceneControl(String cmd, String scene_id) {
        Map<String, Object> body = new HashMap<>();
        body.put("cmd", cmd);
        body.put("scene_id", scene_id);
        body.put("is_virtual", "0");
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        return getHttpsService().sceneControl(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                SmartHomeHttpConfig.getSign(map),
                body);
    }

    /**
     * 场景控制
     *
     * @param scene_id
     * @return
     */
    public Call<SmartBaseData> setSceneAutoSwitch(String scene_id, boolean isOpen) {
        Map<String, Object> body = new HashMap<>();
        body.put("id", scene_id);
        body.put("is_enabled", isOpen ? "1" : "0");
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        return getHttpsService().setSceneAutoSwitch(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                SmartHomeHttpConfig.getSign(map),
                body);
    }


    /**
     * 获取第三方账号列表
     *
     * @return
     */
    public Call<SmartBaseData<List<ThridAccountHttpBean>>> getThirdAccountList(String accept_account_types) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        map.put("accept_account_types", accept_account_types);
        return getHttpsService().getThirdAccountList(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("accept_account_types"),
                SmartHomeHttpConfig.getSign(map));
    }

    /**
     * 解绑第三方账号
     *
     * @param account_type
     * @return
     */
    public Call<SmartBaseData> unbindThirdAccount(String account_type) {
        Map<String, Object> body = new HashMap<>();
        body.put("account_type", account_type);
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        return getHttpsService().unbindThirdAccount(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                SmartHomeHttpConfig.getSign(map),
                body);
    }


    public Call<SmartBaseData<String>> getDeviceDetail(int product_brand_id, int product_type_id,
                                                       String product_model, String device_id) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        map.put("product_brand_id", String.valueOf(product_brand_id));
        map.put("product_type_id", String.valueOf(product_type_id));
        map.put("product_model", product_model);
        map.put("device_id", device_id);
        return getHttpsService().getDeviceDetail(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("product_brand_id"),
                map.get("product_type_id"),
                map.get("product_model"),
                map.get("device_id"),
                SmartHomeHttpConfig.getSign(map));
    }

    public Call<SmartBaseData> modefyDevice(String device_id, Map<String, Object> body) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        map.put("device_id", device_id);
        return getHttpsService().modefyDevice(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("device_id"),
                SmartHomeHttpConfig.getSign(map),
                body);
    }

    public Call<SmartBaseData> setDefaultTV(String device_id, String device_name) {
        Map<String, Object> body = new HashMap<>();
        body.put("device_id", device_id);
        body.put("device_name", device_name);
        body.put("is_default", 1);
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        return getHttpsService().setDefaultTV(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                SmartHomeHttpConfig.getSign(map),
                body);
    }

    public Call<SmartBaseData> bindDevice(Map<String, Object> body) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        return getHttpsService().bindDevice(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                SmartHomeHttpConfig.getSign(map),
                body);
    }

    public Call<SmartBaseData> forceBindDevice(Map<String, Object> body) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        return getHttpsService().forceBindDevice(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                SmartHomeHttpConfig.getSign(map),
                body);
    }

    public Call<SmartBaseData> unBindDevice(String device_id) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        map.put("device_id", device_id);
        return getHttpsService().unBindDevice(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("device_id"),
                SmartHomeHttpConfig.getSign(map));
    }

    public Call<SmartBaseData> forceUnBindDevice(String device_id) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        Map<String, String> bodyParam = new HashMap<>();
        bodyParam.put("device_id", device_id);
        return getHttpsService().forceUnBindDevice(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                SmartHomeHttpConfig.getSign(map), bodyParam);
    }


    public Call<SmartBaseData<List<Map<String, String>>>> getBindStatus(List<Map<String, Object>> body) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        return getHttpsService().getBindStatus(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                SmartHomeHttpConfig.getSign(map),
                body);
    }

    public Call<SmartBaseData> updateDeviceInfo(Map<String, Object> body) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        return getHttpsService().updateDeviceInfo(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                SmartHomeHttpConfig.getSign(map),
                body);
    }

    public Call<SmartBaseData<List<DevicePosBean>>> getDeviceLocations() {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        return getHttpsService().getDeviceLocations(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                SmartHomeHttpConfig.getSign(map));
    }

    public Call<SmartBaseData> setDeviceLocation(String device_id, String location) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        map.put("device_id", device_id);
        map.put("location", location);
        return getHttpsService().setDeviceLocation(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("device_id"),
                map.get("location"),
                SmartHomeHttpConfig.getSign(map));
    }

    public Call<SmartBaseData> bindMideaDevice(Map<String, Object> body) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        return getHttpsService().bindMideaDevice(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                SmartHomeHttpConfig.getSign(map),
                body);
    }

    public Call<SmartBaseData> getMideaToken() {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        return getHttpsService().getMideaToken(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                SmartHomeHttpConfig.getSign(map));
    }


    /**
     * -------------------红外电视---start------------------
     **/

    public Call<SmartBaseData<List<IRMatchKeyData>>> irMatchKey(String type_id, String brand, String include_code_tables, String exclude_keys) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        map.put("type_id", type_id);
        map.put("brand", brand);
        map.put("include_code_tables", include_code_tables);
        map.put("exclude_keys", exclude_keys);
        return getHttpsService().irMatchKey(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("type_id"),
                map.get("brand"),
                map.get("include_code_tables"),
                map.get("exclude_keys"),
                SmartHomeHttpConfig.getSign(map));
    }

    public Call<SmartBaseData> irSendCode(Map<String, Object> body) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        return getHttpsService().irSendCode(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                SmartHomeHttpConfig.getSign(map),
                body);
    }

    public Call<SmartBaseData<IRAddDeviceData>> irDeviceOpt(int opt, Map<String, Object> body) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        map.put("option", String.valueOf(opt));
        return getHttpsService().irDeviceOpt(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("option"),
                SmartHomeHttpConfig.getSign(map),
                body);
    }

    public Call<SmartBaseData<List<DeviceTypeListData>>> getDeviceTypeList() {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        return getHttpsService().getDeviceTypeList(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                SmartHomeHttpConfig.getSign(map));
    }

    public Call<SmartBaseData<List<DeviceBrandListData>>> getDeviceBrandList(String type_id) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        map.put("type_id", type_id);
        return getHttpsService().getDeviceBrandList(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("type_id"),
                SmartHomeHttpConfig.getSign(map));
    }

    /**
     * 获取红外设备子列表
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Call<SmartBaseData<List<DeviceTypeListData>>> getIRDeviceList(String device_id, int page, int pageSize) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        map.put("page_index", String.valueOf(page));
        map.put("page_size", String.valueOf(pageSize));
        map.put("parent_id", device_id);
        return getHttpsService().getIRDeviceList(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("parent_id"),
                map.get("page_index"),
                map.get("page_size"),
                SmartHomeHttpConfig.getSign(map));
    }

    /**
     * 获取京东授权二维码
     *
     * @param device_id:发起获取二维码请求的设备id
     * @return
     */
    public Call<SmartBaseData<JDQRBean>> getJDQR(String device_id) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        map.put("device_id", device_id);
        return getHttpsService().getJDQR(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("device_id"),
                SmartHomeHttpConfig.getSign(map));
    }

    /**
     * 取消京东二维码授权
     *
     * @param qrcode:获取京东授权二维码接口返回的qrcode字符值
     * @return
     */
    public Call<SmartBaseData> cancelGetJDQR(String qrcode) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        Map<String, String> body = new HashMap<>();
        body.put("qrcode", qrcode);
        return getHttpsService().cancelGetJDQR(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                SmartHomeHttpConfig.getSign(map),
                body);
    }

    /**
     * 获取标准按键表
     *
     * @param type_id   红外设备品类id
     * @param is_common 是否常用按键，1表示常用 0:表示所有
     * @return
     */
    public Call<SmartBaseData<List<IRLearnKeyBean>>> getIRKeyList(String type_id, String is_common) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        map.put("type_id", type_id);
        map.put("is_common", is_common);
        return getHttpsService().getIRKeyList(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("type_id"),
                map.get("is_common"),
                SmartHomeHttpConfig.getSign(map));
    }

    /**
     * 红外子设备按键增删改
     *
     * @param option       1:新增 2:修改 3:删除
     * @param id           修改和删除时必选
     * @param name         名称
     * @param serial       序号
     * @param code         自定义时必选
     * @param is_custom    0:非自定义 1:自定义
     * @param key_table_id 标准按键id
     * @param ir_device_id 红外子设备id，新增时必选
     * @return
     */
    public Call<SmartBaseData> userKeyOpt(String option, int id, String name, int serial, String code, int is_custom, int key_table_id, String ir_device_id) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        map.put("option", option);
        Map<String, Object> body = new HashMap<>();
        body.put("id", id);
        body.put("name", name);
        body.put("serial", serial);
        body.put("code", code);
        body.put("is_custom", is_custom);
        body.put("key_table_id", key_table_id);
        body.put("ir_device_id", ir_device_id);
        return getHttpsService().userKeyOpt(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("option"),
                SmartHomeHttpConfig.getSign(map),
                body);
    }


    public Call<SmartBaseData<String>> getAiotHomeStatus(String family_id) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        map.put("family_id", family_id);
        return getHttpsService().getSmartHomeStatus(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("family_id"),
                SmartHomeHttpConfig.getSign(map));
    }

    public Call<SmartBaseData<List<GatewayDeviceBean>>> getGatewayDevice(String grandpa_gatewayid, String parent_did) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        map.put("grandpa_gatewayid", grandpa_gatewayid);
        map.put("parent_did", parent_did);
        return getHttpsService().getGatewayDevice(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("grandpa_gatewayid"),
                map.get("parent_did"),
                SmartHomeHttpConfig.getSign(map));
    }

    /**
     * 操作网关设备
     *
     * @param deviceInfo
     * @param option     1表示添加，2表示删除，3表示修改
     * @return
     */
    public Call<SmartBaseData<String>> operateGatewayDevice(DeviceInfo deviceInfo, String option) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        map.put("option", option);
        Map<String, Object> body = new HashMap<>();
        body.put("device_id", deviceInfo.device_id);
        body.put("gateway_id", deviceInfo.gateway_id);
        body.put("did",deviceInfo.did);
        body.put("access", deviceInfo.acess_type);
        body.put("dname", deviceInfo.device_name);
        body.put("brand_id", Integer.parseInt(deviceInfo.product_brand_id));
        body.put("type_id", Integer.parseInt(deviceInfo.product_type_id));
        body.put("model", deviceInfo.product_model);
        return getHttpsService().operateGatewayDevice(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("option"),
                SmartHomeHttpConfig.getSign(map), body);
    }

    public Call<SmartBaseData<List<SceneListWithConditionDevice>>> getSceneListWithConditionDevice(String condition_device_id) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        map.put("condition_device_id", condition_device_id);
        return getHttpsService().getSceneListWithConditionDevice(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("condition_device_id"),
                SmartHomeHttpConfig.getSign(map));
    }


    /**
     * 更新设备状态
     *
     * @param deviceId
     * @param statusMap
     * @return
     */
    public Call<SmartBaseData> updateDeviceStatus(String deviceId, Map<String, Object> statusMap) {
        Map<String, Object> body = new HashMap<>();
        body.put("message_time", String.valueOf(System.currentTimeMillis()));
        JSONObject state = new JSONObject();
        JSONObject reported = new JSONObject();
        reported.put("reported", statusMap);
        state.put("state", reported);
        body.put("payload", state);
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        map.put("device_id", deviceId);
        return getHttpsService().updateDeviceStatus(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("device_id"),
                SmartHomeHttpConfig.getSign(map),
                body);
    }

    /**
     * 获取传感器感应记录
     *
     * @param gateway_id 网关id
     * @param did        子设备id
     * @param page       分页大小
     * @param pageSize   分页号
     * @return
     */
    public Call<SmartBaseData<List<AlarmLogBean>>> getSensorAlarmLogsList(String gateway_id,
                                                                          String did, int page,
                                                                          int pageSize) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        map.put("gateway_id", gateway_id);
        map.put("did", did);
        map.put("page_index", String.valueOf(page));
        map.put("page_size", String.valueOf(pageSize));
        return getHttpsService().getSensorAlarmLogsList(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("gateway_id"),
                map.get("did"),
                map.get("page_index"),
                map.get("page_size"),
                SmartHomeHttpConfig.getSign(map));
    }

    public Call<SmartBaseData<List<SceneListBean>>> getSceneList(String family_id) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        Log.d("qwwqwew", "getSceneList: "+map);
        map.put("family_id", family_id);
        return getHttpsService().getSceneList(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("family_id"),
                SmartHomeHttpConfig.getSign(map));
    }
}
