package com.skyworth.smarthome.common.ui;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.coocaa.app.core.utils.FuncKt;
import com.skyworth.smarthome.R;
import com.skyworth.util.Util;
import com.smarthome.common.utils.XThemeUtils;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * Description: 空状态View<br>
 * Created by wzh on 2019/3/11 10:21.
 */
public class EmptyView extends LinearLayout {

    private TextView mTips;
    private TextView mRefreshBtn;

    public EmptyView(Context context) {
        super(context);
        setOrientation(VERTICAL);
        setGravity(Gravity.CENTER_HORIZONTAL);
        ImageView icon = new ImageView(context);
        icon.setBackgroundResource(R.drawable.empty_icon);
        LayoutParams params = new LayoutParams(Util.Div(400), Util.Div(200));
        params.topMargin = Util.Div(100);
        addView(icon, params);

        mTips = new TextView(context);
        mTips.setTextSize(Util.Dpi(24));
        mTips.getPaint().setFakeBoldText(true);
        mTips.setTextColor(Color.parseColor("#CCCCCC"));
        mTips.setGravity(Gravity.CENTER);
        params = new LayoutParams(Util.Div(510), ViewGroup.LayoutParams.WRAP_CONTENT);
        params.topMargin = Util.Div(20);
        addView(mTips, params);

        mRefreshBtn = new TextView(context);
        mRefreshBtn.setFocusable(true);
        mRefreshBtn.setText(context.getString(R.string.try_refresh));
        mRefreshBtn.setTextColor(XThemeUtils.c_1a());
        mRefreshBtn.setTextSize(Util.Dpi(32));
        mRefreshBtn.getPaint().setFakeBoldText(true);
        mRefreshBtn.setGravity(Gravity.CENTER);
        mRefreshBtn.setBackgroundResource(R.drawable.cardtype_item_bg);
        params = new LayoutParams(Util.Div(350), Util.Div(90));
        params.topMargin = Util.Div(45);
        addView(mRefreshBtn, params);
    }

    public void setText(final String tips) {
        FuncKt.runOnUiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mTips.setText(tips);
                return Unit.INSTANCE;
            }
        });
    }

    public void setBtnText(final String content) {
        FuncKt.runOnUiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mRefreshBtn.setText(content);
                return Unit.INSTANCE;
            }
        });
    }

    public void setTextSize(final int size) {
        FuncKt.runOnUiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mTips.setTextSize(size);
                return Unit.INSTANCE;
            }
        });
    }

    public void getFocus() {
        mRefreshBtn.requestFocus();
    }

    public void setOnClick(OnClickListener onClick) {
        if (onClick != null) mRefreshBtn.setOnClickListener(onClick);
    }

    public void setOnKeyListener(OnKeyListener onKeyListener) {
        if (onKeyListener != null) mRefreshBtn.setOnKeyListener(onKeyListener);
    }
}
