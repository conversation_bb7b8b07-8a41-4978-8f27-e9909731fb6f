package com.skyworth.smarthome.home.smartdevice.controlpanel.thirdapp.appmanager.http;

import com.skyworth.smarthome.home.smartdevice.controlpanel.thirdapp.appmanager.AppInfo;

import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Query;

/**
 * Created by <PERSON> on 2018/5/28.
 */

public interface ThirdAppHttpMethod {
    @GET("download")
    Call<AppInfo.DownloadInfo> getdDownloadInfo(@Query("pkg") String pkg);

//    @GET("appDetail")
//    Call<AppDetailInfoData> getAppDetail(@Query("pkg") String pkg);

    @GET("appDetail")
    Call<AppInfo.UpgradeInfo> getAppUpdateBeans(@Query("pkg") String pkgs);
}
