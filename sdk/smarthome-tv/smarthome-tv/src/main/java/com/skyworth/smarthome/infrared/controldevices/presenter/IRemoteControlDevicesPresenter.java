package com.skyworth.smarthome.infrared.controldevices.presenter;

import com.skyworth.smarthome.infrared.controldevices.view.IRemoteControlDevicesView;
import com.skyworth.smarthome.infrared.controldevices.view.IShowRemoteControl;
import com.skyworth.smarthome.infrared.electriclist.model.DeviceTypeListData;

import java.util.List;
import java.util.Map;

/**
 * Describe:
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/4/28
 */
public interface IRemoteControlDevicesPresenter {

    void setDeviceId(String deviceId);

    void setDeviceName(String deviceName);

    String getDeviceName();

    void setDeviceTypeId(String id);

    String getDeviceTypeId();

    void loadData();

    List<DeviceTypeListData> assembleData(List<DeviceTypeListData> data, boolean isShowDelete);

    void deleletIrDevices(DeviceTypeListData data);

    void setView(IRemoteControlDevicesView view);

    void setShowAddInfraredDevice(IShowRemoteControl mShowAddInfraredDevice);

    void showAddInfraredDevice();

    void setParams(Map<String, Object> params);

}
