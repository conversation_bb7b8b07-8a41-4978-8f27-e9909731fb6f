package com.skyworth.smarthome.common.ui;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.widget.FrameLayout;

import com.skyworth.ui.api.widget.SimpleFocusDrawable;
import com.skyworth.util.Util;

/**
 * @ProjectName: NewTV_SmartHome
 * @Package: com.skyworth.smarthome_tv.common.ui
 * @ClassName: CommonFocusBox
 * @Description: java类作用描述
 * @Author: wangyuehui
 * @CreateDate: 2020/6/18 17:20
 * @UpdateUser: 更新者
 * @UpdateDate: 2020/6/18 17:20
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class CommonFocusBox extends FrameLayout implements View.OnFocusChangeListener, View.OnTouchListener {
    private int radius;
    private OnFocusChangeListener OnFocusChangeListener;
    private OnTouchListener onTouchListener;
    private SimpleFocusDrawable mFocusBg;

    public CommonFocusBox(Context context) {
        super(context);
        init();
    }

    public CommonFocusBox(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public CommonFocusBox(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        radius = Util.Div(10);
        setFocusable(true);
        setFocusableInTouchMode(true);
        setClickable(true);
//        setPadding(Util.Div(8),Util.Div(8),Util.Div(8),Util.Div(8));

        setOnFocusChangeListener(this);
        setOnTouchListener(this);
        mFocusBg = new SimpleFocusDrawable(getContext()).setRadius(radius);
        setBackground(mFocusBg);
    }

    public void setCornerRadius(int radius) {
        this.radius = radius;
        mFocusBg.setRadius(radius);
    }

    public void setOnFocusChangeListener(CommonFocusBox.OnFocusChangeListener onFocusChangeListener) {
        OnFocusChangeListener = onFocusChangeListener;
    }

    public void setOnTouchListener(OnTouchListener onTouchListener) {
        this.onTouchListener = onTouchListener;
    }


    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        mFocusBg.setFocus(hasFocus);
        if (OnFocusChangeListener != null) {
            OnFocusChangeListener.onFocusChange(v, hasFocus);
        }
    }

    @Override
    public boolean onTouch(View v, MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            mFocusBg.setFocus(true);
        } else if (event.getAction() == MotionEvent.ACTION_UP || event.getAction() == MotionEvent.ACTION_CANCEL) {
            mFocusBg.setFocus(false);
        }
        if (onTouchListener != null) {
            onTouchListener.onTouch(v, event);
        }
        return false;
    }

    public interface OnFocusChangeListener {
        void onFocusChange(View v, boolean hasFocus);
    }

    public interface OnTouchListener {
        void onTouch(View v, MotionEvent event);
    }
}
