package com.skyworth.smarthome.infrared.learn.model;

import android.content.Context;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Log;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.coocaa.app.core.http.HttpServiceManager;
import com.skyworth.smarthome.infrared.learn.model.statuslistener.ILearnStatusReceiver;
import com.skyworth.smarthome.infrared.learn.model.statuslistener.IRReceiverFactory;
import com.skyworth.smarthome.common.bean.DeviceControlData;
import com.skyworth.smarthome.common.bean.IRAddDeviceData;
import com.skyworth.smarthome.common.bean.IRLearnKeyBean;
import com.skyworth.smarthome.common.http.SmartDevicesHttpService;
import com.smarthome.common.model.SmartBaseData;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;

import static com.skyworth.smarthome.infrared.learn.IRLearnDialog.TAG;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/7/2 14:44.
 */
public class IRLearnModelImpl implements IIRLearnModel {
    private IIRLearnNotify mListener = null;
    private List<IRLearnKeyBean> mKeyList = null;
    private static final String PARENT_ID = "parent_id";
    private static final String TYPE_ID = "type_id";
    private static final String NAME = "name";
    private static final String DEVICE_ID = "device_id";
    private static final String STATUS = "status";
    private static final String STD_S = "STD_S";

    private static final String JSON_CODE = "CODE";
    private static final String JSON_DID = "DID";
    private static final String JSON_KEY_NUM = "KEY_NUM";
    private static final String JSON_MSGID = "MSGID";
    private static final String JSON_RESULT = "RESULT";
    private static final String JSON_KEY_CODE = "KEY_CODE";

    private static final int LEARNING = 0;
    private static final int LEARN_SUCCESS = 1;
    private static final int LEARN_FAIL = 2;
    private static final int STOP_LEARN = 3;

    private long learnMsgID = 0L;
    private String mDeviceId = null;
    private ILearnStatusReceiver statusReceiver = null;

    public IRLearnModelImpl() {

    }

    @Override
    public void checkLearnResult(String STDRValue) {
        Log.i(TAG, "checkLearnResult: " + STDRValue);
        JSONObject STDRValueJson = JSON.parseObject(STDRValue);
        if (STDRValueJson == null) {
            Log.w(TAG, "checkLearnResult: STD_R empty");
            return;
        }

        long msgId = STDRValueJson.getLongValue(JSON_MSGID);
        if (msgId != learnMsgID) {
            Log.w(TAG, "checkLearnResult: msg id not compatible");
            return;
        }

        int code = (int) STDRValueJson.get(JSON_CODE);
        Log.i(TAG, "checkLearnResult: code: " + code);
        switch (code) {
            case LEARNING:
                mListener.onLearnStart();
                break;
            case LEARN_SUCCESS:
                JSONObject result = STDRValueJson.getJSONObject(JSON_RESULT);
                if (result == null) {
                    return;
                }
                JSONArray keyCodeJson = result.getJSONArray(JSON_KEY_CODE);
                if (keyCodeJson == null) {
                    return;
                }
                int[] keyCode = new int[keyCodeJson.size()];
                for (int i = 0; i < keyCodeJson.size(); i++) {
                    keyCode[i] = (int) keyCodeJson.get(i);
                }
                mListener.onLearnSuccess(keyCode);
                break;
            case LEARN_FAIL:
                mListener.onLearnFail();
                break;
            case STOP_LEARN:
                mListener.onLearnFail();
                break;
        }
    }

    @Override
    public void create(Context context) {

    }

    @Override
    public boolean loadRemoteLearnList(final String type_id, final String is_common) {
        Call<SmartBaseData<List<IRLearnKeyBean>>> call = SmartDevicesHttpService.SERVICE.getIRKeyList(type_id, is_common);
        SmartBaseData<List<IRLearnKeyBean>> data = HttpServiceManager.Companion.call(call);
        if (data != null && data.data != null) {
            mKeyList = data.data;
            return true;
        }
        return false;
    }

    @Override
    public String createIRChildDevice(String deviceId, int type_id, String name) {
        String id = null;
        Map<String, Object> body = new HashMap<>();
        body.put(PARENT_ID, deviceId);
        body.put(TYPE_ID, type_id);
        body.put(NAME, name);
        Call<SmartBaseData<IRAddDeviceData>> call = SmartDevicesHttpService.SERVICE.irDeviceOpt(1, body);
        SmartBaseData<IRAddDeviceData> data = HttpServiceManager.Companion.call(call);
        if (data != null && data.data != null) {
            id = data.data.id;
        }
        return id;
    }

    @Override
    public boolean saveLearntKey(String keyName, int keyId, String deviceId, String code) {
        Call<SmartBaseData> call = SmartDevicesHttpService.SERVICE.userKeyOpt("1", 0, keyName, 0, code, 1, keyId, deviceId);
        SmartBaseData data = HttpServiceManager.Companion.call(call);
        return data != null && "0".equals(data.code);
    }

    @Override
    public void regLearnNotify(IIRLearnNotify listener) {
        mListener = listener;
    }

    @Override
    public int getSizeOfLearnList() {
        if (mKeyList == null) {
            return 0;
        }
        return mKeyList.size();
    }

    @Override
    public IRLearnKeyBean getKeyNeedLearn(int index) {
        if (mKeyList == null) {
            return null;
        }
        return mKeyList.get(index);
    }

    @Override
    public void startListen(String irTypeId) {
        if (TextUtils.isEmpty(irTypeId)) {
            Log.e(TAG, "startListen: irTypeId is empty");
            return;
        }
        Log.i(TAG, "startListen: type: " + irTypeId);
        if (statusReceiver == null) {
            statusReceiver = IRReceiverFactory.create(irTypeId);
        }
        Map<String, Object> params = new HashMap<>();
        params.put(DEVICE_ID, mDeviceId);
        statusReceiver.startListen(this, params);
    }

    @Override
    public boolean startLearnStatus(final String deviceId, final String key_id) {
        generateLearnMsgId();
        Map<String, Object> body = new HashMap<>();
        body.put(DEVICE_ID, deviceId);
        Map<String, String> status = new HashMap<>();
        status.put(STD_S, generateLearnMsgJson(deviceId, key_id));
        body.put(STATUS, status);
        Call<SmartBaseData<DeviceControlData>> call = SmartDevicesHttpService.SERVICE.deviceControl(body);
        SmartBaseData<DeviceControlData> data = HttpServiceManager.Companion.call(call);
        return data != null && "0".equals(data.code);
    }

    private String generateLearnMsgJson(String deviceId, String keyNum) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(JSON_CODE, 0);
        jsonObject.put(JSON_DID, deviceId);
        jsonObject.put(JSON_KEY_NUM, keyNum);
        jsonObject.put(JSON_MSGID, learnMsgID);
        return jsonObject.toString();
    }

    private void generateLearnMsgId() {
        learnMsgID = SystemClock.uptimeMillis();
        Log.i(TAG, "generateLearnMsgId: " + learnMsgID);
    }

    @Override
    public void setDeviceId(String deviceId) {
        mDeviceId = deviceId;
    }

    @Override
    public void destroy() {
        learnMsgID = 0L;
        if (statusReceiver != null) {
            statusReceiver.stopListen();
        }
    }
}
