package com.skyworth.smarthome.common.dialog.view;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Typeface;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.skyworth.smarthome.R;
import com.skyworth.util.Util;

/**
 * 检测iot版本提示View
 */
public class CheckUpdataExceptionTipsView extends LinearLayout {

    private ImageView imageView;
    private TextView textView;

    public CheckUpdataExceptionTipsView(Context context) {
        super(context);
        init(context);
    }

    private void init(Context mContext){
        setOrientation(HORIZONTAL);
        setBackground(getResources().getDrawable(R.drawable.check_updata_tips_bg));
        setGravity(Gravity.CENTER);
        imageView = new ImageView(mContext);
        FrameLayout.LayoutParams imageLayout = new FrameLayout.LayoutParams(Util.Div(48), Util.Div(48));
        imageLayout.leftMargin = 108;
        imageLayout.topMargin = 31;
        imageLayout.bottomMargin = 31;
        addView(imageView,imageLayout);
        textView = new TextView(mContext);
        textView.setTextSize(Util.Div(26));
        textView.setTypeface(Typeface.DEFAULT_BOLD);
        textView.setTextColor(getResources().getColor(R.color.smartHomeBgColor));
        FrameLayout.LayoutParams textLayout = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,ViewGroup.LayoutParams.WRAP_CONTENT);
        textLayout.leftMargin = 18;
        textLayout.rightMargin = 108;
        addView(textView,textLayout);
    }

    public void setText(boolean showStatus){
        int stringID;
        int imageID;
        Resources resources = getResources();
        if (showStatus){
            //显示成功的样式
            stringID = R.string.check_iot_sucess;
            imageID = R.drawable.tv_updata_tips_normal;
        }else{
            //显示失败的样式
            stringID = R.string.check_iot_exception;
            imageID = R.drawable.tv_updata_tips_exception;
        }
        if (imageView != null && textView != null){
            imageView.setImageDrawable(resources.getDrawable(imageID));
            textView.setText(resources.getText(stringID));
        }
    }
}
