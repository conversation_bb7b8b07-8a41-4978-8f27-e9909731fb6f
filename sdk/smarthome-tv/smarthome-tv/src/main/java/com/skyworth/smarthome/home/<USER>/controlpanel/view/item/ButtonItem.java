package com.skyworth.smarthome.home.smartdevice.controlpanel.view.item;

import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.alibaba.fastjson.JSONObject;
import com.skyworth.smarthome.home.smartdevice.controlpanel.common.itemdata.ButtonControlData;
import com.skyworth.smarthome.home.smartdevice.controlpanel.view.base.BaseControlItem;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.bean.JObject;
import com.skyworth.smarthome.common.event.CloseDeviceControlDialogEvent;
import com.skyworth.smarthome.common.util.CCAttrOnclick;
import com.skyworth.util.Util;

import org.greenrobot.eventbus.EventBus;

public class ButtonItem extends BaseControlItem<ButtonControlData> {
    public static final String TYPE = "button";
    private TextView mTitle = null;

    public ButtonItem(Context context) {
        super(context);
        initText();
        initArrow();
    }

    private void initText() {
        mTitle = new TextView(getContext());
        mTitle.setTextSize(Util.Dpi(32));
        mTitle.setTextColor(Color.parseColor("#000000"));
        mTitle.setGravity(Gravity.CENTER);
        mTitle.getPaint().setFakeBoldText(true);
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.leftMargin = Util.Div(20);
        layoutParams.gravity = Gravity.CENTER_VERTICAL;
        addView(mTitle, layoutParams);
    }

    private void initArrow() {
        View arrow = new View(getContext());
        arrow.setBackgroundResource(R.drawable.arrow_right_focus);
        LayoutParams layoutParams = new LayoutParams(Util.Div(11), Util.Div(20));
        layoutParams.gravity = Gravity.RIGHT | Gravity.CENTER_VERTICAL;
        layoutParams.rightMargin = Util.Div(60);
        addView(arrow, layoutParams);
    }

    @Override
    protected void refreshUI() {
        mTitle.setText(mData.title);
    }

    @Override
    public void onClick() {
        super.onClick();
        if (mData == null || TextUtils.isEmpty(mData.click)) {
            logi("onClick: click is empty");
            return;
        }
        CCAttrOnclick onclick = JObject.parseJObject(mData.click, CCAttrOnclick.class);
        appendParams(onclick, "device_id", mData.deviceId);
        boolean result = CCAttrOnclick.start(getContext(), onclick);
        canCloseDialog(result);
        logi("onClick: " + result);
    }

    private void canCloseDialog(boolean canClose) {
        if (canClose) {
            EventBus.getDefault().post(new CloseDeviceControlDialogEvent());
        }
    }

    private void appendParams(CCAttrOnclick onclick, String key, Object value) {
        if (onclick == null || TextUtils.isEmpty(key)) {
            return;
        }
        JSONObject newParams = new JSONObject();
        newParams.put(key, value);
        String data = onclick.getData();
        if (TextUtils.isEmpty(data)) {
            data = "";
        }
        JSONObject currentParams = JSONObject.parseObject(data);
        if (currentParams == null) {
            currentParams = new JSONObject();
        }
        currentParams.putAll(newParams);
        onclick.setData(currentParams.toJSONString());
    }

    @Override
    public boolean canFocusableDefault() {
        return true;
    }

    @Override
    public void onRecycle() {

    }

    @Override
    public void onFocus(boolean hasFocus) {
        super.onFocus(hasFocus);
    }

    @Override
    public String getType() {
        return TYPE;
    }
}
