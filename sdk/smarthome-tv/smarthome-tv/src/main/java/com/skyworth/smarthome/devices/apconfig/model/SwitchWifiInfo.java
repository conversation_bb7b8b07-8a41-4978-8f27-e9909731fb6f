package com.skyworth.smarthome.devices.apconfig.model;

import android.net.wifi.ScanResult;

import java.io.Serializable;

/**
 * Description:2.4G Wi-Fi列表View数据结构 <br>
 * Created by wzh on 2019/1/8 18:23.
 */
public class SwitchWifiInfo implements Serializable {
    public final static int LOCK_STATUS_1 = 1;
    public final static int LOCK_STATUS_2 = 2;
    public final static int LOCK_STATUS_3 = 3;

    public String wifiName;
    public ScanResult raw;
    public int lockStatus;//1有锁，2有锁已连接过，3无锁
    public int wifiSignal;//WiFi信号值：1、2、3、4

    @Override
    public boolean equals(Object o) {
        if (o == null) {
            return false;
        }
        if (!(o instanceof SwitchWifiInfo)) {
            return false;
        }
        return wifiName != null && wifiName.equals(((SwitchWifiInfo) o).wifiName);
    }
}
