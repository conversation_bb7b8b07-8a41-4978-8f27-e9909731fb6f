package com.skyworth.smarthome.devices.apconfig.presenter.step.auto;

import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.util.NetworkUtils;
import com.skyworth.smarthome.devices.apconfig.presenter.step.BaseStep;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/9/17 11:23.
 */
public class AutoStepCheckEtherNet extends BaseStep {
    public static final String STEP_TAG = "auto_check_ethernet";

    @Override
    public void run() {
        super.run();
        if (NetworkUtils.isEthernetConnect(context)) {
            logi("ethernet connect!");
            presenter.recordNotBindAndExit(AppConstants.APCONFIG_FAIL_REASON_ETHERNET_CONNECT);
            return;
        } else {
            logi("ethernet not connect!");
            next();
        }
    }

    @Override
    public boolean input(String msg, Object... params) {
        return false;
    }

    @Override
    public String getTag() {
        return STEP_TAG;
    }

    @Override
    public void destroy() {
        super.destroy();
    }
}
