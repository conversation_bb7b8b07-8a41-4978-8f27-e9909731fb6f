package com.skyworth.smarthome.devices.apconfig.model;

import com.alibaba.fastjson.JSONObject;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.common.util.NetworkUtils;
import com.smarthome.common.dataer.DataHelpInfo;
import com.smarthome.common.dataer.LogSDK;
import com.smarthome.common.utils.EmptyUtils;
import com.swaiot.aiotlib.common.entity.DiscoverNetworkDevice;
import com.swaiot.aiotlib.common.entity.DiscoverWifiDevice;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: ApconfigReportDataModel
 * @Author: AwenZeng
 * @CreateDate: 2020/8/5 17:20
 * @Description:
 */
public class ApconfigReportDataModel {

    /**
     * 上报网络类型
     */
    public static void reportNetworkType(){
        try {
            String networkType = "";
            if(NetworkUtils.isEthernetConnect(SmartHomeTvLib.getContext())){
                networkType = "有线";
            }else{
                if(NetworkUtils.isCurrentWifi24G(SmartHomeTvLib.getContext())){
                    networkType = "2.4Gwifi";
                }else{
                    networkType = "5Gwifi";
                }
            }
            //数据统计
            Map<String, String> map = new HashMap<>();
            map.put("network_type", networkType);
            LogSDK.submit(LogSDK.EVENT_ID_NETWORK_TYPE,map);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 上报开始配网
     * @param discoverWifiDevice
     */
    public static void reportStartApconfig(DiscoverWifiDevice discoverWifiDevice){
        try {

            DataHelpInfo.getInstance().setDeviceApconfigTime(System.currentTimeMillis());
            //数据统计
            Map<String, String> map = new HashMap<>();
            map.put("device_id", discoverWifiDevice.getWifiInfo().SSID);
            map.put("device_brand",discoverWifiDevice.getDeviceDetail().getBrand());
            map.put("device_name",discoverWifiDevice.getDeviceDetail().getProduct());
            LogSDK.submit(LogSDK.EVENT_ID_NETWORK_START,map);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 上报配网结果
     * @param discoverWifiDevice
     * @param result
     * @param failReason
     */
    public static void reportApconfigResult(DiscoverWifiDevice discoverWifiDevice,String result,String failReason){
        try {
            //数据统计
            Map<String, String> map = new HashMap<>();
            map.put("device_id", discoverWifiDevice.getWifiInfo().SSID);
            map.put("device_brand",discoverWifiDevice.getDeviceDetail().getBrand());
            map.put("device_name",discoverWifiDevice.getDeviceDetail().getProduct());
            map.put("fail_reason",failReason);
            map.put("network_result",result);
            LogSDK.submit(LogSDK.EVENT_ID_NETWORK_END,map);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 上报配网时间
     * @param discoverWifiDevice
     */
    public static void reportApconfigTime(DiscoverWifiDevice discoverWifiDevice,String result){
        try {
            //数据统计
            Map<String, String> map = new HashMap<>();
            map.put("device_id", discoverWifiDevice.getWifiInfo().SSID);
            map.put("device_brand",discoverWifiDevice.getDeviceDetail().getBrand());
            map.put("device_name",discoverWifiDevice.getDeviceDetail().getProduct());
            map.put("network_result",result);
            map.put("apconfig_time",String.valueOf(System.currentTimeMillis() - DataHelpInfo.getInstance().getDeviceApconfigTime()));
            LogSDK.submit(LogSDK.EVENT_ID_NETWORK_TIME,map);
            DataHelpInfo.getInstance().setDeviceApconfigTime(0);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 上报设备绑定结果
     * @param reply
     * @param result
     */
    public static void reportDeviceBindResult(JSONObject reply, String result,String failReason){
        try {
            if(EmptyUtils.isEmpty(reply))
                return;
            DiscoverNetworkDevice discoverNetworkDevice = new DiscoverNetworkDevice();
            discoverNetworkDevice.device_id = reply.getString("device_id");
            discoverNetworkDevice.device_name = reply.getString("device_name");
            discoverNetworkDevice.brand_cn = reply.getString("brand_cn");
            discoverNetworkDevice.product_type_id = reply.getString("product_type_id");
            //数据统计
            Map<String, String> map = new HashMap<>();
            map.put("device_id", discoverNetworkDevice.device_id);
            map.put("device_brand",discoverNetworkDevice.brand_cn);
            map.put("device_name",discoverNetworkDevice.device_name);
            map.put("bind_result",result);
            if(EmptyUtils.isNotEmpty(failReason)){
                map.put("fail_reason",failReason);
            }
            LogSDK.submit(LogSDK.EVENT_ID_DEVICE_BIND,map);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
