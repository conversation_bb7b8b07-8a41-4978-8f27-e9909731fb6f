package com.skyworth.smarthome.home.base;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.skyworth.smarthome.R;
import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.util.Util;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/20
 */
public abstract class BaseAddItemView extends BaseSmartItemView<String> {

    private ImageView icon;
    protected TextView txt;
    private CCFocusDrawable mFocusBg;
    protected int mWidth = Util.Div(410);
    protected int mHeight = Util.Div(246);

    public BaseAddItemView(Context context) {
        super(context);
        init();
        setLayoutParams(new LayoutParams(mWidth, mHeight));
        mFocusBg = new CCFocusDrawable(context).setRadius(Util.Div(8)).setBorderVisible(false).setSolidColor(getContext().getResources().getColor(R.color.white_10));
        setBackground(mFocusBg);

        icon = new ImageView(context);
        icon.setBackgroundResource(R.drawable.ic_add_white);
        LayoutParams params = new LayoutParams(Util.Div(36), Util.Div(36));
        params.gravity = Gravity.CENTER_VERTICAL;
        params.leftMargin = Util.Div(118);
        addView(icon, params);

        txt = new TextView(context);
        txt.setTextColor(Color.WHITE);
        txt.setTextSize(Util.Dpi(32));
        txt.getPaint().setFakeBoldText(true);
        params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.gravity = Gravity.CENTER_VERTICAL;
        params.leftMargin = Util.Div(164);
        addView(txt, params);
    }

    public void init() {

    }

    @Override
    public void onFocusChange(View v, boolean b) {
        super.onFocusChange(v, b);
        if (b) {
            icon.setBackgroundResource(R.drawable.ic_add_black);
            txt.setTextColor(Color.BLACK);
        } else {
            icon.setBackgroundResource(R.drawable.ic_add_white);
            txt.setTextColor(Color.WHITE);
        }
        mFocusBg.setBorderVisible(b).setSolidColor(getResources().getColor(b ? R.color.white : R.color.white_10));
        Util.focusAnimate(v, b);
    }
}
