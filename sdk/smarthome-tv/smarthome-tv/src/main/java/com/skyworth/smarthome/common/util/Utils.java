package com.skyworth.smarthome.common.util;

import android.app.ActivityManager;
import android.content.ComponentName;
import android.content.Context;
import android.util.Log;

import com.coocaa.app.core.utils.FuncKt;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.smarthome.common.utils.Android;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;


public class Utils {
    public static String FONT_PATH = "/system/fonts/SourceHanSerifCN-Heavy.ttf";
    public static String SCREEN_SAVER_COMPONENT_NAME = "com.tianci.ad.ScreensaverActivity";
    public static String POWER_OFF_CLASS_NAME = "com.tianci.ad.PowerOffActivity";

    public static boolean checkNetAndShowNetSetting(Context mContext) {
        if (!FuncKt.isNetConnected(mContext)) {
            Utils.showConnectNetworkDialogWithConfirmUI();
            return false;
        }
        return true;
    }

    public static void showConnectNetworkDialogWithConfirmUI() {
    }

    public static void startNetSetting() {
    }

    public static synchronized void speech(Context context, String content) {
//          TTSManager.sendTTS(context, content);
//        Intent intent = new Intent("com.skyworth.srtnj.lafite.dueros.callback.action");
//        Bundle bundle = new Bundle();
//        bundle.putInt("cmd", 2);
//        bundle.putString("content", content);
//        bundle.putString("utteranceId", "system");
//        intent.putExtras(bundle);
//        context.sendBroadcast(intent);
    }

    public static int getArrayIndex(Object[] array, Object value) {
        if (array == null || array.length <= 0) {
            return -1;
        }
        for (int i = 0; i < array.length; i++) {
            if (array[i].equals(value)) {
                return i;
            }
        }
        return -1;
    }

    /**
     * APP进程是否存在
     *
     * @param packageName
     * @return
     */
    public static boolean isAppProcessExist(String packageName) {
        ActivityManager am = ((ActivityManager) SmartHomeTvLib.getContext().getSystemService(Context.ACTIVITY_SERVICE));
        List<ActivityManager.RunningAppProcessInfo> processInfos = am.getRunningAppProcesses();
        String mainProcessName = packageName;
        for (ActivityManager.RunningAppProcessInfo info : processInfos) {
            if (mainProcessName.equals(info.processName)) {
                return true;
            }
        }
        return false;
    }


    /**
     * 是否AI待机
     */
    public static boolean isAistandbymode() {
        return  SystemProperty.isAistandbymode();
    }

    /**
     * 是否是数字
     *
     * @param str
     * @return
     */
    public static boolean isNumeric(String str) {
        String bigStr;
        try {
            bigStr = new BigDecimal(str).toString();
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    public static byte[] intToByte(int[] i) {
        byte[] bytes = new byte[i.length];
        for (int a = 0; a < i.length; a++) {
            bytes[a] = (byte) (i[a] & 0xff);
        }
        return bytes;
    }

    public static short[] byteToShort(byte[] i) {
        short[] shorts = new short[i.length];
        for (int a = 0; a < i.length; a++) {
            shorts[a] = (short) i[a];
        }
        return shorts;
    }

    public static String listToStringDivideByComma(Collection list) {
        if (list == null || list.size() <= 0) {
            return "";
        }
        StringBuilder builder = new StringBuilder();
        int index = 0;
        for (Object o : list) {
            builder.append(o.toString());
            if (index < list.size() - 1) {
                builder.append(",");
            }
            index++;
        }
        return builder.toString();
    }


    public static String getAcceptAccountTypes() {
        if (AppConstants.SUPPORT_ACCOUNT_TYPE.values().length <= 0) {
            return "";
        }
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < AppConstants.SUPPORT_ACCOUNT_TYPE.values().length; i++) {
            builder.append(AppConstants.SUPPORT_ACCOUNT_TYPE.values()[i]);
            if (i < AppConstants.SUPPORT_ACCOUNT_TYPE.values().length - 1) {
                builder.append(",");
            }
        }
        return builder.toString();
    }

    public static String listToStr(List<Integer> list) {
        StringBuffer stringBuffer = new StringBuffer();
        for (int i = 0; i < list.size(); i++) {
            stringBuffer.append(list.get(i));
            if (i < list.size() - 1) {
                stringBuffer.append(",");
            }
        }
        return stringBuffer.toString();
    }

    public static String printMap(Map map) {
        StringBuilder builder = new StringBuilder();
        for (Object item : map.entrySet()) {
            if (item instanceof Map.Entry) {
                builder.append(((Map.Entry) item).getKey()).append(" : ").append(((Map.Entry) item).getValue()).append("\n");
            }
        }
        return builder.toString();
    }

    /**
     * 酷开系统屏保是否运行
     *
     * @return
     */
    public static boolean isScreenSaverShow() {
        boolean result = isActivityShow(SCREEN_SAVER_COMPONENT_NAME);
        Log.i("isActivityShow", "isScreenSaverShow: " + result);
        return result;
    }

    /**
     * 关机画面是否运行
     *
     * @return
     */
    public static boolean isPowerOffShow() {
        boolean result = isActivityShow(POWER_OFF_CLASS_NAME);
        Log.i("isActivityShow", "isPowerOffShow: " + result);
        return result;
    }

    public static boolean isActivityShow(String componentNameStr) {
        ComponentName componentName = Android.getRunningTopActivity(SmartHomeTvLib.getContext());
        Log.i("top", componentName != null ? componentName.toString() : "null");
        return componentName != null && componentName.getClassName().equals(componentNameStr);
    }
}
