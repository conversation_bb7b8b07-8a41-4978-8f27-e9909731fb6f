package com.skyworth.smarthome.service.model;

import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.swaiot.aiotlib.common.entity.DiscoverNetworkDevice;
import com.swaiot.aiotlib.common.entity.DiscoverWifiDevice;
import com.swaiot.aiotlib.common.entity.FamilyBean;
import com.swaiot.aiotlib.common.entity.SceneBean;

import java.util.List;


/**
 * 智能设备数据Model
 */
public interface ISmartDeviceDataModel {

    SmartDeviceDataModel INSTANCE = new SmartDeviceDataModel();
    /**
     * 获取设备列表数据，超过有效期，拉取新数据
     * @return
     */
    List<DeviceInfo> getSmartDeviceList();

    /**
     * 获取缓存中的数据，不加载新数据
     * @return
     */
    List<DeviceInfo> getCacheSmartDeviceList();


    /**
     * 通过deviceID获取单个设备的信息
     * @param deviceID
     * @return
     */
    DeviceInfo getSmartDeviceInfo(String deviceID);



    /**
     * 获取家庭列表
     * @return
     */
    List<FamilyBean> getFamilyList();

    /**
     * 获取场景列表信息
     * @return
     */
    List<SceneBean> getSceneList();


    /**
     * 通过sceneID获取单个场景信息
     * @param sceneID
     * @return
     */
    SceneBean getSceneInfo(String sceneID);

    /**
     * 获取AIOT提供对外接口核心设备状态信息
     * @param familyId
     * @return
     */
    String getAiotHomeStatus(String familyId);


    /**
     * 更新设备控制状态数据
     * @param msg
     */
    void updateControlStatusData(String msg);

    /**
     * 更新设备的在线/离线状态
     * @param msg
     */
    void updateOnlineStatusData(String msg);

    /**
     * 添加附近的设备到列表中
     */
    void addNearbyDeviceToDeviceList();

    /**
     * 添加发现的附近的智能设备
     * @param discoverNetworkDevice
     */
    void addDiscoverDevice(DiscoverNetworkDevice discoverNetworkDevice);

    /**
     * 删除发现的附近的智能设备
     * @param discoverNetworkDevice
     */
    boolean removeDiscoverDevice(DiscoverNetworkDevice discoverNetworkDevice);

    /**
     * 获取附近的智能设备（联网或未联网）
     * @return
     */
    List<DiscoverNetworkDevice> getNearNetworkDeviceList();


    /**
     * 组装配网数据信息
     * @param discoverDeviceInfo
     * @return
     */
    DiscoverNetworkDevice assembleApconfigDeviceInfo(DiscoverWifiDevice discoverDeviceInfo);

    /**
     * 组装首页状态变化推送数据
     *
     * @param status
     * @return
     */
    String assemblePushHomePageData(String status);


}
