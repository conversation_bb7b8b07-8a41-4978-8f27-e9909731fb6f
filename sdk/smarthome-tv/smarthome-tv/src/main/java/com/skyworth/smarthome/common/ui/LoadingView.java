package com.skyworth.smarthome.common.ui;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.LinearInterpolator;
import android.view.animation.RotateAnimation;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import android.support.annotation.NonNull;

import com.skyworth.smarthome.R;
import com.skyworth.util.Util;

/**
 * Created by fc on 2019/5/9
 * Describe:
 */
public class LoadingView extends FrameLayout {

    public LoadingView(@NonNull Context context) {
        super(context);
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.gravity = Gravity.CENTER_HORIZONTAL;

        FrameLayout frameLayout = new FrameLayout(context);
        params = new LayoutParams(Util.Div(400), Util.Div(200));
        params.topMargin = Util.Div(310);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        addView(frameLayout, params);

        ImageView icon = new ImageView(context);
        icon.setBackgroundResource(R.drawable.loading_view_bg);
        frameLayout.addView(icon);

        ImageView loadingView = new ImageView(context);
        loadingView.setBackgroundResource(R.drawable.loading_view);
        params = new LayoutParams(Util.Div(34), Util.Div(34));
        params.gravity = Gravity.CENTER;
        frameLayout.addView(loadingView, params);

        RotateAnimation rotate = new RotateAnimation(0f, 360f,
                Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
        LinearInterpolator lin = new LinearInterpolator();
        rotate.setInterpolator(lin);
        rotate.setDuration(1000);//设置动画持续时间   
        rotate.setRepeatCount(-1);//设置重复次数   
        rotate.setFillAfter(true);//动画执行完后是否停留在执行完的状态   

        TextView mTips = new TextView(context);
        mTips.setText(context.getString(R.string.loading));
        mTips.setTextSize(Util.Dpi(26));
        mTips.getPaint().setFakeBoldText(true);
        mTips.setTextColor(Color.parseColor("#CCCCCC"));
        mTips.setGravity(Gravity.CENTER);
        params = new LayoutParams(Util.Div(510), ViewGroup.LayoutParams.WRAP_CONTENT);
        params.topMargin = Util.Div(530);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        addView(mTips, params);

        if (getVisibility() == VISIBLE)
            loadingView.startAnimation(rotate);
        else
            loadingView.clearAnimation();

    }


}
