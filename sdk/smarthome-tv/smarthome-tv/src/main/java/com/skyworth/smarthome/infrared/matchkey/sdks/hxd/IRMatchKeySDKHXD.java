package com.skyworth.smarthome.infrared.matchkey.sdks.hxd;

import com.coocaa.app.core.http.HttpServiceManager;
import com.skyworth.smarthome.common.bean.IRMatchKeyData;
import com.skyworth.smarthome.common.http.SmartDevicesHttpService;
import com.skyworth.smarthome.common.util.Utils;
import com.skyworth.smarthome.infrared.matchkey.sdks.BaseIRMatchSDK;
import com.smarthome.common.model.SmartBaseData;
import com.smarthome.common.utils.EmptyUtils;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import retrofit2.Call;

import static com.skyworth.smarthome.infrared.matchkey.IRMatchKeyDialog.PARAM_IR_HOST_DEVICE_ID;
import static com.skyworth.smarthome.infrared.matchkey.IRMatchKeyDialog.PARAM_IR_SLAVE_BRAND;
import static com.skyworth.smarthome.infrared.matchkey.IRMatchKeyDialog.PARAM_IR_SLAVE_TYPE_ID;
import static com.skyworth.smarthome.infrared.matchkey.IRMatchKeyDialog.PARAM_IR_SLAVE_TYPE_NAME;

/**
 * Description: 宏芯达SDK<br>
 * Created by XuZexiao on 2019/9/2 16:11.
 */
public class IRMatchKeySDKHXD extends BaseIRMatchSDK<IRMatchKeyData> {
    public static final String HXD = "hxd";
    private static final int IR_CONFIG_SUCCESS_CONDITION_COUNT = 10;

    private Set<Integer> mIncludeCode = new HashSet<>();
    private Set<String> mExcludeKeys = new HashSet<>();

    private String mIrSlaveTypeName = null;
    private String mIrSlaveTypeId = null;
    private String mIrSlaveBrand = null;
    private String mIrHostDeviceId = null;

    @Override
    public void init(Map<String, String> params) {
        super.init(params);
        mIrSlaveTypeName = params.get(PARAM_IR_SLAVE_TYPE_NAME);
        mIrSlaveTypeId = params.get(PARAM_IR_SLAVE_TYPE_ID);
        mIrSlaveBrand = params.get(PARAM_IR_SLAVE_BRAND);
        mIrHostDeviceId = params.get(PARAM_IR_HOST_DEVICE_ID);
    }

    @Override
    public void start() {
        resetState();
        if (getNextKeyGroup() && mResultListener != null) {
            mResultListener.onMatchNextKey();
        }
    }

    @Override
    public void onKeyResponse() {
        recordResponseKey();
        checkSuccess();
    }

    private void recordResponseKey() {
        IRMatchKeyData currentKeyCode = getCurrentKeyInfo();
        if (mIncludeCode.isEmpty()) {
            mIncludeCode.addAll(currentKeyCode.code_table_ids);
        }
        logi("recordResponseKey: current key codes: " + Utils.listToStringDivideByComma(currentKeyCode.code_table_ids));
        logi("recordResponseKey: current include codes: " + Utils.listToStringDivideByComma(mIncludeCode));
        logi("-----------------------------------------");
        mIncludeCode.retainAll(currentKeyCode.code_table_ids);
        logi("recordResponseKey: retained codes: " + Utils.listToStringDivideByComma(mIncludeCode));
        logi("recordResponseKey: retained size: " + mIncludeCode.size());
        mExcludeKeys.add(currentKeyCode.key_id);
    }

    private void checkSuccess() {
        int result = 0;
        if (EmptyUtils.isEmpty(mIncludeCode)) {
            loge("checkSuccess: key codes is empty");
            result = 0;
        } else {
            result = mIncludeCode.size() <= IR_CONFIG_SUCCESS_CONDITION_COUNT ? 1 : -1;
        }
        if (mResultListener != null) {
            if (result == 0) {
                mResultListener.onMatchFail();
            } else if (result == 1) {
                mResultListener.onMatchSuccess("");
            } else if (result == -1 && getNextKeyGroup()) {
                mResultListener.onMatchNextKey();
            } else {
                mResultListener.onMatchFail();
            }
        }
    }

    @Override
    public void onKeyNotResponse() {
        if (tryNextKeyCode()) {
            if (mResultListener != null) {
                mResultListener.onMatchNextKey();
            }
        } else {
            recordExcludeKey();
            if (getNextKeyGroup()) {
                mResultListener.onMatchNextKey();
            } else if (mResultListener != null) {
                mResultListener.onMatchFail();
            }
        }
    }


    private void recordExcludeKey() {
        IRMatchKeyData currentKeyCode = getCurrentKeyInfo();
        if (currentKeyCode != null) {
            mExcludeKeys.add(currentKeyCode.key_id);
        }
    }

    @Override
    public IRMatchKeyData getCurrentKeyInfo() {
        return mCurrentKeyList.get(mCurrentKeyIndex);
    }

    @Override
    protected void resetState() {
        super.resetState();
        mIncludeCode.clear();
        mExcludeKeys.clear();
    }

    private boolean getNextKeyGroup() {
        String include_code_tables = Utils.listToStringDivideByComma(mIncludeCode);
        String exclude_keys = Utils.listToStringDivideByComma(mExcludeKeys);
        mCurrentKeyList = getNextMatchKey(mIrSlaveTypeId, mIrSlaveBrand, include_code_tables, exclude_keys);
        return mCurrentKeyList != null && mCurrentKeyList.size() > 0;
    }

    public List<IRMatchKeyData> getNextMatchKey(String typeId, String brand, String includeCodeTables, String excludeKeys) {
        Call<SmartBaseData<List<IRMatchKeyData>>> call = SmartDevicesHttpService.SERVICE.irMatchKey(typeId, brand, includeCodeTables, excludeKeys);
        SmartBaseData<List<IRMatchKeyData>> result = HttpServiceManager.Companion.call(call);
        return result == null ? null : result.data;
    }

    @Override
    public Map<String, Object> getSendIrParams() {
        Map<String, Object> params = new HashMap<>();
        params.put("send_type", 4);
        params.put("parent_id", mIrHostDeviceId);
        params.put("match_key_id", Integer.valueOf(getCurrentKeyInfo().id));
        return params;
    }

    @Override
    public Map<String, Object> getSaveMatchResultParams() {
        Map<String, Object> params = new HashMap<>();
        params.put("parent_id", mIrHostDeviceId);
        params.put("type_id", Integer.valueOf(mIrSlaveTypeId));
        params.put("code_table_id", getCurrentKeyInfo().code_table_ids.get(0));
        params.put("name", mIrSlaveBrand + mIrSlaveTypeName);
        return params;
    }

    @Override
    protected String getName() {
        return HXD;
    }

    @Override
    public void destroy() {
        super.destroy();
    }
}
