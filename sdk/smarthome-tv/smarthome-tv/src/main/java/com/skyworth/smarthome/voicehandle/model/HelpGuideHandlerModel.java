package com.skyworth.smarthome.voicehandle.model;

import android.content.Context;
import android.content.Intent;

import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.common.util.LogUtil;
import com.smarthome.common.utils.Constants;

import static android.content.Intent.FLAG_ACTIVITY_NEW_TASK;

/**
 * Created by fc on 2019/9/2
 * Describe: 新手学习页面中《帮助引导页面》跳转工具类
 */
public class HelpGuideHandlerModel implements IVoiceHandlerModel {

    private static final String VOICE_CMD_INFRARED_LIST = "infrared";//红外遥控器列表
    private static final String VOICE_CMD_SMARTHOME_ADD = "homeAdd";//跳转添加设备页
    private static final String VOICE_CMD_SMARTHOME_LIST = "homeDevice";//跳转设备列表
    private static final String VOICE_CMD_THRID_ACCOUNT = "thridAccount";//跳转关联账号

    private Context mContext;

    public HelpGuideHandlerModel(Context context) {
        mContext = context;
    }

    @Override
    public void handleCmd(Intent intent) {
        String openPage = intent.getStringExtra("openPage");
        LogUtil.androidLog("新手学习处理：" + openPage);
        switch (openPage) {
            case VOICE_CMD_THRID_ACCOUNT:
                goToThridAccount();
                break;
            case VOICE_CMD_SMARTHOME_LIST:
                goToSmartDeviceList();
                break;
            case VOICE_CMD_SMARTHOME_ADD:
                goToHomeAddDevice();
                break;
            case VOICE_CMD_INFRARED_LIST:
                goToInfraredDeviceList();
                break;
            default:
                break;
        }
    }

    private void goToInfraredDeviceList() {
//        if (SystemProperty.isInfraredDevice()) {
//            IFunctionGoToModel.INSTANCE.goToInfraredDeviceList("click");
//        } else {
//            XToast.showToast(SmartHomeTvLib.getContext(), "你当前电视不支持红外控制");
//        }
    }

    private void goToHomeAddDevice() {
        try {
            Intent intent = new Intent("com.smarthome.action.DEVICE_GUIDE");
            intent.setFlags(FLAG_ACTIVITY_NEW_TASK);
            SmartHomeTvLib.getContext().startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void goToSmartDeviceList() {
        try {
            Intent intent = new Intent("com.smarthome.action.HOME");
            intent.putExtra(Constants.KEY_DEVICE_ID, "null");
            intent.setFlags(FLAG_ACTIVITY_NEW_TASK);
            SmartHomeTvLib.getContext().startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void goToThridAccount() {
        try {
            Intent intent = new Intent("com.smarthome.action.THRID_ACCOUNT");
            intent.setFlags(FLAG_ACTIVITY_NEW_TASK);
            SmartHomeTvLib.getContext().startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
