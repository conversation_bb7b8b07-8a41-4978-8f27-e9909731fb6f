package com.skyworth.smarthome.home.smartdevice.controlpanel.thirdapp.appmanager;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.coocaa.app.core.downloader.AppCoreDownloadListener;
import com.coocaa.app.core.downloader.AppCoreDownloadManager;
import com.coocaa.app.core.downloader.IAppCoreDownloaderManager;
import com.coocaa.app.core.downloader.data.AppCoreDownloadError;
import com.coocaa.app.core.downloader.data.AppCoreDownloadInfo;
import com.coocaa.app.core.downloader.data.AppCoreDownloadProcessInfo;
import com.coocaa.app.core.downloader.data.AppCoreTableDownload;
import com.coocaa.app.core.http.HttpServiceManager;
import com.coocaa.app.core.installer.AppCoreInstaller;
import com.coocaa.app.core.installer.SilentInstaller;
import com.coocaa.app.core.utils.MyObject;
import com.skyworth.smarthome.home.smartdevice.controlpanel.thirdapp.appmanager.http.ThirdAppHttpServer;
import com.smarthome.common.utils.Android;

import java.util.concurrent.CountDownLatch;

public class AppManager {

    public static final boolean checkUpdate(Context c, String pkg) {
        try {
            int localVersion = Android.getVersionCode(c, pkg);
            Log.d("1224", "checkPkg localVersion:" + localVersion);
            if (localVersion > 0) {
                AppInfo.UpgradeInfo info = HttpServiceManager.Companion.call(ThirdAppHttpServer.getInstance(c).getAppUpdateBeans(pkg));
                String item = null;
                if (info != null && info.data != null && !info.data.isEmpty()) {
                    item = info.data.get(0).versioncode;
                }
                if (item != null && Integer.valueOf(item) > localVersion) {
                    Log.d("1224", "updateFromServer need update ");
                    return true;
                }
                return false;
            } else {
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public static boolean downloadAndInstall(final Context c, final String pkg, final IUpdateListener listener) {
        Log.d("1102", "downloadAndInstall  ");
        final MyObject<Boolean> mo = new MyObject();
        mo.set(false);
        final IAppCoreDownloaderManager downManager = AppCoreDownloadManager.create(c);
        AppInfo.DownloadInfo downloadInfo = HttpServiceManager.Companion.call(ThirdAppHttpServer.getInstance(c).getdDownloadInfo(pkg));
        if (downloadInfo != null && !TextUtils.isEmpty(downloadInfo.md5) && !TextUtils.isEmpty(downloadInfo.download)) {
            Log.d("1102", "downloadAndInstall  start download ");
            AppCoreDownloadInfo dInfo = new AppCoreDownloadInfo();
            dInfo.md5 = downloadInfo.md5;
            dInfo.url = downloadInfo.download;
            dInfo.urlEx = downloadInfo.downloadEx;
            final CountDownLatch cdt = new CountDownLatch(1);
            AppCoreDownloadListener downloadListener = new AppCoreDownloadListener() {
                @Override
                public void onReady(AppCoreTableDownload task) {

                }

                @Override
                public void onStart(AppCoreTableDownload task) {
                    if (listener != null) listener.onDownloadStart();
                }

                @Override
                public void onDelete(AppCoreTableDownload task) {
                    Log.d("1102", "onDelete ");
                    cdt.countDown();
                }

                @Override
                public void onProcess(AppCoreTableDownload task, AppCoreDownloadProcessInfo process) {
                    if (listener != null) {
                        Log.d("1102", "onProcess  process: " + process.percent + " " + "showprocess:" + process.percent * 95 / 100);
                        listener.onDownloadProcess(process.percent * 95 / 100);
                    }
                }

                @Override
                public void onSuccess(AppCoreTableDownload task) {
                    Log.d("1102", "AppCoreDownloadListener  onSuccess " + task.getSavedFilePath());
                    if (listener != null) listener.onDownloadEnd(true);
                    String path = task.getSavedFilePath();
                    AppCoreInstaller.INSTALL_RESULT res = SilentInstaller.get(c).install(path, new AppCoreInstaller.InstallerListener() {
                        @Override
                        public void onInstallStart(String archive) {
                            if (listener != null) listener.onInstallStart();
                        }

                        @Override
                        public void onInstallEnd(String archive, AppCoreInstaller.INSTALL_RESULT result, String pkg, String extra) {
                            Log.d("1102", "SilentInstaller  onInstallEnd " + result.toString());
                            if (listener != null) {
                                if (result != null && result == SilentInstaller.INSTALL_RESULT.SUCCESS) {
                                    mo.set(true);
                                    listener.onInstallEnd(true, "");
                                } else {
                                    mo.set(false);
                                    listener.onInstallEnd(false, "install_failed");
                                }
                            }
                        }
                    });
                    Log.d("1102", "SilentInstaller  res " + res.toString());
                    cdt.countDown();
                    try {
                        downManager.processToRemove(task.url);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                @Override
                public void onPause(AppCoreTableDownload task) {

                }

                @Override
                public void onError(AppCoreTableDownload task, AppCoreDownloadError error) {
                    Log.d("1102", "AppCoreDownloadListener  onError " + error.toString() + " pkg:" + task.name);
                    if (listener != null) listener.onDownloadEnd(false);
                    mo.set(false);
                    cdt.countDown();
                    try {
                        downManager.processToRemove(task.url);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            };
            downManager.registerDownloadListener(downloadListener);
            downManager.processToStart(dInfo);
            try {
                cdt.await();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            try {
                Log.d("1102", "downloadAndInstall  end:" + mo.get() + "----remove cache.");
                downManager.processToRemove(dInfo.url);
                downManager.processToRemove(dInfo.urlEx);
            } catch (Exception e) {
                e.printStackTrace();
            }
            downManager.unRegisterDownloadListener(downloadListener);
        }
        return mo.get();
    }

}
