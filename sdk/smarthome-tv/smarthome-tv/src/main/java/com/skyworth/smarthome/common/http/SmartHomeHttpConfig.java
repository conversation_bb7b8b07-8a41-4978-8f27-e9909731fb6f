package com.skyworth.smarthome.common.http;

import android.util.Log;

import com.coocaa.app.core.http.HttpServiceManager;
import com.coocaa.app.core.utils.FuncKt;
import com.skyworth.smarthome.common.model.UserInfo;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.smarthome.common.sal.SalImpl;
import com.smarthome.common.utils.Md5;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON> on 2018/4/17.
 */
public class SmartHomeHttpConfig {
    public static final String APP_KEY = "bf5527edc4da4a40be832f26e27adf32";//新
    public static final String SECRET = "b509ec6e10a84f56ab792023508e47ae";//新
    public static final String DEVICE_SERVER = "DEVICE_SERVER";
    public static final String HOMEPAGE_SERVER = "HOMEPAGE_SERVER";
    public static final String APPSTORE_SERVER  = "APPSTORE_SERVER";

    public static String getServer(String metaDataKey) {
        return FuncKt.getMetaData(SmartHomeTvLib.getContext(), SmartHomeTvLib.getContext().getPackageName(), metaDataKey);
    }

    public static String getSign(Map<String, String> map) {
        String sign = sign(map, SECRET);
        Log.d("sign", "sign:" + sign);
        return sign;
    }

    public static Map<String, String> getBaseUrlParams() {
        Map<String, String> map = new HashMap<>();
        map.put("appkey", APP_KEY);
        map.put("time", String.valueOf(System.currentTimeMillis() / 1000));
        map.put("uid", UserInfo.getInstance().getUserID());
        map.put("ak", UserInfo.getInstance().getToken());
        map.put("vuid", SalImpl.getSAL(SmartHomeTvLib.getContext()).getMAC());
        return map;
    }

    private static String sign(Map<String, String> map, String secret) {
        List<String> keys = new ArrayList<String>(map.keySet());
        Collections.sort(keys);

        String temStr = "";
        for (String key : keys) {
            temStr += key + map.get(key);
        }
        Log.d("sign", " temStr:" + temStr);
        String mysign = "";
        try {
            mysign = Md5.getMd5(temStr + secret);
        } catch (Exception e) {

        }
        Log.d("sign", " mysign:" + mysign);
        return mysign;
    }

    public static final HttpServiceManager.HeaderLoader SMARTHOME_HEADER_LOADER = new MyHeaderLoader();

    public static class MyHeaderLoader implements HttpServiceManager.HeaderLoader {

        Map<String, String> DEFAULT_HEADERS = null;

        private Map<String, String> loadHeader() {
            DEFAULT_HEADERS = new HashMap<>();
            Map<String, String> headerMap = SalImpl.getSAL(SmartHomeTvLib.getContext()).getCommonHeader();
            if (headerMap != null && headerMap.size() > 0) {
                DEFAULT_HEADERS.putAll(headerMap);
            }
            return DEFAULT_HEADERS;
        }

        @Override
        public synchronized Map<String, String> getHeader() {
            if (DEFAULT_HEADERS == null || DEFAULT_HEADERS.size() < 1) return loadHeader();
            return DEFAULT_HEADERS;
        }

        @Override
        public synchronized void updateHeader() {
            DEFAULT_HEADERS = null;
            getHeader();
        }
    }

}
