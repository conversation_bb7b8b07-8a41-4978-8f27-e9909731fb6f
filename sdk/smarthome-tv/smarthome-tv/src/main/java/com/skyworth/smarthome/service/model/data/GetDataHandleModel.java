package com.skyworth.smarthome.service.model.data;

import com.coocaa.app.core.app.AppCoreApplication;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.service.model.SmartHomeModel;
import com.smarthome.common.utils.EmptyUtils;
import com.swaiot.aiotlib.common.entity.FamilyBean;
import com.swaiot.aiotlib.common.entity.SceneBean;

import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * Describe:获取数据逻辑处理
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/9/17
 */
public class GetDataHandleModel implements IGetDataHandleModel {

    @Override
    public List<DeviceInfo> getSmartDeviceList() {
        List<DeviceInfo> deviceInfoList = AppData.getInstance().getDeviceInfoList();
        return deviceInfoList;
    }

    @Override
    public List<FamilyBean> getFamilyList() {
        List<FamilyBean> familyList = AppData.getInstance().getFamilyList();
        return familyList;
    }

    @Override
    public List<SceneBean> getSceneList() {
        List<SceneBean> sceneList = AppData.getInstance().getSceneList();
        return sceneList;
    }

    @Override
    public List<DeviceInfo> getCacheSmartDeviceList() {
        return AppData.getInstance().getDeviceInfoList();
    }

    @Override
    public SceneBean getSceneInfo(String sceneId) {
        if (EmptyUtils.isEmpty(sceneId))
            return null;
        List<SceneBean> sceneList = AppData.getInstance().getSceneList();
        if (EmptyUtils.isNotEmpty(sceneList)) {
            if (EmptyUtils.isNotEmpty(sceneList)) {
                for (SceneBean item : sceneList) {
                    if (item.scene_id.equals(sceneId)) {
                        return item;
                    }
                }
            }
        }
        return null;
    }

    @Override
    public DeviceInfo getSmartDeviceInfo(String deviceID) {
        if (EmptyUtils.isEmpty(deviceID))
            return null;
        List<DeviceInfo> deviceInfoList = AppData.getInstance().getDeviceInfoList();
        if (EmptyUtils.isNotEmpty(deviceInfoList)) {
            List<DeviceInfo> deviceList = deviceInfoList;
            if (EmptyUtils.isNotEmpty(deviceList)) {
                for (DeviceInfo item : deviceList) {
                    if (item.device_id.equals(deviceID)) {
                        return item;
                    }
                }
            }
        }
        return null;
    }

    @Override
    public String getAiotHomeStatus(String familyId) {
        String aiotHomeStatus = AppData.getInstance().getAiotHomeStatus();
        if (EmptyUtils.isEmpty(aiotHomeStatus)) {
            AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
                @Override
                public Unit invoke() {
                    SmartHomeModel.INSTANCE.getAiotHomeStaus("");
                    return Unit.INSTANCE;
                }
            });
        }
        return aiotHomeStatus;
    }
}
