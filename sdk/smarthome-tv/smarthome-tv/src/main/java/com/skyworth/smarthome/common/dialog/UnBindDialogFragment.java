package com.skyworth.smarthome.common.dialog;


import android.os.Bundle;
import android.support.v4.app.DialogFragment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.ui.CommonFocusBox;
import com.skyworth.smarthome.common.util.Contants;
import com.skyworth.smarthome.common.util.ViewsBuilder;

/**
 * @ProjectName: NewTV_SmartHome
 * @Package: com.skyworth.smarthome_tv.common.dialog
 * @ClassName: UnBindDialogFragment
 * @Description: java类作用描述
 * @Author: wangyuehui
 * @CreateDate: 2020/6/9 17:24
 * @UpdateUser: 更新者
 * @UpdateDate: 2020/6/9 17:24
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class UnBindDialogFragment extends DialogFragment {

    private CommonFocusBox mUnBindSureBtn,mUnBindCancelBtn;
    private String mDeviceId;
    private UnBindCallBack mUnBindCallBack;

    public static UnBindDialogFragment newInstance(String deviceId) {

        Bundle args = new Bundle();
        args.putString(Contants.COOCAA_INTENT_CURRENT_DEVICE_ID,deviceId);

        UnBindDialogFragment fragment = new UnBindDialogFragment();
        fragment.setArguments(args);
        return fragment;
    }

    public void setUnBindCallBack(UnBindCallBack unBindCallBack) {
        mUnBindCallBack = unBindCallBack;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NO_FRAME, R.style.common_style);
        if (getArguments() != null)
            mDeviceId = getArguments().getString(Contants.COOCAA_INTENT_CURRENT_DEVICE_ID);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return ViewsBuilder.getUnBindDialogView(getActivity());
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {

        mUnBindSureBtn = view.findViewById(R.id.unbind_devices_btn_sure);
        mUnBindCancelBtn = view.findViewById(R.id.unbind_devices_btn_cancle);

        mUnBindCancelBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        mUnBindSureBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mUnBindCallBack != null) {
                    mUnBindCallBack.callBack();
                }
                dismiss();
            }
        });
    }

    @Override
    public void onStart() {
        super.onStart();
        Window window = getDialog().getWindow();
        WindowManager.LayoutParams windowParams = window.getAttributes();
        windowParams.dimAmount = 0.50f;
        windowParams.flags |= WindowManager.LayoutParams.FLAG_DIM_BEHIND;
        window.setAttributes(windowParams);
    }

    public interface UnBindCallBack {
        void callBack();
    }
}
