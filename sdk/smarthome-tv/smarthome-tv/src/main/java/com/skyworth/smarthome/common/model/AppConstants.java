package com.skyworth.smarthome.common.model;

/**
 * Describe:公共常量
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/2/1
 */
public class AppConstants {
    public static final String DEFAULT_TIME = "2019-01-01 00-00-00";
    public static final long VALID_TIME = 24 * 60 * 60 * 1000;//一天

    public static final int DIALOG_DISMISS_TIME = 60 * 1000;//60秒消失
    //后台推送事件
    public static final String EVENT_PUSH_TTS_MSG = "play-tts";//语音播报
    public static final String EVENT_PUSH_VIDEO = "play-video";//视频推送
    public static final String EVENT_PUSH_DEVICE_COUNT = "device-count-updated";//设备数量变化消息
    public static final String EVENT_PUSH_DEVICE_STATUS = "device-status-updated";//设备状态变化
    public static final String EVENT_PUSH_DEVICE_ONLINE = "device-online-updated";//设备在线状态变化
    public static final String EVENT_PUSH_VOICE_COMMAND_RESULT = "voice-command-result";//语音指令解析结果
    public static final String EVENT_PUSH_VOICE_DEVICES_SYNC = "voice-devices-sync";//语音设备列表同步
    public static final String EVENT_PUSH_REPORT_CURRENT_MEDIA = "report-current-media";//向服务器上报当前播放媒体
    public static final String EVENT_PUSH_PLAY_STREAM_VIDEO = "play-stream-video";//播放流视频(可视门铃)
    public static final String EVENT_PUSH_STOP_PLAY_STREAM_VIDEO = "stop-play-stream-video";//停止播放流视频(可视门铃)
    public static final String EVENT_PUSH_DEVICE_NOTIFY = "device-notify";//设备通知消息
    public static final String EVENT_PUSH_DEVICE_ALERT = "device-alert";//设备警告消息
    public static final String EVENT_PUSH_MIDEA_AUTH_SUCCEED = "midea-auth-succeed";//美的账号授权成功
    public static final String EVENT_PUSH_JD_AUTH_SUCCEED = "jd-auth-succeed";//京东账号授权成功
    public static final String EVENT_PUSH_JD_AUTH_FAILED = "jd-auth-failed";//京东账号授权成功
    public static final String EVENT_PUSH_OTHER_AUTH_SUCCEED = "thirdparty-auth-succeed";//其他账号授权成功  包含美的 华为   2019年11月7号
    public static final String EVENT_PUSH_OTHER_AUTH_FAILED = "thirdparty-auth-failed";//其他账号授权失败  包含美的 华为
    public static final String EVENT_PUSH_AIOT_HOME_STATUS = "home-status-updated";//AIOT数据提供（状态变化）
    public static final String EVENT_PUSH_SCENE_COUNT = "scene-count-updated";//场景数量变化消息
    public static final String EVENT_PUSH_SCENE_STATUS = "scene-status-updated";//场景状态变化消息
    public static final String EVENT_PUSH_SCENE_AUTO = "scene-triggered";//自动触发的场景推送消息
    public static final String EVENT_PUSH_START_OTHER_APP = "start-other-app";//启动第三方App
    public static final String EVENT_PUSH_NOTIFY_OTHER_APP = "notify-other-app";//通知第三方App
    public static final String EVENT_PUSH_DEVICE_INFO_UPDATE = "device-info-updated";//设备信息更新

    public static final String EVENT_PUSH_DEVICE_IOT_MODIFY = "device-iot-modify";//TV控制指令


    //语音指令解析结果事件
    public static final String VOICE_COMMAND_CONTROL_DEVICE = "control_device";
    public static final String VOICE_COMMAND_CONTROL_MULTI_DEVICE = "control_multi_device";
    public static final String VOICE_COMMAND_EXECUTE_SCENE = "execute_scene";
    public static final String VOICE_COMMAND_QUERY_DEVICE_LIST = "query_device_list";
    public static final String VOICE_COMMAND_OTHER = "other";

    //设备变化事件
    public static final String EVENT_CHANGE_DEVICE_LIST = "deviceList";
    public static final String EVENT_CHANGE_SCENE_LIST = "sceneList";
    public static final String EVENT_CHANGE_DEVICE_STATUS = "deviceControlStatus";
    public static final String EVENT_CHANGE_DEVICE_ONLINE_STATUS = "deviceOnlineStatus";


    //启动悬浮框相关参数
    public static final String KEY_SHOW_TYPE = "show_type";//展示type
    public static final String KEY_SELECT_ID = "device_id";//选择显示在id
    public static final String KEY_ENTER_INFRARED_CONTROL = "enter_infrared_control";//进入红外控制
    public static final String KEY_ENTER_INFRARED_ADD = "enter_infrared_add";//进入红外控制
    public static final String KEY_ENTER_INFRARED_FROM = "from";//进入方式

    public static final String START_SHOW_TYPE_SCENE = "scene";//场景
    public static final String START_SHOW_TYPE_DEVICE = "device";//设备
    public static final String START_SHOW_TYPE_ADD = "add";//添加

    //场景执行相关状态
    public final static int SCENE_EXECUTING = 0;//场景执行中
    public final static int SCENE_EXECUTE_SUCCESS = 1;//执行成功
    public final static int SCENE_EXECUTE_FAILURE = 2;//执行失败

    //设备列表特殊类型
    public static final int SPECIAL_DEVICE_TYPE_NORMAL = 0;//正常设备
    public static final int SPECIAL_DEVICE_TYPE_INFRARED = 1;//红外设备
    public static final int SPECIAL_DEVICE_TYPE_NO_CONNECT_NETWORK = 2;//未配网（未联网）
    public static final int SPECIAL_DEVICE_TYPE_CONNECTED_NETWORK = 3;//未配网（已联网）

    public static final String DEVICE_STATUS_NOT_APCONFIG_NETWORK = "0";//未配网
    public static final String DEVICE_STATUS_ADDED = "1";//已添加
    public static final String DEVICE_STATUS_UNBIND = "2";//未绑定
    public static final String DEVICE_STATUS_BINDED = "3";//已绑定

    //设备类型
    public static final int DEVICE_ACESS_TYPE_WIFI = 0;//wifi设备
    public static final int DEVICE_ACESS_TYPE_ZIGBEE = 1;//zigbee
    public static final int DEVICE_ACESS_TYPE_BLE = 2;//ble
    public static final int DEVICE_ACESS_TYPE_INFRARED = 3;//红外设备

    //设备ID后缀
    public static final String DEVICE_ID_FFIX_INFRARED = "_infrared";//添加
    public static final String DEVICE_ID_FFIX_UNBIND = "_unBind";//添加

    //资瓷的第三方账号
    public enum SUPPORT_ACCOUNT_TYPE {
        MIDEA, SMART_JD, CW_HILINK
    }

    //酷控SDK
    public static final String KK_SDK_KEY = "********************************";

    //配网失败原因
    public static final String APCONFIG_FAIL_REASON_NOT_BIND_MOBILE = "NOT_BIND_MOBILE";
    public static final String APCONFIG_FAIL_REASON_ACCOUNT_BIND_DEVICE_FAIL = "ACCOUNT_BIND_DEVICE_FAIL";
    public static final String APCONFIG_FAIL_REASON_DEVICE_OTHER_BIND = "DEVICE_OTHER_BIND";
    public static final String APCONFIG_FAIL_REASON_ETHERNET_CONNECT = "ETHERNET_CONNECT";
    public static final String APCONFIG_FAIL_REASON_NOT_LOGIN = "NOT_LOGIN";
    public static final String APCONFIG_FAIL_REASON_NOT_BIND_MIDEA = "NOT_BIND_MIDEA";
    public static final String APCONFIG_FAIL_REASON_MIDEA_TOKEN_NULL = "MIDEA_TOKEN_NULL";
    public static final String APCONFIG_FAIL_REASON_WIFI_NOT_2_4G = "WIFI_NOT_2_4G";
    public static final String APCONFIG_FAIL_REASON_GET_WIFI_PASS_ERROR = "GET_WIFI_PASS_ERROR";
    public static final String APCONFIG_FAIL_REASON_APCONFIG_FAIL = "APCONFIG_FAIL";
    public static final String APCONFIG_FAIL_REASON_USER_CANCEL_BIND = "USER_CANCEL_BIND";
    public static final String APCONFIG_FAIL_REASON_MANUAL_EXIT = "MANUAL_EXIT";
    //场景状态
    public static final String SCENE_STATUS_INVALID = "invalid";//无效
    public static final String SCENE_STATUS_AUTO = "auto";//自动
    public static final String SCENE_STATUS_MANU = "manu";//手动

    //自定义术语请求（小维AI）
    public static final String[] BAIDU_REQUESTS = new String[]{
            "IHaveARestRequest", "InBedroomRequest", "WeddingDayRequest",
            "RotatingAlbumRequest", "ReadingModeRequest", "GoingOutRequest",
            "GoingBedGNRequest", "GMGetUpRequest", "CheckOutRequest",
            "PlayOnDeviceRequest", "OpenDoorRequest", "SkyKSongBotResourceRequest",
            "CurrentHumidityRequest", "CurrentTemperatureRequest", "PlayOnDeviceRequest"
    };

    //自定义术语请求（小维AI）
    public static final String[] ROOM_LIST = new String[]{
            "客厅", "主卧", "次卧", "儿童房", "老人房", "书房",
            "厨房", "卫生间", "阳台", "办公室", "车库"
    };

    /**
     * 向公共库中请求的http key
     */
    public static final String KEY_HTTP_DEVCIE_LIST = "device_list";
    public static final String KEY_HTTP_SCENE_LIST = "scene_list";
    public static final String KEY_HTTP_FAMILY_LIST = "family_list";
    public static final String KEY_HTTP_HOME_STATUS = "home_status";
    public static final String KEY_HTTP_CURRENT_FAMILY = "current_family";
    public static final String KEY_HTTP_DEVICE_NOTIFY = "device_notify";
    public static final String KEY_HTTP_CONTROL_DEVICE = "control_device";
    public static final String KEY_HTTP_CONTROL_SENCE = "control_sence";
    public static final String KEY_HTTP_SET_CURRENT_FAMILY = "set_current_family";

    public static final String KEY_HANDEL_DEVICE = "device";

    public static final String LOCAL_CONTROL = "local_control";

    /**
     * 设备状态取值：开、关、开始、暂停
     */
    public static String DEVICE_POW_STATUS_ON = "ON";
    public static String DEVICE_POW_STATUS_OFF = "OFF";
    public static String DEVICE_POW_STATUS_START = "START";
    public static String DEVICE_POW_STATUS_PAUSE = "PAUSE";

    /**
     * 设备变化事件
     */
    public enum SSE_PUSH {
        DEVICE_STATUS,
        DEVICE_LIST,
        SCENE_LIST,
        FAMILY_LIST,
        CURRENT_FAMILY,
        HOME_STATUS_INFO,
        DEVICE_NOTIFY,
    }

    /**
     * Http请求推送事件
     */
    public enum HTTP_PUSH {
        DEVICE_LIST,
        SCENE_LIST,
        FAMILY_LIST,
        CURRENT_FAMILY,
        HOME_STATUS_INFO,
        DEVICE_NOTIFY,
        CONTROL_DEVCIE,
        CONTROL_SENCE
    }

    /**
     * 设备变化事件
     */
    public enum MESSAGE_TYPE {
        DEVICE_NOTICE,
        DEVICE_ALERT,
        DEVICE_VOICE_CONTROL_RESULT,
        SCENE_VOICE_CONTROL_RESULT
    }


}
