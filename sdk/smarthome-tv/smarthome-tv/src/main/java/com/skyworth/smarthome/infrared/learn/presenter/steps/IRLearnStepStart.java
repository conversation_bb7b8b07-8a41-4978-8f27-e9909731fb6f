package com.skyworth.smarthome.infrared.learn.presenter.steps;

import com.coocaa.app.core.app.AppCoreApplication;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/7/4 10:20.
 */
public class IRLearnStepStart extends BaseIRLearnStep {
    public static final String STEP_TAG = "start";
//    private Timer timer = null;

    @Override
    public void create() {
        super.create();
    }

    @Override
    public void run() {
        super.run();
//        presenter.showLearnStart();
        goLearning();
    }

    private void showError() {
        presenter.showError("设置红外设备学习模式失败");
    }

    private void goLearning() {
//        if (timer != null) {
//            timer.cancel();
//        }
//        timer = new Timer();
//        timer.schedule(new TimerTask() {
//            @Override
//            public void run() {
                AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        if (presenter.startLearnStatus()) {
                            presenter.startTimeOutCheck();
                            jumpTo(IRLearnStepLearning.STEP_TAG);
                        } else {
                            showError();
                        }
                        return Unit.INSTANCE;
                    }
                });
//            }
//        }, 3000);
    }

    @Override
    public boolean input(String msg, Object... params) {
//        if (INPUT_KEY_BACK.equals(msg)) {
//            if (timer != null) {
//                logi("cancel timer");
//                timer.cancel();
//            }
//            return true;
//        }
        return false;
    }

    @Override
    public String getTag() {
        return STEP_TAG;
    }

    @Override
    public void destroy() {
        super.destroy();
    }
}
