package com.skyworth.smarthome.devices.apconfig.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.model.UserInfo;
import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.util.Util;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XThemeUtils;

/**
 * @ClassName: BindDeviceActivityView
 * @Author: AwenZeng
 * @CreateDate: 2020/9/27 16:35
 * @Description: 绑定设备结果活动View
 */
public class BindDeviceActivityView extends FrameLayout{
    private TextView mOkBtn,mCancelBtn,mAccountNameTv;
    private Drawable drawable;
    private BindDeviceOnClickListener mListener;
    public interface BindDeviceOnClickListener{
        void onOkBtn();
        void onCancelBtn();
    }

    public BindDeviceActivityView(Context context) {
        super(context);
        setBackgroundResource(R.drawable.bind_device_activity_bg);
        drawable = XThemeUtils.getDrawable(Color.parseColor("#19FFFFFF"), 0, Util.Div(2), Util.Div(16));
        initUI();
        initListener();
    }

    private void initUI(){
        mAccountNameTv = new TextView(getContext());
        mAccountNameTv.setTextColor(Color.parseColor("#ccFFFFFF"));
        mAccountNameTv.setTextSize(Util.Dpi(22));
        mAccountNameTv.setText("当前账号："+ UserInfo.getInstance().getNick_name());
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.gravity = Gravity.RIGHT;
        layoutParams.topMargin = Util.Div(20);
        layoutParams.rightMargin = Util.Div(20);
        addView(mAccountNameTv, layoutParams);

        mOkBtn = new TextView(getContext());
        mOkBtn.setText("去抽奖");
        mOkBtn.setTextSize(Util.Dpi(32));
        mOkBtn.setTextColor(Color.parseColor("#aaFFFFFF"));
        mOkBtn.setGravity(Gravity.CENTER);
        mOkBtn.setFocusable(true);
        mOkBtn.setFocusableInTouchMode(true);
        mOkBtn.setBackground(drawable);

        layoutParams = new LayoutParams(Util.Div(365), Util.Div(90));
        layoutParams.leftMargin = Util.Div(50);
        layoutParams.topMargin = Util.Div(460);
        addView(mOkBtn, layoutParams);

        mCancelBtn = new TextView(getContext());
        mCancelBtn.setText("取消");
        mCancelBtn.setTextSize(Util.Dpi(32));
        mCancelBtn.setTextColor(Color.parseColor("#aaFFFFFF"));
        mCancelBtn.setGravity(Gravity.CENTER);
        mCancelBtn.setFocusable(true);
        mCancelBtn.setFocusableInTouchMode(true);
        mCancelBtn.setBackground(drawable);

        layoutParams = new LayoutParams(Util.Div(365), Util.Div(90));
        layoutParams.leftMargin = Util.Div(445);
        layoutParams.topMargin = Util.Div(460);
        addView(mCancelBtn, layoutParams);
    }

    private void initListener(){
        mOkBtn.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    mOkBtn.setBackground(new CCFocusDrawable(getContext()).setRadius(Util.Dpi(16)).setSolidColor(getResources().getColor(R.color.white)));
                    mOkBtn.setTextColor(Color.parseColor("#000000"));
                    mOkBtn.getPaint().setFakeBoldText(true);
                } else {
                    mOkBtn.setBackground(drawable);
                    mOkBtn.setTextColor(Color.parseColor("#aaFFFFFF"));
                    mOkBtn.getPaint().setFakeBoldText(false);
                }
            }
        });
        mOkBtn.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if(EmptyUtils.isNotEmpty(mListener)){
                    mListener.onOkBtn();
                }
            }
        });
        mCancelBtn.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    mCancelBtn.setBackground(new CCFocusDrawable(getContext()).setRadius(Util.Dpi(16)).setSolidColor(getResources().getColor(R.color.white)));
                    mCancelBtn.setTextColor(Color.parseColor("#000000"));
                    mCancelBtn.getPaint().setFakeBoldText(true);
                } else {
                    mCancelBtn.setBackground(drawable);
                    mCancelBtn.setTextColor(Color.parseColor("#aaFFFFFF"));
                    mCancelBtn.getPaint().setFakeBoldText(false);
                }
            }
        });
        mCancelBtn.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if(EmptyUtils.isNotEmpty(mListener)){
                    mListener.onCancelBtn();
                }
            }
        });
    }

   public void setBindDeviceListener(BindDeviceOnClickListener listener){
        mListener = listener;
   }

}
