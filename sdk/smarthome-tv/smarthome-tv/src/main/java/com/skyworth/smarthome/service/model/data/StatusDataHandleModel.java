package com.skyworth.smarthome.service.model.data;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.service.model.ISmartDeviceDataModel;
import com.smarthome.common.utils.EmptyUtils;

import java.util.List;

/**
 * Describe:设备状态变化数据处理
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/9/17
 */
public class StatusDataHandleModel implements IStatusDataHandleModel {

    private static final String DEVICE_ID_KEY = "device_id";
    private static final String DEVICE_STATUS_KEY = "status";
    private static final String DEVICE_ONLINE_STATUS_KEY = "online_status";

    @Override
    public void updateControlStatusData(String msg) {
        try {
            JSONObject msgJson = JSON.parseObject(msg);
            String targetId = getIdFromMsg(msgJson);
            List<DeviceInfo> deviceInfoList = ISmartDeviceDataModel.INSTANCE.getCacheSmartDeviceList();
            if (EmptyUtils.isNotEmpty(deviceInfoList)) {
                List<DeviceInfo> deviceList = deviceInfoList;
                if (EmptyUtils.isNotEmpty(deviceList)) {
                    for (int i = 0; i < deviceList.size(); i++) {
                        DeviceInfo item = deviceList.get(i);
                        if (targetId.equals(item.device_id) && EmptyUtils.isNotEmpty(item.report_status)) {
                            JSONObject jsonObject = JSONObject.parseObject(item.report_status);
                            JSONObject statusObject = JSONObject.parseObject(getStatusFromMsg(msgJson));
                            for (Object key : statusObject.keySet().toArray()) {
                                jsonObject.put((String) key, statusObject.getString((String) key));
                            }
                            item.report_status = jsonObject.toJSONString();
                            deviceList.set(i, item);
                            deviceInfoList = deviceList;
                            AppData.getInstance().setDeviceInfoList(deviceInfoList);
                            break;
                        }
                    }
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void updateOnlineStatusData(String msg) {
        try {
            JSONObject msgJson = JSON.parseObject(msg);
            String targetId = getIdFromMsg(msgJson);
            String onlineStatus = getOnlineStatusFromMsg(msgJson);
            List<DeviceInfo> deviceInfoList = AppData.getInstance().getDeviceInfoList();
            if (EmptyUtils.isNotEmpty(deviceInfoList)) {
                List<DeviceInfo> deviceList = deviceInfoList;
                if (EmptyUtils.isNotEmpty(deviceList)) {
                    for (int i = 0; i < deviceList.size(); i++) {
                        DeviceInfo item = deviceList.get(i);
                        if (targetId.equals(item.device_id)) {
                            item.online_status = Integer.parseInt(onlineStatus);
                            deviceList.set(i, item);
                            deviceInfoList = deviceList;
                            AppData.getInstance().setDeviceInfoList(deviceInfoList);
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private String getIdFromMsg(JSONObject json) {
        if (json == null) return "";
        Object obj = json.get(DEVICE_ID_KEY);
        return obj == null ? "" : obj.toString();
    }

    private String getStatusFromMsg(JSONObject json) {
        if (json == null) return "";
        Object obj = json.get(DEVICE_STATUS_KEY);
        return obj == null ? "" : obj.toString();
    }

    private String getOnlineStatusFromMsg(JSONObject json) {
        if (json == null) return "";
        Object obj = json.get(DEVICE_ONLINE_STATUS_KEY);
        return obj == null ? "" : obj.toString();
    }
}
