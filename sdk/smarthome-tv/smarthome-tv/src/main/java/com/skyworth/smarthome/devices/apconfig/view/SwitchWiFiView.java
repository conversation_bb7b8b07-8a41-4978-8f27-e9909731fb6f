package com.skyworth.smarthome.devices.apconfig.view;

import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.util.SparseIntArray;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import com.skyworth.smarthome.devices.apconfig.model.SwitchWifiInfo;
import com.skyworth.smarthome.devices.apconfig.presenter.IApConfigPresenter;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.ui.DialogBg;
import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.util.Util;

import java.util.List;

/**
 * Description:切换WiFi-View <br>
 * Created by wzh on 2019/1/7 18:03.
 */
public class SwitchWiFiView extends FrameLayout {
    private Context mContext;
    private IApConfigPresenter mPresenter;
    private LinearLayout listLayout = null;
    private static final SparseIntArray LOCK_ICON_FOCUS = new SparseIntArray();
    private static final SparseIntArray LOCK_ICON_UNFOCUS = new SparseIntArray();
    private static final SparseIntArray SIGNAL_ICON_FOCUS = new SparseIntArray();
    private static final SparseIntArray SIGNAL_ICON_UNFOCUS = new SparseIntArray();

    static {
        LOCK_ICON_UNFOCUS.put(1, R.drawable.sh_switch_lock);
        LOCK_ICON_UNFOCUS.put(2, R.drawable.sh_switch_unlock);

        LOCK_ICON_FOCUS.put(1, R.drawable.sh_switch_lock_focus);
        LOCK_ICON_FOCUS.put(2, R.drawable.sh_switch_unlock_focus);

        SIGNAL_ICON_UNFOCUS.put(1, R.drawable.sh_switch_wifi1);
        SIGNAL_ICON_UNFOCUS.put(2, R.drawable.sh_switch_wifi2);
        SIGNAL_ICON_UNFOCUS.put(3, R.drawable.sh_switch_wifi3);
        SIGNAL_ICON_UNFOCUS.put(4, R.drawable.sh_switch_wifi4);

        SIGNAL_ICON_FOCUS.put(1, R.drawable.sh_switch_wifi1_focus);
        SIGNAL_ICON_FOCUS.put(2, R.drawable.sh_switch_wifi2_focus);
        SIGNAL_ICON_FOCUS.put(3, R.drawable.sh_switch_wifi3_focus);
        SIGNAL_ICON_FOCUS.put(4, R.drawable.sh_switch_wifi4_focus);
    }

    public SwitchWiFiView(Context context) {
        super(context);
        mContext = context;
        setBackground(new DialogBg());
    }

    private void initView() {
        TextView title = new TextView(mContext);
        title.setText(mContext.getString(R.string.apconfig_switchwifi_title));
        title.setTextSize(Util.Dpi(36));
        title.setTextColor(Color.WHITE);
        title.getPaint().setFakeBoldText(true);
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        params.topMargin = Util.Div(37);
        addView(title, params);

        TextView titleTip = new TextView(mContext);
        titleTip.setText(mContext.getString(R.string.apconfig_switchwifi_title_tip));
        titleTip.setTextSize(Util.Dpi(32));
        titleTip.setTextColor(Color.parseColor("#aaFFFFFF"));
        params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        params.topMargin = Util.Div(85);
        addView(titleTip, params);

        ScrollView scrollView = new ScrollView(mContext);
        scrollView.setVerticalScrollBarEnabled(false);
        params = new LayoutParams(Util.Div(580), Util.Div(640));
        params.gravity = Gravity.CENTER_HORIZONTAL;
        params.topMargin = Util.Div(140);
        addView(scrollView, params);

        listLayout = new LinearLayout(mContext);
        listLayout.setOrientation(LinearLayout.VERTICAL);
        params = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        scrollView.addView(listLayout, params);
    }

    private void createWifiItemView(final SwitchWifiInfo wifiInfo) {
        SSIDItem wifiItemView = new SSIDItem(mContext);
        wifiItemView.setPadding(Util.Div(20), 0, Util.Div(20), 0);
        wifiItemView.setFocusable(true);
        wifiItemView.refresh(wifiInfo);
        ViewGroup.LayoutParams params = new LayoutParams(Util.Div(580), Util.Div(90));
        listLayout.addView(wifiItemView, params);
    }

    private void refreshIcon(boolean isFocus, SparseIntArray focusIcons, SparseIntArray unfocusIcons, int value, View view) {
        int res;
        if (isFocus) {
            res = focusIcons.get(value);
        } else {
            res = unfocusIcons.get(value);
        }
        view.setBackgroundResource(res);
    }

    private void createItemLine() {
        View line = new View(mContext);
        line.setBackgroundResource(R.drawable.sh_switch_line);
        listLayout.addView(line, new LayoutParams(Util.Div(580), Util.Div(1)));
    }

    public void setPresenter(IApConfigPresenter presenter) {
        mPresenter = presenter;
    }

    public void refreshUI(List<SwitchWifiInfo> wifiList) {
        try {
            showLoading(null);
            removeAllViews();
            initView();
            if (wifiList != null && wifiList.size() > 0) {
                for (SwitchWifiInfo wifiInfo : wifiList) {
                    createWifiItemView(wifiInfo);
                    createItemLine();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void showLoading(SwitchWifiInfo item) {
        if (listLayout == null || listLayout.getChildCount() <= 0) {
            return;
        }
        for (int i = 0; i < listLayout.getChildCount(); i++) {
            View view = listLayout.getChildAt(i);
            if (view != null && view instanceof SSIDItem) {
                SwitchWifiInfo info = ((SSIDItem) view).getData();
                if (item != null && item.raw != null && info.raw.BSSID.equals(item.raw.BSSID)) {
                    ((SSIDItem) view).showLoading();
                    ((SSIDItem) view).requestFocus();
                } else {
                    ((SSIDItem) view).hideLoading();
                }
            }
        }
    }

    public void setFocus() {
        if (listLayout != null && listLayout.getChildCount() > 0) {
            post(new Runnable() {
                @Override
                public void run() {
                    listLayout.getChildAt(0).requestFocus();
                }
            });
        }
    }

    public class SSIDItem extends FrameLayout {
        private TextView wifiTitle = null;
        private View lock = null;
        private View wifi = null;
        private SwitchWifiInfo wifiInfo = null;
        private View loading = null;
        private boolean isLoading = false;
        private ObjectAnimator animator = null;
        private CCFocusDrawable mFocusBg;

        public SSIDItem(Context context) {
            super(context);
            initView();
        }

        private void initView() {
            LinearLayout loadingAndTitleLayout = new LinearLayout(getContext());
            loadingAndTitleLayout.setOrientation(LinearLayout.HORIZONTAL);
            LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            params.gravity = Gravity.CENTER_VERTICAL;
            addView(loadingAndTitleLayout, params);

            mFocusBg = new CCFocusDrawable(getContext()).setRadius(Util.Div(10)).setBorderVisible(false).setSolidColor(Color.TRANSPARENT);
            setBackground(mFocusBg);

            loading = new View(getContext());
            LinearLayout.LayoutParams linearLayoutParams = new LinearLayout.LayoutParams(Util.Div(30), Util.Div(30));
            linearLayoutParams.gravity = Gravity.CENTER_VERTICAL;
            linearLayoutParams.rightMargin = Util.Div(4);
            loadingAndTitleLayout.addView(loading, linearLayoutParams);
            loading.setVisibility(GONE);

            wifiTitle = new TextView(getContext());
            wifiTitle.setTextSize(Util.Dpi(28));
            wifiTitle.setTextColor(Color.WHITE);
            wifiTitle.setSingleLine();
            wifiTitle.setMaxWidth(Util.Div(300));
            wifiTitle.setEllipsize(TextUtils.TruncateAt.END);
            linearLayoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            linearLayoutParams.gravity = Gravity.CENTER_VERTICAL;
            loadingAndTitleLayout.addView(wifiTitle, linearLayoutParams);

            lock = new View(getContext());
            params = new LayoutParams(Util.Div(30), Util.Div(30));
            params.gravity = Gravity.CENTER_VERTICAL;
            params.leftMargin = Util.Div(470);
            addView(lock, params);

            wifi = new View(getContext());
            params = new LayoutParams(Util.Div(30), Util.Div(30));
            params.gravity = Gravity.CENTER_VERTICAL;
            params.leftMargin = Util.Div(510);
            addView(wifi, params);

            setOnFocusChangeListener(new OnFocusChangeListener() {
                @Override
                public void onFocusChange(View v, boolean hasFocus) {
                    refreshIcon(hasFocus, LOCK_ICON_FOCUS, LOCK_ICON_UNFOCUS, wifiInfo.lockStatus, lock);
                    refreshIcon(hasFocus, SIGNAL_ICON_FOCUS, SIGNAL_ICON_UNFOCUS, wifiInfo.wifiSignal, wifi);
                    mFocusBg.setBorderVisible(hasFocus).setSolidColor(getResources().getColor(hasFocus ? R.color.white : R.color.translucent));
                    if (hasFocus) {
                        wifiTitle.setTextColor(Color.parseColor("#000000"));
                        if (loading != null && loading.getVisibility() == VISIBLE && isLoading) {
                            loading.setBackgroundResource(R.drawable.apconfig_wifi_loading_focus);
                        }
                    } else {
                        wifiTitle.setTextColor(Color.parseColor("#aaFFFFFF"));
                        if (loading != null && loading.getVisibility() == VISIBLE && isLoading) {
                            loading.setBackgroundResource(R.drawable.apconfig_wifi_loading_unfocus);
                        }
                    }
                    setPadding(Util.Div(20), 0, Util.Div(20), 0);
                }
            });
            setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    mPresenter.connectWifiClick(wifiInfo);
                }
            });
        }

        public void refresh(SwitchWifiInfo wifiInfo) {
            this.wifiInfo = wifiInfo;
            wifiTitle.setText(this.wifiInfo.wifiName);
            refreshIcon(false, LOCK_ICON_FOCUS, LOCK_ICON_UNFOCUS, this.wifiInfo.lockStatus, lock);
            refreshIcon(false, SIGNAL_ICON_FOCUS, SIGNAL_ICON_UNFOCUS, this.wifiInfo.wifiSignal, wifi);
        }

        public SwitchWifiInfo getData() {
            return wifiInfo;
        }

        public void showLoading() {
            isLoading = true;
            int res = isFocused() ? R.drawable.apconfig_wifi_loading_focus : R.drawable.apconfig_wifi_loading_unfocus;
            loading.setBackgroundResource(res);
            loading.setVisibility(VISIBLE);
            if (animator == null) {
                animator = ObjectAnimator.ofFloat(loading, "rotation", 0, 360f);
                animator.setInterpolator(null);
                animator.setRepeatCount(ValueAnimator.INFINITE);
                animator.setDuration(1000);
            }
            animator.start();
        }

        public void hideLoading() {
            isLoading = false;
            if (animator != null) {
                animator.cancel();
            }
            if (loading != null) {
                loading.setVisibility(GONE);
            }
        }
    }
}
