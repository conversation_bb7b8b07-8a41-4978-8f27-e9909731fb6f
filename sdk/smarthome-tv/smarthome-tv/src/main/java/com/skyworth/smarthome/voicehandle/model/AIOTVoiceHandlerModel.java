package com.skyworth.smarthome.voicehandle.model;

import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.text.TextUtils;
import android.util.Log;

import com.alibaba.fastjson.JSONObject;
import com.coocaa.app.core.app.AppCoreApplication;
import com.coocaa.app.core.http.HttpServiceManager;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.account.IAppAccountManager;
import com.skyworth.smarthome.common.bean.BaiduResultBean;
import com.skyworth.smarthome.common.http.SmartHomeVoiceHandleHttpService;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.util.LogUtil;
import com.skyworth.smarthome.common.util.SmartHomeServiceManager;
import com.skyworth.smarthome.common.util.SystemProperty;
import com.skyworth.smarthome.common.util.ThreadManager;
import com.skyworth.smarthome.voicehandle.SmartHomeAI;
import com.skyworth.smarthome.voicehandle.VoiceHandleReceiver;
import com.smarthome.common.utils.EmptyUtils;

import java.util.Arrays;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * Describe:
 * Created by AwenZeng on 2019/8/20
 */
public class AIOTVoiceHandlerModel implements IAIOTVoiceHandleModel,IVoiceHandlerModel{
    private Context mContext;
    private VoiceHandleReceiver mVoiceHandleReceiver;
    private static final String VOICE_CMD_DEVICE_CONTROL = "45";//设备控制指令
    private static final String VOICE_CMD_DOOR_CONTROL = "58";//门锁控制
    private static final String VOICE_CMD_TV_CONTROL = "49";//电视TV控制指令，走SSE通道（控制pad本身）

    private static final String CONTROL_TV_KEYCODE = "KEYCODE";//控制TV-按键
    private static final String CONTROL_TV_POW_S = "POW_S";//控制TV-电源
    private static final String CONTROL_TV_VOL = "VOL";//控制TV-VOL
    private static final String CONTROL_TV_MUTE = "MUTE";//控制TV-音量
    private static final String CONTROL_TV_IRCTL = "IRCTL";//控制TV-红外控制
    private static final String CONTROL_TV_STD_S = "STD_S";//控制TV-STD_S
    private static final String CONTROL_TV_STD_R = "STD_R";//控制TV-STD_R

    private static final String CMD_VOICE_CONTROL = "DuerOS.ConnectedHome.Control";//百度控制指令
    private static final String TAG = "voicecmd";

    public AIOTVoiceHandlerModel() {
        this.mContext = SmartHomeTvLib.getContext();
        mVoiceHandleReceiver = new VoiceHandleReceiver();
    }


    @Override
    public void registerVoiceHandleReceiver() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(VoiceHandleReceiver.ACTION_SMARTHOME_VOICEHANDLER);
        intentFilter.addAction(VoiceHandleReceiver.ACTION_SRTNJ_VOICE_OUTCMD);
        intentFilter.addAction(VoiceHandleReceiver.ACTION_SMARTHOME_HELPHANDLER);
        mContext.registerReceiver(mVoiceHandleReceiver, intentFilter);
    }

    @Override
    public void unRegisterVoiceHandleReceiver() {
        try {
            mContext.unregisterReceiver(mVoiceHandleReceiver);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void handleCmd(final Intent intent) {
        AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                LogUtil.androidLog(TAG, "voicehandle start");
                final String voicecmd = intent.getStringExtra("voicecmd");
                LogUtil.androidLog(TAG, "voicehandle voicecmd:" + voicecmd);
                //透传后台接口
                JSONObject jsonObject = JSONObject.parseObject(voicecmd);
                String command = jsonObject.getString("command");
                LogUtil.androidLog(TAG, "voicehandle command:" + command);
                if (!TextUtils.isEmpty(command)) {
                    if (command.equals(VOICE_CMD_DEVICE_CONTROL) || command.equals(VOICE_CMD_DOOR_CONTROL)) {
                        if (IAppAccountManager.INSTANCE.hasLogin(true)) {
                            if (AppData.getInstance().isServiceInit()) {
                                voiceCommandExecute(voicecmd);
                            } else {
                                ThreadManager.getInstance().ioThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        voiceCommandExecute(voicecmd);
                                    }
                                }, 5000);
                            }
                        } else {
                            String detail = jsonObject.getString("detail");
                            if (!isCustomXiaoWeiAiTerms(detail)) {
                                IAppAccountManager.INSTANCE.checkLogin(true);
                            } else {
                                SmartHomeAI.playVoiceTTS(mContext, "不支持的操作");
                            }
                        }
                    } else if (command.equals(VOICE_CMD_TV_CONTROL)) {
                        String detail = jsonObject.getString("detail");
                        LogUtil.androidLog(TAG, "voicehandle 49 detail:" + detail);
                        try {
                            JSONObject jsonObject1 = JSONObject.parseObject(detail);
                            String paramsData = jsonObject1.getString("parameter");
                            LogUtil.androidLog(TAG, "voicehandle paramsData:" + paramsData);

                            JSONObject jsonObject2 = JSONObject.parseObject(paramsData);
                            String payLoadData = jsonObject2.getString("payload");
                            LogUtil.androidLog(TAG, "voicehandle payLoadData:" + payLoadData);

                            JSONObject jsonObject3 = JSONObject.parseObject(payLoadData);
                            String requestBodyData = jsonObject3.getString("requestBody");
                            LogUtil.androidLog(TAG, "voicehandle requestBodyData:" + requestBodyData);

                            JSONObject jsonObject4 = JSONObject.parseObject(requestBodyData);
                            String name = jsonObject4.getJSONObject("header").getString("name");
                            String rpayloadData = jsonObject4.getString("payload");
                            LogUtil.androidLog(TAG, "voicehandle rpayloadData:" + rpayloadData);
                            SmartHomeAI.ReportedData data = JSONObject.parseObject(rpayloadData, SmartHomeAI.ReportedData.class);
                            controlTV(data);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            return Unit.INSTANCE;
        }
    });
}

    /**
     * 是否小维AI自定义术语
     *
     * @param data
     * @return
     */
    private boolean isCustomXiaoWeiAiTerms(String data) {
        try {
            JSONObject jsonObject1 = JSONObject.parseObject(data);
            String paramsData = jsonObject1.getString("parameter");

            if (EmptyUtils.isEmpty(paramsData)) {
                return false;
            }

            JSONObject jsonObject2 = JSONObject.parseObject(paramsData);
            JSONObject payloadObject = jsonObject2.getJSONObject("payload");

            String requestBody = payloadObject.getString("requestBody");

            JSONObject jsonObject4 = JSONObject.parseObject(requestBody);

            String namespace = jsonObject4.getJSONObject("header").getString("namespace");
            String name = jsonObject4.getJSONObject("header").getString("name");

            if (namespace.equals(CMD_VOICE_CONTROL) && EmptyUtils.isNotEmpty(name)) {
                return Arrays.asList(AppConstants.BAIDU_REQUESTS).contains(name);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public void handleData(String data) {
        try {
            LogUtil.androidLog("TVDeviceControlModel handleData:" + data);
            JSONObject jsonObject = JSONObject.parseObject(data);
            JSONObject jsonObject1 = jsonObject.getJSONObject("payload");
            JSONObject jsonObject2 = jsonObject1.getJSONObject("state");
            JSONObject jsonObject3 = jsonObject2.getJSONObject("desired");
            SmartHomeAI.ReportedData reportedData = new SmartHomeAI.ReportedData();
            Object[] temps = jsonObject3.keySet().toArray();
            for (Object item : temps) {
                reportedData.name = (String) item;
                reportedData.value = jsonObject3.getString(reportedData.name);
                controlTV(reportedData);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void voiceCommandExecute(String data) {
        LogUtil.androidLog(TAG, "voiceCommandExecute");
        JSONObject jsonObject = JSONObject.parseObject(data);
        String detail = jsonObject.getString("detail");
        String command = jsonObject.getString("command");
        JSONObject tempJsonObject = JSONObject.parseObject(detail);
        tempJsonObject.put("command", command);
        detail = tempJsonObject.toJSONString();
        BaiduResultBean result = HttpServiceManager.Companion.call(SmartHomeVoiceHandleHttpService.SERVICE.voiceCommandExecute(detail));
        if (EmptyUtils.isNotEmpty(result) && EmptyUtils.isNotEmpty(result.code) && result.code.equals("0")) {
            LogUtil.androidLog(TAG, "voicehandle success");
            String baiduResult = result.baidu_result;
            LogUtil.androidLog(TAG, "voicehandle baiduResult:" + baiduResult);
            if (com.swaiot.aiotlib.common.util.EmptyUtils.isNotEmpty(baiduResult)) {
                SmartHomeAI.reportVoiceResult(mContext, baiduResult);
            }
        } else {
            LogUtil.androidLog(TAG, "voicehandle failed");
        }
    }

    @Override
    public void controlTV(SmartHomeAI.ReportedData cData) {
        LogUtil.androidLog(TAG, "rosolveTV start");
        if (cData != null && !TextUtils.isEmpty(cData.name)) {
            LogUtil.androidLog(TAG, "rosolveTV name:" + cData.name);
            switch (cData.name) {
                case CONTROL_TV_KEYCODE:
                    if (EmptyUtils.isNotEmpty(cData.value)) {
                        int keycode = Integer.valueOf(cData.value);
                        LogUtil.androidLog(TAG, "rosolveTV keycode:" + keycode);
                        SmartHomeServiceManager.getManager().sendSkyKey(keycode);
                    }
                    break;
                case CONTROL_TV_POW_S:
                    if (EmptyUtils.isNotEmpty(cData.value)) {
                        int type = Integer.valueOf(cData.value);  //区分不了是开机还是关机
                        boolean isAISleep = SystemProperty.isAistandbymode();
                        int currentType = isAISleep ? 0 : 1;
                        LogUtil.androidLog(TAG, "type:" + type + " currenttype:" + currentType);
                        if (type != currentType) {
                            if (isAISleep) {
                                SmartHomeAI.respontTVStatus(SmartHomeTvLib.getContext(), "POW_S", 0x80, "1");
                            } else {
                                SmartHomeAI.respontTVStatus(SmartHomeTvLib.getContext(), "POW_S", 0x80, "0");
                            }
                            // if current state is AI sleep so we need set turn on screen(set power on)
                            SmartHomeServiceManager.getManager().setPower(isAISleep);
                        }

                    }
                    break;
                case CONTROL_TV_VOL:
                    if (EmptyUtils.isNotEmpty(cData.value)) {
                        int volume = Integer.valueOf(cData.value);
                        LogUtil.androidLog(TAG, "rosolveTV volume:" + volume);
                        SmartHomeServiceManager.getManager().setVolume(volume);
                        AppCoreApplication.Companion.workerThread(1500, new Function0<Unit>() {
                            @Override
                            public Unit invoke() {
                                int volume = SmartHomeServiceManager.getManager().getVolume();
                                LogUtil.androidLog(TAG, "volume:" + volume);
                                SmartHomeAI.respontTVStatus(SmartHomeTvLib.getContext(), "VOL", 0x80, String.valueOf(volume));
                                return Unit.INSTANCE;
                            }
                        });
                    }
                    break;
                case CONTROL_TV_MUTE:
                    if (EmptyUtils.isNotEmpty(cData.value)) {
                        boolean mute = SmartHomeServiceManager.getManager().isVolumeMute();
                        int currentMute = mute ? 1 : 0;
                        int vMute = Integer.valueOf(cData.value);
                        LogUtil.androidLog(TAG, "rosolveTV currentMute:" + currentMute + " vMute:" + vMute);
                        if (currentMute != vMute) {
                            SmartHomeServiceManager.getManager().setVolumeMute();
                            AppCoreApplication.Companion.workerThread(1500, new Function0<Unit>() {
                                @Override
                                public Unit invoke() {
                                    boolean isMute = SmartHomeServiceManager.getManager().isVolumeMute();
                                    LogUtil.androidLog(TAG, "isMute:" + isMute);
                                    int mute = isMute ? 1 : 0;
                                    SmartHomeAI.respontTVStatus(SmartHomeTvLib.getContext(), "MUTE", 0x80, String.valueOf(mute));
                                    return Unit.INSTANCE;
                                }
                            });
                        }
                    }
                    break;
                case CONTROL_TV_IRCTL:
                    String value = cData.value;
                    LogUtil.androidLog(TAG, "rosolveTV value:" + value);
                    SmartHomeAI.IRCTLData data = JSONObject.parseObject(value, SmartHomeAI.IRCTLData.class);
                    LogUtil.androidLog(TAG, "rosolveTV keyCode:" + data.KEY_CODE);
                    SmartHomeServiceManager.getManager().sendKeyCode(data.KEY_CODE, false);
                    SmartHomeAI.respontIRCTL(SmartHomeTvLib.getContext(), "IRCTL", 0x84, value);
                    break;
                case CONTROL_TV_STD_S:
                    try {
                        String stds_value = cData.value;
                        LogUtil.androidLog(TAG, "rosolveTV stds_value:" + stds_value);
                        SmartHomeAI.STD_SData stads_data = JSONObject.parseObject(stds_value, SmartHomeAI.STD_SData.class);
                        LogUtil.androidLog(TAG, "rosolveTV keyCode:" + stads_data.CODE);
                        if (stads_data.CODE == 0) {
                            SmartHomeAI.STD_RData std_rData = new SmartHomeAI.STD_RData();
                            std_rData.CODE = stads_data.CODE;
                            std_rData.DID = stads_data.DID;
                            std_rData.KEY_NUM = stads_data.KEY_NUM;
                            std_rData.MSGID = stads_data.MSGID;
                            //开始学习了
                            SmartHomeAI.STD_RData.STD_Result result = new SmartHomeAI.STD_RData.STD_Result();
                            result.KEY_CODE = new short[0];
                            std_rData.RESULT = result;
                            SmartHomeServiceManager.getManager().updateIRCTL(std_rData);
                            String reportData = JSONObject.toJSONString(std_rData);
                            LogUtil.androidLog(TAG, " start learninfrared reportData:" + reportData);
                            //触发设备开始学习
                            SmartHomeServiceManager.getManager().sendKeyCode(new int[0], true);
                            SmartHomeServiceManager.getManager().getLearnInfraredCallBack();
                            //正在学习,也需要上报
                            SmartHomeAI.respontIRCTL(SmartHomeTvLib.getContext(), "STD_R", 0x84, reportData);
                        } else {
                            SmartHomeServiceManager.getManager().updateIRCTL(null);
                        }
                    } catch (Exception e) {
                    }
                    break;
                case CONTROL_TV_STD_R:
                    SmartHomeAI.STD_RData learnData = SmartHomeServiceManager.getManager().getLearnData();
                    if (learnData == null) {
                        learnData = new SmartHomeAI.STD_RData();
                        learnData.CODE = 2;
                        SmartHomeAI.STD_RData.STD_Result result = new SmartHomeAI.STD_RData.STD_Result();
                        result.KEY_CODE = null;
                        learnData.RESULT = result;
                    }
                    String v = JSONObject.toJSONString(learnData);
                    LogUtil.androidLog(TAG, "STD_R value:" + v);
                    SmartHomeAI.respontIRCTL(SmartHomeTvLib.getContext(), "STD_R", 0x84, v);
                    break;
            }

        }
    }

    @Override
    public void deviceListForSync() {
        AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                BaiduResultBean result = HttpServiceManager.Companion.call(SmartHomeVoiceHandleHttpService.SERVICE.deviceListForSync());
                if (EmptyUtils.isNotEmpty(result) && EmptyUtils.isNotEmpty(result.code) && result.code.equals("0")) {
                    LogUtil.androidLog(TAG, "deviceListSync success");
                    String baiduResult = result.baidu_result;
                    LogUtil.androidLog(TAG, "deviceListSync baiduResult:" + baiduResult);
                    if (EmptyUtils.isNotEmpty(baiduResult)) {
                        SmartHomeAI.reportVoiceResult(mContext, baiduResult);
                    }
                } else {
                    LogUtil.androidLog(TAG, "deviceListSync failed");
                }
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void playTTS(String data) {
        if (EmptyUtils.isNotEmpty(data)) {
            JSONObject jsonObject = JSONObject.parseObject(data);
            SmartHomeAI.playVoiceTTS(mContext, jsonObject.getString("tts_msg"));
        }
    }
}
