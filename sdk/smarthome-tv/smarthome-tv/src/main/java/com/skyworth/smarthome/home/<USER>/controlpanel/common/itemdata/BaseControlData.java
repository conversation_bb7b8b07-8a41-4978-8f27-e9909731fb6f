package com.skyworth.smarthome.home.smartdevice.controlpanel.common.itemdata;



import com.skyworth.smarthome.home.smartdevice.controlpanel.common.DependList;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;

public abstract class BaseControlData implements Serializable {
    public String deviceId;
    public String type;
    public String title;
    public String data_field;
    public boolean transfer;
    public LinkedHashMap<String, String> values;
    public List<DependList.Depend> depend;
    public String click;
    public String default_value;
}
