package com.skyworth.smarthome.devices.apconfig.view;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.skyworth.smarthome.devices.apconfig.presenter.IApConfigPresenter;
import com.skyworth.smarthome.common.ui.DialogBg;
import com.skyworth.ui.api.widget.SimpleFocusDrawable;
import com.skyworth.util.Util;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/5/13 16:23.
 */
public class TextWithButtonView extends FrameLayout {
    private TextView mTip = null;
    private TextView mButton = null;
    private IApConfigPresenter presenter = null;
    private String tag = null;

    public TextWithButtonView(Context context, final IApConfigPresenter presenter) {
        super(context);
        this.presenter = presenter;

        setBackground(new DialogBg());

        mTip = new TextView(getContext());
        mTip.setTextSize(Util.Dpi(36));
        mTip.setTextColor(Color.WHITE);
        mTip.getPaint().setFakeBoldText(true);
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        layoutParams.topMargin = Util.Div(99);
        addView(mTip, layoutParams);

        mButton = new TextView(getContext());
        mButton.setFocusable(true);
        mButton.setFocusableInTouchMode(true);
        mButton.setTextSize(Util.Dpi(32));
        mButton.setTextColor(Color.parseColor("#000000"));
        SimpleFocusDrawable simpleFocusDrawable = new SimpleFocusDrawable(getContext()).setRadius(Util.Div(10));
        mButton.setBackground(simpleFocusDrawable);
        simpleFocusDrawable.setFocus(true);
        mButton.setPadding(0, 0, 0, 0);
        mButton.setGravity(Gravity.CENTER);
        mButton.getPaint().setFakeBoldText(true);
        mButton.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (presenter != null) {
                    presenter.loginClick(tag);
                }
            }
        });
        layoutParams = new LayoutParams(Util.Div(614), Util.Div(90));
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        layoutParams.topMargin = Util.Div(256);
        addView(mButton, layoutParams);
    }

    public void show(String tip, String button, String tag) {
        this.tag = tag;
        mTip.setText(tip);
        mButton.setText(button);
        post(new Runnable() {
            @Override
            public void run() {
                mButton.requestFocus();
            }
        });
    }
}
