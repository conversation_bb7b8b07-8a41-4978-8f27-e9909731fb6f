package com.skyworth.smarthome.common.bean;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Description: 副屏数据结构<br>
 * Created by wzh on 2019/3/9 11:47.
 */
public class SecondScreenData implements Serializable {
    public int type;  // 1:消息  2，警告  3，增减设备 4，查询设备列表  5，场景启动  6，设备状态变化
    public String title;  // 设备名称或者场景名称
    public String content;  //对应状态或者msg
    public List<String> tts;  //语音tts
    public ModifyData md;  //控制设备的状态信息

    public static class ModifyData implements Serializable {
        public String device_id;
        public String device_type;
        public String user_id;
        public String access_token;
        public Map<String, String> props;
    }
}
