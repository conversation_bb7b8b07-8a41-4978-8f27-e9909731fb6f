package com.skyworth.smarthome.common.http;

import com.coocaa.app.core.http.HttpServiceManager;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.bean.BaiduResultBean;
import com.skyworth.smarthome.common.bean.BaiduResultData;
import com.skyworth.smarthome.common.bean.ReportMediaBean;
import com.skyworth.smarthome.common.util.SystemProperty;
import com.smarthome.common.model.SmartBaseData;

import java.util.Map;

import okhttp3.RequestBody;
import retrofit2.Call;

/**
 * Created by <PERSON> on 2018/4/23.
 */

public class SmartHomeVoiceHandleHttpService extends HttpServiceManager<SmartHomeVoiceHandleHttpMethod> {

    public static final SmartHomeVoiceHandleHttpService SERVICE = new SmartHomeVoiceHandleHttpService();

    private SmartHomeVoiceHandleHttpService() {
        super(SmartHomeHttpConfig.getServer(SmartHomeHttpConfig.DEVICE_SERVER), SmartHomeHttpConfig.SMARTHOME_HEADER_LOADER);
    }

    @Override
    protected Class<SmartHomeVoiceHandleHttpMethod> getServiceClazz() {
        return SmartHomeVoiceHandleHttpMethod.class;
    }

    public Call<BaiduResultBean> voiceCommandExecute(String detail){
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        map.put("screen_id", AppData.getInstance().getScreenId());
        map.put("is_virtual","0");
        RequestBody body = RequestBody.create(okhttp3.MediaType.parse("text/plain; charset=utf-8"), detail);
        return getHttpService().voiceCommandExecute(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("screen_id"),
                map.get("is_virtual"),
                SmartHomeHttpConfig.getSign(map),body);
    }

    public Call<BaiduResultBean> deviceListForSync(){
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        map.put("screen_id",  AppData.getInstance().getScreenId());
        map.put("is_virtual","0");
        return getHttpService().deviceListForSync(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("screen_id"),
                map.get("is_virtual"),
                SmartHomeHttpConfig.getSign(map));
    }


    public Call<BaiduResultData> sendVoiceCommand(RequestBody body) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        String device_id = SystemProperty.getDeviceId();
        map.put("device_id", device_id);
        map.put("is_virtual","0");
        return getHttpsService().sendVoiceCommand(
                map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("device_id"),
                map.get("is_virtual"),
                SmartHomeHttpConfig.getSign(map),
                body);
    }

    public Call<SmartBaseData<String>> reportMedia(ReportMediaBean body) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        String device_id = SystemProperty.getDeviceId();
        map.put("device_id", device_id);
        return getHttpsService().reportMedia(
                map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("device_id"),
                SmartHomeHttpConfig.getSign(map),
                body);
    }

    public Call<BaiduResultData> deviceListSync() {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        String device_id = SystemProperty.getDeviceId();
        map.put("device_id", device_id);
        map.put("is_virtual","0");
        return getHttpsService().deviceListSync(
                map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("device_id"),
                map.get("is_virtual"),
                SmartHomeHttpConfig.getSign(map));
    }

}
