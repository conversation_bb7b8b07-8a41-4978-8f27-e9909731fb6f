package com.skyworth.smarthome.common.util;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.util.Log;

import java.io.IOException;
import java.util.List;

import static android.content.Context.WIFI_SERVICE;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/9/18 10:48.
 */
public class NetworkUtils {
    private static final String PING_HOST = "www.baidu.com";

    public static boolean isCurrentWifi24G(Context context) {
        int frequency = getCurrentWifiFrequency(context);
        return is24G(frequency);
    }

    public static boolean is24G(int frequency) {
        return frequency > 2400 && frequency < 2500;
    }

    public static int getCurrentWifiFrequency(Context context) {
        int frequency = 0;
        WifiManager wm = (WifiManager) context.getApplicationContext().getSystemService(WIFI_SERVICE);
        if (wm != null) {
            WifiInfo currentWifiInfo = wm.getConnectionInfo();
            if (currentWifiInfo != null) {
                for (ScanResult scanResult : wm.getScanResults()) {
                    if (scanResult.BSSID.equals(currentWifiInfo.getBSSID())) {
                        frequency = scanResult.frequency;
                        break;
                    }
                }
            }
        }
        return frequency;
    }

    public static boolean isWifiAvailable(Context context) {
        if (context != null) {
            ConnectivityManager mConnectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            if (mConnectivityManager == null) {
                return false;
            }
            NetworkInfo mWiFiNetworkInfo = mConnectivityManager.getNetworkInfo(ConnectivityManager.TYPE_WIFI);
            if (mWiFiNetworkInfo != null) {
                return mWiFiNetworkInfo.isAvailable();
            }
        }
        return false;
    }

    public static String getCurrentWifiSSID(Context context) {
        WifiManager wifiManager = getWifiManager(context);
        if (wifiManager == null) {
            return null;
        }
        WifiInfo info = wifiManager.getConnectionInfo();
        String ssid = info.getSSID();
        if (ssid.startsWith("\"") && ssid.endsWith("\"")) {
            ssid = ssid.substring(1, ssid.length() - 1);
        }
        return ssid;
    }

    public static String getCurrentWifiBSSID(Context context) {
        WifiManager wifiManager = getWifiManager(context);
        if (wifiManager == null) {
            return "";
        }
        WifiInfo info = wifiManager.getConnectionInfo();
        return info.getBSSID() == null ? "" : info.getBSSID();
    }

    public static String forgetSSID(Context context, String ssid) {
        List<WifiConfiguration> configurations = NetworkUtils.getConfiguredNetworks(context);
        if (configurations == null || configurations.size() <= 0) {
            return "";
        }
        for (WifiConfiguration configuration : configurations) {
            if (configuration == null || configuration.SSID == null) {
                continue;
            }
            if (configuration.SSID.replace("\"", "").equals(ssid)) {
                NetworkUtils.removeNetwork(context, configuration.networkId);
                return ssid;
            }
        }
        return "";
    }

    public static void removeNetwork(Context context, int netWorkId) {
        WifiManager wifiManager = getWifiManager(context);
        if (wifiManager != null) {
            wifiManager.removeNetwork(netWorkId);
        }
    }

    public static List<WifiConfiguration> getConfiguredNetworks(Context context) {
        WifiManager wifiManager = getWifiManager(context);
        if (wifiManager != null) {
            return wifiManager.getConfiguredNetworks();
        }
        return null;
    }

    public static WifiManager getWifiManager(Context context) {
        return (WifiManager) context.getApplicationContext().getSystemService(WIFI_SERVICE);
    }

    public static boolean isEthernetConnect(Context context) {
        ConnectivityManager connectMgr = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectMgr == null) {
            return false;
        }
        NetworkInfo ethNetInfo = connectMgr.getNetworkInfo(ConnectivityManager.TYPE_ETHERNET);
        return ethNetInfo != null && ethNetInfo.isConnected();
    }


    public static boolean checkNetWorkCanUse() {
        try {
            Process process = Runtime.getRuntime().exec("/system/bin/ping -c 1 -w 10 " + PING_HOST);
            int status = process.waitFor();
            Log.i("networkutils", "checkNetWorkCanUse: " + status);
            if (status == 0) {
                return true;
            } else {
                return false;
            }
        } catch (IOException e) {
            e.printStackTrace();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return false;
    }
}
