package com.skyworth.smarthome.common.base;

import android.os.Bundle;
import android.support.v4.app.FragmentActivity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;


import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.util.DialogLauncherUtil;
import com.smarthome.common.sal.SalImpl;

import static com.skyworth.smarthome.SmartHomeTvLib.mContext;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2020/6/3.
 */

public class BaseActivity extends FragmentActivity {

    private FrameLayout mMainLayout;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (SalImpl.getSAL(mContext).getDeviceChip().equals("9TA23")&&SalImpl.getSAL(mContext).getDeviceModel().equals("LB8")) {
            getWindow().setBackgroundDrawableResource(R.drawable.person_background_black);
        } else {
            getWindow().setBackgroundDrawableResource(R.drawable.person_center_background);
        }
        mMainLayout = new FrameLayout(this);
    }


    @Override
    public void setContentView(View view) {
        mMainLayout.addView(view);
        super.setContentView(mMainLayout);
    }

    @Override
    public void setContentView(View view, ViewGroup.LayoutParams params) {
        mMainLayout.addView(view, params);
        super.setContentView(mMainLayout, params);
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
        //页面不可见的时候，关掉所有弹框
        DialogLauncherUtil.dismissOtherDialog("");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }
}
