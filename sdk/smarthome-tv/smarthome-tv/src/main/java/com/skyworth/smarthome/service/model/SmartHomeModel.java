package com.skyworth.smarthome.service.model;

import android.util.Log;

import com.alibaba.fastjson.JSONObject;
import com.coocaa.app.core.app.AppCoreApplication;
import com.coocaa.app.core.http.HttpServiceManager;
import com.skyworth.framework.skysdk.ccos.CcosProperty;
import com.skyworth.smarthome.common.event.DeviceNameChangeEvent;
import com.skyworth.smarthome.service.push.local.DeviceDataPushUtil;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.common.bean.DeviceControlData;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.bean.ReportMediaBean;
import com.skyworth.smarthome.common.bean.ThridAccountHttpBean;
import com.skyworth.smarthome.common.event.DevicesStatusChangeEvent;
import com.skyworth.smarthome.common.event.DiscoverNearDeviceEvent;
import com.skyworth.smarthome.common.event.RefreshScanNetworkDeviceEvent;
import com.skyworth.smarthome.common.http.SmartDevicesHttpService;
import com.skyworth.smarthome.common.http.SmartHomeVoiceHandleHttpService;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.model.UserInfo;
import com.skyworth.smarthome.common.util.CloneObjectUtils;
import com.skyworth.smarthome.common.util.DataCacheUtil;
import com.skyworth.smarthome.common.util.DateUtil;
import com.skyworth.smarthome.common.util.LogUtil;
import com.skyworth.smarthome.common.util.SystemProperty;
import com.skyworth.smarthome.common.util.ThreadManager;
import com.smarthome.common.dataer.DataHelpInfo;
import com.smarthome.common.dataer.LogSDK;
import com.smarthome.common.model.SmartBaseData;
import com.smarthome.common.sal.SalImpl;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XToast;
import com.swaiot.aiotlib.AiotLibSDK;
import com.swaiot.aiotlib.common.base.IResult;
import com.swaiot.aiotlib.common.entity.DeviceBean;
import com.swaiot.aiotlib.common.entity.DiscoverNetworkDevice;
import com.swaiot.aiotlib.common.entity.FamilyBean;
import com.swaiot.aiotlib.common.entity.SceneBean;
import com.swaiot.aiotlib.device.IDevice;
import com.swaiot.aiotlib.family.IFamily;
import com.swaiot.aiotlib.scene.IScene;
import com.tianci.framework.player.SkyPlayerItem;
import com.tianci.framework.player.utils.SkyPlayerUtils;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import retrofit2.Call;

/**
 * 智慧家庭主要Model
 * Created by AwenZeng on 2019/2/1.
 */

public class SmartHomeModel implements ISmartHomeModel {

    public final static String LOCAL_CONTROL = "local_control";
    private static byte[] lock = new byte[0];

    @Override
    public void getSmartDeviceList(final String familyId) {
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                try {
                    AiotLibSDK.getDefault().getDeviceImpl().getDeviceList(familyId, new IDevice.IDevicesCallBack() {
                        @Override
                        public void onDevices(int code, String msg, List<DeviceBean> list) {
                            if (code == 0) {
                                List<DeviceInfo> deviceInfoList = new ArrayList<>();
                                for (DeviceBean item : list) {
                                    DeviceInfo deviceInfo = new DeviceInfo();
                                    deviceInfo.copy(item);
                                    deviceInfoList.add(deviceInfo);
                                }
                                AppData.getInstance().setDeviceListFromServer(CloneObjectUtils.cloneObject(list));
                                AppData.getInstance().setDeviceInfoList(sortDeviceList(deviceInfoList));
                                //添加附近未配网或未绑定的设备
                                SmartDeviceDataModel.INSTANCE.getNearNetworkDeviceList();
                                DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.DEVICE_LIST, "");

                                // 获取到设备列表之后, 更新一下本机名称
                                EventBus.getDefault().post(new DeviceNameChangeEvent());
                            } else {
                                LogUtil.androidLog("获取设备列表失败");
                                AppData.getInstance().setDeviceInfoList(null);
                                DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.DEVICE_LIST, "");
                            }

                        }
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        });
    }

    /**
     * 设备列表排序，本机电视排在最前面
     *
     * @return
     */
    @Override
    public List<DeviceInfo> sortDeviceList(List<DeviceInfo> device_list) {
        List<DeviceInfo> deviceList = null;
        try {
            synchronized (lock){
                Iterator<DeviceInfo> it = device_list.iterator();
                String device_id = SystemProperty.getDeviceId();
                DeviceInfo temp = null;
//                while (it.hasNext()) {
//                    DeviceInfo item = it.next();
//                    if (EmptyUtils.isNotEmpty(item.device_id) && EmptyUtils.isNotEmpty(device_id)) {
//                        if (item.device_id.equals(device_id)) {
//                            it.remove();
//                            temp = item;
//                            break;
//                        }
//                    }
//                }
                if (EmptyUtils.isNotEmpty(temp) && SystemProperty.isInfraredDevice()) {
                    temp.is_infrared = true;
                    temp.voice_tips.clear();
                    temp.voice_tips.add("万能遥控");
                    temp.voice_tips.add("打开万能遥控");
                    temp.voice_tips.add("进入万能遥控");
                    temp.voice_tips.add("万能遥控器");
                    temp.voice_tips.add("打开万能遥控器");
                    temp.voice_tips.add("进入万能遥控器");
                    temp.voice_tips.add("红外遥控");
                    temp.voice_tips.add("打开红外遥控");
                    temp.voice_tips.add("进入红外遥控");
                    temp.voice_tips.add("红外遥控器");
                    temp.voice_tips.add("打开红外遥控器");
                    temp.voice_tips.add("进入红外遥控器");
                    temp.voice_tips.add("万能红外");
                    temp.voice_tips.add("打开万能红外");
                    temp.voice_tips.add("进入万能红外");
                    temp.voice_tips.add("万能红外遥控");
                    temp.voice_tips.add("打开万能红外遥控");
                    temp.voice_tips.add("进入万能红外遥控");
                    temp.voice_tips.add("万能红外遥控器");
                    temp.voice_tips.add("打开万能红外遥控器");
                    temp.voice_tips.add("进入万能红外遥控器");
                    device_list.add(0, temp);
                } else {//新设备排序
                    while (it.hasNext()) {
                        DeviceInfo item = it.next();
                        if (item.device_id.equals(device_id)) {
                            it.remove();
                            device_list.add(0, item);
                            break;
                        }
                        if (EmptyUtils.isNotEmpty(item) && item.is_new) {
                            it.remove();
                            device_list.add(1, item);
                            break;
                        }
                    }
                }
            }
            deviceList = device_list;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return deviceList;
    }

    @Override
    public void setCurrentFamily(final String familyID) {
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                AiotLibSDK.getDefault().getFamilyImp().setCurrentFamily(familyID, new IResult() {
                    @Override
                    public void onResult(int code, String msg) {
                        if (code != 0) {
                            XToast.showToast(SmartHomeTvLib.getContext(), "切换家庭失败");
                        }
                    }
                });
            }
        });
    }

    @Override
    public void getSceneList() {
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                try {
                    AiotLibSDK.getDefault().getSceneImp().getSceneList(new IScene.ISceneCallBack() {
                        @Override
                        public void onScenes(int code, String msg, List<SceneBean> list) {
                            if (code == 0) {
                                AppData.getInstance().setSceneList(sortSceneList(list));
                                DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.SCENE_LIST, "");
                            } else {
                                LogUtil.androidLog("获取场景列表失败");
                                AppData.getInstance().setSceneList(null);
                                DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.SCENE_LIST, "");
                            }

                        }
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        });
    }

    @Override
    public List<SceneBean> sortSceneList(List<SceneBean> scene_lsit) {
        List<SceneBean> sceneList = null;
        try {
            Iterator<SceneBean> it = scene_lsit.iterator();
            //新设备排序
            while (it.hasNext()) {
                SceneBean item = it.next();
                if (EmptyUtils.isNotEmpty(item) && item.is_new) {
                    it.remove();
                    scene_lsit.add(0, item);
                    break;
                }
            }
            sceneList = scene_lsit;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sceneList;
    }

    @Override
    public void getFamilyList() {
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                try {
                    AiotLibSDK.getDefault().getFamilyImp().getFamilyList(new IFamily.IFamiliesCallBack() {
                        @Override
                        public void onFamilies(int code, String msg, List<FamilyBean> list) {
                            if (code == 0) {
                                AppData.getInstance().setFamilyList(list);
                                DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.FAMILY_LIST, "");
                            } else {
                                LogUtil.androidLog("获取家庭列表失败");
                                AppData.getInstance().setFamilyList(null);
                                DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.FAMILY_LIST, "");
                            }
                        }
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        });
    }


    @Override
    public boolean handleDevice(String device_id, Map<String, Object> map) {
        SmartBaseData<String> baseData = null;
        try {
            Log.d("test", "handleDevice  map:" + JSONObject.toJSONString(map));
            baseData = HttpServiceManager.Companion.call(SmartDevicesHttpService.SERVICE.modefyDevice(device_id, map));
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (baseData != null) {
            if (baseData.code.equals("0")) return true;
        }
        return false;
    }


    @Override
    public boolean controlDevice(String device_id, Map<String, String> status) {
        Map<String, Object> body = new HashMap<>();
        body.put("device_id", device_id);
        body.put("status", status);
        SmartBaseData<DeviceControlData> baseData = HttpServiceManager.Companion.call(SmartDevicesHttpService.SERVICE.deviceControl(body));
        if (baseData != null) {
            if (baseData.code.equals("0")) {//修改成功
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("device_id", device_id);
                jsonObject.put("status", status);
                jsonObject.put(LOCAL_CONTROL, "localControl");
                DevicesStatusChangeEvent event = new DevicesStatusChangeEvent();
                event.setTypeString(jsonObject.toJSONString());
                EventBus.getDefault().post(event);
                reportDeviceControlData(device_id, "success");
                return true;
            } else {
                reportDeviceControlData(device_id, "fail");
            }
        }
        return false;
    }

    private void reportDeviceControlData(final String deviceId, final String result) {
        ThreadManager.getInstance().uiThread(new Runnable() {
            @Override
            public void run() {
                try {
                    DeviceInfo deviceInfo = ISmartDeviceDataModel.INSTANCE.getSmartDeviceInfo(deviceId);
                    if (EmptyUtils.isEmpty(deviceInfo))
                        return;
                    //数据统计
                    Map<String, String> map = new HashMap<>();
                    map.put("panel_control", "手动控制");
                    map.put("control_detail", DataHelpInfo.getInstance().getControlDetail());
                    map.put("device_id", deviceInfo.device_id);
                    map.put("device_brand", deviceInfo.product_brand);
                    map.put("device_name", deviceInfo.device_name);
                    map.put("control_result", result);
                    map.put("if_infrared", deviceInfo.is_infrared ? "yes" : "no");
                    LogSDK.submit(LogSDK.EVENT_ID_DEVICE_CONTROL, map);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

    }


    @Override
    public int getDeviceBindStatus() {
        try {
            String device_id = SystemProperty.getDeviceId();
            Map<String, Object> map = new HashMap<>();
            map.put("device_id", device_id);
            map.put("product_type_id", 1);
            map.put("product_brand_id", 1);
            List<Map<String, Object>> list = new ArrayList<>();
            list.add(map);
            Call<SmartBaseData<List<Map<String, String>>>> call = SmartDevicesHttpService.SERVICE.getBindStatus(list);
            SmartBaseData<List<Map<String, String>>> data = HttpServiceManager.Companion.call(call);
            if (data != null && data.data != null && data.data.size() > 0) {
                Map<String, String> mData = data.data.get(0);
                return Integer.valueOf(mData.get("bind_status"));  //设备绑定状态，1表示自己已绑定，2表示未绑定，3表示他人已绑定
            }
        } catch (Exception e) {
        }
        return -1;
    }

    @Override
    public SmartBaseData<List<Map<String, String>>> getDeviceBindStatus(List<DiscoverNetworkDevice> list) {
        try {
            if (EmptyUtils.isEmpty(list)) return null;
            List<Map<String, Object>> mapList = new ArrayList<>();
            for (DiscoverNetworkDevice item : list) {
                Map<String, Object> map = new HashMap<>();
                map.put("device_id", item.device_id);
                map.put("product_type_id", Integer.parseInt(item.product_type_id));
                map.put("product_brand_id", Integer.parseInt(item.product_brand_id));
                mapList.add(map);
            }
            Call<SmartBaseData<List<Map<String, String>>>> call = SmartDevicesHttpService.SERVICE.getBindStatus(mapList);
            SmartBaseData<List<Map<String, String>>> data = HttpServiceManager.Companion.call(call);
            if (data != null && data.data != null && data.data.size() > 0) {
                List<DiscoverNetworkDevice> devices = JSONObject.parseArray(JSONObject.toJSONString(data.data), DiscoverNetworkDevice.class);
                if (EmptyUtils.isNotEmpty(devices)) {
                    List<DiscoverNetworkDevice> tempDevicesList = assembleDiscoverNearDevice(devices, list);
                    if (EmptyUtils.isNotEmpty(tempDevicesList)) {
                        AppData.getInstance().setDiscoverUnbindDeviceList(tempDevicesList);
                        ThreadManager.getInstance().uiThread(new Runnable() {
                            @Override
                            public void run() {
                                EventBus.getDefault().post(new RefreshScanNetworkDeviceEvent());
                                EventBus.getDefault().post(new DiscoverNearDeviceEvent());
                            }
                        });
                    }
                }
                return data;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 过滤为未绑定的设备
     *
     * @param afterBindlist
     * @param beforeBindList
     * @return
     */
    private List<DiscoverNetworkDevice> assembleDiscoverNearDevice(List<DiscoverNetworkDevice> afterBindlist, List<DiscoverNetworkDevice> beforeBindList) {
        List<DiscoverNetworkDevice> temp = new ArrayList<>();
        for (DiscoverNetworkDevice item : afterBindlist) {
            for (DiscoverNetworkDevice item1 : beforeBindList) {
                if (item.device_id.equals(item1.device_id) && item.bind_status.equals("2")) {
                    item.iot_cloud = item1.iot_cloud;
                    item.mac_address = item1.mac_address;
                    item.module_chip = item1.module_chip;
                    item.product_brand_id = item1.product_brand_id;
                    item.product_model = item1.product_model;
                    item.product_type_id = item1.product_type_id;
                    item.protocol_version = item1.protocol_version;
                    temp.add(item);
                    break;
                }
            }
        }
        return temp;
    }

    @Override
    public String getAiotHomeStaus(String familyId) {
        Map<String, Object> body = new HashMap<>();
        body.put("family_id", familyId);
        SmartBaseData<String> baseData = HttpServiceManager.Companion.call(SmartDevicesHttpService.SERVICE.getAiotHomeStatus(familyId));
        if (EmptyUtils.isNotEmpty(baseData)) {
            if (EmptyUtils.isNotEmpty(baseData.data)) {
                AppData.getInstance().setAiotHomeStatus(baseData.data);
            }
            return baseData.data;
        }
        return null;
    }

    /**
     * 绑定电视本身
     *
     * @return
     */
    @Override
    public boolean bindSelfDevice() {
        try {
            String device_id = SystemProperty.getDeviceId();
            String protocol_version = "1.1.25";
            String module_chip = CcosProperty.getCcosDeviceInfo().skymodel + "-" + CcosProperty.getCcosDeviceInfo().skytype;
            String device_name = "";
            String product_model = getSelfTvName();
            String mac_address = "";
            Map<String, Object> map = new HashMap<>();
            map.put("device_id", device_id);
            map.put("protocol_version", protocol_version);
            map.put("module_chip", module_chip);
            map.put("device_name", device_name);
            map.put("product_type_id", 1);
            map.put("product_brand_id", 1);
            map.put("product_model", product_model);
            map.put("mac_address", mac_address);
            Call<SmartBaseData> call = SmartDevicesHttpService.SERVICE.forceBindDevice(map);
            SmartBaseData<String> data = HttpServiceManager.Companion.call(call);
            if (data != null) {
                int code = Integer.valueOf(data.code);
                if (code == 0) {
                    return true;
                }
                return false;
            }
        } catch (Exception e) {
        }
        return false;
    }

    private String getSelfTvName() {
        String hwVaule = SystemProperty.get("ro.skyconfig.hw.iot_rc", "0");
        String swVaule = SystemProperty.get("ro.skyconfig.sw.mesh", "0");

        if (hwVaule.equals("0") && swVaule.equals("0")) {
            return "SmartTV";
        } else if (hwVaule.equals("1") && swVaule.equals("0")) {
            return "SmartTV-AIot10";
        } else if (hwVaule.equals("0") && swVaule.equals("1")) {
            return "SmartTV-AIot20";
        } else if (hwVaule.equals("1") && swVaule.equals("1")) {
            return "SmartTV-AIot30";
        }
        return "SmartTV";
    }

    /**
     * 强制解绑设备
     *
     * @return
     */
    @Override
    public boolean forceUnBindDevices() {
        try {
            String device_id = SystemProperty.getDeviceId();
            Call<SmartBaseData> call = SmartDevicesHttpService.SERVICE.forceUnBindDevice(device_id);
            SmartBaseData<String> data = HttpServiceManager.Companion.call(call);
            if (data != null) {
                int code = Integer.valueOf(data.code);
                if (code == 0) {

                    return true;
                }
                return false;
            }
        } catch (Exception e) {
        }
        return false;
    }


    @Override
    public SmartBaseData controlScene(String cmd, String scene_id) {
        return HttpServiceManager.Companion.call(SmartDevicesHttpService.SERVICE.sceneControl(cmd, scene_id));
    }

    @Override
    public SmartBaseData setSceneAutoSwitch(String scene_id, boolean isEnabled) {
        return HttpServiceManager.Companion.call(SmartDevicesHttpService.SERVICE.setSceneAutoSwitch(scene_id, isEnabled));
    }

    @Override
    public void reportCurrentMedia(String targetDeviceID, SkyPlayerItem skyPlayerItem) {
        ReportMediaBean reportMediaBean = new ReportMediaBean();
        if (skyPlayerItem.type == SkyPlayerUtils.SkyPlayerType.VIDEO_LOCAL || skyPlayerItem.type == SkyPlayerUtils.SkyPlayerType.VIDEO_ONLINE || skyPlayerItem.type == SkyPlayerUtils.SkyPlayerType.LIVE) {
            reportMediaBean.video = new ReportMediaBean.MediaBean();
            reportMediaBean.video.source = skyPlayerItem.source;
            reportMediaBean.video.id = skyPlayerItem.id;
            reportMediaBean.video.child_id = skyPlayerItem.childId;
            reportMediaBean.video.url_type = skyPlayerItem.urlType;
            reportMediaBean.video.position = skyPlayerItem.position;
            reportMediaBean.video.name = skyPlayerItem.name;
        } else if (skyPlayerItem.type == SkyPlayerUtils.SkyPlayerType.MUSIC_LOCAL || skyPlayerItem.type == SkyPlayerUtils.SkyPlayerType.MUSIC_ONLINE) {
            reportMediaBean.audio = new ReportMediaBean.MediaBean();
            reportMediaBean.audio.source = skyPlayerItem.source;
            reportMediaBean.audio.id = skyPlayerItem.id;
            reportMediaBean.audio.child_id = skyPlayerItem.childId;
            reportMediaBean.audio.url_type = skyPlayerItem.urlType;
            reportMediaBean.audio.position = skyPlayerItem.position;
            reportMediaBean.audio.name = skyPlayerItem.name;
        }
        reportMediaBean.target_device_id = targetDeviceID;
        SmartBaseData<String> data = HttpServiceManager.Companion.call(SmartHomeVoiceHandleHttpService.SERVICE.reportMedia(reportMediaBean));
        if (data != null) {
            int code = Integer.valueOf(data.code);
            if (code == 0) {
                return;
            }
        }
    }


    @Override
    public List<ThridAccountHttpBean> getThirdAccountList(String supportAccounts) {
        SmartBaseData<List<ThridAccountHttpBean>> data = HttpServiceManager.Companion.call(SmartDevicesHttpService.SERVICE.getThirdAccountList(supportAccounts));
        if (data != null) {
            return data.data;
        }
        return null;
    }

    @Override
    public SmartBaseData unbindThirdAccount(String account_type) {
        return HttpServiceManager.Companion.call(SmartDevicesHttpService.SERVICE.unbindThirdAccount(account_type));
    }

    @Override
    public void commitLog(String eventName, Map<String, String> msg) {
    }

    @Override
    public void updateDeviceInfo() {
        try {
            String device_id = SystemProperty.getDeviceId();
            String device_name = SalImpl.getSAL(SmartHomeTvLib.getContext()).getDeviceName();
            Map<String, Object> map = new HashMap<>();
            map.put("device_id", device_id);
            map.put("device_name", device_name);
            Call<SmartBaseData> call = SmartDevicesHttpService.SERVICE.updateDeviceInfo(map);
            SmartBaseData<String> data = HttpServiceManager.Companion.call(call);
            if (data != null) {
                int code = Integer.valueOf(data.code);
                if (code == 0) {
                }
            }
        } catch (Exception e) {
        }
    }

    @Override
    public void updateSelfTvDeviceInfo() {
        AppCoreApplication.Companion.workerThread(1500, new Function0<Unit>() {
            @Override
            public Unit invoke() {
                String device_id = SystemProperty.getDeviceId();
                String deviceName = SalImpl.getSAL(SmartHomeTvLib.getContext()).getDeviceName();
                if (EmptyUtils.isNotEmpty(device_id) && EmptyUtils.isNotEmpty(deviceName)) {
                    DeviceBean deviceInfo = null;
                    List<DeviceBean> deviceInfoList = AppData.getInstance().getDeviceListFromServer();
                    if (EmptyUtils.isNotEmpty(deviceInfoList)) {
                        for (DeviceBean item : deviceInfoList) {
                            if (item.device_id.equals(device_id)) {
                                deviceInfo = item;
                                break;
                            }
                        }
                    }
                    if (EmptyUtils.isNotEmpty(deviceInfo) && !deviceInfo.device_name.equals(deviceName)) {
                        updateDeviceInfo();
                        LogUtil.androidLog("本机电视名字与后台列表名字不同");
                    }
                }
                return Unit.INSTANCE;
            }
        });
    }

    /**
     * 绑定电视自己本身到登录用户
     */
    @Override
    public void bindSelfTvDevice() {
        if (EmptyUtils.isEmpty(SystemProperty.getDeviceId()))
            return;
        AppCoreApplication.Companion.workerThread(500, new Function0<Unit>() {
            @Override
            public Unit invoke() {
                if (!DataCacheUtil.getInstance().getString(DataCacheUtil.KEY_SAVE_USER_ID, "").equals(UserInfo.getInstance().getUserID())) {
                    int status = INSTANCE.getDeviceBindStatus();
                    if (status != 1) {//1表示自己已绑定
                        if (status == 3) { //3表示被他人绑定
                            INSTANCE.forceUnBindDevices();
                        }
                        INSTANCE.bindSelfDevice();
                    }
                    String saveTime = DateUtil.date2String(System.currentTimeMillis(), DateUtil.DEFAULT_FORMAT_DATETIME);
                    DataCacheUtil.getInstance().putString(DataCacheUtil.KEY_SAVE_USER_ID_TIME, saveTime);
                    DataCacheUtil.getInstance().putString(DataCacheUtil.KEY_SAVE_USER_ID, UserInfo.getInstance().getUserID());
                }
                return Unit.INSTANCE;
            }
        });
    }

    /**
     * 加载设备列表和场景列表数据
     */
    @Override
    public void loadData() {
        AppCoreApplication.Companion.workerThread(500, new Function0<Unit>() {
            @Override
            public Unit invoke() {
                INSTANCE.getAiotHomeStaus("");
                return Unit.INSTANCE;
            }
        });
    }


    public void loadSceneList() {
        AppCoreApplication.Companion.workerThread(500, new Function0<Unit>() {
            @Override
            public Unit invoke() {
                return Unit.INSTANCE;
            }
        });
    }

}
