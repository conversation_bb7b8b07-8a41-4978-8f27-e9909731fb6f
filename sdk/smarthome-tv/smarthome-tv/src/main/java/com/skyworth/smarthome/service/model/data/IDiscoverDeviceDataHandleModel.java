package com.skyworth.smarthome.service.model.data;

import com.swaiot.aiotlib.common.entity.DiscoverNetworkDevice;

import java.util.List;

/**
 * Describe:
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/9/17
 */
public interface IDiscoverDeviceDataHandleModel {

    /**
     * 添加发现的附近的智能设备
     * @param discoverNetworkDevice
     */
    void addDiscoverDevice(DiscoverNetworkDevice discoverNetworkDevice);


    /**
     * 添加附近的设备到列表中
     */
    void addNearbyDeviceToDeviceList();

    /**
     * 删除发现的附近的智能设备
     * @param discoverNetworkDevice
     */
    boolean removeDiscoverDevice(DiscoverNetworkDevice discoverNetworkDevice);

    /**
     * 获取附近的智能设备（联网或未联网）
     * @return
     */
    List<DiscoverNetworkDevice> getNearNetworkDeviceList();

}
