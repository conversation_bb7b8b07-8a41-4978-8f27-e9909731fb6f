package com.skyworth.smarthome.service.binder;

import android.os.RemoteException;

import com.skyworth.smarthome.service.model.ISmartDeviceDataModel;
import com.skyworth.smarthome_tv.ISmartHomeExtraInfo;

/**
 * Describe:智慧家庭额外信息Binder
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/1/30
 */
public class SmartHomeExtraInfoBinder extends ISmartHomeExtraInfo.Stub {

    public final static String ACTION_AIOT_HOME_STATUS = "com.skyworth.smarthome.statusinfo";//智慧家庭数据提供Action
    public final static String KEY_AIOT_HOME_STAUS = "aiot_status_info";//key值

    @Override
    public String getSmartHomeExtraInfo() throws RemoteException {
        String aiotHomeStatus = ISmartDeviceDataModel.INSTANCE.getAiotHomeStatus("");
        return aiotHomeStatus;
    }
}
