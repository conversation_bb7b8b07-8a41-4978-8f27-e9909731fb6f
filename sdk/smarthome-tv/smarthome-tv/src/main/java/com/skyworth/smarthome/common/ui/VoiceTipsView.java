package com.skyworth.smarthome.common.ui;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.skyworth.smarthome.R;
import com.skyworth.util.Util;
import com.smarthome.common.utils.EmptyUtils;

import java.util.List;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/29
 */
public class VoiceTipsView extends LinearLayout {

    private MarqueeView mMarqueeView;

    public VoiceTipsView(Context context) {
        super(context);
        setOrientation(HORIZONTAL);
        setBackgroundResource(R.drawable.voice_tips_bg);

        ImageView imageView = new ImageView(context);
        imageView.setBackgroundResource(R.drawable.icon_xiaowei_ai);
        LayoutParams layoutParams = new LayoutParams(Util.Div(40), Util.Div(40));
        layoutParams.leftMargin = Util.Div(10);
        layoutParams.gravity = Gravity.CENTER_VERTICAL;
        addView(imageView,layoutParams);


        mMarqueeView = new MarqueeView(context);
        mMarqueeView.setTextSize(Util.Dpi(22));
        mMarqueeView.setTextColor(Color.parseColor("#FFFFFF"));
        layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.MATCH_PARENT);
        layoutParams.gravity = Gravity.CENTER_VERTICAL;
        layoutParams.leftMargin = Util.Div(10);
        layoutParams.rightMargin = Util.Div(20);
        addView(mMarqueeView,layoutParams);
    }

    /**
     * 开始播放列表
     * @param tipList
     */
    public void startWithList(List<String> tipList) {
        if(EmptyUtils.isNotEmpty(tipList)){
            mMarqueeView.startWithList(tipList);
        }
    }

    /**
     * 停止播放列表
     */
    public void stopFlipping(){
        mMarqueeView.stopFlipping();
    }
}
