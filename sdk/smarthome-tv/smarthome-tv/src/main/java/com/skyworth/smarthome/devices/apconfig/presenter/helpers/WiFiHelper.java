package com.skyworth.smarthome.devices.apconfig.presenter.helpers;


import android.content.Context;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiManager;
import android.text.TextUtils;
import android.util.Log;

import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.common.event.WifiConnectEvent;
import com.skyworth.smarthome.common.util.NetworkUtils;
import com.skyworth.smarthome.common.util.WiFiPassword;
import com.skyworth.smarthome.devices.apconfig.model.SwitchWifiInfo;
import com.smarthome.common.utils.XToast;
import com.swaiot.aiotlib.common.util.EmptyUtils;
import com.swaiot.aiotlib.device.apconfig.module.WifiModule;
import com.tianci.net.data.SkyWifiAPItem;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.skyworth.smarthome.devices.apconfig.ApConfigService.TAG;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/4/9 14:47.
 */
public class WiFiHelper {
    private WifiManager wifiManager = null;
    private WifiModule mWifiModule;
    private Map<String, String> currentWifiPassword = new HashMap<>();
    private Context mContext = null;

    public WiFiHelper(Context context) {
        mContext = context;
        wifiManager = NetworkUtils.getWifiManager(mContext);
        mWifiModule = new WifiModule(context,null);
    }

    public List<SwitchWifiInfo> get24GHzWifiList(String ignoreSSID) {
        if (!wifiManager.isWifiEnabled()) {
            wifiManager.setWifiEnabled(true);
            wifiManager.startScan();
        }
        List<ScanResult> totalList = wifiManager.getScanResults();
        int waitCount = 0;
        while (totalList.size() <= 0 && waitCount <= 2) {
            try {
                Thread.sleep(1000);
                totalList = wifiManager.getScanResults();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            waitCount++;
            Log.i(TAG, "get24GHzWifiList: sleep count " + waitCount);
        }
        List<WifiConfiguration> configuredNetworks = wifiManager.getConfiguredNetworks();
        List<SwitchWifiInfo> showList = new ArrayList<>();
        for (ScanResult scanResult : totalList) {
            if (filterWifiList(scanResult) && !scanResult.SSID.equals(ignoreSSID)) {
                SwitchWifiInfo info = new SwitchWifiInfo();
                info.raw = scanResult;
                info.wifiName = scanResult.SSID;
                info.wifiSignal = WifiManager.calculateSignalLevel(scanResult.level, 4) + 1;
                info.lockStatus = getLockInfo(scanResult, configuredNetworks);
                Log.i(TAG, "get24GHzWifiList: " + info.wifiName + " " + info.wifiSignal + " " + info.lockStatus);
                showList.add(info);
            }
        }
        if(EmptyUtils.isEmpty(showList)){
            XToast.showToast(SmartHomeTvLib.getContext(),"2.4GWifi列表获取失败,请重试");
        }
        Collections.sort(showList, new Comparator<SwitchWifiInfo>() {
            @Override
            public int compare(SwitchWifiInfo lhs, SwitchWifiInfo rhs) {
                switch (lhs.lockStatus) {
                    case 1:
                        switch (rhs.lockStatus) {
                            case 1:
                                return rhs.wifiSignal - lhs.wifiSignal;
                            case 2:
                                return 1;
                            case 3:
                                return 1;
                        }
                    case 2:
                        switch (rhs.lockStatus) {
                            case 1:
                                return -1;
                            case 2:
                                return rhs.wifiSignal - lhs.wifiSignal;
                            case 3:
                                return -1;
                        }
                    case 3:
                        switch (rhs.lockStatus) {
                            case 1:
                                return -1;
                            case 2:
                                return 1;
                            case 3:
                                return rhs.wifiSignal - lhs.wifiSignal;
                        }
                }
                return 0;
            }
        });
        List<SwitchWifiInfo> result = new ArrayList<>();
        for (SwitchWifiInfo info : showList) {
            if (!result.contains(info)) {
                result.add(info);
            }
        }
        return result;
    }

    private boolean filterWifiList(ScanResult scanResult) {
        if (is24GHzWifi(scanResult) && !TextUtils.isEmpty(scanResult.SSID) && !scanResult.SSID.equals(NetworkUtils.getCurrentWifiSSID(mContext))) {
            return true;
        }
        return false;
    }

    public boolean is24GHzWifi(ScanResult scanResult) {
        return (scanResult != null && NetworkUtils.is24G(scanResult.frequency));
    }

    public int getLockInfo(ScanResult scanResult, List<WifiConfiguration> configurations) {
        if (TextUtils.isEmpty(scanResult.capabilities)) {
            return 3;
        }
        String cap = scanResult.capabilities.toLowerCase();
        if (TextUtils.isEmpty(cap)) {
            return 3;
        }
        if (cap.contains("wpa") || cap.contains("wep")) {
            if (isWifiConfigured(scanResult, configurations)) {
                return 2;
            } else {
                return 1;
            }
        } else {
            return 3;
        }
    }

    public boolean isWifiConfigured(ScanResult scanResult, List<WifiConfiguration> configurations) {
        for (WifiConfiguration configuration : configurations) {
            if (scanResult.SSID.equals(configuration.SSID.replace("\"", ""))) {
                return true;
            }
        }
        return false;
    }

    public boolean canGetCurrentWifiPassword() {
        String ssid = NetworkUtils.getCurrentWifiSSID(mContext);
        if (currentWifiPassword.get(ssid) != null) {
            return true;
        }
        String password = WiFiPassword.getCurrentWifiPassword();
        Log.i(TAG, "canGetCurrentWifiPassword: from prop: " + WiFiPassword.hidePassword(password));
        if (TextUtils.isEmpty(password)) {
            Map<String, String> allPassword = WiFiPassword.getWifiPassWord();
            if (allPassword != null) {
                password = allPassword.get(ssid);
                Log.i(TAG, "canGetCurrentWifiPassword: from .conf: " + WiFiPassword.hidePassword(password));
            }
        }
        if (TextUtils.isEmpty(password)) {
            return false;
        } else {
            currentWifiPassword.put(ssid, password);
            return true;
        }
    }

    public boolean isCurrentWifiNeedPassword() {
        List<ScanResult> scanResult = wifiManager.getScanResults();
        for (ScanResult result : scanResult) {
            if (result.BSSID.equals(NetworkUtils.getCurrentWifiBSSID(mContext))) {
                if (TextUtils.isEmpty(result.capabilities)) {
                    return false;
                }
            }
        }
        return true;
    }

    public String getWifiPassword(String ssid) {
        String password = currentWifiPassword.get(ssid);
        return password == null ? "" : password;
    }

    public void saveWifiPassword(String ssid, String password) {
        currentWifiPassword.put(ssid, password);
    }

    public void connectWifiByDhcp(SwitchWifiInfo wifiInfo,WifiModule.WifiConnectCallback callback) {
        SkyWifiAPItem item = convertScanResultToSkyWifiAPItem(wifiInfo.raw, wifiInfo.lockStatus == 2);
        try {
            Log.i(TAG, "connectWifiByDhcp: " + wifiInfo.wifiName + " pwd: " + item.pwd);
            mWifiModule.connectWiFi(wifiInfo.wifiName, item.pwd, 20000, new WifiModule.WifiConnectCallback() {
                @Override
                public void onConnectFail(int code, String msg) {
                    Log.d(TAG, "onConnectFail() called with: code = [" + code + "], msg = [" + msg + "]");
                    if(callback!= null){
                        callback.onConnectFail(code,msg);
                    }else{
                        EventBus.getDefault().post(new WifiConnectEvent(false));
                    }
                }

                @Override
                public void onConnectOk() {
                    Log.d(TAG, "onConnectOk() called");
                    if(callback!= null){
                        callback.onConnectOk();
                    }else{
                        EventBus.getDefault().post(new WifiConnectEvent(true));
                    }
                }
            });
//            SmartHomeServiceManager.getManager().connectWifiByDhcp(item);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public SkyWifiAPItem convertScanResultToSkyWifiAPItem(ScanResult scanResult, boolean isConfigured) {
        SkyWifiAPItem item = new SkyWifiAPItem(scanResult.SSID, scanResult.BSSID, scanResult.capabilities, scanResult.level, scanResult.frequency);
        item.isConfig = isConfigured;
        String password = getWifiPassword(scanResult.SSID);
        if (password != null) {
            item.pwd = password;
        }
        return item;
    }
}
