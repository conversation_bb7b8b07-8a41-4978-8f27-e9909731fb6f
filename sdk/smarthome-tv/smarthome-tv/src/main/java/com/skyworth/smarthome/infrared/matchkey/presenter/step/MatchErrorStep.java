package com.skyworth.smarthome.infrared.matchkey.presenter.step;

import com.skyworth.smarthome.infrared.matchkey.presenter.IMatchKeyPresenter;

import static com.skyworth.smarthome.infrared.matchkey.presenter.IMatchKeyPresenterImpl.HANDLE_KEY_LEARN;
import static com.skyworth.smarthome.infrared.matchkey.presenter.IMatchKeyPresenterImpl.HANDLE_KEY_RE_MATCH;

/**
 * Description: <br>
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2019/4/29 18:32.
 */
public class MatchErrorStep extends IRConfigBaseStep<IMatchKeyPresenter> {
    public static final String STEP_TAG = "match_error";

    @Override
    public void create() {
        super.create();
    }

    @Override
    public void run() {
        super.run();
        presenter.showMatchErrorView();
    }

    @Override
    public boolean input(String msg, Object... params) {
        switch (msg) {
            case HANDLE_KEY_RE_MATCH:
                jumpTo(MatchReadyStep.STEP_TAG);
                return true;
            case HANDLE_KEY_LEARN:
                presenter.launchIRLearn();
        }
        return false;
    }

    @Override
    public String getTag() {
        return STEP_TAG;
    }

    @Override
    public void destroy() {
        super.destroy();
    }
}
