package com.skyworth.smarthome.infrared.electriclist.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.support.v7.widget.NewRecycleAdapter;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import android.support.annotation.NonNull;

import com.coocaa.app.core.utils.FuncKt;
import com.skyworth.smarthome.infrared.electriclist.model.ElectricBrandData;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.ui.DialogBg;
import com.skyworth.smarthome.common.ui.EmptyView;
import com.skyworth.smarthome.common.ui.LoadingView;
import com.skyworth.smarthome.infrared.electriclist.presenter.IElectricBrandPresenter;
import com.skyworth.smarthome.service.model.ISmartDeviceDataModel;
import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.ui.newrecycleview.NewRecycleAdapterItem;
import com.skyworth.ui.newrecycleview.NewRecycleLayout;
import com.skyworth.ui.newrecycleview.OnBoundaryListener;
import com.skyworth.ui.newrecycleview.OnItemClickListener;
import com.skyworth.ui.newrecycleview.OnItemFocusChangeListener;
import com.skyworth.util.Util;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XThemeUtils;

import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;


/**
 * Created by fc on 2019/4/26
 * Describe:
 */
public class ElectricBrandView extends FrameLayout implements IElectricBrandView, OnBoundaryListener, View.OnClickListener {
    private Context mContext;
    private LinearLayout mAddLayout;
    private ImageView mAddImg;
    private TextView mAddTv;
    private TextView mSubtitleView;
    private EmptyView emptyView;
    private LinearLayout mLayout;
    private String mId;
    private String mTypeName;
    private String mDeviceID;
    public List<String> mhotList;
    private LoadingView loadingView;
    private IElectricBrandPresenter mPrenter;
    private NewRecycleLayout<ElectricBrandData> mHotBrandListView = null;
    private NewRecycleAdapter<ElectricBrandData> mHotBrandAdapter = null;
    private NewRecycleLayout<String> mCharacterListView = null;
    private NewRecycleAdapter<String> mCharacterAdapter = null;
    private NewRecycleLayout<ElectricBrandData> mAllBrandListView = null;
    private NewRecycleAdapter<ElectricBrandData> mAllBrandAdapter = null;

    public ElectricBrandView(@NonNull Context context) {
        super(context);
        mContext = context;
    }

    @SuppressLint("ResourceType")
    private void initView() {
        setBackground(new DialogBg());
        mLayout = new LinearLayout(mContext);
        mLayout.setOrientation(LinearLayout.VERTICAL);
        LayoutParams layoutParams = new LayoutParams(Util.Div(660), Util.Div(810));
        addView(mLayout, layoutParams);

        TextView titleView = new TextView(mContext);
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        titleView.setTextSize(Util.Dpi(32));
        titleView.setTextColor(XThemeUtils.c_1a());
        params.topMargin = Util.Div(20);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        titleView.setText(mContext.getString(R.string.smart_home_select_electric_brand));
        addView(titleView, params);

        //请保持电器与红外电视靠近，相互之间没有遮挡
        mSubtitleView = new TextView(mContext);
        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        mSubtitleView.setTextSize(Util.Dpi(26));
        mSubtitleView.setTextColor(Color.parseColor("#9A9FA6"));
        lp.topMargin = Util.Div(75);
        lp.gravity = Gravity.CENTER_HORIZONTAL;
        mSubtitleView.setText(mContext.getString(R.string.smart_home_select_electric_brand_subtitle));
        mLayout.addView(mSubtitleView, lp);

        //热门品牌
        TextView hotBrandView = new TextView(mContext);
        hotBrandView.getPaint().setFakeBoldText(true);
        lp = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        hotBrandView.setTextSize(Util.Dpi(32));
        hotBrandView.setTextColor(XThemeUtils.c_1a());
        lp.topMargin = Util.Div(30 - 10);
        lp.leftMargin = Util.Div(30);
        hotBrandView.setText(mContext.getString(R.string.smart_home_select_electric_hot_rand_subtitle));
        mLayout.addView(hotBrandView, lp);

        mAddLayout = new LinearLayout(mContext);
        mAddLayout.setOrientation(LinearLayout.HORIZONTAL);
        mAddLayout.setFocusable(true);
        mAddLayout.setFocusableInTouchMode(true);
        mAddLayout.setBackground(new CCFocusDrawable(mContext).setRadius((float) Util.Div(50)).setBorderVisible(false).setSolidColor(Color.parseColor("#19CCCCCC")));
        mAddLayout.setOnClickListener(this);

        mAddImg = new ImageView(mContext);
        LinearLayout.LayoutParams imgParams = new LinearLayout.LayoutParams(Util.Div(16), Util.Div(16));
        imgParams.leftMargin = Util.Div(15);
        imgParams.gravity = Gravity.CENTER_VERTICAL;
        mAddImg.setImageResource(R.drawable.icon_manual_normal);
        mAddLayout.addView(mAddImg, imgParams);

        mAddTv = new TextView(mContext);
        mAddTv.setTextColor(Color.parseColor("#CDD2D8"));
        mAddTv.setTextSize(Util.Dpi(18));
        mAddTv.setText(mContext.getString(R.string.manual_study));
        LinearLayout.LayoutParams addPramas = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        addPramas.gravity = Gravity.CENTER_VERTICAL;
        addPramas.leftMargin = Util.Div(4);
        mAddLayout.addView(mAddTv, addPramas);

        layoutParams = new LayoutParams(Util.Div(124), Util.Div(42));
        layoutParams.topMargin = Util.Div(130);
        layoutParams.leftMargin = Util.Div(500);
        addView(mAddLayout, layoutParams);

        mAddLayout.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    mAddImg.setImageResource(R.drawable.icon_manual_focus);
                    mAddTv.setTextColor(Color.parseColor("#000000"));
                    mAddLayout.setBackground(new CCFocusDrawable(mContext).setRadius((float) Util.Div(50)).setSolidColor(Color.parseColor("#FFFFFF")));
                } else {
                    mAddImg.setImageResource(R.drawable.icon_manual_normal);
                    mAddTv.setTextColor(Color.parseColor("#CDD2D8"));
                    mAddLayout.setBackground(new CCFocusDrawable(mContext).setRadius((float) Util.Div(50)).setBorderVisible(false).setSolidColor(Color.parseColor("#19CCCCCC")));
                }
            }
        });


        mHotBrandListView = new NewRecycleLayout<>(mContext, 4);
        mHotBrandListView.setItemSpace(Util.Div(22), Util.Div(22));
        mHotBrandListView.setClipChildren(false);
        mHotBrandListView.setClipToPadding(false);
        mHotBrandListView.setmBoundaryListener(this);
        mHotBrandListView.setmItemFocusChangeListener(onItemFocusChangeListener);
        mHotBrandListView.setmItemClickListener(onItemClickListener);
//        mHotBrandListView.setPadding(0, 0, 0, Util.Div(22));
        lp = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, Util.Div(144));
        lp.leftMargin = Util.Div(10);
        mLayout.addView(mHotBrandListView, lp);

        LinearLayout allBrandLayout = new LinearLayout(mContext);
        allBrandLayout.setOrientation(LinearLayout.HORIZONTAL);
        lp = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        lp.topMargin = Util.Div(20);
        mLayout.addView(allBrandLayout, lp);

        mCharacterListView = new NewRecycleLayout<>(mContext, 1);
        mCharacterListView.setItemSpace(Util.Div(10), Util.Div(10));
        mCharacterListView.setClipChildren(false);
        mCharacterListView.setClipToPadding(false);
        mCharacterListView.setmBoundaryListener(this);
        mCharacterListView.setmItemFocusChangeListener(onItemFocusChangeListener);
//        mCharacterListView.setmItemClickListener(onItemClickListener);
//        mCharacterListView.setPadding(0, 0, 0, Util.Div(10));
        lp = new LinearLayout.LayoutParams(Util.Div(60 + 10), Util.Div(460));
        lp.leftMargin = Util.Div(20);
        allBrandLayout.addView(mCharacterListView, lp);

        mAllBrandListView = new NewRecycleLayout<>(mContext, 1);
        mAllBrandListView.setClipChildren(false);
        mAllBrandListView.setClipToPadding(false);
        mAllBrandListView.setmBoundaryListener(this);
        mAllBrandListView.setmItemFocusChangeListener(onItemFocusChangeListener);
        mAllBrandListView.setmItemClickListener(onItemClickListener);
//        mCharacterListView.setPadding(0, 0, 0, Util.Div(10));
        lp = new LinearLayout.LayoutParams(Util.Div(510), Util.Div(460));
        lp.leftMargin = Util.Div(20);
        allBrandLayout.addView(mAllBrandListView, lp);
    }


    @Override
    public void createView(Context context, IElectricBrandPresenter presenter) {
        mPrenter = presenter;
        initView();
    }


    @Override
    public void setReLoadParams(String deviceID,String type_id, String brand, List<String> hotList) {
        mDeviceID = deviceID;
        mId = type_id;
        mTypeName = brand;
        mhotList = hotList;
    }

    @Override
    public void showHotBrandList(List<ElectricBrandData> list) {
        if (mLayout != null)
            mLayout.setVisibility(VISIBLE);
        if (mAddLayout != null){
            mAddLayout.setVisibility(VISIBLE);
        }
        DeviceInfo deviceInfo = ISmartDeviceDataModel.INSTANCE.getSmartDeviceInfo(mDeviceID);
        if(EmptyUtils.isNotEmpty(deviceInfo)&&deviceInfo.product_type_id.equals("47")){
            mSubtitleView.setText(String.format(mContext.getString(R.string.smart_home_select_electric_brand_subtitle1),deviceInfo.device_name));
        }
        mHotBrandAdapter = new NewRecycleAdapter<ElectricBrandData>(list, 4) {
            @Override
            public NewRecycleAdapterItem<ElectricBrandData> onCreateItem(Object type) {
                return new ElectricBrandItem.HotBrandItemView(mContext);
            }
        };
        mHotBrandListView.setRecyclerAdapter(mHotBrandAdapter);
        mHotBrandListView.setVisibility(VISIBLE);
        mHotBrandListView.post(new Runnable() {
            @Override
            public void run() {
                mHotBrandListView.setSelection(0);
            }
        });
    }

    @Override
    public void showCharacterList(List<String> list) {
        mCharacterAdapter = new NewRecycleAdapter<String>(list, 1) {
            @Override
            public NewRecycleAdapterItem<String> onCreateItem(Object type) {
                return new ElectricBrandItem.CharacterItemView(mContext);
            }
        };
        mCharacterListView.setRecyclerAdapter(mCharacterAdapter);
        mCharacterListView.setVisibility(VISIBLE);
//        mCharacterListView.post(new Runnable() {
//            @Override
//            public void run() {
//                mCharacterListView.setSelection(0);
//            }
//        });
    }

    private static final String ITEM_TYPE_TITLE = "title";
    private static final String ITEM_TYPE_BRAND = "brand";

    @Override
    public void showBrandList(List<ElectricBrandData> list) {
        mAllBrandAdapter = new NewRecycleAdapter<ElectricBrandData>(list, 1) {
            @Override
            public Object getItemType(ElectricBrandData electricBrandData) {
                if (TextUtils.isEmpty(electricBrandData.electricName)) {
                    return ITEM_TYPE_BRAND;
                } else {
                    return ITEM_TYPE_TITLE;
                }
            }

            @Override
            public NewRecycleAdapterItem<ElectricBrandData> onCreateItem(Object type) {
                if (type == ITEM_TYPE_TITLE)
                    return new ElectricBrandItem.AllBrandItemView(mContext);
                else return new ElectricBrandItem.AllBrandTitleItemView(mContext);
            }

        };
        mAllBrandListView.setRecyclerAdapter(mAllBrandAdapter);
        mAllBrandListView.setVisibility(VISIBLE);
//        mAllBrandListView.post(new Runnable() {
//            @Override
//            public void run() {
//                mAllBrandListView.setSelection(0);
//            }
//        });
    }


    private int allBrandFocusItemPosition = 1;//右侧落焦的那个item的位置
    private OnItemFocusChangeListener onItemFocusChangeListener = new OnItemFocusChangeListener() {
        @Override
        public void focusChange(View v, int position, boolean hasFocus) {
            if (v instanceof ElectricBrandItem.HotBrandItemView) {
                ((ElectricBrandItem.HotBrandItemView) v).onItemFocus(hasFocus);
            } else if (v instanceof ElectricBrandItem.CharacterItemView) {
                ((ElectricBrandItem.CharacterItemView) v).onItemFocus(hasFocus);
                if (position == 0 && hasFocus) mAllBrandListView.scrollToPosition(0);
            } else if (v instanceof ElectricBrandItem.AllBrandItemView) {
                ((ElectricBrandItem.AllBrandItemView) v).onItemFocus(hasFocus);
                if (position == 1 && hasFocus) mAllBrandListView.scrollToPosition(0);

                try {
                    if (hasFocus) {
                        ElectricBrandData electricBrandData = ((ElectricBrandItem.AllBrandItemView) v).getData();
                        for (int i = 0; i < mCharacterAdapter.getData().size(); i++) {
                            if (electricBrandData.character.equals(mCharacterAdapter.getData().get(i))) {
                                refreshCharacterLayout();
                                ((ElectricBrandItem.CharacterItemView) mCharacterListView.getItemByPosition(i)).setDeputyFocus();
                                mCharacterListView.scrollToPosition(i);
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }


            //处理在字符列表上上下按键  右侧具体品牌信息的联动
            if (v instanceof ElectricBrandItem.CharacterItemView && hasFocus) {
                String focusCharacter = ((ElectricBrandItem.CharacterItemView) v).getData();

                for (int i = 0; i < mAllBrandAdapter.getData().size(); i++) {
                    if (focusCharacter.equals(mAllBrandAdapter.getData().get(i).character)) {
//                        Log.i("ElectricBrandView", "onOtherKeyEvent: " + focusCharacter + "    i:" + i);
                        allBrandFocusItemPosition = i + 1;
                        mAllBrandListView.scrollToPosition(allBrandFocusItemPosition);
//                        Log.i("ElectricBrandView", "************ smoothScrollToPosition: " + allBrandFocusItemPosition);
                        return;
                    }

                }
            }
        }
    };

    @Override
    public void onClick(View v) {
        mPrenter.onClickIrLearn();
    }

    private OnItemClickListener onItemClickListener = new OnItemClickListener() {
        @Override
        public void click(View v, int position) {
            ElectricBrandData realBrand = null;
            if (v instanceof ElectricBrandItem.HotBrandItemView) {
                realBrand = mHotBrandAdapter.getData().get(position);
            } else if (v instanceof ElectricBrandItem.AllBrandItemView)
                realBrand = mAllBrandAdapter.getData().get(position);
            mPrenter.onItemClick(v, realBrand, position);
        }
    };


    @Override
    public void showLoading() {
        if (loadingView == null) {
            loadingView = new LoadingView(mContext);
            addView(loadingView);
        }
        loadingView.setVisibility(VISIBLE);

    }

    @Override
    public void hideLoading() {
        FuncKt.runOnUiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                if (loadingView != null) loadingView.setVisibility(GONE);
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void getFocus() {
        if (emptyView != null && emptyView.getVisibility() == VISIBLE) emptyView.getFocus();
        else {
            if (mHotBrandAdapter != null && mHotBrandAdapter.getData() != null && mHotBrandAdapter.getData().size() > 0) {
                mHotBrandListView.post(new Runnable() {
                    @Override
                    public void run() {
                        mHotBrandListView.setSelection(0);
                    }
                });
            } else {
                if (mCharacterAdapter != null && mCharacterAdapter.getData() != null && mCharacterAdapter.getData().size() > 0) {
                    mHotBrandListView.post(new Runnable() {
                        @Override
                        public void run() {
                            mCharacterListView.setSelection(0);
                        }
                    });
                }

            }

        }
    }

    @Override
    public void showErrorView(String errorMsg, String errorCode) {
        if (emptyView == null) {
            emptyView = new EmptyView(mContext);
            emptyView.setText(errorMsg);
            emptyView.getFocus();
            LayoutParams p = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            p.topMargin = Util.Div(28);
            addView(emptyView, p);
            emptyView.setOnClick(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    mPrenter.loadList(mId, mTypeName, mhotList);
                }
            });
        }
        emptyView.getFocus();
        emptyView.setVisibility(VISIBLE);
        if (mLayout != null) mLayout.setVisibility(GONE);
    }

    @Override
    public void hideErrorView() {
        if (emptyView != null) emptyView.setVisibility(GONE);
        if (mLayout != null) mLayout.setVisibility(GONE);
        if(mAddLayout != null) mAddLayout.setVisibility(GONE);
    }

    @Override
    public View getView() {
        return this;
    }

    @SuppressLint("ResourceType")
    @Override
    public boolean onLeftBoundary(View leaveView, int position) {
        if (leaveView.getId() == 2) {
            //防止上侧就近的item抢焦点，先把整个焦点禁用
            if (mHotBrandAdapter != null && mHotBrandAdapter.getData() != null && mHotBrandAdapter.getData().size() > 0) {
                for (int i = 0; i < mHotBrandAdapter.getData().size(); i++) {
                    if (mHotBrandListView.getItemByPosition(i) != null)
                        mHotBrandListView.getItemByPosition(i).setFocusable(false);
                }
            }
            //刷新字母列表的副焦点状态
            refreshCharacterLayout();
            //校准左侧字母列表落焦item
            String character = ((ElectricBrandItem.AllBrandItemView) leaveView).getData().character;
            for (int i = 0; i < mCharacterAdapter.getData().size(); i++) {
                //防止左侧就近的item抢焦点，先把整个焦点禁用
                if (mCharacterListView.getItemByPosition(i) != null)
                    mCharacterListView.getItemByPosition(i).setFocusable(false);
                if (character.equals(mCharacterAdapter.getData().get(i))) {
                    final int finalI = i;
                    mCharacterListView.post(new Runnable() {
                        @Override
                        public void run() {
                            mCharacterListView.setSelection(finalI);
                            if (mCharacterListView.getItemByPosition(finalI) != null) {
                                mCharacterListView.getItemByPosition(finalI).setFocusable(true);
                            }
                            //放开焦点
                            for (int i = 0; i < mCharacterAdapter.getData().size(); i++) {
                                if (mCharacterListView.getItemByPosition(i) != null)
                                    mCharacterListView.getItemByPosition(i).setFocusable(true);
                            }
                            if (mHotBrandAdapter != null && mHotBrandAdapter.getData() != null && mHotBrandAdapter.getData().size() > 0) {
                                for (int i = 0; i < mHotBrandAdapter.getData().size(); i++) {
                                    if (mHotBrandListView.getItemByPosition(i) != null)
                                        mHotBrandListView.getItemByPosition(i).setFocusable(true);
                                }
                            }
                        }
                    });
                }
            }

        }
        return false;
    }

    /**
     * 刷新字符列表副焦点状态
     */

    private void refreshCharacterLayout() {
        try {
            if (mCharacterAdapter == null || mCharacterAdapter.getData().size() <= 0 || mCharacterListView == null)
                return;
            for (int i = 0; i < mCharacterAdapter.getData().size(); i++) {
                if (mCharacterListView.getItemByPosition(i) != null) {
                    ((ElectricBrandItem.CharacterItemView) mCharacterListView.getItemByPosition(i)).setDeputyFocusPosition(-1);
                    ((ElectricBrandItem.CharacterItemView) mCharacterListView.getItemByPosition(i)).refreshFocusChange();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean onTopBoundary(View leaveView, int position) {
        switch (leaveView.getId()) {
            case 0:
                mAddLayout.requestFocus();
                return false;
            case 1:
                if (mHotBrandListView != null) mHotBrandListView.post(new Runnable() {
                    @Override
                    public void run() {
                        int size = mHotBrandListView.getRecyclerAdapter().getData().size();
                        if (size > 4) {
                            mHotBrandListView.setSelection(4);
                        } else {
                            mHotBrandListView.setSelection(0);
                        }
                    }
                });
                return false;

        }
        return false;
    }

    @Override
    public boolean onDownBoundary(View leaveView, int position) {
        switch (leaveView.getId()) {
            case 0:
                if (mCharacterListView != null) mCharacterListView.post(new Runnable() {
                    @Override
                    public void run() {
                        mCharacterListView.setSelection(0);
                        refreshCharacterLayout();
                    }
                });
                return false;

        }
        return false;
    }

    @SuppressLint("ResourceType")
    @Override
    public boolean onRightBoundary(final View leaveView, final int position) {
        if (leaveView.getId() == 1) {
            //禁止右侧BrandList所有焦点
            for (int i = 0; i < mAllBrandAdapter.getData().size(); i++) {
                if (mAllBrandListView.getItemByPosition(i) != null && !TextUtils.isEmpty(mAllBrandAdapter.getData().get(i).electricName))
                    if (mAllBrandListView.getItemByPosition(i) != null)
                        mAllBrandListView.getItemByPosition(i).setFocusable(false);

            }
            //防止上侧就近的item抢焦点，先把整个焦点禁用
            if (mHotBrandAdapter != null && mHotBrandAdapter.getData() != null && mHotBrandAdapter.getData().size() > 0) {
                for (int i = 0; i < mHotBrandAdapter.getData().size(); i++) {
                    if (mHotBrandListView.getItemByPosition(i) != null)
                        mHotBrandListView.getItemByPosition(i).setFocusable(false);
                }
            }
            mAllBrandListView.post(new Runnable() {
                @Override
                public void run() {
                    mAllBrandListView.setSelection(allBrandFocusItemPosition);
                    if (leaveView instanceof ElectricBrandItem.CharacterItemView) {
                        ((ElectricBrandItem.CharacterItemView) leaveView).setDeputyFocusPosition(position);
                        //放开所有焦点
                        for (int i = 0; i < mAllBrandAdapter.getData().size(); i++) {
                            if (mAllBrandListView.getItemByPosition(i) != null && !TextUtils.isEmpty(mAllBrandAdapter.getData().get(i).electricName))
                                mAllBrandListView.getItemByPosition(i).setFocusable(true);
                        }
                        if (mHotBrandAdapter != null && mHotBrandAdapter.getData() != null && mHotBrandAdapter.getData().size() > 0) {
                            for (int i = 0; i < mHotBrandAdapter.getData().size(); i++) {
                                if (mHotBrandListView.getItemByPosition(i) != null)
                                    mHotBrandListView.getItemByPosition(i).setFocusable(true);
                            }
                        }
                    }
                }
            });

            return false;
        }

        return true;
    }

    @SuppressLint("ResourceType")
    @Override
    public boolean onOtherKeyEvent(View v, int position, int keyCode) {
        //处理右侧具体品牌按上键 让热门第四个item落焦
        if (v.getId() == 2 && position == 1 && keyCode == KeyEvent.KEYCODE_DPAD_UP) {
            if (mHotBrandListView != null) mHotBrandListView.post(new Runnable() {
                @Override
                public void run() {
                    int size = mHotBrandListView.getRecyclerAdapter().getData().size();
                    if (size > 4) {
                        mHotBrandListView.setSelection(4);
                    } else {
                        mHotBrandListView.setSelection(0);
                    }
                }
            });
            return false;
        }

        return false;
    }
}
