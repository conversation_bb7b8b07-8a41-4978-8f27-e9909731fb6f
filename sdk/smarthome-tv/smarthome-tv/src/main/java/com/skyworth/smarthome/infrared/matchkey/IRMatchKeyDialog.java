package com.skyworth.smarthome.infrared.matchkey;

import android.util.Log;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.skyworth.smarthome.infrared.matchkey.model.IMatchKeyModel;
import com.skyworth.smarthome.infrared.matchkey.model.IMatchKeyModelImpl;
import com.skyworth.smarthome.infrared.matchkey.presenter.IMatchKeyPresenter;
import com.skyworth.smarthome.infrared.matchkey.presenter.IMatchKeyPresenterImpl;
import com.skyworth.smarthome.infrared.matchkey.view.IMatchKeyView;
import com.skyworth.smarthome.infrared.matchkey.view.IMatchKeyViewImpl;
import com.skyworth.smarthome.common.base.BaseSysDialog;
import com.skyworth.smarthome.common.util.DialogLauncherUtil;
import com.skyworth.util.Util;

import java.util.HashMap;
import java.util.Map;

import static com.skyworth.smarthome.common.util.DialogLauncherUtil.DIALOG_KEY_IRConfig;


/**
 * Description: <br>
 * Created by <PERSON><PERSON>exiao on 2019/5/5 14:33.
 */
public class IRMatchKeyDialog extends BaseSysDialog {
    private IMatchKeyView view;
    private IMatchKeyPresenter presenter;
    private IMatchKeyModel model;
    public static final String PARAM_IR_HOST_DEVICE_ID = "PARAM_IR_HOST_DEVICE_ID";
    public static final String PARAM_IR_HOST_NAME = "PARAM_IR_HOST_NAME";
    public static final String PARAM_IR_HOST_TYPE_ID = "PARAM_IR_HOST_TYPE_ID";
    public static final String PARAM_IR_SLAVE_TYPE_NAME = "PARAM_IR_SLAVE_TYPE_NAME";
    public static final String PARAM_IR_SLAVE_TYPE_ID = "PARAM_IR_SLAVE_TYPE_ID";
    public static final String PARAM_IR_SLAVE_BRAND = "PARAM_IR_SLAVE_BRAND";
    public static final String PARAM_IR_KK_TYPE_ID = "PARAM_IR_KK_TYPE_ID";
    public static final String PARAM_IR_KK_BRAND_ID = "PARAM_IR_KK_BRAND_ID";
    public static final String PARAM_IR_HXD_HAS = "PARAM_IR_HXD_HAS";
    public static final String PARAM_IR_SDK_PRIORITY = "PARAM_IR_SDK_PRIORITY";

    public static void launch(String irHostDeviceId, String irHostName, String irHostTypeId, String irSlaveName, String irSlaveTypeId, String irSlaveBrand, String kkTypeID, String kkBrandID, String hxdHas, String prioritySDK) {
        Map<String, String> params = new HashMap<>();
        params.put(PARAM_IR_HOST_DEVICE_ID, irHostDeviceId);
        params.put(PARAM_IR_HOST_NAME, irHostName);
        params.put(PARAM_IR_HOST_TYPE_ID, irHostTypeId);
        params.put(PARAM_IR_SLAVE_TYPE_NAME, irSlaveName);
        params.put(PARAM_IR_SLAVE_TYPE_ID, irSlaveTypeId);
        params.put(PARAM_IR_SLAVE_BRAND, irSlaveBrand);
        params.put(PARAM_IR_KK_TYPE_ID, kkTypeID);
        params.put(PARAM_IR_KK_BRAND_ID, kkBrandID);
        params.put(PARAM_IR_HXD_HAS, hxdHas);
        params.put(PARAM_IR_SDK_PRIORITY, prioritySDK);
        DialogLauncherUtil.showIRMatchKeyDialog(params);
    }

    @Override
    protected void initContentView() {
        super.initContentView();
    }

    @Override
    public void showDialog(Map<String, String> params) {
        super.showDialog(params);
        run(params);
    }

    private void run(Map<String, String> params) {
        addBlackTransparentBg();
        view = new IMatchKeyViewImpl();
        presenter = new IMatchKeyPresenterImpl();
        presenter.setListener(new IMatchKeyPresenterImpl.IRMatchListener() {
            @Override
            public void dismiss() {
                IRMatchKeyDialog.this.dismiss();
            }
        });
        model = new IMatchKeyModelImpl();
        view.create(mContext, presenter);
        mContentView.addView(view.getView());
        presenter.create(mContext, view, model);
        presenter.startMatch(params);
        openAutoDismissDialog();
    }

    private void addBlackTransparentBg() {
        ImageView bg = new ImageView(mContext);
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Util.Div(1920), Util.Div(1080));
        mContentView.addView(bg, layoutParams);
    }

    @Override
    protected void onDismiss() {
        super.onDismiss();
        Log.i("irconfig", "onDismiss: ");
        if (view != null) {
            view.destroy();
        }
        if (presenter != null) {
            presenter.destroy();
        }
        if (model != null) {
            model.destroy();
        }
    }

    @Override
    protected void initParams() {
        mDialogKey = DIALOG_KEY_IRConfig;
    }
}
