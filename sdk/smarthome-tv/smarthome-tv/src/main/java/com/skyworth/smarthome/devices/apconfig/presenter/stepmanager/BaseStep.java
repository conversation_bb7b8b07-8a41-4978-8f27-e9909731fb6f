package com.skyworth.smarthome.devices.apconfig.presenter.stepmanager;

import android.content.Context;
import android.util.Log;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/1/7 21:54.
 */
public abstract class BaseStep<Presenter> implements IOneStep<Presenter> {
    protected BaseStepManager manager = null;
    protected Context context = null;
    protected Presenter presenter = null;

    public BaseStep() {

    }

    @Override
    public void setManger(BaseStepManager manger) {
        this.manager = manger;
    }

    @Override
    public void setPresenter(Presenter presenter) {
        this.presenter = presenter;
    }

    protected void next() {
        if (manager != null) {
            manager.next();
        }
    }

    protected void previous() {
        if (manager != null) {
            manager.previous();
        }
    }

    protected void reload() {
        if (manager != null) {
            manager.reload();
        }
    }

    protected void stop() {
        if (manager != null) {
            manager.stop();
        }
    }

//    protected void jumpTo(int index) {
//        if (manager != null) {
//            manager.jumpTo(index);
//        }
//    }

    protected void jumpTo(String tag) {
        if (manager != null) {
            manager.jumpTo(tag);
        }
    }

    protected void output(String msg, Object... params) {
        if (manager != null) {
            manager.output(msg, params);
        }
    }

    protected abstract String getLogTag();

    @Override
    public void create() {
        Log.i(getLogTag(), "create: \t----> " + getTag());
    }

    @Override
    public void run() {
        Log.i(getLogTag(), "run: \t----> [ " + getTag() + " ]");
    }

    @Override
    public void destroy() {
        Log.i(getLogTag(), "destroy: \t----> " + getTag());
    }

    @Override
    public void setContext(Context context) {
        this.context = context;
    }

    protected void logi(String msg) {
        Log.i(getLogTag(), "\t\t\t[ " + getTag() + " ] : " + msg);
    }

    protected void loge(String msg) {
        Log.e(getLogTag(), "\t\t\t[ " + getTag() + " ] : " + msg);
    }
}
