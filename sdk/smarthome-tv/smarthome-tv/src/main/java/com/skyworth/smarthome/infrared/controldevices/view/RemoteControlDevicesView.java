package com.skyworth.smarthome.infrared.controldevices.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.support.v7.widget.NewRecycleAdapter;
import android.util.Log;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import android.support.annotation.NonNull;

import com.skyworth.smarthome.infrared.controldevices.presenter.IRemoteControlDevicesPresenter;
import com.skyworth.smarthome.infrared.electriclist.model.DeviceTypeListData;
import com.skyworth.smarthome.infrared.electriclist.view.ElectricTypeItem;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.ui.DialogBg;
import com.skyworth.smarthome.common.ui.LoadingView;
import com.skyworth.smarthome.common.util.ThreadManager;
import com.skyworth.smarthome.infrared.manager.IREntry;
import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.ui.newrecycleview.NewRecycleAdapterItem;
import com.skyworth.ui.newrecycleview.NewRecycleLayout;
import com.skyworth.ui.newrecycleview.OnBoundaryListener;
import com.skyworth.ui.newrecycleview.OnItemClickListener;
import com.skyworth.ui.newrecycleview.OnItemFocusChangeListener;
import com.skyworth.util.Util;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XThemeUtils;
import com.smarthome.common.utils.XToast;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Describe:万能遥控器（可控设备）
 * Created by AwenZeng on 2019/4/28
 */
public class RemoteControlDevicesView extends FrameLayout implements IRemoteControlDevicesView, View.OnClickListener, View.OnKeyListener {
    private NewRecycleLayout<DeviceTypeListData> mRecycleView = null;
    private NewRecycleAdapter<DeviceTypeListData> mAdapter = null;
    private IRemoteControlDevicesPresenter mPresenter;
    private Context mContext;

    private LinearLayout mAddLayout;
    private ImageView mAddImg;
    private TextView mAddTv;
    private TextView mTitleTv, tipsTv;
    private FrameLayout mContentLayout;
    //    private EmptyView mEmptyView;
    private InfraredGuideView infraredGuideView;
    private LoadingView mLoadingView;

    private List<DeviceTypeListData> mDataList;
    private boolean isShowDelete = false;

    private int mFocusPos = 0;//获焦位置
    private Map<String, Object> mParams = null;
    private String entry_style = null;
    private static byte[] lock = new byte[0];


    public RemoteControlDevicesView(@NonNull Context context) {
        super(context);
        IREntry.regOnKeyListener(this);
    }

    @Override
    public void setParams(Map<String, Object> params) {
        mParams = params;
    }

    @Override
    public void createView(Context context, IRemoteControlDevicesPresenter presenter) {
        mContext = context;
        mPresenter = presenter;
        setBackground(new DialogBg());
        initView();
    }

    private void initView() {
        LayoutParams layoutParams = new LayoutParams(Util.Div(660), Util.Div(810));
        setLayoutParams(layoutParams);

        mTitleTv = new TextView(mContext);
        mTitleTv.setTextSize(Util.Dpi(32));
        mTitleTv.setTextColor(XThemeUtils.c_1a());
        mTitleTv.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));

        layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.topMargin = Util.Div(20);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        addView(mTitleTv, layoutParams);

        mAddLayout = new LinearLayout(mContext);
        mAddLayout.setOrientation(LinearLayout.HORIZONTAL);
        mAddLayout.setOnClickListener(this);
        mAddImg = new ImageView(mContext);
        LinearLayout.LayoutParams imgParams = new LinearLayout.LayoutParams(Util.Div(20), Util.Div(20));
        imgParams.leftMargin = Util.Div(18);
        imgParams.gravity = Gravity.CENTER_VERTICAL;
        mAddImg.setImageResource(R.drawable.add);
        mAddLayout.addView(mAddImg, imgParams);

        mAddTv = new TextView(mContext);
        mAddTv.setTextColor(Color.parseColor("#CDD2D8"));
        mAddTv.setTextSize(Util.Dpi(20));
        mAddTv.setText(mContext.getString(R.string.add));
        LinearLayout.LayoutParams addPramas = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        addPramas.gravity = Gravity.CENTER_VERTICAL;
        addPramas.leftMargin = Util.Div(4);
        mAddLayout.addView(mAddTv, addPramas);

        layoutParams = new LayoutParams(Util.Div(100), Util.Div(46));
        layoutParams.topMargin = Util.Div(30);
        layoutParams.leftMargin = Util.Div(540);
        addView(mAddLayout, layoutParams);
        mAddLayout.setBackground(new CCFocusDrawable(mContext).setRadius((float) Util.Div(50)).setBorderVisible(false).setSolidColor(Color.parseColor("#19CCCCCC")));
        mAddLayout.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    mAddImg.setImageResource(R.drawable.add_focus);
                    mAddTv.setTextColor(Color.parseColor("#000000"));
                    mAddLayout.setBackground(new CCFocusDrawable(mContext).setRadius((float) Util.Div(50)).setSolidColor(Color.parseColor("#FFFFFF")));
                } else {
                    mAddImg.setImageResource(R.drawable.add);
                    mAddTv.setTextColor(Color.parseColor("#CDD2D8"));
                    mAddLayout.setBackground(new CCFocusDrawable(mContext).setRadius((float) Util.Div(50)).setBorderVisible(false).setSolidColor(Color.parseColor("#19CCCCCC")));
                }
            }
        });

        tipsTv = new TextView(mContext);
        tipsTv.setTextSize(Util.Dpi(20));
        tipsTv.setTextColor(Color.parseColor("#9A9FA6"));
        tipsTv.setGravity(Gravity.CENTER_HORIZONTAL);
        tipsTv.setText(mContext.getString(R.string.delete_ir_device_tips));
        LayoutParams tipsLp = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        tipsLp.topMargin = Util.Div(72);
        addView(tipsTv, tipsLp);
        tipsTv.setVisibility(GONE);

        mContentLayout = new FrameLayout(mContext);
        layoutParams = new LayoutParams(Util.Div(635), ViewGroup.LayoutParams.MATCH_PARENT);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        layoutParams.topMargin = Util.Div(92);
        mContentLayout.setClipChildren(false);
        mContentLayout.setClipToPadding(false);
        addView(mContentLayout, layoutParams);
    }

    @Override
    public void refreshUI(List<DeviceTypeListData> list) {
        mDataList = list;
        updateUI();
        if (EmptyUtils.isEmpty(mDataList)) {
            showInfraredGuideView();
            isAddToInfraredGuideView = false;
        } else {
            showRemoteControlDevices();
        }
    }

    private void updateUI() {
        if (mPresenter.getDeviceTypeId().equals("1")) {//电视的品类id为1
            mTitleTv.setText(mContext.getString(R.string.tv_all_power_remote_control));
        } else {
            String titleStr = mPresenter.getDeviceName();
            if(EmptyUtils.isEmpty(titleStr)){
              titleStr = mContext.getString(R.string.all_power_remote_control);
            }
            mTitleTv.setText(titleStr);
        }
        if (tipsTv != null)
            tipsTv.setVisibility(VISIBLE);
    }

    private void showRemoteControlDevices() {
        mAddLayout.setVisibility(VISIBLE);
        mRecycleView = new NewRecycleLayout<>(mContext, 3);
        mRecycleView.setItemSpace(Util.Div(30), Util.Div(30));
        mRecycleView.setClipChildren(false);
        mRecycleView.setClipToPadding(false);
        mRecycleView.setmItemFocusChangeListener(onItemFocusChangeListener);
        mRecycleView.setmBoundaryListener(onBoundaryListener);
        mRecycleView.setmItemClickListener(onItemClickListener);
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.MATCH_PARENT);
        mContentLayout.addView(mRecycleView, layoutParams);
        mAdapter = new NewRecycleAdapter<DeviceTypeListData>(mDataList, 1) {

            @Override
            public NewRecycleAdapterItem<DeviceTypeListData> onCreateItem(Object type) {
                return new ElectricTypeItem(mContext);
            }
        };
        mRecycleView.setRecyclerAdapter(mAdapter);
        mRecycleView.setVisibility(VISIBLE);
        mRecycleView.post(new Runnable() {
            @Override
            public void run() {
                mRecycleView.setSelection(0);
            }
        });

        ThreadManager.getInstance().uiThread(new Runnable() {
            @Override
            public void run() {
                mAddLayout.setFocusable(true);
                mAddLayout.setFocusableInTouchMode(true);
            }
        },200);


    }


    /**
     * 显示红外指引列表
     */
    private void showInfraredGuideView() {
        hideRecycleView(true);
        String downloadUrl = "";
        try {
            downloadUrl = AppData.getInstance().getVHomeDownloadUrl();
        } catch (Exception e) {
            e.printStackTrace();
        }
        infraredGuideView = new InfraredGuideView(mContext);
        infraredGuideView.showQrCode(downloadUrl);
        infraredGuideView.getFocus();
        String titleStr = mPresenter.getDeviceName();
        if(EmptyUtils.isNotEmpty(titleStr)){
            infraredGuideView.setTitile(titleStr);
        }
        addView(infraredGuideView, new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        infraredGuideView.setVisibility(VISIBLE);
        infraredGuideView.setOnBtnClickListener(new InfraredGuideView.OnClickListener() {
            @Override
            public void OnClick() {
                mPresenter.showAddInfraredDevice();
            }
        });
    }

    private void hideRecycleView(boolean isHide) {
        if (isHide) {
            mAddLayout.setVisibility(View.GONE);
            mAddLayout.setFocusable(false);
            mAddLayout.setFocusableInTouchMode(false);
            if (mRecycleView != null)
                mRecycleView.setVisibility(GONE);
            if (mTitleTv != null)
                mTitleTv.setVisibility(GONE);
            if (tipsTv != null)
                tipsTv.setVisibility(GONE);
        } else {
            mAddLayout.setVisibility(View.VISIBLE);
            mAddLayout.setFocusable(true);
            mAddLayout.setFocusableInTouchMode(true);
            if (mRecycleView != null)
                mRecycleView.setVisibility(VISIBLE);
            if (mTitleTv != null)
                mTitleTv.setVisibility(VISIBLE);
            if (tipsTv != null)
                tipsTv.setVisibility(VISIBLE);
        }

    }

    /**
     * 刷新列表
     */
    private void refreshList() {
        if (mFocusPos >= mDataList.size()) {
            mFocusPos--;
        }
        mRecycleView.setSelection(mFocusPos);
        mRecycleView.notifyDataSetChanged();
    }

    @Override
    public void showLoading() {
        if (EmptyUtils.isEmpty(mLoadingView)) {
            mAddLayout.setVisibility(View.GONE);
            mAddLayout.setFocusable(false);
            mAddLayout.setFocusableInTouchMode(false);
            mLoadingView = new LoadingView(mContext);
            LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
            addView(mLoadingView, layoutParams);
        }
        mLoadingView.setVisibility(VISIBLE);
    }

    @Override
    public void hideLoading() {
        if (EmptyUtils.isNotEmpty(mLoadingView)) {
            mLoadingView.setVisibility(View.GONE);
        }
    }


    @Override
    public void requestFocusUI() {
        if (EmptyUtils.isEmpty(mDataList)) {
//            mEmptyView.getFocus();
            if (infraredGuideView != null)
                infraredGuideView.getFocus();
        } else {
            mAddLayout.requestFocus();
        }
    }

    @Override
    public void deleteSuccess(DeviceTypeListData data) {
        synchronized (lock){
            Iterator<DeviceTypeListData> it = mDataList.iterator();
            while (it.hasNext()) {
                DeviceTypeListData item = it.next();
                if (EmptyUtils.isNotEmpty(item.id) && item.id.equals(data.id)) {
                    it.remove();
                    break;
                }
            }
        }
        isShowDelete = false;
        mPresenter.assembleData(mDataList, false);
        refreshList();
        if (EmptyUtils.isEmpty(mDataList)) {
            showInfraredGuideView();
            isAddToInfraredGuideView = false;
            isShowDelete = false;
        }
    }

    @Override
    public void deleteFailed() {
        XToast.showToast(SmartHomeTvLib.getContext(), mContext.getString(R.string.delete_failed));
    }

    private boolean isAddToInfraredGuideView = false;

    @Override
    public void onClick(View v) {
        showInfraredGuideView();
        isAddToInfraredGuideView = true;
    }

    @Override
    public View getView() {
        return this;
    }

    private OnItemClickListener onItemClickListener = new OnItemClickListener() {
        @Override
        public void click(View v, int position) {
            DeviceTypeListData data = mDataList.get(position);
            if (EmptyUtils.isNotEmpty(data) && data.isShowDelete) {
                mPresenter.deleletIrDevices(data);
            } else {
                XToast.showToast(mContext, mContext.getString(R.string.try_voice_control));
            }
        }
    };

    private OnItemFocusChangeListener onItemFocusChangeListener = new OnItemFocusChangeListener() {
        @Override
        public void focusChange(View v, int position, boolean hasFocus) {
            if (v instanceof ElectricTypeItem) {
                mFocusPos = position;
                if (position >= mDataList.size()) {
                    position--;
                }

                if (position >= 0) {
                    ((ElectricTypeItem) v).onItemFocus(hasFocus, mDataList.get(position));
                    Util.focusAnimate(v, hasFocus);//放大动画
                }
            }
        }
    };

    private OnBoundaryListener onBoundaryListener = new OnBoundaryListener() {
        @Override
        public boolean onLeftBoundary(View leaveView, int position) {
            return false;
        }

        @Override
        public boolean onTopBoundary(View leaveView, int position) {
            mAddLayout.requestFocus();
            return true;
        }

        @Override
        public boolean onDownBoundary(View leaveView, int position) {
            return false;
        }

        @Override
        public boolean onRightBoundary(View leaveView, int position) {
            return false;
        }

        @Override
        public boolean onOtherKeyEvent(View v, int position, int keyCode) {
            if (keyCode == KeyEvent.KEYCODE_MENU) {
                if (!isShowDelete) {
                    isShowDelete = true;
                    mPresenter.assembleData(mDataList, true);
                    refreshList();
                }
                return true;
            }
            return false;
        }
    };

    @Override
    public boolean onKey(View v, int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            Log.i("SmartHomeV3", "KEYCODE_BACK :  " + isAddToInfraredGuideView);
            if (!mAddLayout.isFocused() && isShowDelete) {
                isShowDelete = false;
                mPresenter.assembleData(mDataList, false);
                refreshList();
                return true;
            }
            isShowDelete = false;

            if (isAddToInfraredGuideView) {
                if (infraredGuideView != null)
                    infraredGuideView.setVisibility(GONE);
                hideRecycleView(false);
                mAddLayout.requestFocus();
                isAddToInfraredGuideView = false;
                return true;
            }
        }
        return false;
    }
}
