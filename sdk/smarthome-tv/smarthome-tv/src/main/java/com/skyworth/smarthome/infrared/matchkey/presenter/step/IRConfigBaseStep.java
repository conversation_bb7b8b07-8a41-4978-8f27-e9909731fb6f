package com.skyworth.smarthome.infrared.matchkey.presenter.step;


import com.skyworth.smarthome.devices.apconfig.presenter.stepmanager.BaseStep;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/5/5 16:07.
 */
public abstract class IRConfigBaseStep<Presenter> extends BaseStep<Presenter> {
    private static final String TAG = "irconfig";

    @Override
    protected String getLogTag() {
        return TAG;
    }
}
