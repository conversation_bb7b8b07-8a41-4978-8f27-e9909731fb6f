package com.skyworth.smarthome.home.smartdevice.controlpanel.view.base;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.GradientDrawable;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.widget.FrameLayout;

import com.alibaba.fastjson.JSONObject;
import com.skyworth.smarthome.home.smartdevice.controlpanel.common.itemdata.BaseControlData;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.home.smartdevice.controlpanel.common.DependList;
import com.skyworth.smarthome.home.smartdevice.controlpanel.view.ControlPanelView;
import com.skyworth.smarthome.home.smartdevice.controlpanel.view.IControlListener;
import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.util.Util;
import com.smarthome.common.dataer.DataHelpInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.skyworth.smarthome.home.smartdevice.controlpanel.common.DependList.dependResultFactory;


public abstract class BaseControlItem<DATA extends BaseControlData> extends FrameLayout implements IControlItem<DATA> {
    protected DATA mData = null;
    protected JSONObject mStatus = null;
    protected IControlListener mListener = null;
    protected boolean isEnable = true;
    protected GradientDrawable mFocusDrawable = null;
    protected GradientDrawable mTitleBgFocus = null;
    protected GradientDrawable mTitleBgUnFocus = null;
    protected boolean isShowDivideLine = false;
    protected Paint mDividerPaint = null;
    protected static final int CONTROL_DELAY = 300;
    protected boolean isControlDeviceOnWait = false;
    private CCFocusDrawable mFocusBg;

    public BaseControlItem(Context context) {
        super(context);
        setWillNotDraw(false);
        setOnClickListener(onClickListener);
        initTitleBg();
    }

    protected void initTitleBg() {
        mTitleBgFocus = new GradientDrawable();
        mTitleBgFocus.setColor(Color.parseColor("#19000000"));
        mTitleBgFocus.setCornerRadius(Util.Div(30));

        mTitleBgUnFocus = new GradientDrawable();
        mTitleBgUnFocus.setColor(Color.argb(25, 204, 204, 204));
        mTitleBgUnFocus.setStroke(Util.Div(2), Color.argb(25, 204, 204, 204));
        mTitleBgUnFocus.setCornerRadius(Util.Div(30));

        mFocusBg = new CCFocusDrawable(getContext()).setRadius(Util.Div(10)).setBorderVisible(false).setSolidColor(Color.TRANSPARENT);
        setBackground(mFocusBg);
    }


    @Override
    public void show(String status, DATA data) {
        if (isControlDeviceOnWait) {
            Log.i(ControlPanelView.TAG, "isControlDeviceOnWait");
            return;
        }
        showInternal(status, data);
    }

    private void showInternal(String status, DATA data) {
        mData = data;
        mStatus = JSONObject.parseObject(status);
        refreshUI();
        checkEnable();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (mDividerPaint == null) {
            mDividerPaint = new Paint();
            mDividerPaint.setColor(Color.parseColor("#33FFFFFF"));
            mDividerPaint.setStyle(Paint.Style.STROKE);
            mDividerPaint.setStrokeWidth(Util.Div(1));
        }
        if (isShowDivideLine) {
            canvas.drawLine(Util.Div(20), getHeight() - 1, getWidth() - Util.Div(20), getHeight() - 1, mDividerPaint);
        }
    }

    @Override
    public void setControlListener(IControlListener listener) {
        mListener = listener;
    }

    abstract protected void refreshUI();

    /**
     * 控件是否支持落焦
     *
     * @return
     */
    public boolean canFocusable() {
        return true;
    }

    public abstract boolean canFocusableDefault();

    public void onClick() {

    }

    @Override
    public boolean onKey(View view, int keyCode, KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN) {
            if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT) {
                return onKeyLeft();
            } else if (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
                return onKeyRight();
            }
        }
        return false;
    }

    public boolean onKeyLeft() {
        return false;
    }

    public boolean onKeyRight() {
        return false;
    }

    @Override
    public View getView() {
        return this;
    }

    @Override
    public DATA getData() {
        return mData;
    }

    @Override
    protected void onFocusChanged(boolean gainFocus, int direction,Rect previouslyFocusedRect) {
        super.onFocusChanged(gainFocus, direction, previouslyFocusedRect);
    }

    @Override
    public void onFocus(boolean hasFocus) {
        mFocusBg.setBorderVisible(hasFocus).setSolidColor(getResources().getColor(hasFocus ? R.color.white : R.color.translucent));
    }

    public List<DependList.BaseDependData> checkDepend(String type) {
        Log.i(ControlPanelView.TAG, "checkDepend: " + type);
        List<DependList.BaseDependData> result = new ArrayList<>();
        if (mData == null || mData.depend == null) {
            Log.i(ControlPanelView.TAG, "checkDepend: data == null");
            return result;
        }
        for (DependList.Depend depend : mData.depend) {
            if (type.equals(depend.type)) {
                DependList.BaseDependData dependResult = dependResultFactory(type);
                if (dependResult == null) {
                    continue;
                }
                dependResult.check(mStatus, depend);
                result.add(dependResult);
            }
        }
        return result;
    }


    private void checkEnable() {
        Log.i(ControlPanelView.TAG, "checkEnable: ");
        isEnable = canFocusableDefault();
        List<DependList.BaseDependData> dependResult = checkDepend(DependList.Depend.TYPE_ENABLE);
        if (dependResult.size() <= 0) {
            Log.i(ControlPanelView.TAG, "checkEnable: no result");
            return;
        }

        isEnable = true;
        for (DependList.BaseDependData dependData : dependResult) {
            if (dependData.result == DependList.BaseDependData.RESULT_COMPARE_FAILED) {
                isEnable = false;
                return;
            } else if (dependData.result == DependList.BaseDependData.RESULT_NO_FIELD) {
                isEnable = canFocusableDefault();
            }
        }
    }

    @Override
    public void enable() {
        setAlpha(1);
        setFocusable(true);
    }

    @Override
    public void disable() {
        setAlpha(0.4f);
        postDelayed(new Runnable() {
            @Override
            public void run() {
                setFocusable(false);
            }
        }, 500);
    }

    private OnClickListener onClickListener = new OnClickListener() {
        @Override
        public void onClick(View v) {
            BaseControlItem.this.onClick();
        }
    };

    public void setShowDivideLine(boolean showDivideLine) {
        isShowDivideLine = showDivideLine;
        invalidate();
    }

    protected void logi(String msg) {
        Log.i(ControlPanelView.TAG, getType() + " : " + msg);
    }

    protected void loge(String msg) {
        Log.e(ControlPanelView.TAG, getType() + " : " + msg);
    }

    public class ControlRunnable implements Runnable {
        private Map<String, String> newValue = null;

        public void setNewValue(Map<String, String> newValue) {
            this.newValue = newValue;
        }

        @Override
        public void run() {
            if (mListener != null && newValue != null) {
                logi("controlRunnable: " + newValue);
                mListener.onControl(newValue);
                isControlDeviceOnWait = false;
            }
        }
    }

    private ControlRunnable controlRunnable = new ControlRunnable();

    protected void controlDevice(Map<String, String> newValue) {
        DataHelpInfo.getInstance().setControlDetail(mData.title);
        controlRunnable.setNewValue(newValue);
        mergeNewValueToCurrent(newValue);
        refreshNewValueImmediately();
        removeCallbacks(controlRunnable);
        postDelayed(controlRunnable, CONTROL_DELAY);
        isControlDeviceOnWait = true;
    }

    private void mergeNewValueToCurrent(Map<String, String> newValue) {
        if (mStatus == null || newValue == null || newValue.size() <= 0) {
            return;
        }
        for (Map.Entry<String, String> entry : newValue.entrySet()) {
            mStatus.put(entry.getKey(), entry.getValue());
        }
    }

    private void refreshNewValueImmediately() {
        if (mStatus == null || mData == null) {
            return;
        }
        showInternal(mStatus.toJSONString(), mData);
    }
}
