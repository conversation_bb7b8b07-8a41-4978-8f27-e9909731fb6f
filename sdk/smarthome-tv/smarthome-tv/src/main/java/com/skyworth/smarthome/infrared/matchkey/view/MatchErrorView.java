package com.skyworth.smarthome.infrared.matchkey.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import android.support.annotation.NonNull;

import com.skyworth.smarthome.infrared.matchkey.presenter.IMatchKeyPresenterImpl;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.ui.DialogBg;
import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.util.Util;
import com.smarthome.common.utils.XThemeUtils;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/4/30 15:12.
 */
public class MatchErrorView extends FrameLayout {
    private OnClickListener onClickListener = null;
    private TextView button = null;
    private TextView learn = null;

    public MatchErrorView(@NonNull Context context, OnClickListener onClickListener) {
        super(context);
        this.onClickListener = onClickListener;
        initView();
        addLearnButton();
    }

    private void initView() {
        setBackground(new DialogBg());
        TextView textView = new TextView(getContext());
        textView.setText(R.string.ir_config_error);
        textView.setTextColor(Color.WHITE);
        textView.setTextSize(Util.Dpi(36));
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        layoutParams.topMargin = Util.Div(99);
        addView(textView, layoutParams);

        button = new TextView(getContext());
        button.setTextSize(Util.Dpi(32));
        button.setTextColor(Color.parseColor("#aaFFFFFF"));
        button.setGravity(Gravity.CENTER);
        button.setTag(IMatchKeyPresenterImpl.HANDLE_KEY_RE_MATCH);
        button.setOnClickListener(onClickListener);
        button.setText(R.string.ir_config_error_button);
        button.setFocusable(true);
        button.setFocusableInTouchMode(true);
        final Drawable drawable =  XThemeUtils.getDrawable(Color.parseColor("#19FFFFFF"), 0, Util.Div(2), Util.Div(16));
        button.setBackground(drawable);
        button.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    button.setBackground(new CCFocusDrawable(getContext()).setRadius(Util.Dpi(16)).setSolidColor(getResources().getColor(R.color.white)));
                    button.setTextColor(Color.parseColor("#000000"));
                    button.getPaint().setFakeBoldText(true);
                } else {
                    button.setBackground(drawable);
                    button.setTextColor(Color.parseColor("#aaFFFFFF"));
                    button.getPaint().setFakeBoldText(false);
                }
            }
        });
        layoutParams = new LayoutParams(Util.Div(292), Util.Div(90));
        layoutParams.leftMargin = Util.Div(50);
        layoutParams.topMargin = Util.Div(260);
        addView(button, layoutParams);
    }

    private void addLearnButton() {
        learn = new TextView(getContext());
        learn.setTextSize(Util.Dpi(32));
        learn.setTextColor(Color.parseColor("#aaFFFFFF"));
        learn.setGravity(Gravity.CENTER);
        learn.setTag(IMatchKeyPresenterImpl.HANDLE_KEY_LEARN);
        learn.setOnClickListener(onClickListener);
        learn.setText(R.string.ir_config_error_learn);
        learn.setFocusable(true);
        learn.setFocusableInTouchMode(true);
        final Drawable drawable = XThemeUtils.getDrawable(Color.parseColor("#19FFFFFF"), 0, Util.Div(2), Util.Div(16));
        learn.setBackground(drawable);
        learn.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    learn.setBackground(new CCFocusDrawable(getContext()).setRadius(Util.Dpi(16)).setSolidColor(getResources().getColor(R.color.white)));
                    learn.setTextColor(Color.parseColor("#000000"));
                    learn.getPaint().setFakeBoldText(true);
                } else {
                    learn.setBackground(drawable);
                    learn.setTextColor(Color.parseColor("#aaFFFFFF"));
                    learn.getPaint().setFakeBoldText(false);
                }
            }
        });
        LayoutParams layoutParams = new LayoutParams(Util.Div(292), Util.Div(90));
        layoutParams.leftMargin = Util.Div(372);
        layoutParams.topMargin = Util.Div(260);
        addView(learn, layoutParams);
    }

    public void show() {
        post(new Runnable() {
            @Override
            public void run() {
                button.requestFocus();
            }
        });
    }
}
