package com.skyworth.smarthome.service.push.binder.model;

import android.content.Intent;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.bean.MessageBean;
import com.skyworth.smarthome.common.event.DeviceInfoUptateEvent;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.message.MessageDialog;
import com.skyworth.smarthome.service.model.ISecondScreenModel;
import com.skyworth.smarthome.service.model.ISmartDeviceDataModel;
import com.smarthome.common.utils.EmptyUtils;
import com.swaiot.aiotlib.common.util.ThreadManager;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

/**
 * Describe:设备状态变化Model
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/3/5
 */
public class DeviceStatusModel implements IDeviceStatusModel {

    public final static String ACTION_AIOT_HOME_STATUS = "com.skyworth.smarthome.statusinfo";//智慧家庭数据提供Action
    public final static String KEY_AIOT_HOME_STAUS = "aiot_status_info";//key值
    public DeviceStatusModel() {
    }

    @Override
    public void sendAiotHomeStatusUpdateEvent(String data) {
        Intent intent = new Intent(ACTION_AIOT_HOME_STATUS);
        intent.putExtra(KEY_AIOT_HOME_STAUS,data);
        SmartHomeTvLib.getContext().sendBroadcast(intent);
    }

    @Override
    public void updateDeviceInfo(String data) {
        if (EmptyUtils.isEmpty(data)) {
            return;
        }
        try {
            JSONObject jsonObject = JSONObject.parseObject(data);
            String deviceId = jsonObject.getString("device_id");
            String deviceName = jsonObject.getString("device_name");
            List<String> voice_tips = new ArrayList<>();
            JSONArray jsonArray = jsonObject.getJSONArray("voice_tips");
            for(int i=0;i<jsonArray.size();i++){
                voice_tips.add(jsonArray.get(i).toString());
            }
            List<DeviceInfo> deviceInfoList = ISmartDeviceDataModel.INSTANCE.getCacheSmartDeviceList();
            if (EmptyUtils.isNotEmpty(deviceInfoList)) {
                    for (int i = 0; i < deviceInfoList.size(); i++) {
                        DeviceInfo item = deviceInfoList.get(i);
                        if (EmptyUtils.isNotEmpty(deviceId)&&deviceId.equals(item.device_id)) {
                            item.device_name = deviceName;
                            item.voice_tips = voice_tips;
                            deviceInfoList.set(i, item);
                            AppData.getInstance().setDeviceInfoList(deviceInfoList);
                            break;
                    }
                    EventBus.getDefault().post(new DeviceInfoUptateEvent());
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void showDeviceNotify(final String data) {
        if (EmptyUtils.isEmpty(data))
            return;
        ISecondScreenModel.INSTANCE.sendNotifyMsg(data);
        JSONObject jsonObject = JSONObject.parseObject(data);
        final String content = jsonObject.getString("notify_msg");
        final String deviceIconUrl = jsonObject.getString("device_img_url");
        if (EmptyUtils.isNotEmpty(content)) {
           MessageBean messageBean = new MessageBean();
            messageBean.type = AppConstants.MESSAGE_TYPE.DEVICE_NOTICE;
            messageBean.imgUrl = deviceIconUrl;
            messageBean.content = content;
            showMessageDialog(messageBean);
        }
    }

    @Override
    public void showDeviceAlert(final String data) {
        if (EmptyUtils.isEmpty(data))
            return;
        ISecondScreenModel.INSTANCE.sendAlertMsg(data);
        JSONObject jsonObject = JSONObject.parseObject(data);
        final String content = jsonObject.getString("alert_msg");
        if (EmptyUtils.isNotEmpty(content)) {
            MessageBean messageBean = new MessageBean();
            messageBean.type = AppConstants.MESSAGE_TYPE.DEVICE_ALERT;
            messageBean.content = content;
            showMessageDialog(messageBean);
        }
    }

    @Override
    public void showMessageDialog(final MessageBean messageBean){
        ThreadManager.getInstance().uiThread(new Runnable() {
            @Override
            public void run() {
                MessageDialog dialog = new MessageDialog();
                dialog.showDialog(messageBean);
            }
        });
    }
}
