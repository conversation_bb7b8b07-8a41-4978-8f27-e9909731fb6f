package com.skyworth.smarthome.infrared.matchkey.view;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import android.support.annotation.NonNull;

import com.skyworth.smarthome.infrared.matchkey.presenter.IMatchKeyPresenterImpl;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.ui.DialogBg;
import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.util.Util;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/4/30 11:21.
 */
public class MatchReadyView extends FrameLayout {
    private TextView title = null;
    private TextView tip = null;
    private TextView button = null;
    private OnClickListener onClickListener = null;

    public MatchReadyView(@NonNull Context context, OnClickListener onClickListener) {
        super(context);
        this.onClickListener = onClickListener;
        setBackground(new DialogBg());
        initView();
    }

    private void initView() {
        title = new TextView(getContext());
        title.setTextSize(Util.Dpi(36));
        title.setTextColor(Color.WHITE);
        title.getPaint().setFakeBoldText(true);
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.topMargin = Util.Div(75);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        addView(title, layoutParams);

        tip = new TextView(getContext());
        tip.setTextSize(Util.Dpi(32));
        tip.setTextColor(Color.parseColor("#aaFFFFFF"));
        layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.topMargin = Util.Div(144);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        addView(tip, layoutParams);

        button = new TextView(getContext());
        button.setFocusableInTouchMode(true);
        button.setFocusable(true);
        button.setGravity(Gravity.CENTER);
        button.setTag(IMatchKeyPresenterImpl.HANDLE_KEY_START);
        button.setTextSize(Util.Dpi(32));
        button.setTextColor(Color.parseColor("#000000"));
        button.setOnClickListener(onClickListener);
        button.setBackground(new CCFocusDrawable(getContext()).setRadius(Util.Dpi(16)).setSolidColor(getResources().getColor(R.color.white)));
        layoutParams = new LayoutParams(Util.Div(614), Util.Div(90));
        layoutParams.topMargin = Util.Div(256);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        addView(button, layoutParams);
    }

    public void show(String titleText, String tipText, String buttonText) {
        if (title != null) {
            title.setText(titleText);
        }
        if (tip != null) {
            tip.setText(tipText);
        }
        if (button != null) {
            button.setText(buttonText);
            post(new Runnable() {
                @Override
                public void run() {
                    button.requestFocus();
                }
            });
        }
    }
}
