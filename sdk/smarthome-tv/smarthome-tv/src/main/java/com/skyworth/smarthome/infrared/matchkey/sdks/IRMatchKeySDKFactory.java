package com.skyworth.smarthome.infrared.matchkey.sdks;

import com.skyworth.smarthome.infrared.matchkey.sdks.hxd.IRMatchKeySDKHXD;
import com.skyworth.smarthome.infrared.matchkey.sdks.kk.IRMatchKeySDKKK;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/9/2 16:15.
 */
public class IRMatchKeySDKFactory {
    private static final String JI_DING_HE_TYPE_ID = "5";

    public static IBaseIRMatchSDK create(String prioritySDK, String hxdHas, String kkBrandId, String typeId) {
        if (JI_DING_HE_TYPE_ID.equals(typeId)) {
            return new IRMatchKeySDKHXD();
        } else if (prioritySDK.toLowerCase().equals(IRMatchKeySDKKK.KK) && !kkBrandId.equals("0")) {
            return new IRMatchKeySDKKK();
        } else if (Boolean.TRUE.toString().equals(hxdHas)) {
            return new IRMatchKeySDKHXD();
        }
        return null;
    }
}
