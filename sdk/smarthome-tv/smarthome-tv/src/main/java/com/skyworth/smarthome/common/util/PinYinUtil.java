package com.skyworth.smarthome.common.util;

import com.github.promeg.pinyinhelper.Pinyin;
import com.github.promeg.pinyinhelper.PinyinMapDict;

import java.util.HashMap;
import java.util.Map;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/9/6 16:13.
 */
public class PinYinUtil {
    static {
        Pinyin.init(Pinyin.newConfig().with(new PinyinMapDict() {
            @Override
            public Map<String, String[]> mapping() {
                HashMap<String, String[]> map = new HashMap<>();
                map.put("长城", new String[]{"CHANG", "CHENG"});
                map.put("长虹", new String[]{"CHANG", "HONG"});
                return map;
            }
        }));
    }

    public static String toPinYin(char c) {
        return Pinyin.toPinyin(c);
    }

    public static String toPinYin(String string) {
        return Pinyin.toPinyin(string, "");
    }

    public static boolean isChinese(char c) {
        return Pinyin.isChinese(c);
    }
}
