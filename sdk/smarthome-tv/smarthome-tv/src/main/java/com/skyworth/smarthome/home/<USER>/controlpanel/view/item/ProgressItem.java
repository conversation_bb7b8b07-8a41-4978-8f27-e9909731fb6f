package com.skyworth.smarthome.home.smartdevice.controlpanel.view.item;

import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.skyworth.smarthome.home.smartdevice.controlpanel.common.DependList;
import com.skyworth.smarthome.home.smartdevice.controlpanel.common.itemdata.ProgressControlData;
import com.skyworth.smarthome.home.smartdevice.controlpanel.view.base.BaseControlItem;
import com.skyworth.smarthome.R;
import com.skyworth.util.Util;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ProgressItem extends BaseControlItem<ProgressControlData> {
    public static final String TYPE = "progress";
    private TextView mTitle = null;
    private TextView mValue = null;
    private View mLeftArrow = null;
    private View mRightArrow = null;
    private View mLeftArrowClick = null;
    private View mRightArrowClick = null;
    private FrameLayout mValueLayout = null;

    public ProgressItem(Context context) {
        super(context);
        initText();
        initValue();
        initListener();
    }

    private void initText() {
        mTitle = new TextView(getContext());
        mTitle.setTextSize(Util.Dpi(32));
        mTitle.setTextColor(Color.parseColor("#aaFFFFFF"));
        mTitle.setGravity(Gravity.CENTER);
        mTitle.getPaint().setFakeBoldText(true);
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.leftMargin = Util.Div(20);
        layoutParams.gravity = Gravity.CENTER_VERTICAL;
        addView(mTitle, layoutParams);
    }

    private void initValue() {
        mValueLayout = new FrameLayout(getContext());
        mValueLayout.setBackground(mTitleBgUnFocus);
        mValueLayout.setMinimumWidth(Util.Div(200));
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, Util.Div(50));
        layoutParams.gravity = Gravity.CENTER_VERTICAL | Gravity.RIGHT;
        layoutParams.rightMargin = Util.Div(20);
        addView(mValueLayout, layoutParams);

        mLeftArrow = new View(getContext());
        mLeftArrow.setBackgroundResource(R.drawable.arrow_left);
        LayoutParams layoutParamsItem = new LayoutParams(Util.Div(11), Util.Div(22));
        layoutParamsItem.gravity = Gravity.LEFT|Gravity.CENTER_VERTICAL;
        layoutParamsItem.leftMargin = Util.Div(20);
        mValueLayout.addView(mLeftArrow, layoutParamsItem);

        mLeftArrowClick = new View(getContext());
        mLeftArrowClick.setBackgroundResource(R.color.translucent);
        layoutParamsItem = new LayoutParams(Util.Div(50), Util.Div(50));
        layoutParamsItem.gravity = Gravity.LEFT;
        mValueLayout.addView(mLeftArrowClick, layoutParamsItem);

        mValue = new TextView(getContext());
        mValue.setTextSize(Util.Dpi(28));
        mValue.setTextColor(Color.argb(204, 255, 255, 255));
        mValue.setGravity(Gravity.CENTER);
        mValue.setPadding(Util.Div(32),0,Util.Div(32),0);
        mValue.setSingleLine(true);
        mValue.setMinWidth(Util.Div(120));
        layoutParamsItem = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParamsItem.gravity = Gravity.CENTER;
        mValueLayout.addView(mValue, layoutParamsItem);

        mRightArrow = new View(getContext());
        mRightArrow.setBackgroundResource(R.drawable.arrow_right);
        layoutParamsItem = new LayoutParams(Util.Div(11), Util.Div(22));
        layoutParamsItem.gravity = Gravity.RIGHT|Gravity.CENTER_VERTICAL;
        layoutParamsItem.rightMargin = Util.Div(20);
        mValueLayout.addView(mRightArrow, layoutParamsItem);

        mRightArrowClick = new View(getContext());
        mRightArrowClick.setBackgroundResource(R.color.translucent);
        layoutParamsItem = new LayoutParams(Util.Div(50), Util.Div(50));
        layoutParamsItem.gravity = Gravity.RIGHT;
        mValueLayout.addView(mRightArrowClick, layoutParamsItem);
    }

    private void initListener(){
        mLeftArrowClick.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if(isEnable){
                    onKeyLeft();
                }
            }
        });

        mRightArrowClick.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if(isEnable){
                    onKeyRight();
                }
            }
        });
    }

    @Override
    protected void refreshUI() {
        logi("refreshUI:");
        if (mData == null) {
            loge("refreshUI: mData == null");
            return;
        }
        mTitle.setText(mData.title);
        String value = mStatus.getString(mData.data_field);
        String showText;
        if (mData.transfer && mData.values != null) {
            showText = mData.values.get(value);
        } else {
            // Limit min or max value ONLY if support modify.
            // When the item can't set, it's value may not be in the range.
            boolean supportModify = supportModify();
            if(supportModify && Integer.parseInt(value) >= Integer.parseInt(mData.max)){
                value = mData.max;
            }else if(supportModify && Integer.parseInt(value) < Integer.parseInt(mData.min)){
                value = mData.min;
            }
            showText = value + mData.unit;
        }
        logi("refreshUI: " + showText);
        if (showText != null && !showText.equals(mValue.getText().toString())) {
            mValue.setText(showText);
        }
        if (showText == null) {
            mValue.setText("");
        }
    }

    @Override
    public void onRecycle() {

    }

    @Override
    public void onFocus(boolean hasFocus) {
        super.onFocus(hasFocus);
        if (hasFocus) {
            mTitle.setTextColor(Color.parseColor("#000000"));
            mValue.setTextColor(Color.parseColor("#cc000000"));
            mValue.getPaint().setFakeBoldText(true);
            mValueLayout.setBackground(mTitleBgFocus);
            mLeftArrow.setBackgroundResource(R.drawable.arrow_left_focus);
            mRightArrow.setBackgroundResource(R.drawable.arrow_right_focus);
        } else {
            mTitle.setTextColor(Color.parseColor("#aaFFFFFF"));
            mValue.setTextColor(Color.parseColor("#ccFFFFFF"));
            mValue.getPaint().setFakeBoldText(false);
            mValueLayout.setBackground(mTitleBgUnFocus);
            mLeftArrow.setBackgroundResource(R.drawable.arrow_left);
            mRightArrow.setBackgroundResource(R.drawable.arrow_right);
        }
    }

    @Override
    public boolean onKeyLeft() {
        if (mData == null) {
            loge("onKeyLeft: mData == null");
            return true;
        }
        BigDecimal currentValue = null;
        try {
            currentValue = new BigDecimal(mStatus.getString(mData.data_field));
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        if (currentValue == null) {
            loge("onKeyLeft: current value == null");
            return true;
        }

        String resultValue;
        BigDecimal step = new BigDecimal(mData.step);
        if (currentValue.subtract(step).compareTo(new BigDecimal(mData.min)) <= 0) {
            resultValue = String.valueOf(mData.min);
        } else {
            resultValue = currentValue.subtract(step).toString();
        }
        if (!TextUtils.isEmpty(resultValue) && mListener != null) {
            logi("onKeyLeft: " + mData.data_field + " " + resultValue);
            Map<String, String> status = new HashMap<>();
            status.put(mData.data_field, resultValue);
            controlDevice(status);
        }
        return true;
    }

    @Override
    public boolean onKeyRight() {
        if (mData == null) {
            loge("onKeyRight: mData == null");
            return true;
        }
        BigDecimal currentValue = null;
        try {
            currentValue = new BigDecimal(mStatus.getString(mData.data_field));
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        if (currentValue == null) {
            loge("onKeyRight: current value == null");
            return true;
        }

        String resultValue = null;
        BigDecimal step = new BigDecimal(mData.step);
        if (currentValue.add(step).compareTo(new BigDecimal(mData.max)) >= 1) {
            resultValue = String.valueOf(mData.max);
        } else {
            resultValue = currentValue.add(step).toString();
        }
        if (!TextUtils.isEmpty(resultValue) && mListener != null) {
            logi("onKeyRight: " + mData.data_field + " " + resultValue);
            Map<String, String> status = new HashMap<>();
            status.put(mData.data_field, resultValue);
            controlDevice(status);
        } else {
            logi("onKeyRight: resultValue: " + resultValue + " mListener: " + mListener);
        }
        return true;
    }

    @Override
    public void enable() {
        super.enable();
        mLeftArrowClick.setFocusable(true);
        mRightArrowClick.setFocusable(true);
    }

    @Override
    public void disable() {
        super.disable();
        mLeftArrowClick.setFocusable(false);
        mRightArrowClick.setFocusable(false);
    }

    @Override
    public String getType() {
        return TYPE;
    }

    @Override
    public boolean canFocusable() {
        return isEnable;
    }

    @Override
    public boolean canFocusableDefault() {
        return true;
    }

    private boolean supportModify() {
        boolean supportModify = true;
        List<DependList.BaseDependData> dependResult = checkDepend(DependList.Depend.TYPE_ENABLE);
        for (DependList.BaseDependData dependData : dependResult) {
            if (dependData.result == DependList.BaseDependData.RESULT_COMPARE_FAILED) {
                supportModify = false;
                break;
            }
        }
        return supportModify;
    }
}
