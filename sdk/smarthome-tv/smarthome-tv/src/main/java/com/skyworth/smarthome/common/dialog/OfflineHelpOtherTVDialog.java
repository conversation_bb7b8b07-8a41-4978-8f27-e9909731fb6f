package com.skyworth.smarthome.common.dialog;

import android.content.Context;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;


import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.base.BaseCommonDialog;
import com.skyworth.smarthome.common.ui.CommonFocusBox;
import com.skyworth.smarthome.common.util.DialogCommonUtil;
import com.skyworth.smarthome.common.util.ViewsBuilder;

public class OfflineHelpOtherTVDialog extends BaseCommonDialog {

    private CommonFocusBox mCancleBtn;
    private OfflineHelpOtherTVDialog.OfflineHelpOtherTVCallBack mOfflineHelpOtherTVCallBack;

    public OfflineHelpOtherTVDialog(@NonNull Context context) {
        this(context, R.style.common_style);
    }

    public OfflineHelpOtherTVDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    @Override
    protected void initParams() {

    }

    public static OfflineHelpOtherTVDialog newInstance(Context context) {
        OfflineHelpOtherTVDialog fragment = new OfflineHelpOtherTVDialog(context, R.style.common_style);
        return fragment;
    }

    public void setOfflineHelpTVCallBack(OfflineHelpOtherTVDialog.OfflineHelpOtherTVCallBack offlineHelpOtherTVCallBack) {
        this.mOfflineHelpOtherTVCallBack = offlineHelpOtherTVCallBack;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        View contentView = ViewsBuilder.getOfflineHelpOfTVOther(getContext());
        setContentView(contentView);
        contentView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dismiss();
            }
        });

        mCancleBtn = findViewById(R.id.offline_help_cancle);
        mCancleBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        mCancleBtn.requestFocus();
    }

    @Override
    public void onStart() {
        super.onStart();
        Window window = getWindow();
        WindowManager.LayoutParams windowParams = window.getAttributes();
        windowParams.width = WindowManager.LayoutParams.MATCH_PARENT;
        windowParams.height = WindowManager.LayoutParams.MATCH_PARENT;
        windowParams.gravity = Gravity.CENTER;
        windowParams.dimAmount = 0.50f;
        windowParams.flags |= WindowManager.LayoutParams.FLAG_DIM_BEHIND;
        window.setAttributes(windowParams);
    }

    public interface OfflineHelpOtherTVCallBack {
        void callBack();
    }

    @Override
    public void show() {
        super.show();
        DialogCommonUtil.putDialog(DialogCommonUtil.DIALOG_KEY_OFFLINE_HELP_WIFI, this);
    }

    @Override
    protected void onDismiss() {
        super.onDismiss();
        DialogCommonUtil.removeDialog(DialogCommonUtil.DIALOG_KEY_OFFLINE_HELP_WIFI);
    }
}
