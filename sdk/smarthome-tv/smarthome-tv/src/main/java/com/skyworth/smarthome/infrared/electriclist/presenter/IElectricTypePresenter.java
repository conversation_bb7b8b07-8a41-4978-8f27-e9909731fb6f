package com.skyworth.smarthome.infrared.electriclist.presenter;

import android.view.View;

import com.skyworth.smarthome.infrared.controldevices.view.IShowRemoteControl;
import com.skyworth.smarthome.infrared.electriclist.model.DeviceTypeListData;
import com.skyworth.smarthome.infrared.electriclist.view.IElectircTypeView;

/**
 * Created by fc on 2019/4/25
 * Describe:
 */
public interface IElectricTypePresenter {

    void setDeviceId(String deviceId);

    void setDeviceName(String deviceName);

    void setDeviceTypeId(String id);

    public void loadList();

    public void onItemClick(View view, DeviceTypeListData itemData, int position);

    public void setView(IElectircTypeView view);

    public void setIShowRemoteControl(IShowRemoteControl mShowRemoteControl);
}
