package com.skyworth.smarthome.common.util;

import android.util.Log;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.base.BaseCommonDialog;
import com.skyworth.smarthome.common.base.BaseSysDialog;
import com.skyworth.smarthome.common.dialog.DownloadRrCodeDialog;
import com.skyworth.smarthome.devices.apconfig.ApConfigDialog;
import com.skyworth.smarthome.devices.discover.dialog.AddDeviceDialog;
import com.skyworth.smarthome.devices.discover.dialog.AddDeviceResultDialog;
import com.skyworth.smarthome.devices.discover.dialog.DiscoverNearDeviceDialog;
import com.skyworth.smarthome.devices.discover.dialog.ScanNearbyDeviceHelpDialog;
import com.skyworth.smarthome.home.smartdevice.controlpanel.thirdapp.view.ThirdAppLoadingDialog;
import com.skyworth.smarthome.infrared.InfraredDeviceDialog;
import com.skyworth.smarthome.infrared.learn.IRLearnDialog;
import com.skyworth.smarthome.infrared.matchkey.IRMatchKeyDialog;
import com.smarthome.common.utils.Android;
import com.smarthome.common.utils.EmptyUtils;

import java.util.HashMap;
import java.util.Map;


/**
 * Description: 全局Dialog启动控制工具类（Activity改成Dialog） <br>
 * Created by wzh on 2019/4/8 17:31.
 */
public class DialogCommonUtil {

    public static final String DIALOG_KEY_DEVICE_CONTROL = "dialog_key_device_control";
    public static final String DIALOG_KEY_OFFLINE_HELP_WIFI = "dialog_key_offline_help_wifi";
    public static final String DIALOG_KEY_OFFLINE_HELP_ZIGBEE = "dialog_key_offline_help_zigbee";


    private static Map<String, BaseCommonDialog> mDialogCache = new HashMap<>();

    public synchronized static void putDialog(String key, BaseCommonDialog dialog) {
        mDialogCache.put(key, dialog);
    }

    public synchronized static void removeDialog(String key) {
        if (mDialogCache.containsKey(key)){
            mDialogCache.remove(key);
        }
    }

    public synchronized static void dismissDialog(final String dialogKey) {
        if (mDialogCache.containsKey(dialogKey)) {
            dismissCacheDialog(dialogKey);
        }
    }


    private static void dismissCacheDialog(String dialogKey) {
        try {
            BaseCommonDialog dialog = mDialogCache.get(dialogKey);
            if (EmptyUtils.isNotEmpty(dialog) && dialog.isShowing()) {
                dialog.dismiss();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 关闭存在的所有dialog
     */
    public synchronized static void dismissAlllDialog() {
        for (String key : mDialogCache.keySet()) {
            dismissDialog(key);
        }
    }
}
