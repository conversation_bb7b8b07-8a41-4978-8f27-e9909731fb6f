package com.skyworth.smarthome.infrared.matchkey.presenter;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.coocaa.app.core.app.AppCoreApplication;
import com.skyworth.smarthome.devices.apconfig.presenter.stepmanager.BaseStepManager;
import com.skyworth.smarthome.infrared.matchkey.presenter.step.ConfirmResultStep;
import com.skyworth.smarthome.infrared.matchkey.presenter.step.MatchErrorStep;
import com.skyworth.smarthome.infrared.matchkey.presenter.step.MatchReadyStep;
import com.skyworth.smarthome.infrared.matchkey.presenter.step.SendIRCodeStep;
import com.skyworth.smarthome.infrared.matchkey.sdks.IBaseIRMatchSDK;
import com.skyworth.smarthome.infrared.matchkey.sdks.IRMatchKeyResultListener;
import com.skyworth.smarthome.infrared.matchkey.view.IMatchKeyView;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.bean.IRAddDeviceData;
import com.skyworth.smarthome.common.bean.IRMatchKeyData;
import com.skyworth.smarthome.common.util.Utils;
import com.skyworth.smarthome.infrared.learn.IRLearnDialog;
import com.skyworth.smarthome.infrared.matchkey.model.IMatchKeyModel;
import com.skyworth.smarthome.infrared.matchkey.sdks.IRMatchKeySDKFactory;
import com.skyworth.smarthome.service.model.FunctionGoToModel;
import com.skyworth.smarthome.service.model.ISmartDeviceDataModel;
import com.skyworth.smarthome.voicehandle.SmartHomeAI;
import com.smarthome.common.utils.EmptyUtils;

import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

import static com.skyworth.smarthome.infrared.matchkey.IRMatchKeyDialog.PARAM_IR_HOST_DEVICE_ID;
import static com.skyworth.smarthome.infrared.matchkey.IRMatchKeyDialog.PARAM_IR_HOST_NAME;
import static com.skyworth.smarthome.infrared.matchkey.IRMatchKeyDialog.PARAM_IR_HOST_TYPE_ID;
import static com.skyworth.smarthome.infrared.matchkey.IRMatchKeyDialog.PARAM_IR_HXD_HAS;
import static com.skyworth.smarthome.infrared.matchkey.IRMatchKeyDialog.PARAM_IR_KK_BRAND_ID;
import static com.skyworth.smarthome.infrared.matchkey.IRMatchKeyDialog.PARAM_IR_SDK_PRIORITY;
import static com.skyworth.smarthome.infrared.matchkey.IRMatchKeyDialog.PARAM_IR_SLAVE_BRAND;
import static com.skyworth.smarthome.infrared.matchkey.IRMatchKeyDialog.PARAM_IR_SLAVE_TYPE_ID;
import static com.skyworth.smarthome.infrared.matchkey.IRMatchKeyDialog.PARAM_IR_SLAVE_TYPE_NAME;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/4/29 18:33.
 */
public class IMatchKeyPresenterImpl implements IMatchKeyPresenter {
    public static final String TAG = "irconfig";
    private BaseStepManager<IMatchKeyPresenterImpl> stepManager = null;
    private Context mContext = null;
    private IMatchKeyView mView = null;
    private IMatchKeyModel mModel = null;
    private String mIrSlaveTypeName = null;
    private String mIrSlaveTypeId = null;
    private String mIrSlaveBrand = null;
    private String mIrHostDeviceId = null;
    private Map<String, String> mParams = null;
    private String irDeviceId = null;

    public static final String HANDLE_KEY_START = "start";
    public static final String HANDLE_KEY_SEND_FINISH = "send_finish";
    public static final String HANDLE_KEY_CONFIRM_YES = "confirm_yes";
    public static final String HANDLE_KEY_CONFIRM_NO = "confirm_no";
    public static final String HANDLE_KEY_CONFIRM_AGAIN = "confirm_again";
    public static final String HANDLE_KEY_RE_MATCH = "re_match";
    public static final String HANDLE_KEY_LEARN = "learn";

    private IRMatchListener listener = null;
    private IBaseIRMatchSDK matchSDK = null;
    private IRMatchKeyResultListener matchKeyResultListener = new IRMatchKeyResultListener() {
        @Override
        public void onMatchSuccess(String remoteId) {
            Log.i(TAG, "[[[onMatchSuccess]]]");
            saveIRConfig();
            goConfigSuccess();
            speechSuccess();
            logTelecontrolMatchResult(true);
        }

        @Override
        public void onMatchFail() {
            Log.i(TAG, "[[[onMatchFail]]]");
            logTelecontrolMatchResult(false);
            stepManager.jumpTo(MatchErrorStep.STEP_TAG);
        }

        @Override
        public void onMatchNextKey() {
            Log.i(TAG, "[[[onMatchNextKey]]]");
            stepManager.jumpTo(SendIRCodeStep.STEP_TAG);
        }

        @Override
        public void onError(String msg) {
            Log.i(TAG, "[[[onError]]]: " + msg);
            stepManager.jumpTo(MatchErrorStep.STEP_TAG);
        }
    };

    @Override
    public void setListener(IRMatchListener listener) {
        this.listener = listener;
    }

    public interface IRMatchListener {
        void dismiss();
    }

    @Override
    public void create(Context context, IMatchKeyView view, IMatchKeyModel model) {
        mContext = context;
        mView = view;
        mModel = model;
        stepManager = new BaseStepManager<>(context, this);
        stepManager.addStep(new MatchReadyStep());
        stepManager.addStep(new SendIRCodeStep());
        stepManager.addStep(new ConfirmResultStep());
        stepManager.addStep(new MatchErrorStep());
    }

    @Override
    public void startMatch(Map<String, String> params) {
        mParams = params;
        mIrSlaveTypeName = params.get(PARAM_IR_SLAVE_TYPE_NAME);
        mIrSlaveTypeId = params.get(PARAM_IR_SLAVE_TYPE_ID);
        mIrSlaveBrand = params.get(PARAM_IR_SLAVE_BRAND);
        mIrHostDeviceId = params.get(PARAM_IR_HOST_DEVICE_ID);

        String kkBrandID = params.get(PARAM_IR_KK_BRAND_ID);
        String hxdHas = params.get(PARAM_IR_HXD_HAS);
        String prioritySDK = params.get(PARAM_IR_SDK_PRIORITY);
        Log.i(TAG, "<<<startMatch>>>: params: " + Utils.printMap(params));
        try {
            String ret = EmptyUtils.checkParams(mIrSlaveTypeName, mIrSlaveTypeId, mIrSlaveBrand, mIrHostDeviceId);
            Log.i(TAG, "startMatch: " + ret);
        } catch (Exception e) {
            Log.e(TAG, "startMatch: " + e.getMessage());
        }
        matchSDK = IRMatchKeySDKFactory.create(prioritySDK, hxdHas, kkBrandID, mIrSlaveTypeId);
        if (matchSDK == null) {
            Log.e(TAG, "startMatch: match sdk == null");
            return;
        }
        matchSDK.setResultListener(matchKeyResultListener);
        matchSDK.init(params);
        AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                stepManager.start();
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void startMatchInternal() {
        matchSDK.start();
    }

    @Override
    public boolean sendIR() {
        Map<String, Object> params = matchSDK.getSendIrParams();
        Log.i(TAG, "<<<sendIR>>>: " + Utils.printMap(params));
        return mModel.sendIRCode(params);
    }

    @Override
    public void handleClick(final String key) {
        AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                stepManager.input(key);
                return Unit.INSTANCE;
            }
        });
        if (key.equals(HANDLE_KEY_START)) {
            logCloseDeviceOnclick("确认已关闭，开始匹配");
        }
    }

    private void logCloseDeviceOnclick(String str) {
//        try {
//            Map<String, String> map = new HashMap<>();
//            map.put("button_name", str);
//            LoggerImpl.Companion.onEvent(EVENT_INFRARED_CLOSE_DEVICE_ONCLICK, map);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
    }

    @Override
    public void showReadyView() {
        final String title = mContext.getResources().getString(R.string.ir_config_ready_title, mIrSlaveTypeName);
        final String tip = mContext.getResources().getString(R.string.ir_config_ready_tip, mIrSlaveTypeName);
        final String button = mContext.getResources().getString(R.string.ir_config_ready_button);
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mView.showReadyView(title, tip, button);
                return Unit.INSTANCE;
            }
        });
        logCloseDeviceShow(mIrSlaveTypeName);
    }

    private void logCloseDeviceShow(String deviceName) {
//        try {
//            Map<String, String> map = new HashMap<>();
//            map.put("device_name", deviceName);
//            LoggerImpl.Companion.onEvent(EVENT_INFRARED_CLOSE_DEVICE_SHOW, map);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
    }

    @Override
    public void showSendingView() {
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mView.showSendingView();
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void showConfirmView() {
        IRMatchKeyData data = matchSDK.getCurrentKeyInfo();
        if (data == null) {
            return;
        }
        final String sendKeyTip = mContext.getResources().getString(R.string.ir_config_confirm_send_key_tip, data.key_name);
        final String confirmTip = mContext.getResources().getString(R.string.ir_config_confirm_tip, mIrSlaveTypeName);
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                int index = matchSDK.getCurrentIndex() + 1;
                int size = matchSDK.getCurrentKeyListCount();
                mView.showConfirmView(index, size, sendKeyTip, confirmTip);
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void keyResponse() {
        matchSDK.onKeyResponse();
    }

    @Override
    public void keyNotResponse() {
        matchSDK.onKeyNotResponse();
    }

    private void logTelecontrolMatchResult(boolean isSuccess) {
//        try {
//            Map<String, String> map = new HashMap<>();
//            map.put("match_result", isSuccess ? "成功" : "失败");
//            String origin;
//            if (DeviceUtil.isIRAI(String.valueOf(mParams.get(IREntry.DEVICE_TYPE_ID_KEY)))) {
//                origin = DataConstants.KEY_ENTER_INFRARED_ORIGIN_IRAI;
//            } else {
//                origin = DataConstants.KEY_ENTER_INFRARED_ORIGIN_IRTV;
//            }
//            map.put("origin", origin);
//            LoggerImpl.Companion.onEvent(EVENT_INFRARED_TELECONTROL_MATCH_RESULT, map);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
    }

    @Override
    public boolean saveIRConfig() {
        Map<String, Object> params = matchSDK.getSaveMatchResultParams();
        IRAddDeviceData res = mModel.addNewIRDevice(params);
        irDeviceId = res == null ? "" : res.id;
        Log.i(TAG, "<<<saveIRConfig>>>: irDeviceId: " + irDeviceId);
        return res != null;
    }

    @Override
    public void goConfigSuccess() {
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mView.showMatchSuccessView(irDeviceId);
                mView.getView().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (listener != null) {
                            listener.dismiss();
                        }
                        FunctionGoToModel.INSTANCE.goToInfraredDeviceList("click",mIrHostDeviceId);
                    }
                }, 3000);
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void speechSuccess() {
        AppCoreApplication.Companion.workerThread(3000, new Function0<Unit>() {
            @Override
            public Unit invoke() {
                String voiceFromList = "";
                List<DeviceInfo> mDeviceList = ISmartDeviceDataModel.INSTANCE.getSmartDeviceList();
                for (DeviceInfo item : mDeviceList) {
                    if (item.device_id.equals(irDeviceId) && EmptyUtils.isNotEmpty(item.voice_tips)) {
                        voiceFromList = item.voice_tips.get(0);
                        break;
                    }
                }
                Log.i(TAG, "speechSuccess: voice: " + voiceFromList);
                if (!TextUtils.isEmpty(voiceFromList)) {
                    String voice = mContext.getResources().getString(R.string.ir_config_success_speech, mIrSlaveTypeName, voiceFromList);
                    SmartHomeAI.playVoiceTTS(mContext, voice);
                }
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void showMatchErrorView() {
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mView.showMatchErrorView();
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void launchIRLearn() {
        Log.i(TAG, "launchIRLearn");
        IRLearnDialog.launch(mParams.get(PARAM_IR_HOST_DEVICE_ID), mParams.get(PARAM_IR_HOST_NAME), mParams.get(PARAM_IR_HOST_TYPE_ID), mParams.get(PARAM_IR_SLAVE_TYPE_NAME), mParams.get(PARAM_IR_SLAVE_TYPE_ID));
    }

    @Override
    public void destroy() {
        if (stepManager != null && stepManager.getCurrentStepTag().equals(MatchReadyStep.STEP_TAG)) {
            logCloseDeviceOnclick("返回");
        }
        mIrSlaveTypeName = null;
        mIrSlaveTypeId = null;
        mIrSlaveBrand = null;
        if (matchSDK != null) {
            matchSDK.destroy();
        }
    }
}
