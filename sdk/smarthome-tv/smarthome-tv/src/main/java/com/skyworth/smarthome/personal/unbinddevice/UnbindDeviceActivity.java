package com.skyworth.smarthome.personal.unbinddevice;

import android.os.Bundle;
import android.support.v4.app.FragmentTransaction;


import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.base.BaseActivity;
import com.skyworth.smarthome.common.util.Contants;

/**
 * @Description: 解绑设备
 * @Author: wzh
 * @CreateDate: 2020/6/3
 */
public class UnbindDeviceActivity extends BaseActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main2);

        addFragment();
    }

    private void addFragment() {
        Bundle bundle = getIntent().getExtras();
        String currentFamilyId = bundle.getString(Contants.COOCAA_INTENT_CURRENT_FAMILY_ID);

        FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
        UnbindDeviceFragment unbindDeviceFragment = UnbindDeviceFragment.newInstance(currentFamilyId);
        ft.replace(R.id.content, unbindDeviceFragment);
        ft.commit();
    }
}
