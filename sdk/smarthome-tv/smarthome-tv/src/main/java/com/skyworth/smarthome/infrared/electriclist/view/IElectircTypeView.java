package com.skyworth.smarthome.infrared.electriclist.view;

import android.content.Context;
import android.view.View;

import com.skyworth.smarthome.infrared.electriclist.model.DeviceTypeListData;
import com.skyworth.smarthome.infrared.electriclist.presenter.IElectricTypePresenter;

import java.util.List;

/**
 * Created by fc on 2019/4/25
 * Describe:
 */
public interface IElectircTypeView {
    /**
     * view 初始化
     *
     * @param context
     * @param presenter
     */
    void createView(Context context, IElectricTypePresenter presenter);

    void showList(List<DeviceTypeListData> list);

    void showLoading();

    void hideLoading();

    void showErrorView(String errorMsg, String errorCode);

    void hideErrorView();

    void getFocus();


    View getView();
}
