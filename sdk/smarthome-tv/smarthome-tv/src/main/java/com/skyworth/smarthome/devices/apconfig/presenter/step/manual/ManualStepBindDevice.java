package com.skyworth.smarthome.devices.apconfig.presenter.step.manual;

import com.coocaa.app.core.app.AppCoreApplication;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.util.NetworkUtils;
import com.skyworth.smarthome.devices.apconfig.presenter.step.BaseStep;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_BIND_FAILED_HIDE;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_BIND_FAILED_OTHER_BIND;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_CONFIG_FINISH;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_CONFIG_PROGRESS_HIDE;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_INPUT_GO_BIND_MOBILE;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_INPUT_GO_LOGIN;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_SET_LOCATION_OK;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_SET_LOCATION_SHOW;


/**
 * Description: <br>
 * Created by XuZexiao on 2019/1/7 21:54.
 */
public class ManualStepBindDevice extends BaseStep {
    public static final String STEP_TAG = "manual_bindDevice";
    @Override
    public void run() {
        super.run();
        int retryTimes = 0;
        while (!NetworkUtils.checkNetWorkCanUse() && retryTimes < 5) {
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            retryTimes++;
            logi("check net times: " + retryTimes);
        }
        if (!presenter.isLogin()) {
            output(STEP_MSG_CONFIG_PROGRESS_HIDE);
            showDialog(STEP_MSG_INPUT_GO_LOGIN);
            presenter.showDialog();
            return;
        }
        if (!presenter.isBindMobile()) {
            output(STEP_MSG_CONFIG_PROGRESS_HIDE);
            showDialog(STEP_MSG_INPUT_GO_BIND_MOBILE);
            presenter.showDialog();
            return;
        }
        output(STEP_MSG_CONFIG_PROGRESS_HIDE);
        output(STEP_MSG_SET_LOCATION_SHOW);
        presenter.showDialog();
    }

    private void bindSuccess() {
        output(STEP_MSG_BIND_FAILED_HIDE);
        output(STEP_MSG_CONFIG_FINISH);
        output(STEP_MSG_CONFIG_PROGRESS_HIDE);
    }

    @Override
    public boolean input(String msg, final Object... params) {
        if (msg.equals(STEP_MSG_SET_LOCATION_OK)) {
            AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
                @Override
                public Unit invoke() {
                    int bindStatus = presenter.getBindStatus();
                    if (bindStatus == 1) {//已绑定
                        presenter.updateDevicePos(params[0].toString());
                        bindSuccess();
                    } else if (bindStatus == 2 || (bindStatus == 3 && !presenter.isMideaDevice())) {
                        // 未绑定, 2 为未绑定, 3 为被绑定到其他账号,
                        // 创维设备(非美的设备)根据需求在设备已经绑定在其他账号的情况下依然进行强制绑定
                        if (presenter.bindDevice(params[0].toString())) {
                            bindSuccess();
                        }
                    }
                    AppData.getInstance().setApconfigDeviceInfo("");
                    AppData.getInstance().setApconfigWifiDevice(null);
                    return Unit.INSTANCE;
                }
            });
            return true;
        } else if (msg.equals(STEP_MSG_INPUT_GO_LOGIN)) {
            presenter.goLogin();
            return true;
        } else if (msg.equals(STEP_MSG_INPUT_GO_BIND_MOBILE)) {
            presenter.goBindMobile();
            return true;
        }
        return false;
    }

    @Override
    public String getTag() {
        return STEP_TAG;
    }

    @Override
    public void destroy() {
        dialogShowTimes.clear();
        output(STEP_MSG_BIND_FAILED_HIDE);
        output(STEP_MSG_CONFIG_PROGRESS_HIDE);
        presenter.hideNotLogin();
        super.destroy();
    }
}
