package com.skyworth.smarthome.home.smartdevice.controlpanel.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;
import android.util.Pair;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import android.support.annotation.NonNull;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.coocaa.app.core.app.AppCoreApplication;
import com.skyworth.smarthome.common.bean.DeviceListHttpBean;
import com.skyworth.smarthome.home.smartdevice.controlpanel.common.itemdata.BaseControlData;
import com.skyworth.smarthome.home.smartdevice.controlpanel.common.itemdata.ControlDataFactory;
import com.skyworth.smarthome.home.smartdevice.controlpanel.common.itemdata.GroupDisplayControlData;
import com.skyworth.smarthome.home.smartdevice.controlpanel.model.IDeviceControlModel;
import com.skyworth.smarthome.home.smartdevice.controlpanel.view.base.BaseControlItem;
import com.skyworth.smarthome.home.smartdevice.controlpanel.view.base.IControlItem;
import com.skyworth.smarthome.home.smartdevice.controlpanel.view.item.GroupDisplayItem;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.bean.AlarmLogBean;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.ui.DialogBg;
import com.skyworth.smarthome.common.util.BitmapUtil;
import com.skyworth.smarthome.home.smartdevice.controlpanel.view.item.GroupDisplayLeftItem;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XThemeUtils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

public class ControlPanelView extends LinearLayout implements IControlPanel {
    private TextView mEmptyText = null;
    private TextView mTitle = null;
    private TextView mInfoTitle = null;
    private View mIcon = null;
    private GroupDisplayItem mGroupDisplay = null;
    private ScrollView mScrollView = null;
    private FrameLayout mControlsLayout = null;
    private SensorRecordView sensorRecordView = null;
    private TextView mIREntry = null;
    private LinearLayout mLeftLayout;
    private RelativeLayout mRightLayout;
    private LinearLayout mRightContentLayout;
    private List<BaseControlItem> mRecycled = null;
    private IControlListener mListener = null;
    private IDeviceControlModel mDeviceControlModel;
    private int mControlItemCount = 0;
    private String mCurrentIcon = "";
    private String mDeviceId = null;
    private DeviceInfo mDetailData = null;
    private JSONArray mDetailLayoutArray = null;
    private RefreshFilter mRefreshFilter = new RefreshFilter();
    private static byte[] lock = new byte[0];
    public static final String TAG = "controlpanel";
    public static final String IRENTRY = "irControl";
    public static final int PANEL_MIN_HEIGHT = Util.Div(232);
    private static final int ITEM_HEIGHT = Util.Div(90);
    private static final int ITEM_WIDTH = Util.Div(480);
    private static final int EXPAND_THRESHOLD_COUNT = 2;
    private static final int FILTER_DELAY_MS = 2500;
    private GroupDisplayLeftItem mGroupDisplayLeft = null;


    public ControlPanelView(@NonNull Context context, IDeviceControlModel deviceControlModel) {
        super(context);
        init();
        mDeviceControlModel = deviceControlModel;
    }

    private void init() {
        setOrientation(HORIZONTAL);
        setBackground(new DialogBg());

        mLeftLayout = new LinearLayout(getContext());
        mLeftLayout.setOrientation(VERTICAL);
        mRightLayout = new RelativeLayout(getContext());
        mRecycled = new ArrayList<>();
    }


    @Override
    public void show(DeviceInfo deviceDetailData, boolean isLocal) {
        Log.i(TAG, "show: isLocal: " + isLocal);
//        if (isLocal) {
            showInternal(deviceDetailData);
//        } else if (mRefreshFilter.needRefresh(getDiffStatus(deviceDetailData))) {
//            Log.i(TAG, "show: need refresh");
//            showInternal(deviceDetailData);
//        }
    }

    private List<RefreshFilter.ControlCommand> getDiffStatus(DeviceInfo newData) {
        if (newData == null || TextUtils.isEmpty(newData.report_status)) {
            return null;
        }
        JSONObject newStatus = JSONObject.parseObject(newData.report_status);
        if (newStatus == null) {
            return null;
        }
        List<RefreshFilter.ControlCommand> result = new ArrayList<>();
        for (Map.Entry<String, Object> item : newStatus.entrySet()) {
            result.add(new RefreshFilter.ControlCommand(item.getKey(), item.getValue()));
        }
        return result;
    }

    private void showInternal(DeviceInfo deviceDetailData) {
        Log.i(TAG, "showInternal: ");
        if (deviceDetailData == null) {
            mDetailData = null;
            Log.e(TAG, "show: detailData == null");
            return;
        }
        mDetailData = deviceDetailData;
        mDeviceId = deviceDetailData.device_id;
        mDetailLayoutArray = JSONObject.parseArray(deviceDetailData.detail_layout);
        refreshContentLayout(mDetailData);
        refreshTitle(deviceDetailData.device_name);
        refreshInfoTitle(AppData.getInstance().getCurrentFamilyName() + " | " + deviceDetailData.device_position);
        refreshIcon(deviceDetailData.device_icon);
        refreshGroupDisplay(deviceDetailData);
//        refreshRealEffectDeviceBg(mDetailData, mDeviceControlModel.isDevicePowerOn(mDetailData));
        refreshScrollView();
        if (EmptyUtils.isNotEmpty(mDetailData.product_type_id)&&isAlarmDevice(mDetailData.product_type_id)) {//传感器
            refreshSensorRecord();
        }else{
            refreshControls(deviceDetailData.report_status, deviceDetailData.detail_layout, deviceDetailData.online_status == 1);
        }
    }

    private void initTitle() {
        if (mTitle == null) {
            mTitle = new TextView(getContext());
            mTitle.setTextSize(Util.Dpi(40));
            mTitle.setTextColor(Color.parseColor("#FFFFFF"));
            mTitle.getPaint().setFakeBoldText(true);
        }
        if (mTitle.getParent() == null) {
            LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            layoutParams.topMargin = Util.Div(30);
            layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
            mLeftLayout.addView(mTitle, 0, layoutParams);
        }
    }

    private void initInfoTitle() {
        if (mInfoTitle == null) {
            mInfoTitle = new TextView(getContext());
            mInfoTitle.setTextSize(Util.Dpi(28));
            mInfoTitle.setTextColor(Color.parseColor("#80FFFFFF"));
        }
        if (mInfoTitle.getParent() == null) {
            LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            layoutParams.topMargin = Util.Div(10);
            layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
            mLeftLayout.addView(mInfoTitle, 1, layoutParams);
        }
    }

    private void initIcon() {
        if (mIcon == null) {
            mIcon = ImageLoader.getLoader().getView(getContext());
        }
        if (mIcon.getParent() == null) {
            LayoutParams layoutParams = new LayoutParams(Util.Div(240), Util.Div(240));
            layoutParams.topMargin = Util.Div(30);
            layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
            mLeftLayout.addView(mIcon, 2, layoutParams);
        }
    }

    /**
     * 隐藏相关设备信息
     */
    private void hideDeviceInfoView() {
        if (EmptyUtils.isNotEmpty(mInfoTitle)) {
            mInfoTitle.setVisibility(INVISIBLE);
        }
        if (EmptyUtils.isNotEmpty(mIcon)) {
            mIcon.setVisibility(INVISIBLE);
        }
        if (EmptyUtils.isNotEmpty(mGroupDisplay)) {
            mGroupDisplay.setVisibility(INVISIBLE);
        }
        if (EmptyUtils.isNotEmpty(mGroupDisplayLeft)) {
            mGroupDisplayLeft.setVisibility(INVISIBLE);
        }
        if (EmptyUtils.isNotEmpty(mIREntry)) {
            mIREntry.setVisibility(INVISIBLE);
        }
    }

    /**
     * 显示常用设备真实效果背景图
     *
     * @param deviceInfo
     */
    private void refreshRealEffectDeviceBg(DeviceInfo deviceInfo, boolean isOpen) {
        switch (deviceInfo.product_type_id) {
            case "8"://空调
                hideDeviceInfoView();
                if (isOpen) {
                    setBackground(BitmapUtil.bitmap2Drawable(BitmapUtil.getRoundCornerImage(BitmapUtil.drawable2Bitmap(getContext().getResources().getDrawable(R.drawable.virtual_aircondition_on)), Util.Div(14), BitmapUtil.HalfType.ALL)));
                } else {
                    setBackground(BitmapUtil.bitmap2Drawable(BitmapUtil.getRoundCornerImage(BitmapUtil.drawable2Bitmap(getContext().getResources().getDrawable(R.drawable.virtual_aircondition_off)), Util.Div(14), BitmapUtil.HalfType.ALL)));
                }
                break;
            case "9"://冰箱
                hideDeviceInfoView();
                if (isOpen) {
                    setBackground(BitmapUtil.bitmap2Drawable(BitmapUtil.getRoundCornerImage(BitmapUtil.drawable2Bitmap(getContext().getResources().getDrawable(R.drawable.virtual_refrigerator_on)), Util.Div(14), BitmapUtil.HalfType.ALL)));
                } else {
                    setBackground(BitmapUtil.bitmap2Drawable(BitmapUtil.getRoundCornerImage(BitmapUtil.drawable2Bitmap(getContext().getResources().getDrawable(R.drawable.virtual_refrigerator_off)), Util.Div(14), BitmapUtil.HalfType.ALL)));
                }
                break;
            case "84"://窗帘
                hideDeviceInfoView();
                setBackground(BitmapUtil.bitmap2Drawable(BitmapUtil.getRoundCornerImage(BitmapUtil.drawable2Bitmap(getContext().getResources().getDrawable(R.drawable.virtual_curtain_on)), Util.Div(14), BitmapUtil.HalfType.ALL)));
                break;
            case "40"://台灯
                hideDeviceInfoView();
                if (isOpen) {
                    setBackground(BitmapUtil.bitmap2Drawable(BitmapUtil.getRoundCornerImage(BitmapUtil.drawable2Bitmap(getContext().getResources().getDrawable(R.drawable.virtual_light_on)), Util.Div(14), BitmapUtil.HalfType.ALL)));
                } else {
                    setBackground(BitmapUtil.bitmap2Drawable(BitmapUtil.getRoundCornerImage(BitmapUtil.drawable2Bitmap(getContext().getResources().getDrawable(R.drawable.virtual_light_off)), Util.Div(14), BitmapUtil.HalfType.ALL)));
                }
                break;
        }
    }

    private void refreshContentLayout(DeviceInfo deviceDetailData) {
        Log.d("TAG", "refreshContentLayout: "+deviceDetailData.detail_layout);
        if (mLeftLayout.getParent() == null) {
            if (handleDisplayItemCount(deviceDetailData.detail_layout) > 3) {
                LayoutParams layoutParams = new LayoutParams(Util.Div(1160), Util.Div(590));
                addView(mLeftLayout, layoutParams);
//                addView(mRightLayout, layoutParams);
            } else {
                LayoutParams layoutParams = new LayoutParams(Util.Div(580), Util.Div(590));
                addView(mLeftLayout, layoutParams);
                addView(mRightLayout, layoutParams);
            }
        }
    }

    private void refreshTitle(String title) {
        initTitle();
        if (!mTitle.getText().equals(title)) {
            mTitle.setText(title);
        }
        if (mTitle.getText().equals("")) {
            mTitle.setText("创维汽车");
        }

    }

    private void refreshInfoTitle(String title) {
        initInfoTitle();
        if (!mInfoTitle.getText().equals(title)) {
            mInfoTitle.setText(title);
        }
    }

    private void refreshIcon(String url) {
        initIcon();
        if (url == null) {
            url = "";
        }
        if (TextUtils.isEmpty(mCurrentIcon) || !mCurrentIcon.equals(url)) {
            mIcon.setBackgroundDrawable(null);
            ImageLoader.getLoader().with(getContext()).load(Uri.parse(url)).resize(Util.Div(220), Util.Div(220)).setScaleType(ImageView.ScaleType.FIT_XY).into(mIcon);
            mCurrentIcon = url;
        }
    }

    private void initGroupDisplay() {
        if (mGroupDisplay == null) {
            mGroupDisplay = new GroupDisplayItem(getContext());
            mGroupDisplay.setFocusable(false);
        }
        if (mGroupDisplay.getParent() == null) {
            LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            int index = 0;
            if (mLeftLayout.getChildCount() < 4) {
                index = mLeftLayout.getChildCount();
            } else {
                index = 4;
            }
            mLeftLayout.addView(mGroupDisplay, index, layoutParams);
        }

        if (mGroupDisplayLeft == null) {
            mGroupDisplayLeft = new GroupDisplayLeftItem(getContext());
            mGroupDisplayLeft.setFocusable(false);
        }
        if (mGroupDisplayLeft.getParent() == null) {
            LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            int index = 0;
            if (mLeftLayout.getChildCount() < 4) {
                index = mLeftLayout.getChildCount();
            } else {
                index = 4;
            }
            mLeftLayout.addView(mGroupDisplayLeft, index, layoutParams);
        }
    }

    private void refreshGroupDisplay(DeviceInfo deviceDetailData) {
        if (EmptyUtils.isEmpty(deviceDetailData.report_status)) {
            Log.e(TAG, "refreshGroupDisplay: status is empty");
            hideGroupDisplay();
            return;
        }
        if (EmptyUtils.isEmpty(deviceDetailData.detail_layout)) {
            Log.e(TAG, "refreshGroupDisplay: detail_layout is empty");
            hideGroupDisplay();
            return;
        }
        initGroupDisplay();
        JSONArray jsonObject = JSONObject.parseArray(deviceDetailData.detail_layout);
        if (jsonObject.size() <= 0) {
            hideGroupDisplay();
            Log.e(TAG, "refreshGroupDisplay: detail_layout size <= 0");
            return;
        }
        JSONObject first = (JSONObject) jsonObject.get(0);
        String type = first.getString("type");
        if (EmptyUtils.isNotEmpty(type) && type.equals("display")) {
            GroupDisplayControlData displayControlData = (GroupDisplayControlData) ControlDataFactory.getData(type, first.toJSONString());
            if (displayControlData == null || EmptyUtils.isEmpty(displayControlData.items)) {
                hideGroupDisplay();
            } else {
                displayControlData.deviceId = mDeviceId;
                if (jsonObject.size() > 1){
                    mGroupDisplay.setVisibility(GONE);
                    mGroupDisplayLeft.show(deviceDetailData.report_status, displayControlData);
                    mGroupDisplayLeft.setVisibility(VISIBLE);
                    LayoutParams layoutParams = new LayoutParams(Util.Div(580), Util.Div(590));
                    mLeftLayout.setLayoutParams(layoutParams);
                    if (mRightLayout.getParent() == null) {
                        addView(mRightLayout, layoutParams);
                    }else {
                        mRightLayout.setVisibility(VISIBLE);
                        mRightLayout.setLayoutParams(layoutParams);
                        mLeftLayout.setGravity(GONE);
                    }
                }else {
                    mGroupDisplayLeft.setVisibility(GONE);
                    mGroupDisplay.show(deviceDetailData.report_status, displayControlData);
                    mGroupDisplay.setVisibility(VISIBLE);
                    LayoutParams layoutParams = new LayoutParams(Util.Div(1160), Util.Div(590));
                    mRightLayout.setLayoutParams(layoutParams);
                    if (mLeftLayout.getParent() == null) {
                        addView(mLeftLayout, layoutParams);
                    }else {
                        mLeftLayout.setVisibility(VISIBLE);
                        mLeftLayout.setLayoutParams(layoutParams);
                        mRightLayout.setVisibility(GONE);
                    }
                }
            }
        } else {
            hideGroupDisplay();
        }
    }

    private void hideGroupDisplay() {
        if (mGroupDisplay != null) {
            mGroupDisplay.setVisibility(GONE);
        }
        if (mGroupDisplayLeft != null) {
            mGroupDisplayLeft.setVisibility(GONE);
        }
    }

    private void refreshScrollView() {
        if (mScrollView == null) {
            mScrollView = new ScrollView(getContext());
            GradientDrawable gradientDrawable = new GradientDrawable();
            gradientDrawable.setColor(Color.argb(10, 255, 255, 255));
            float[] cornerRadius = new float[]{0F, 0F, Util.Div(14), Util.Div(14), Util.Div(14), Util.Div(14), 0F, 0F};
            gradientDrawable.setCornerRadii(cornerRadius);
            mScrollView.setBackground(gradientDrawable);
            mScrollView.setPadding(0, Util.Div(20), 0, 0);
            mScrollView.setFocusable(false);
            mScrollView.setVerticalScrollBarEnabled(false);
            mScrollView.setHorizontalScrollBarEnabled(false);
            mScrollView.setOverScrollMode(OVER_SCROLL_NEVER);
        }
        if (mScrollView.getParent() == null) {
            LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            mRightLayout.addView(mScrollView, layoutParams);
        }
    }

    private void refreshIREntry(DeviceInfo deviceDetailData) {
        if (deviceDetailData != null && !TextUtils.isEmpty(deviceDetailData.detail_layout) && deviceDetailData.detail_layout.contains(IRENTRY)) {
            if (mIREntry == null) {
                mIREntry = new TextView(getContext());
                mIREntry.setText(R.string.control_panel_ir_entry);
                mIREntry.setTextSize(Util.Dpi(20));
                mIREntry.setTextColor(Color.parseColor("#CDD2D8"));
                mIREntry.setFocusable(true);
                mIREntry.setGravity(Gravity.CENTER);
                mIREntry.setCompoundDrawablesWithIntrinsicBounds(R.drawable.control_panel_ir_entry_unfocus, 0, 0, 0);
                mIREntry.setPadding(Util.Div(17), 0, Util.Div(17), 0);
                mIREntry.setCompoundDrawablePadding(Util.Div(5));
                Drawable drawable = XThemeUtils.getDrawable(Color.parseColor("#19CCCCCC"), 0, Util.Div(1), Util.Div(30));
                mIREntry.setBackgroundDrawable(drawable);
                mIREntry.setOnFocusChangeListener(new OnFocusChangeListener() {
                    @Override
                    public void onFocusChange(View v, boolean hasFocus) {
                        if (hasFocus) {
                            mIREntry.setTextColor(Color.BLACK);
                            mIREntry.setCompoundDrawablesWithIntrinsicBounds(R.drawable.control_panel_ir_entry_unfocus, 0, 0, 0);
                            Drawable drawable = XThemeUtils.getDrawable(Color.parseColor("#FFFFFF"), 0, Util.Div(1), Util.Div(30));
                            mIREntry.setBackgroundDrawable(drawable);
                        } else {
                            mIREntry.setTextColor(Color.parseColor("#CDD2D8"));
                            mIREntry.setCompoundDrawablesWithIntrinsicBounds(R.drawable.control_panel_ir_entry_focus, 0, 0, 0);
                            Drawable drawable = XThemeUtils.getDrawable(Color.parseColor("#19CCCCCC"), 0, Util.Div(1), Util.Div(30));
                            mIREntry.setBackgroundDrawable(drawable);
                        }
                    }
                });
                mIREntry.setOnClickListener(new OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        //todo 进入红外设备
                    }
                });
            }
            if (mIREntry.getParent() == null) {
                LayoutParams layoutParams = new LayoutParams(Util.Div(180), Util.Div(40));
                layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
                layoutParams.topMargin = Util.Div(40);
                mLeftLayout.addView(mIREntry, 3, layoutParams);
            }
            mIREntry.setVisibility(VISIBLE);
        } else if (mIREntry != null) {
            mIREntry.setVisibility(GONE);
            removeView(mIREntry);
        }
    }

    /**
     * 设备是否有报警功能
     *
     * @param typeId
     * @return
     */
    private boolean isAlarmDevice(String typeId) {
        //13: '门磁',16: '人体红外感应',17: '温湿度传感器',18一氧化碳报警器, 19: '可燃气体报警器',
        // 20: '烟雾报警器',23: '水浸传感器',30: '紧急报警按钮'
        return typeId.equals("13") || typeId.equals("16") || typeId.equals("17") || typeId.equals("18")
                || typeId.equals("19") || typeId.equals("20") || typeId.equals("23") || typeId.equals("30");
    }

    public void refreshSensorRecord() {
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Util.Div(480), ViewGroup.LayoutParams.WRAP_CONTENT);
        mScrollView.removeAllViews();
        mRightContentLayout = new LinearLayout(getContext());
        mRightContentLayout.setOrientation(VERTICAL);
        mScrollView.addView(mRightContentLayout, layoutParams);

        sensorRecordView = new SensorRecordView(getContext());
        FrameLayout.LayoutParams recordViewLayoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT);
        mRightContentLayout.addView(sensorRecordView, recordViewLayoutParams);
        AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                final List<AlarmLogBean> logList = mDeviceControlModel.requestAlarmLogList(
                        mDetailData.gateway_id, mDetailData.did);
                AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        sensorRecordView.refreshUI(logList);
                        return Unit.INSTANCE;
                    }
                });
                return Unit.INSTANCE;
            }
        });

    }

    private void addCarBg(){
        ImageView img  = new ImageView(getContext());
        img.setBackgroundResource(R.drawable.car_bg);
        RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(Util.Div(500),
                Util.Div(450));
        layoutParams.addRule(RelativeLayout.CENTER_IN_PARENT);
        mRightLayout.addView(img,layoutParams);
    }

    public void refreshControls(String statusdata, String layout, boolean isOnLine) {

        Log.d(TAG, "refreshControls: start ------------"+statusdata.toString());
        if (mControlsLayout == null) {
            mControlsLayout = new FrameLayout(getContext());
            GradientDrawable gradientDrawable = new GradientDrawable();
            gradientDrawable.setStroke(Util.Div(1), Color.parseColor("#33FFFFFF"));
            gradientDrawable.setCornerRadius(Util.Div(10));
            mControlsLayout.setBackground(gradientDrawable);
        }
        if (mControlsLayout.getParent() == null) {
            FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Util.Div(480), ViewGroup.LayoutParams.WRAP_CONTENT);
            layoutParams.gravity = Gravity.CENTER_HORIZONTAL | Gravity.CENTER_VERTICAL;
            mScrollView.removeAllViews();
            mRightContentLayout = new LinearLayout(getContext());
            mRightContentLayout.setOrientation(VERTICAL);
            mScrollView.addView(mRightContentLayout, layoutParams);
            if (EmptyUtils.isNotEmpty(mDetailData.voice_tips) && EmptyUtils.isNotEmpty(mDetailLayoutArray) && mDetailLayoutArray.size() >= 6) {//控制项大于等于6时，才需设备topMargin
                layoutParams.topMargin = Util.Div(20);
            }
            mRightContentLayout.addView(mControlsLayout, layoutParams);
            if (EmptyUtils.isNotEmpty(mDetailData.voice_tips)) {
                VoiceTipsView voiceTipsView = new VoiceTipsView(getContext());
                layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                if (EmptyUtils.isNotEmpty(mDetailLayoutArray) && mDetailLayoutArray.size() >= 6) {//控制项大于等于6时，才需设备topMargin
                    layoutParams.bottomMargin = Util.Div(20);
                }
                mRightContentLayout.addView(voiceTipsView, layoutParams);
                if (mDetailData.voice_tips.size() > 2) {
                    voiceTipsView.show(mDetailData.voice_tips.subList(0, 1));
                } else {
                    voiceTipsView.show(mDetailData.voice_tips);
                }
            }
        }
        mControlItemCount = 0;
        JSONArray layoutJson = handleVirtualCurtainDetailLayout(JSONObject.parseArray(layout));
        if (layoutJson == null || layoutJson.size() == 0 || EmptyUtils.isJsonStringEmpty(statusdata)) {
            recycleAll();
            showEmpty();
            Log.i(TAG, "show: layoutJson is empty");
            return;
        }
        hideEmpty();

        Log.i(TAG, "refreshControls: start ++++++statusdata+" + statusdata);
        for (Object o : layoutJson) {
            Log.i(TAG, "refreshControls: " + o.toString());
            String type = ((JSONObject) o).getString("type");
            if (!ControlWidgetFactory.isSupport(type)) {
                Log.e(TAG, "show: not support type: " + type);
                continue;
            }
            Pair<Boolean, String> hasDataField = hasDataField(statusdata, ((JSONObject) o));
            if (hasDataField.second != null && !hasDataField.first) {
                Log.e(TAG, "show: data_field: \"" + hasDataField.second + "\" not find in status");
                continue;
            }
            IControlItem item;
            View view = mControlsLayout.getChildAt(mControlItemCount);
            if (view != null && view instanceof BaseControlItem && ((BaseControlItem) view).getType().equals(type)) {
                item = (IControlItem) view.getTag();
            } else {
                recycle(view);
                item = getItem(type);
            }

            BaseControlData itemData = ControlDataFactory.getData(type, ((JSONObject) o).toJSONString());
            if (itemData != null) {
                itemData.deviceId = mDeviceId;
            }
            item.show(statusdata, itemData);
            item.getView().setOnKeyListener(onKeyListener);
            item.getView().setOnFocusChangeListener(onFocusChangeListener);
            item.getView().setTag(item);
            if (item.getView().getParent() == null) {
                FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ITEM_WIDTH, ITEM_HEIGHT);
                layoutParams.topMargin = mControlItemCount * ITEM_HEIGHT;
                mControlsLayout.addView(item.getView(), mControlItemCount, layoutParams);
            }
            mControlItemCount++;
            Log.i(TAG, "show: addView: " + type);
        }
        if (mControlItemCount == 0) {
            mRightContentLayout.removeAllViews();
        }
        recycleRestView();
        checkOnLine(isOnLine);
        setDivideLine();
    }

    /**
     * 处理虚拟窗帘设备的detail_layout
     *
     * @param layoutJson
     * @return
     */
    private JSONArray handleVirtualCurtainDetailLayout(JSONArray layoutJson) {
        try {
            if (EmptyUtils.isEmpty(layoutJson))
                return null;
            if (mDetailData.is_virtual && mDetailData.product_type_id.equals("84")) {
                for (int i = 0; i < layoutJson.size(); i++) {
                    JSONObject jsonObject = (JSONObject) layoutJson.get(i);
                    if (jsonObject.getString("title").equals("窗帘")) {
                        JSONObject valueJson = new JSONObject();
                        valueJson.put("0", "收起");
                        valueJson.put("1", "展开");
                        jsonObject.put("values", valueJson);
                        layoutJson.add(i, valueJson);
                        break;
                    }
                }
            }
            return layoutJson;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private Pair<Boolean, String> hasDataField(String status, JSONObject layout) {
        String dataFiled = layout.getString("data_field");
        JSONObject statusJson = JSONObject.parseObject(status);
        Boolean hasDataField = statusJson.containsKey(dataFiled);
        return new Pair<>(hasDataField, dataFiled);
    }

    private void checkOnLine(boolean isOnLine) {
        Log.i(TAG, "checkOnLine: " + isOnLine);
        if (isOnLine) {
            enableAll();
        } else {
            disableAll();
        }
    }

    private void setDivideLine() {
        for (int i = 0; i < mControlItemCount; i++) {
            View view = mControlsLayout.getChildAt(i);
            if (view == null || !(view instanceof BaseControlItem)) {
                continue;
            }
            boolean isShowDivideLine = false;
            if (i < mControlItemCount - 1) {
                isShowDivideLine = true;
            }
            ((BaseControlItem) view).setShowDivideLine(isShowDivideLine);
        }
    }

    private void recycleRestView() {
        if (mControlItemCount < mControlsLayout.getChildCount()) {
            for (int i = mControlsLayout.getChildCount() - 1; i >= mControlItemCount; i--) {
                recycle(mControlsLayout.getChildAt(i));
            }
        }
    }

    private void fixMinHeight() {
        ViewGroup.LayoutParams layoutParams = getLayoutParams();
        if (mControlItemCount > EXPAND_THRESHOLD_COUNT) {
            layoutParams.height = PANEL_MIN_HEIGHT;
        } else {
            layoutParams.height = mControlItemCount * ITEM_HEIGHT;
        }
        setLayoutParams(layoutParams);
    }

    private OnKeyListener onKeyListener = new OnKeyListener() {
        @Override
        public boolean onKey(View v, int keyCode, KeyEvent event) {
            if (!(v instanceof BaseControlItem)) {
                return false;
            }
            return ((BaseControlItem) v).onKey(v, keyCode, event);
        }
    };

    private OnFocusChangeListener onFocusChangeListener = new OnFocusChangeListener() {
        @Override
        public void onFocusChange(View v, boolean hasFocus) {
            if (v instanceof BaseControlItem) {
                ((BaseControlItem) v).onFocus(hasFocus);
            }
        }
    };

    private void recycleAll() {
        for (int i = mControlsLayout.getChildCount() - 1; i >= 0; i--) {
            recycle(mControlsLayout.getChildAt(i));
        }
    }

    private void recycle(View view) {
        if (view == null) {
            return;
        }
        if (view instanceof BaseControlItem) {
            ((BaseControlItem) view).onRecycle();
            mRecycled.add((BaseControlItem) view);
        }
        mControlsLayout.removeView(view);
    }

    private IControlItem getItem(String type) {
        IControlItem result = null;
        synchronized (lock) {
            Iterator iterator = mRecycled.iterator();
            while (iterator.hasNext()) {
                BaseControlItem item = (BaseControlItem) iterator.next();
                if (item != null && item.getType() != null && item.getType().equals(type)) {
                    result = item;
                    iterator.remove();
                    break;
                }
            }
        }
        if (result == null) {
            result = ControlWidgetFactory.getItem(type, getContext());
        }
        if (result != null) {
            result.setControlListener(new IControlListener() {
                @Override
                public void onControl(Map<String, String> status) {
                    for (Map.Entry<String, String> stringStringEntry : status.entrySet()) {
                        mRefreshFilter.addNewControlCommand(new RefreshFilter.ControlCommand(stringStringEntry.getKey(), stringStringEntry.getValue()));
                    }
                    mListener.onControl(status);
                }

                @Override
                public void showVoiceTip(List<String> tip) {
                    mListener.showVoiceTip(tip);
                }

                @Override
                public void onExitControlPanel() {
                    mListener.onExitControlPanel();
                }
            });
        }
        return result;
    }

    private void disableAll() {
        for (int i = 0; i < mControlsLayout.getChildCount(); i++) {
            View view = mControlsLayout.getChildAt(i);
            if (view instanceof BaseControlItem) {
                if (view.isFocused()) {
                    focusToOther(view);
                }
                ((BaseControlItem) view).disable();
            }
        }
    }

    private void enableAll() {
        for (int i = 0; i < mControlsLayout.getChildCount(); i++) {
            View view = mControlsLayout.getChildAt(i);
            if (view instanceof BaseControlItem) {
                if (((BaseControlItem) view).canFocusable()) {
                    ((BaseControlItem) view).enable();
                } else {
                    if (view.isFocused()) {
                        focusToOther(view);
                    }
                    ((BaseControlItem) view).disable();
                }
            }
        }
    }

    private void focusToOther(View currentView) {
        int index = mControlsLayout.indexOfChild(currentView);
        if (index > 0) {
            for (int i = index - 1; i >= 0; i--) {
                View view = mControlsLayout.getChildAt(i);
                if (view instanceof BaseControlItem && ((BaseControlItem) view).canFocusable()) {
                    view.requestFocus();
                    return;
                }
            }
        }
    }

    @Override
    public void requestFocusAtFirstItem() {
        if (mControlsLayout != null) {
            for (int i = 0; i < mControlsLayout.getChildCount(); i++) {
                final View view = mControlsLayout.getChildAt(i);
                if (view.isFocusable()) {
                    view.post(new Runnable() {
                        @Override
                        public void run() {
                            view.requestFocus();
                        }
                    });
                    resetFocusLater(view);
                    return;
                }
            }
        }
        requestFocusAtIREntry();
    }

    private void requestFocusAtIREntry() {
        if (mIREntry != null) {
            post(new Runnable() {
                @Override
                public void run() {
                    mIREntry.requestFocus();
                }
            });
        }
    }

    private void resetFocusLater(final View view) {
        postDelayed(new Runnable() {
            @Override
            public void run() {
                for (int i = 0; i < mControlsLayout.getChildCount(); i++) {
                    View view = mControlsLayout.getChildAt(i);
                    if (view != null && view.isFocused()) {
                        return;
                    }
                }
                if (view != null && getVisibility() == VISIBLE) {
                    view.requestFocus();
                }
            }
        }, 300);
    }

    private void showEmpty() {
        if (mEmptyText == null) {
            mEmptyText = new TextView(getContext());
            mEmptyText.setText(R.string.control_panel_empty);
            mEmptyText.setTextSize(Util.Dpi(28));
            mEmptyText.setTextColor(XThemeUtils.c_4a());
        }
        if (mEmptyText.getParent() == null) {
            FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            layoutParams.gravity = Gravity.CENTER;
            mScrollView.removeAllViews();
            mScrollView.addView(mEmptyText, layoutParams);
        }
    }

    private void hideEmpty() {
        if (mEmptyText != null) {
            mScrollView.removeView(mEmptyText);
        }
    }

    /**
     * 计算处理设备核心状态显示数
     *
     * @param data
     * @return
     */
    public int handleDisplayItemCount(String data) {
        if (EmptyUtils.isEmpty(data))
            return 0;
        JSONArray jsonObject = JSONObject.parseArray(data);
        JSONObject first = (JSONObject) jsonObject.get(0);
        String type = first.getString("type");
        if (EmptyUtils.isNotEmpty(type) && type.equals("display")) {
            GroupDisplayControlData displayControlData = (GroupDisplayControlData) ControlDataFactory.getData(type, first.toJSONString());
            if (EmptyUtils.isNotEmpty(displayControlData.items)) {
                return displayControlData.items.size();
            }
        }
        return 0;
    }

    @Override
    public void destroy() {
        mRefreshFilter.destroy();
    }

    @Override
    public View getView() {
        return this;
    }

    @Override
    public void setControlListener(IControlListener listener) {
        mListener = listener;
    }

    @Override
    public String getCurrentDeviceId() {
        return mDeviceId;
    }
}
