package com.skyworth.smarthome.common.base;

import android.app.Dialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Binder;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.FrameLayout;


import com.skyworth.framework.skycommondefine.SkyBroadcast;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.util.DialogLauncherUtil;
import com.skyworth.smarthome.common.util.LogUtil;
import com.skyworth.smarthome.common.util.SystemProperty;
import com.skyworth.smarthome.devices.discover.receiver.StartServiceReceiver;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.util.Util;
import com.smarthome.common.utils.EmptyUtils;

import java.util.Map;


/**
 * Description: 基础系统Dialog <br>
 * Created by ZengFanWen on 2020/8/19 17:34.
 */
public abstract class BaseSysDialog extends Dialog {
    protected Context mContext;
    protected FrameLayout mContentView;
    protected String mDialogKey = "";
    public DismissDialogHandler mHandler;

    private BroadcastReceiver mScreenSaverReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            try {
                String action = intent.getAction();
                Log.i("BaseDialog", "onReceive: " + action);
                if (EmptyUtils.isNotEmpty(action)) {
                    if (StartServiceReceiver.ACTION_START_SCREENSAVER.equals(action)) {//启动屏保
                        DialogLauncherUtil.dismissOtherDialog("");
                    } else if (SkyBroadcast.SKY_BCT_SEND_HOTKEYS.equals(action)) {//键值广播
                        int keyCode = intent.getIntExtra("specialKey", 0);
                        if (keyCode == 26 || keyCode == 759) {//监听两个待机按键
                            Log.i("BaseDialog", "onReceive: keyCode:" + keyCode);
                            DialogLauncherUtil.dismissOtherDialog("");
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    };

    public BaseSysDialog() {
        super(SmartHomeTvLib.getContext(), R.style.global_dialog);
        initDialog();
    }

    public BaseSysDialog(int themeResId) {
        super(SmartHomeTvLib.getContext(), themeResId);
        initDialog();
    }

    protected abstract void initParams();

    private void initDialog(){
        mContext = SmartHomeTvLib.getContext();
        initParams();
        init();
        initContentView();
        setCanceledOnTouchOutside(true);
        if (getWindow() != null) {
            getWindow().setGravity(Gravity.CENTER);
            getWindow().setWindowAnimations(R.style.dialogWindowAnim);
            WindowManager.LayoutParams params = getWindow().getAttributes();
            if (SystemProperty.isAodDevice()) {
                params.token = new Binder();
                params.gravity = Gravity.TOP;
                params.softInputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE;
                params.setTitle("StatusBar");
                params.packageName = mContext.getPackageName();
                getWindow().setAttributes(params);
                getWindow().setType(-20032003);
            } else {
                getWindow().setAttributes(params);
                getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
            }
        }
        setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                BaseSysDialog.this.onDismiss();
            }
        });
        mContentView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        mContentView.setFocusable(false);
        setContentView(mContentView, new ViewGroup.LayoutParams(Util.Div(1920), Util.Div(1080)));
    }

    private void init() {
        try {
            DialogLauncherUtil.dismissOtherDialog(mDialogKey);
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction(StartServiceReceiver.ACTION_START_SCREENSAVER);
            intentFilter.addAction(SkyBroadcast.SKY_BCT_SEND_HOTKEYS);
            mContext.registerReceiver(mScreenSaverReceiver, intentFilter);
        } catch (Exception e) {
            Log.i("BaseDialog", "init: error:" + e.getMessage());
        }
    }


    protected void initContentView() {
        mContentView = new FrameLayout(mContext);
    }

    public void showDialog(Map<String, String> params) {
        show();
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (EmptyUtils.isNotEmpty(mHandler)) {
            mHandler.removeCallbacksAndMessages(null);
            mHandler.sendEmptyMessageDelayed(0, AppConstants.DIALOG_DISMISS_TIME);
        }
        return super.dispatchKeyEvent(event);
    }

    protected void onDismiss() {
        try {
            if(EmptyUtils.isNotEmpty(mHandler)){
                mHandler.removeCallbacksAndMessages(null);
            }
            DialogLauncherUtil.removeDialog(mDialogKey);
            mContext.unregisterReceiver(mScreenSaverReceiver);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void setDialogContentView(View view) {
        mContentView.addView(view);
    }


    /**
     * 取消动画
     */
    public void cancelDialogAnimation(){
        getWindow().setWindowAnimations(0);
    }


    /**
     * 关闭自动关闭弹窗
     */
    public void closeAutoDismissDialog() {
        mHandler.removeCallbacksAndMessages(null);
        mHandler = null;
    }

    /***
     * 打开自动关闭弹窗
     */
    public void openAutoDismissDialog() {
        if(SystemProperty.isOLED()){
            if (EmptyUtils.isEmpty(mHandler)) {
                mHandler = new DismissDialogHandler(mContext.getMainLooper());
            }
            mHandler.removeCallbacksAndMessages(null);
            mHandler.sendEmptyMessageDelayed(0, AppConstants.DIALOG_DISMISS_TIME);
        }
    }


    private class DismissDialogHandler extends Handler {

        public DismissDialogHandler(Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(Message msg) {
            LogUtil.androidLog(mDialogKey + "弹窗自动关闭");
            dismiss();
        }
    }

}
