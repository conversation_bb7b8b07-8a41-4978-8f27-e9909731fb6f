package com.skyworth.smarthome.personal.thirdaccount.dialog;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.net.Uri;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.bean.ThridAccountHttpBean;
import com.skyworth.smarthome.common.util.QRUtils;
import com.skyworth.smarthome.common.util.ThreadManager;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XThemeUtils;


/**
 * Description:绑定第三方账号的密码-View <br>
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/1/8 10:04.
 */
public class AccountQrCodeView extends FrameLayout {

    private Context mContext;
    private TextView mTitileTv, nameText, subNameText, descText;
    private TextView mTipsTv;
    private View mIcon;
    private ImageView mQrCodeImageView;

    public AccountQrCodeView(Context context) {
        super(context);
        mContext = context;
        setBackground(XThemeUtils.getDrawable(Color.parseColor("#454649"), 0, 0, Util.Div(14)));
        setAlpha(0.96f);

        mTitileTv = new TextView(context);
        mTitileTv.setText(mContext.getString(R.string.binding_account_midea_title));
        mTitileTv.setTextSize(Util.Dpi(36));
        mTitileTv.setTextColor(Color.parseColor("#FFFFFF"));
        mTitileTv.getPaint().setFakeBoldText(true);
        mTitileTv.setGravity(Gravity.CENTER);
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        params.topMargin = Util.Div(40);
        addView(mTitileTv, params);

        mIcon = ImageLoader.getLoader().getView(context);
        params = new LayoutParams(Util.Div(80), Util.Div(80));
        params.leftMargin = Util.Div(60);
        params.topMargin = Util.Div(141);
        addView(mIcon, params);

        nameText = new TextView(context);
        nameText.setTextSize(Util.Dpi(36));
        nameText.getPaint().setFakeBoldText(true);
        nameText.setTextColor(Color.parseColor("#FFFFFF"));
        nameText.setGravity(Gravity.CENTER);
        params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.leftMargin = Util.Div(160 - 3);
        params.topMargin = Util.Div(145 - 3);
        addView(nameText, params);

        subNameText = new TextView(context);
        subNameText.setTextSize(Util.Dpi(24));
        subNameText.setTextColor(Color.parseColor("#CDD2D8"));
        subNameText.setGravity(Gravity.CENTER);
        params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.leftMargin = Util.Div(160 - 3);
        params.topMargin = Util.Div(193 - 3);
        addView(subNameText, params);

        descText = new TextView(context);
        descText.setLineSpacing(5, 1);
        descText.setMaxLines(2);
        descText.setTextSize(Util.Dpi(28));
        descText.setTextColor(Color.parseColor("#999999"));
        params = new LayoutParams(Util.Div(425), Util.Div(80));
        params.leftMargin = Util.Div(60);
        params.topMargin = Util.Div(269);
        addView(descText, params);

        TextView verticalLine = new TextView(context);
        verticalLine.setBackgroundColor(Color.parseColor("#999999"));
        params = new LayoutParams(Util.Div(2), Util.Div(272));
        params.leftMargin = Util.Div(528);
        params.topMargin = Util.Div(126);
        addView(verticalLine, params);

        mQrCodeImageView = new ImageView(context);
        LayoutParams qrCodeParams = new LayoutParams(Util.Div(240), Util.Div(240));
        qrCodeParams.leftMargin = Util.Div(570);
        qrCodeParams.topMargin = Util.Div(125);
        mQrCodeImageView.setPadding(Util.Div(9), Util.Div(9), Util.Div(9), Util.Div(9));
        mQrCodeImageView.setBackgroundColor(Color.parseColor("#FFFFFF"));
        addView(mQrCodeImageView, qrCodeParams);

        mTipsTv = new TextView(context);
        mTipsTv.setTextSize(Util.Dpi(24));
        mTipsTv.setMaxLines(1);
        mTipsTv.setTextColor(Color.parseColor("#999999"));
        mTipsTv.setGravity(Gravity.CENTER);
        mTipsTv.setEllipsize(TextUtils.TruncateAt.valueOf("END"));
        mTipsTv.setText(mContext.getString(R.string.binding_account_midea_tips));
        LayoutParams tipsParams = new LayoutParams(Util.Div(240 + 10), ViewGroup.LayoutParams.WRAP_CONTENT);
        tipsParams.leftMargin = Util.Div(570 - 5);
        tipsParams.topMargin = Util.Div(375);
        addView(mTipsTv, tipsParams);
    }


    public void setData(ThridAccountHttpBean data) {
        if (data == null) {
            return;
        }
        if (EmptyUtils.isNotEmpty(data.account_title)) {
            mTitileTv.setText(mContext.getString(R.string.scan_bind_thridacc, data.account_title));
        }
        mTipsTv.setText(data.qrcode_tips);
        nameText.setText(data.account_title);
        subNameText.setText(data.account_intro);
        descText.setText(data.account_desc);
        ImageLoader.getLoader().with(mContext).resize(Util.Div(60), Util.Div(60))
                .setScaleType(ImageView.ScaleType.FIT_XY).load(Uri.parse(data.icon)).into(mIcon);
    }


    /**
     * 展示美的绑定账号的二维码
     *
     * @param authUrl
     */
    public void showQRcode(final String authUrl) {
        if (!TextUtils.isEmpty(authUrl)) {
            ThreadManager.getInstance().ioThread(new Runnable() {
                @Override
                public void run() {
                    try {
                        final Bitmap bitmap = QRUtils.createQRImage(authUrl, Util.Div(222), Util.Div(222), null);
                        if (bitmap != null) {
                            ThreadManager.getInstance().uiThread(new Runnable() {
                                @Override
                                public void run() {
                                    mQrCodeImageView.setImageBitmap(bitmap);
                                }
                            });
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            });
        }
    }

}
