package com.skyworth.smarthome.infrared.electriclist.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.TextView;

import android.support.annotation.NonNull;

import com.skyworth.smarthome.infrared.electriclist.model.ElectricBrandData;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.ui.AlwaysMarqueeTextView;
import com.skyworth.smarthome.common.ui.CardBg;
import com.skyworth.ui.newrecycleview.NewRecycleAdapterItem;
import com.skyworth.util.Util;
import com.smarthome.common.utils.XThemeUtils;

/**
 * Created by fc on 2019/4/26
 * Describe:
 */
public class ElectricBrandItem {

    public static class HotBrandItemView extends FrameLayout implements NewRecycleAdapterItem<ElectricBrandData> {

        private AlwaysMarqueeTextView nameView;

        @SuppressLint("ResourceType")
        public HotBrandItemView(@NonNull Context context) {
            super(context);
            setFocusable(true);
            setFocusableInTouchMode(true);
            setId(0);
            nameView = new AlwaysMarqueeTextView(context);
            nameView.setGravity(Gravity.CENTER);
            LayoutParams lp = new LayoutParams(Util.Div(130), Util.Div(50));
            lp.gravity = Gravity.START;
            nameView.setTextSize(Util.Dpi(26));
            nameView.setTextColor(Color.parseColor("#aaFFFFFF"));
            nameView.setBackgroundDrawable(XThemeUtils.getDrawable(Color.parseColor("#19CCCCCC"), 0, 0, Util.Div(10)));
            addView(nameView, lp);
        }


        @Override
        public View getView() {
            return this;
        }

        @Override
        public void onUpdateData(ElectricBrandData data, int position) {
            nameView.setText(data.electricName);
        }

        public void onItemFocus(boolean hasFocus) {
            nameView.setSelected(hasFocus);
            if (hasFocus) {
                nameView.setTextColor(Color.parseColor("#000000"));
                nameView.setBackground(new CardBg());
            } else {
                nameView.setTextColor(Color.parseColor("#FFCDD2D8"));
                nameView.setBackgroundDrawable(XThemeUtils.getDrawable(Color.parseColor("#19CCCCCC"), 0, 0, Util.Div(10)));
            }
        }

        @Override
        public void clearItem() {
            nameView.setText("");
        }

        @Override
        public void refreshUI() {

        }

        @Override
        public void destroy() {

        }
    }


    public static class CharacterItemView extends FrameLayout implements NewRecycleAdapterItem<String> {
        private AlwaysMarqueeTextView nameView;
        private String mData;
        private int mPosition = 0;

        @SuppressLint("ResourceType")
        public CharacterItemView(@NonNull Context context) {
            super(context);
            setFocusable(true);
            setFocusableInTouchMode(true);
            setId(1);
            nameView = new AlwaysMarqueeTextView(context);
            nameView.setGravity(Gravity.CENTER);
            LayoutParams lp = new LayoutParams(Util.Div(60), Util.Div(50));
            lp.gravity = Gravity.CENTER;
            nameView.setTextSize(Util.Dpi(26));
            nameView.setTextColor(Color.parseColor("#FFCDD2D8"));
            nameView.setBackground(null);
            addView(nameView, lp);
        }

        @Override
        public View getView() {
            return this;
        }

        @Override
        public void onUpdateData(String data, int position) {
            mData = data;
            mPosition = position;
            nameView.setText(data);
        }

        public void onItemFocus(boolean hasFocus) {
            nameView.setSelected(hasFocus);
            if (hasFocus) {
                nameView.setTextColor(Color.parseColor("#000000"));
                nameView.setBackground(new CardBg());
            } else {
                if (mPosition == deputyFocusPosition) {
                    nameView.setTextColor(Color.parseColor("#0FA0B9"));
                    nameView.setBackground(null);
                } else {
                    nameView.setTextColor(Color.parseColor("#FFCDD2D8"));
                    nameView.setBackground(null);
                }
            }
        }

        private int deputyFocusPosition = 0;

        public void setDeputyFocusPosition(int position) {
            deputyFocusPosition = position;
        }

        public void refreshFocusChange() {
            onItemFocus(hasFocus());
        }

        //右侧列表一直下按 让左侧字母副焦点 随之变换
        public void setDeputyFocus() {
            if (nameView == null)
                return;
            nameView.setTextColor(Color.parseColor("#0FA0B9"));
            nameView.setBackground(null);
        }


        @Override
        public void clearItem() {
            nameView.setText("");
        }

        @Override
        public void refreshUI() {

        }

        public String getData() {
            return mData;

        }

        @Override
        public void destroy() {

        }

    }


    public static class AllBrandItemView extends FrameLayout implements NewRecycleAdapterItem<ElectricBrandData> {
        private AlwaysMarqueeTextView nameView;
        private ElectricBrandData mData;
        private Context mContext;
        private View bottomLine = null;

        @SuppressLint("ResourceType")
        public AllBrandItemView(@NonNull Context context) {
            super(context);
            mContext = context;
            setFocusable(true);
            setFocusableInTouchMode(true);
            setId(2);
            nameView = new AlwaysMarqueeTextView(context);
            nameView.setPadding(Util.Div(20), Util.Div(0), 0, 0);
            LayoutParams lp = new LayoutParams(Util.Div(510), Util.Div(60));
            nameView.setTextSize(Util.Dpi(26));
            nameView.setGravity(Gravity.CENTER_VERTICAL);
            nameView.setTextColor(Color.parseColor("#FFCDD2D8"));
//            nameView.setBackgroundDrawable(XThemeUtils.getDrawable(Color.parseColor("#19CCCCCC"), 0, 0, Util.Div(10)));
            addView(nameView, lp);

            bottomLine = new View(mContext);
            bottomLine.setBackgroundResource(R.drawable.electric_all_brand_bottom_line);
            lp = new LayoutParams(Util.Div(510), Util.Div(1));
            lp.leftMargin = Util.Div(20);
            lp.gravity = Gravity.BOTTOM;
            addView(bottomLine, lp);
            bottomLine.setVisibility(GONE);
        }

        @Override
        public View getView() {
            return this;
        }

        @Override
        public void onUpdateData(ElectricBrandData data, int position) {
            mData = data;
            nameView.setText(data.electricName);
            setNameViewBg();
        }


        private void setNameViewBg() {
            switch (mData.uiType) {
                case 0:
                    nameView.setBackgroundDrawable(XThemeUtils.getDrawable(Color.parseColor("#19CCCCCC"), 0, 0, Util.Div(10)));
                    bottomLine.setVisibility(GONE);
                    break;
                case 1:
                    nameView.setBackgroundDrawable(mContext.getResources().getDrawable(R.drawable.electric_all_brand_top_corner_bg));
                    bottomLine.setVisibility(VISIBLE);
                    break;
                case 2:
                    nameView.setBackgroundDrawable(new ColorDrawable(Color.parseColor("#19CCCCCC")));
                    bottomLine.setVisibility(VISIBLE);
                    break;
                case 3:
                    nameView.setBackgroundDrawable(mContext.getResources().getDrawable(R.drawable.electric_all_brand_bottom_corner_bg));
                    bottomLine.setVisibility(GONE);
                    break;
            }
        }

        public ElectricBrandData getData() {
            return mData;
        }

        private void setNameViewFocus() {
            switch (mData.uiType) {
                case 0:
                    nameView.setBackground(new CardBg());
                    bottomLine.setVisibility(GONE);
                    break;
                case 1:
                    nameView.setBackgroundDrawable(mContext.getResources().getDrawable(R.drawable.electric_all_brand_top_corner_focus));
                    bottomLine.setVisibility(VISIBLE);
                    break;
                case 2:
                    nameView.setBackgroundDrawable(new ColorDrawable(Color.parseColor("#FFFFFF")));
                    bottomLine.setVisibility(VISIBLE);
                    break;
                case 3:
                    nameView.setBackgroundDrawable(mContext.getResources().getDrawable(R.drawable.electric_all_brand_bottom_corner_focus));
                    bottomLine.setVisibility(GONE);
                    break;
            }
        }

        public void onItemFocus(boolean hasFocus) {
            nameView.setSelected(hasFocus);
            if (hasFocus) {
                nameView.setTextColor(Color.parseColor("#000000"));
                setNameViewFocus();
                Log.i("ElectricBrandView", "uiType  " + mData.uiType);
            } else {
                nameView.setTextColor(Color.parseColor("#FFCDD2D8"));
                setNameViewBg();
            }
        }


        @Override
        public void clearItem() {
            nameView.setText("");
        }

        @Override
        public void refreshUI() {

        }

        @Override
        public void destroy() {

        }

    }

    public static class AllBrandTitleItemView extends FrameLayout implements NewRecycleAdapterItem<ElectricBrandData> {
        private TextView nameView;

        @SuppressLint("ResourceType")
        public AllBrandTitleItemView(@NonNull Context context) {
            super(context);
            setFocusable(false);
            setFocusableInTouchMode(false);
            nameView = new TextView(context);
            LayoutParams lp = new LayoutParams(Util.Div(510), Util.Div(50));
            nameView.setPadding(Util.Div(20), Util.Div(0), 0, 0);
            nameView.setGravity(Gravity.CENTER_VERTICAL);
            nameView.setSingleLine();
            nameView.setEllipsize(TextUtils.TruncateAt.END);
            nameView.setTextSize(Util.Dpi(22));
            nameView.setTextColor(Color.parseColor("#FFCDD2D8"));
            addView(nameView, lp);
        }

        @Override
        public View getView() {
            return this;
        }

        @Override
        public void onUpdateData(ElectricBrandData data, int position) {
            nameView.setText(data.character);
        }


        @Override
        public void clearItem() {
            nameView.setText("");
        }

        @Override
        public void refreshUI() {

        }

        @Override
        public void destroy() {

        }

    }
}
