package com.skyworth.smarthome.infrared.electriclist.presenter;

import android.view.View;

import com.skyworth.smarthome.infrared.electriclist.model.ElectricBrandData;
import com.skyworth.smarthome.infrared.electriclist.view.IElectricBrandView;

import java.util.List;
import java.util.Map;

/**
 * Created by fc on 2019/4/26
 * Describe:
 */
public interface IElectricBrandPresenter {

    void loadList(String type_id, String typeName, List<String> hotList);

    void onItemClick(View view, ElectricBrandData itemData, int position);

    void setView(IElectricBrandView view);

    void setDeviceId(String deviceId);

    void setDeviceName(String deviceName);

    void setDeviceTypeId(String mDeviceTypeId);

    void onClickIrLearn();

    void setParams(Map<String, Object> params);
}
