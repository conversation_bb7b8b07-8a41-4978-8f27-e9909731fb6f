package com.skyworth.smarthome.common.dialog;

import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.skyworth.smarthome.common.dialog.view.DownloadPhomeAppQrCodeView;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.base.BaseSysDialog;
import com.skyworth.util.Util;
import com.smarthome.common.utils.EmptyUtils;

import java.util.Map;

import static com.skyworth.smarthome.common.util.DialogLauncherUtil.DIALOG_KEY_DOWNLOAD_QRCODE;


/**
 * Describe: 下载二维码弹框
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/10/9
 */
public class DownloadRrCodeDialog extends BaseSysDialog {
    private DownloadPhomeAppQrCodeView aiAtHomeQrCodeView;

    public static final String SHOW_TITLE = "title";
    public static final String SHOW_SUB_TITLE = "sub-title";
    public static final String SHOW_TYPE = "type";
    public static final String SHOW_TYPE_MIDEA = "midea";

    public DownloadRrCodeDialog(int themeResId) {
        super(themeResId);
    }

    @Override
    protected void initParams() {
        mDialogKey = DIALOG_KEY_DOWNLOAD_QRCODE;
    }

    @Override
    public void showDialog(Map<String, String> params) {
        super.showDialog(params);
        initUI();
        refeshUI(params);
    }


    @Override
    protected void initContentView() {
        super.initContentView();
    }

    private void initUI() {
        ImageView bg = new ImageView(mContext);
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(Util.Div(1920), Util.Div(1080));
        mContentView.addView(bg, params);


        FrameLayout mLayout = new FrameLayout(mContext);
        aiAtHomeQrCodeView = new DownloadPhomeAppQrCodeView(mContext);
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Util.Div(860), Util.Div(600));
        layoutParams.gravity = Gravity.CENTER;
        mLayout.addView(aiAtHomeQrCodeView, layoutParams);
        mContentView.addView(mLayout, new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        cancelDialogAnimation();
        //触摸弹框之外的部分关掉弹窗逻辑处理
        aiAtHomeQrCodeView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

            }
        });
        openAutoDismissDialog();
    }

    private void refeshUI(Map<String, String> params){
        String title = params.get(SHOW_TITLE);
        String subTitle = params.get(SHOW_SUB_TITLE);
        String type = params.get(SHOW_TYPE);
        aiAtHomeQrCodeView.setTitile(title);
        aiAtHomeQrCodeView.setSubTitile(subTitle);
        if(EmptyUtils.isNotEmpty(type)&&type.equals(SHOW_TYPE_MIDEA)){
            aiAtHomeQrCodeView.showQrCode("www.baidu.com", R.drawable.icon_midea);
        }else{
            aiAtHomeQrCodeView.showQrCode(AppData.getInstance().getVHomeDownloadUrl(), R.drawable.icon_smarthome);
        }
    }

}
