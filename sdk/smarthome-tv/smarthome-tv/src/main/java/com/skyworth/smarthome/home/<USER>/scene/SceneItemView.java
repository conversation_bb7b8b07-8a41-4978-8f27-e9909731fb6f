package com.skyworth.smarthome.home.smartdevice.scene;

import android.content.Context;
import android.graphics.Color;
import android.net.Uri;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.ui.VoiceTipsView;
import com.skyworth.smarthome.common.util.ThreadManager;
import com.skyworth.smarthome.home.base.BaseSmartItemView;
import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;
import com.smarthome.common.utils.EmptyUtils;
import com.swaiot.aiotlib.common.entity.SceneBean;

import java.util.List;


/**
 * @ClassName: SceneItemView
 * @Author: <PERSON>wen<PERSON>eng
 * @CreateDate: 2020/6/11 14:36
 * @Description: 场景itemView
 */
public class SceneItemView extends BaseSmartItemView<SceneBean> {
    private TextView mDeviceNameTv;
    private TextView mExecuteBtn;
    private View mSwitchView;
    private View mNewTagView;
    private LinearLayout mDevicesLayout;
    private CCFocusDrawable mFocusBg;
    private PopupWindow mPopupWindow;
    private VoiceTipsView mVoiceTipsView;
    private UiRunnable uiRunnable;
    private int mMaxShowActionCount;

    public SceneItemView(Context context) {
        this(context, 3);
    }

    public SceneItemView(Context context, int maxShowActionCount) {
        super(context);
        this.mMaxShowActionCount = maxShowActionCount;
        mFocusBg = new CCFocusDrawable(context).setRadius(Util.Div(8)).setBorderVisible(false).setSolidColor(getContext().getResources().getColor(R.color.white_10));
        setBackground(mFocusBg);
        initUI();
    }

    private void initUI() {
        mDeviceNameTv = new TextView(getContext());
        mDeviceNameTv.setTextColor(Color.WHITE);
        mDeviceNameTv.setTextSize(Util.Dpi(28));
        mDeviceNameTv.getPaint().setFakeBoldText(true);
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.leftMargin = Util.Div(31);
        params.topMargin = Util.Div(28);
        addView(mDeviceNameTv, params);

        mNewTagView = new View(getContext());
        mNewTagView.setBackground(getContext().getResources().getDrawable(R.drawable.shape_circle_green));
        params = new LayoutParams(Util.Div(12), Util.Div(12));
        params.leftMargin = Util.Div(396);
        params.topMargin = Util.Div(15);
        addView(mNewTagView, params);
        mNewTagView.setVisibility(GONE);

        mDevicesLayout = new LinearLayout(getContext());
        params = new LayoutParams(Util.Div(300), Util.Div(40));
        params.leftMargin = Util.Div(28);
        params.topMargin = Util.Div(87);
        mDevicesLayout.setOrientation(LinearLayout.HORIZONTAL);
        addView(mDevicesLayout, params);

        mExecuteBtn = new TextView(getContext());
        mExecuteBtn.setTextColor(Color.WHITE);
        mExecuteBtn.setTextSize(Util.Dpi(18));
        params = new LayoutParams(Util.Div(70), Util.Div(36));
        params.leftMargin = Util.Div(335);
        params.topMargin = Util.Div(87);
        mExecuteBtn.setText("执行");
        mExecuteBtn.setGravity(Gravity.CENTER);
        mExecuteBtn.setBackgroundResource(R.drawable.button_main);
        addView(mExecuteBtn, params);
        mExecuteBtn.setVisibility(GONE);

        mSwitchView = new View(getContext());
        mSwitchView.setBackground(getContext().getResources().getDrawable(R.drawable.switch_off));
        params = new LayoutParams(Util.Div(70), Util.Div(36));
        params.leftMargin = Util.Div(335);
        params.topMargin = Util.Div(87);
        addView(mSwitchView, params);
        mSwitchView.setVisibility(GONE);
    }

    private void addDeviceIcons(List<String> conditions, List<String> actions) {
        mDevicesLayout.removeAllViews();

        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(Util.Div(40), Util.Div(40));
        params.rightMargin = Util.Div(5);

        LinearLayout.LayoutParams paramsWithoutMargin = new LinearLayout.LayoutParams(Util.Div(40), Util.Div(40));

        boolean hasConditions = conditions != null && conditions.size() > 0;
        if (hasConditions) {
            View firstCondition = ImageLoader.getLoader().getView(getContext());
            ImageLoader.getLoader().with(getContext())
                    .setScaleType(ImageView.ScaleType.FIT_XY).load(Uri.parse(conditions.get(0))).into(firstCondition);
            mDevicesLayout.addView(firstCondition, params);

            if (conditions.size() > 1) {
                View more = ImageLoader.getLoader().getView(getContext());
                more.setBackground(getContext().getResources().getDrawable(R.drawable.icon_scene_more));
                mDevicesLayout.addView(more, paramsWithoutMargin);
            }
        }

        boolean hasActions = actions != null && actions.size() > 0;
        if (hasActions) {
            if (hasConditions) {
                View to = ImageLoader.getLoader().getView(getContext());
                to.setBackground(getContext().getResources().getDrawable(R.drawable.icon_scene_to));
                mDevicesLayout.addView(to, params);
            }

            for (int i = 0; i < mMaxShowActionCount && i < actions.size(); ++i) {
                View action = ImageLoader.getLoader().getView(getContext());
                ImageLoader.getLoader().with(getContext())
                        .setScaleType(ImageView.ScaleType.FIT_XY).load(Uri.parse(actions.get(i))).into(action);
                mDevicesLayout.addView(action, params);
            }

            if (actions.size() > mMaxShowActionCount) {
                View more = ImageLoader.getLoader().getView(getContext());
                more.setBackground(getContext().getResources().getDrawable(R.drawable.icon_scene_more));
                mDevicesLayout.addView(more, paramsWithoutMargin);
            }
        }
    }


    public void refreshUI(SceneBean item, int position) {
        mData = item;
        mDeviceNameTv.setText(item.scene_name);
        addDeviceIcons(item.conditions, item.actions);
        if (item.is_auto) {
            mExecuteBtn.setVisibility(View.GONE);
            mSwitchView.setVisibility(View.VISIBLE);
            if (item.is_enabled) {
                mSwitchView.setBackground(getContext().getResources().getDrawable(R.drawable.switch_on));
            } else {
                if (hasFocus()) {
                    mSwitchView.setBackground(getContext().getResources().getDrawable(R.drawable.switch_off_focus));
                } else {
                    mSwitchView.setBackground(getContext().getResources().getDrawable(R.drawable.switch_off));
                }
            }
        } else {
            mExecuteBtn.setVisibility(View.VISIBLE);
            mSwitchView.setVisibility(View.GONE);
        }
        if (item.is_new) {
            mNewTagView.setVisibility(VISIBLE);
        } else {
            mNewTagView.setVisibility(GONE);
        }
    }

    @Override
    public void onFocusChange(View view, boolean hasFocus) {
        super.onFocusChange(view, hasFocus);
        if (hasFocus) {
            if (!mData.is_enabled) {
                mSwitchView.setBackground(getContext().getResources().getDrawable(R.drawable.switch_off_focus));
            }
            mDeviceNameTv.setTextColor(Color.BLACK);
            mExecuteBtn.setSelected(true);
        } else {
            if (!mData.is_enabled) {
                mSwitchView.setBackground(getContext().getResources().getDrawable(R.drawable.switch_off));
            }
            mExecuteBtn.setSelected(false);
            mDeviceNameTv.setTextColor(Color.WHITE);
        }
        mFocusBg.setBorderVisible(hasFocus).setSolidColor(getResources().getColor(hasFocus ? R.color.white : R.color.white_10));
        Util.focusAnimate(view, hasFocus);

        //增加语音提示
        if(EmptyUtils.isNotEmpty(mData.voice_tips)){
            initPopWindow();
            showPopWindow(view,hasFocus);
        }
    }

    /**
     * 初始化PopWindow
     */
    private void initPopWindow(){
        if (mPopupWindow == null) {
            mVoiceTipsView = new VoiceTipsView(getContext());
            mPopupWindow = new PopupWindow(mVoiceTipsView, Util.Div(356), Util.Div(60));
            mPopupWindow.setOutsideTouchable(false);
        }
        if (mPopupWindow.isShowing()) {
            mPopupWindow.dismiss();
        }
    }

    /**
     * 展示popWindow
     * @param view
     * @param hasFocus
     */
    private void showPopWindow(View view,boolean hasFocus){
        if(hasFocus){
            uiRunnable = new UiRunnable(view);
            ThreadManager.getInstance().uiThread(uiRunnable, 350);
        }else{
            if(EmptyUtils.isNotEmpty(mVoiceTipsView)){
                mVoiceTipsView.stopFlipping();
            }
            if(EmptyUtils.isNotEmpty(mPopupWindow)){
                mPopupWindow.dismiss();
            }
            if(EmptyUtils.isNotEmpty(uiRunnable)){
                ThreadManager.getInstance().removeUiThread(uiRunnable);
                uiRunnable = null;
            }
        }
    }

    class UiRunnable implements Runnable{

        View view = null;
        public UiRunnable(View _view) {
            view = _view;
        }

        @Override
        public void run() {
            mVoiceTipsView.startWithList(mData.voice_tips);
            int width = mPopupWindow.getWidth();
            int[] xy = new int[2];
            view.getLocationInWindow(xy);
            mPopupWindow.showAtLocation(view, Gravity.NO_GRAVITY,
                    xy[0]  + Util.Div(13) + (view.getWidth() - Util.Div(width))/2, xy[1] - Util.Div(75));
            mPopupWindow.setAnimationStyle(R.style.popupWindowAnim);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        try {
            if (mData != null && EmptyUtils.isNotEmpty(mData.actions)) {
                for (String url : mData.actions) {
                    if (EmptyUtils.isNotEmpty(url)) {
                        ImageLoader.getLoader().clearCacheFromMemory(url);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
