package com.skyworth.smarthome.personal.thirdaccount.dialog;

import com.skyworth.smarthome.common.bean.ThridAccountHttpBean;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/6/5 15:00.
 */
public interface IBindThirdAccountDialog<T extends IBindThirdAccountDialog> {
    T setParams(ThridAccountHttpBean thridAccountHttpBean);

    T stopPolling();

    T setOnBindResultListener(OnBindResultListener listener);

    void show();

    void dismiss();

    interface OnBindResultListener {
        void result(boolean result);
    }
}
