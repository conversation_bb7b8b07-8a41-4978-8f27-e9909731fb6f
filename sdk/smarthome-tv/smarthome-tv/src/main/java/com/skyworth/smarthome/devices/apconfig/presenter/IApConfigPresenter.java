package com.skyworth.smarthome.devices.apconfig.presenter;

import android.view.KeyEvent;

import com.skyworth.smarthome.devices.apconfig.model.IApConfigModel;
import com.skyworth.smarthome.devices.apconfig.model.SwitchWifiInfo;
import com.skyworth.smarthome.common.base.IPresenter;
import com.skyworth.smarthome.devices.apconfig.view.IApConfigView;
import com.swaiot.aiotlib.device.apconfig.module.WifiModule;

public interface IApConfigPresenter extends IPresenter<IApConfigView, IApConfigModel> {
    void discoverDialogClick(Boolean isConfirm);

    void checkEtherNetClick();

    void passwordEntered(String ssid, String password);

    void onResume();

    void reConfig();

    void exitConfig();

    void goLogin();

    void recordIgnoreCurrentDevice();

    boolean dispatchKeyEvent(KeyEvent keyEvent);

    void setDevicePosClick(String pos);

    void startCurrentDeviceDetail();

    void connectWifiClick(SwitchWifiInfo wifiInfo);

    void connectWifiByDhcp(SwitchWifiInfo scanResult);

    void connectWifiByDhcp(SwitchWifiInfo scanResult, WifiModule.WifiConnectCallback callback);

    void showToast(String text);

    void showNotLogin(String tag);

    void loginClick(String tag);

    void onBindConfirmClick(boolean isBind);

    void onLoginAndBindClick();

    void closeDialog();

    void onDialogDismiss();

    void showDownloadPhoneApp(String erro);

    void stopService();

    void onDialogAutoDismiss(int reason);
}
