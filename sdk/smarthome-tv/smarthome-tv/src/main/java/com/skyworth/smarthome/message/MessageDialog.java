package com.skyworth.smarthome.message;

import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.skyworth.smarthome.common.base.BaseSysDialog;
import com.skyworth.smarthome.common.bean.MessageBean;
import com.skyworth.smarthome.common.util.DialogLauncherUtil;
import com.skyworth.smarthome.message.view.MessageView;
import com.skyworth.smarthome.R;
import com.skyworth.util.Util;
import com.smarthome.common.utils.EmptyUtils;

import java.lang.ref.WeakReference;

/**
 * Description: 设备消息 <br>
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/4/9 16:55.
 */
public class MessageDialog extends BaseSysDialog {
    private MessageView mDeviceNotifyView;
    private CloseHandler mHandler;
    private static final int DEFAULT_SHOW_TIME = 3 * 1000;//默认展示时间
    private static class CloseHandler extends Handler {

        private WeakReference<MessageDialog> mContextWR;

        public CloseHandler(MessageDialog context) {
            mContextWR = new WeakReference<>(context);
        }

        @Override
        public void handleMessage(Message msg) {
            if (mContextWR.get() != null) {
                mContextWR.get().dismiss();
            }
        }
    }

    public MessageDialog() {
        super(R.style.message_dialog);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getWindow() != null) {
            getWindow().setWindowAnimations(R.style.dialogWindowRightAnim);
        }
    }

    @Override
    protected void initParams() {
     mDialogKey = DialogLauncherUtil.DIALOG_KEY_MESSAGE;
    }

    @Override
    protected void initContentView() {
        super.initContentView();
        initUI();
        initData();
    }

    public void showDialog(MessageBean messageBean) {
        updateUI(messageBean);
    }

    protected void initData() {
        mHandler = new CloseHandler(this);
    }

    private void initUI() {
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(Util.instance().getDisplayWidth(), Util.instance().getDisplayHeight());
        FrameLayout mainLayout = new FrameLayout(mContext);
        mContentView.addView(mainLayout, params);
        params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.gravity = Gravity.RIGHT;
        params.topMargin = Util.Div(25);
        params.rightMargin = Util.Div(25);
        mDeviceNotifyView = new MessageView(mContext);
        mainLayout.addView(mDeviceNotifyView, params);
    }

    private void updateUI(MessageBean messageBean) {
        if (EmptyUtils.isNotEmpty(messageBean)) {
            mDeviceNotifyView.refreshUI(messageBean);
        }
        show();
        mHandler.removeCallbacksAndMessages(null);
        mHandler.sendEmptyMessageDelayed(0, DEFAULT_SHOW_TIME);
    }
}
