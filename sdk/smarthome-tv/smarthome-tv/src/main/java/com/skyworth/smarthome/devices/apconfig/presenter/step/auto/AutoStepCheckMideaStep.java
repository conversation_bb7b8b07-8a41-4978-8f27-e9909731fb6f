package com.skyworth.smarthome.devices.apconfig.presenter.step.auto;

import android.text.TextUtils;

import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.devices.apconfig.presenter.step.BaseStep;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/9/17 11:23.
 */
public class AutoStepCheckMideaStep extends BaseStep {
    public static final String STEP_TAG = "auto_check_midea";

    @Override
    public void run() {
        super.run();
        logi("this:" + this.toString());
        if (!presenter.isMideaDevice()) {
            next();
            return;
        }
        if (!presenter.isLogin()) {
            presenter.recordNotBindAndExit(AppConstants.APCONFIG_FAIL_REASON_NOT_LOGIN);
            return;
        }
        if (!presenter.isBindMobile()) {
            presenter.recordNotBindAndExit(AppConstants.APCONFIG_FAIL_REASON_NOT_BIND_MOBILE);
            return;
        }
        if (!presenter.isBindMidea()) {
            presenter.recordNotBindAndExit(AppConstants.APCONFIG_FAIL_REASON_NOT_BIND_MIDEA);
            return;
        }
        if (TextUtils.isEmpty(presenter.getMideaToken())) {
            presenter.recordNotBindAndExit(AppConstants.APCONFIG_FAIL_REASON_MIDEA_TOKEN_NULL);
            return;
        }
        next();
    }

    @Override
    public boolean input(String msg, Object... params) {
        return false;
    }

    @Override
    public String getTag() {
        return STEP_TAG;
    }

    @Override
    public void destroy() {
        super.destroy();
    }
}
