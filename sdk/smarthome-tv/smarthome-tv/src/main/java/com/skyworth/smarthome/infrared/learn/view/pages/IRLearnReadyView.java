package com.skyworth.smarthome.infrared.learn.view.pages;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import android.support.annotation.NonNull;

import com.skyworth.smarthome.R;
import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.util.Util;

import java.util.HashMap;
import java.util.Map;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/7/2 18:24.
 */
public class IRLearnReadyView extends BaseIRLearnViews {
    public static final String PARAMS_KEY_REMOTE_NAME = "remoteName";
    private TextView mTitle = null;
    private TextView mButton = null;

    public IRLearnReadyView(@NonNull Context context) {
        super(context);
        initTitle();
        initTip();
        initButton();
    }

    private void initTitle() {
        mTitle = new TextView(getContext());
        mTitle.setTextColor(Color.WHITE);
        mTitle.setTextSize(Util.Dpi(36));
        mTitle.getPaint().setFakeBoldText(true);
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.topMargin = Util.Div(80);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        addView(mTitle, layoutParams);
    }

    private void initTip() {
        TextView textView = new TextView(getContext());
        textView.setText(R.string.ir_learn_ready_tip);
        textView.setTextColor(Color.parseColor("#aaFFFFFF"));
        textView.setTextSize(Util.Dpi(32));
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.topMargin = Util.Div(144);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        addView(textView, layoutParams);
    }

    private void initButton() {
        mButton = new TextView(getContext());
        mButton.setBackground(new CCFocusDrawable(getContext()).setRadius(Util.Dpi(16)).setSolidColor(getResources().getColor(R.color.white)));
        mButton.setTextColor(Color.parseColor("#000000"));
        mButton.setTextSize(Util.Dpi(32));
        mButton.setFocusableInTouchMode(true);
        mButton.setFocusable(true);
        mButton.setTypeface(Typeface.DEFAULT_BOLD);
        mButton.setGravity(Gravity.CENTER);
        mButton.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mPresenter != null) {
                    mPresenter.iAmReady();
                }
            }
        });
        LayoutParams layoutParams = new LayoutParams(Util.Div(614), Util.Div(90));
        layoutParams.topMargin = Util.Div(256);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        addView(mButton, layoutParams);
    }

    @Override
    protected void onShow(Map<String, Object> params) {
        if (params == null || params.size() <= 0) {
            return;
        }
        String name = (String) params.get(PARAMS_KEY_REMOTE_NAME);
        mButton.requestFocus();
        mTitle.setText(getResources().getString(R.string.ir_learn_ready_title, name));
        mButton.setText(getResources().getString(R.string.ir_learn_ready_button));
    }

    @Override
    public int getViewWidth() {
        return Util.Div(714);
    }

    @Override
    public int getViewHeight() {
        return Util.Div(400);
    }

    public static Map<String, Object> getParams(String remoteName) {
        Map<String, Object> params = new HashMap<>(1);
        params.put(PARAMS_KEY_REMOTE_NAME, remoteName);
        return params;
    }

    @Override
    public String getName() {
        return "IRLearnReadyView";
    }
}
