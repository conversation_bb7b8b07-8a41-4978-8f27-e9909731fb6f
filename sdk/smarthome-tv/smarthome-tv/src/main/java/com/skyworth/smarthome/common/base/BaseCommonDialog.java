package com.skyworth.smarthome.common.base;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.FrameLayout;

import com.skyworth.smarthome.R;
import com.skyworth.util.Util;

import java.util.Map;


/**
 * Description: 普通弹框
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/4/8 17:34.
 */
public abstract class BaseCommonDialog extends Dialog {

    protected Context mContext;
    protected FrameLayout mContentView;
    protected String mDialogKey = "";


    public BaseCommonDialog(Context context) {
        super(context, R.style.global_dialog);
        initDialog(context);
    }

    public BaseCommonDialog(Context context, int themeResId) {
        super(context, themeResId);
        initDialog(context);
    }


    protected abstract void initParams();

    private void initDialog(Context context){
         mContext = context;
        initParams();
        initContentView();
        if (getWindow() != null) {
            getWindow().setGravity(Gravity.CENTER);
            getWindow().setWindowAnimations(R.style.dialogWindowAnim);
            WindowManager.LayoutParams params = getWindow().getAttributes();
            getWindow().setAttributes(params);
        }
        mContentView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        mContentView.setFocusable(false);
        setContentView(mContentView, new ViewGroup.LayoutParams(Util.Div(1920), Util.Div(1080)));
        setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                BaseCommonDialog.this.onDismiss();
            }
        });
    }

    protected void initContentView() {
        mContentView = new FrameLayout(mContext);
    }

    public void showDialog(Map<String, String> params) {
        show();
    }


    public void setDialogContentView(View view) {
        mContentView.addView(view);
    }


    /**
     * 取消动画
     */
    public void cancelDialogAnimation(){
        getWindow().setWindowAnimations(0);
    }



    protected void onDismiss() {
    }
}
