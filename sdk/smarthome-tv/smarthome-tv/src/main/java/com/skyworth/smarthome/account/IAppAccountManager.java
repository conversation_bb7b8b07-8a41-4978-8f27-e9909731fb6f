package com.skyworth.smarthome.account;


import com.smarthome.common.account.AccountInfo;

/**
 * Created by lu on 17-5-4.
 */

public interface IAppAccountManager {
    IAppAccountManager INSTANCE = new AppAccountManager();

    void registerAccountReceiver();

    void unRegisterAccountReceiver();

    boolean hasLogin();

    boolean hasLogin(boolean needGotoLogin);

    boolean checkLogin(boolean isRemindVoice);

    boolean isBindMobile();

    void gotoBindMobile();

    void gotoLogin();

    void logout();

    AccountInfo getAccountInfo(boolean isNeedPush);

    String getOpenID();

    String getSession();
}
