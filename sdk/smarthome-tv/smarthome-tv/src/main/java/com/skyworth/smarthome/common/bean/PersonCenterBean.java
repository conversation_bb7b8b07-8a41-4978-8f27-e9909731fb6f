package com.skyworth.smarthome.common.bean;

import com.swaiot.aiotlib.common.entity.FamilyBean;

import java.io.Serializable;

/**
 * @ProjectName: NewTV_SmartHome
 * @Package: com.skyworth.smarthome_tv.common.bean
 * @ClassName: PersonCenterBean
 * @Description: java类作用描述
 * @Author: wa<PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2020/6/8 15:07
 * @UpdateUser: 更新者
 * @UpdateDate: 2020/6/8 15:07
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class PersonCenterBean implements Serializable {
    private FamilyBean familyBean;
    private int deviceNumber; //设备数

    public FamilyBean getFamilyBean() {
        return familyBean;
    }

    public void setFamilyBean(FamilyBean familyBean) {
        this.familyBean = familyBean;
    }

    public int getDeviceNumber() {
        return deviceNumber;
    }

    public void setDeviceNumber(int deviceNumber) {
        this.deviceNumber = deviceNumber;
    }
}
