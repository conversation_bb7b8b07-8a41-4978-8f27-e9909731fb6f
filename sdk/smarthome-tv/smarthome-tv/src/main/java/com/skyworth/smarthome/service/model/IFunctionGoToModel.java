package com.skyworth.smarthome.service.model;

import com.swaiot.aiotlib.common.entity.DiscoverNetworkDevice;

import java.util.Map;

/**
 * Describe:功能跳转Model
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/9/17
 */
public interface IFunctionGoToModel {

    FunctionGoToModel INSTANCE = new FunctionGoToModel();

    /**
     * 跳转到添加扫描附近设备界面
     */
    void goToAddScanSmartDevice(String origin);

    /**
     * 设备配网自动静默模式
     *
     * @param device
     */
    void goToDeviceConfigAuto(DiscoverNetworkDevice device);

    /**
     * 设备配网手动模式
     *
     * @param device
     */
    void goToDeviceConfigManual(DiscoverNetworkDevice device);

    /**
     * 设备配网手动模式，但是有发现新设备的提示，倒计时30s
     *
     * @param device
     */
    void goToDeviceConfigManualWithDiscoverDialog(DiscoverNetworkDevice device);

    /**
     * 跳转到设备绑定界面
     *
     * @param device
     */
    void goToDeviceBind(DiscoverNetworkDevice device);

    /**
     * 跳转到小维智联APP引导下载
     *
     */
    void goToAppDownloadGuide(Map<String, String> params);

    /**
     * 跳转到红外遥控设备列表页面
     */
    void goToInfraredDeviceList(String from,String deviceId);

    /**
     * 跳转到添加红外设备界面
     */
    void goToAddInfraredDevice(String from);

}