package com.skyworth.smarthome.infrared.matchkey.sdks.kk;

import com.hzy.tvmao.KKSingleMatchManager;
import com.hzy.tvmao.KookongSDK;
import com.hzy.tvmao.interf.IRequestResult;
import com.hzy.tvmao.interf.ISingleMatchResult;
import com.kookong.app.data.RcTestRemoteKeyV3;
import com.kookong.app.data.RemoteList;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.common.bean.IRMatchKeyData;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.infrared.matchkey.sdks.BaseIRMatchSDK;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.skyworth.smarthome.common.util.Utils.listToStr;
import static com.skyworth.smarthome.infrared.matchkey.IRMatchKeyDialog.PARAM_IR_HOST_DEVICE_ID;
import static com.skyworth.smarthome.infrared.matchkey.IRMatchKeyDialog.PARAM_IR_KK_BRAND_ID;
import static com.skyworth.smarthome.infrared.matchkey.IRMatchKeyDialog.PARAM_IR_KK_TYPE_ID;
import static com.skyworth.smarthome.infrared.matchkey.IRMatchKeyDialog.PARAM_IR_SLAVE_BRAND;
import static com.skyworth.smarthome.infrared.matchkey.IRMatchKeyDialog.PARAM_IR_SLAVE_TYPE_ID;
import static com.skyworth.smarthome.infrared.matchkey.IRMatchKeyDialog.PARAM_IR_SLAVE_TYPE_NAME;

/**
 * Description: 酷控SDK<br>
 * Created by XuZexiao on 2019/9/2 16:12.
 */
public class IRMatchKeySDKKK extends BaseIRMatchSDK<RcTestRemoteKeyV3> {
    public static final String KK = "kk";
    private KKSingleMatchManager singleMatch = null;
    private String allRemotes = null;
    private int kkTypeId = 0;
    private int typeId = 0;
    private int brandId = 0;
    private String mIrHostDeviceId = null;
    private String matchSuccessRemoteId = null;
    private String mIrSlaveBrand = null;
    private String mIrSlaveTypeName = null;

    @Override
    public void init(Map<String, String> params) {
        super.init(params);
        kkTypeId = Integer.valueOf(params.get(PARAM_IR_KK_TYPE_ID));
        typeId = Integer.valueOf(params.get(PARAM_IR_SLAVE_TYPE_ID));
        brandId = Integer.valueOf(params.get(PARAM_IR_KK_BRAND_ID));
        mIrHostDeviceId = params.get(PARAM_IR_HOST_DEVICE_ID);
        mIrSlaveTypeName = params.get(PARAM_IR_SLAVE_TYPE_NAME);
        mIrSlaveBrand = params.get(PARAM_IR_SLAVE_BRAND);
        boolean result = KookongSDK.init(SmartHomeTvLib.getContext(), AppConstants.KK_SDK_KEY);
        logi("init: result: " + result);
        if (!result) {
            if (mResultListener != null) {
                mResultListener.onError("init sdk err");
            }
            return;
        }
        KookongSDK.setDebugMode(true);
        getAllRemotes();
    }

    private void getAllRemotes() {
        IRequestResult<RemoteList> result = new IRequestResult<RemoteList>() {
            @Override
            public void onSuccess(String msg, RemoteList list) {
                List<Integer> rids = list.rids;
                if (rids != null && rids.size() > 0) {
                    allRemotes = listToStr(rids);
                } else {
                    loge("getAllRemotes: list is empty");
                }
            }

            @Override
            public void onFail(Integer code, String msg) {
                loge("getAllRemotes: onFail: code: " + code + " msg: " + msg);
            }
        };
        KookongSDK.getAllRemoteIds(kkTypeId, brandId, null, result);
        singleMatch = new KKSingleMatchManager();
    }

    @Override
    public void start() {
        resetState();
        //匹配入口函数，获取匹配的按键，testSwitch是否测试电源键，如果电器是开机状态，传false跳过测试电源，测试其他键
        singleMatch.getMatchKey(kkTypeId, allRemotes, true, iSingleMatchResult);
    }

    private ISingleMatchResult iSingleMatchResult = new ISingleMatchResult() {

        @Override
        public void onNotMatchIR() {//匹配流程结束，没有匹配到可使用的红外码
            matchSuccessRemoteId = null;
            if (mResultListener != null) {
                mResultListener.onMatchFail();
            }
        }

        @Override
        public void onNextGroupKey(List<RcTestRemoteKeyV3> groupKeyList) {//返回一组需要测试的键
            mCurrentKeyList = groupKeyList;
            mCurrentKeyIndex = 0;
            if (mResultListener != null) {
                mResultListener.onMatchNextKey();
            }
        }

        @Override
        public void onMatchedIR(String remoteId) {//匹配结束，匹配到了可使用的红外码
            matchSuccessRemoteId = remoteId;
            if (mResultListener != null) {
                mResultListener.onMatchSuccess(remoteId);
            }
        }

        @Override
        public void onError() {//匹配过程中出错（网络及其他问题），重新走匹配流程
            matchSuccessRemoteId = null;
            if (mResultListener != null) {
                mResultListener.onError("");
            }
        }
    };

    @Override
    public void onKeyResponse() {
        singleMatch.keyIsWorking(mCurrentKeyList.get(mCurrentKeyIndex), iSingleMatchResult);
    }

    @Override
    public void onKeyNotResponse() {
        if (tryNextKeyCode()) {
            if (mResultListener != null) {
                mResultListener.onMatchNextKey();
            }
        } else {
            reportGroupKeyNotWork(mCurrentKeyList);
        }
    }

    private void reportGroupKeyNotWork(List<RcTestRemoteKeyV3> keyList) {
        singleMatch.groupKeyNotWork(keyList, iSingleMatchResult);
    }

    @Override
    public IRMatchKeyData getCurrentKeyInfo() {
        IRMatchKeyData data = new IRMatchKeyData();
        RcTestRemoteKeyV3 current = mCurrentKeyList.get(mCurrentKeyIndex);
        data.key_name = current.displayName;
        data.key_id = current.remoteIds;
        return data;
    }

    @Override
    public Map<String, Object> getSendIrParams() {
        Map<String, Object> params = new HashMap<>();
        params.put("send_type", 5);
        params.put("parent_id", mIrHostDeviceId);
        params.put("code_type", KK);
        params.put("code_table_id", Integer.valueOf(mCurrentKeyList.get(mCurrentKeyIndex).remoteIds.split(",")[0]));
        params.put("key_id", mCurrentKeyList.get(mCurrentKeyIndex).functionId);
        return params;
    }

    @Override
    public Map<String, Object> getSaveMatchResultParams() {
        Map<String, Object> params = new HashMap<>();
        params.put("parent_id", mIrHostDeviceId);
        params.put("type_id", typeId);//这里不传kkTypeId
        params.put("code_table_id", Integer.valueOf(matchSuccessRemoteId));
        params.put("name", mIrSlaveBrand + mIrSlaveTypeName);
        params.put("code_type", KK);
        return params;
    }

    @Override
    protected String getName() {
        return KK;
    }

    @Override
    public void destroy() {
        super.destroy();
    }
}
