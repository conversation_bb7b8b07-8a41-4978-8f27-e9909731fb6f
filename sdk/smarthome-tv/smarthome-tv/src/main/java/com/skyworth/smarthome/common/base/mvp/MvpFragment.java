package com.skyworth.smarthome.common.base.mvp;

import android.os.Bundle;

/**
 * <AUTHOR>
 * @time 2020/3/27
 * @describe
 */
public abstract class MvpFragment<P extends IBasePresenter> extends BaseFragment {

    protected abstract P getPresenter();


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

    }

    @Override
    public void onPause() {
        super.onPause();

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        P presenter = getPresenter();
        if (presenter != null) {
            presenter.detachView();
        }
    }

}
