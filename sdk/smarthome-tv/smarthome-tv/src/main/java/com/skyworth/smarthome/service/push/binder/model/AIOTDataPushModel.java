package com.skyworth.smarthome.service.push.binder.model;


import com.alibaba.fastjson.JSONObject;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.common.bean.DevcieStatusBean;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.event.AodDeviceInfoChangeEvent;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.model.SPCacheData;
import com.skyworth.smarthome.common.util.LogUtil;
import com.skyworth.smarthome.service.model.ISecondScreenModel;
import com.skyworth.smarthome.service.model.ISmartHomeModel;
import com.skyworth.smarthome.service.push.local.DeviceDataPushUtil;
import com.skyworth.smarthome.voicehandle.model.AIOTVoiceHandlerModel;
import com.smarthome.common.utils.EmptyUtils;
import com.swaiot.aiotlib.common.entity.FamilyBean;
import com.swaiot.aiotlib.common.entity.SceneBean;

import org.greenrobot.eventbus.EventBus;

import java.util.List;

import static com.skyworth.smarthome.common.model.AppConstants.EVENT_CHANGE_DEVICE_LIST;
import static com.skyworth.smarthome.common.model.AppConstants.EVENT_CHANGE_DEVICE_ONLINE_STATUS;
import static com.skyworth.smarthome.common.model.AppConstants.EVENT_CHANGE_DEVICE_STATUS;

/**
 * @ClassName: AIOTDataPushModel
 * @Author: AwenZeng
 * @CreateDate: 2020/3/27 19:41
 * @Description:
 */
public class AIOTDataPushModel implements IAiotDataPushModel {
    private IDeviceStatusModel mDeviceStatusModel;
    private IVoiceHandleModel mVoiceHandleModel;
    private IOtherPushModel mOtherPushModel;
    private IMediaControlModel mMediaControlModel;
    private ISceneChangeModel mSceneChangeModel;
    private AIOTVoiceHandlerModel mAiotVoiceHandlerModel;

    public AIOTDataPushModel() {
        mDeviceStatusModel = new DeviceStatusModel();
        mVoiceHandleModel = new VoiceHandleModel();
        mOtherPushModel = new OtherPushModel();
        mMediaControlModel = new MediaControlModel();
        mSceneChangeModel = new SceneChangeModel();
        mAiotVoiceHandlerModel = new AIOTVoiceHandlerModel();
    }

    @Override
    public void onReceivedData(String event, String data) {
        LogUtil.androidLog( "onReceivedData:  event = " + event + " data = " + data);
        switch (event){
            case AppConstants.EVENT_PUSH_VOICE_COMMAND_RESULT:
                mVoiceHandleModel.handleVoiceCommand(data);
                break;
            case AppConstants.EVENT_PUSH_DEVICE_ONLINE:
                EventBus.getDefault().post(new AodDeviceInfoChangeEvent(EVENT_CHANGE_DEVICE_ONLINE_STATUS,data));
                break;
            case AppConstants.EVENT_PUSH_DEVICE_COUNT:
                ISecondScreenModel.INSTANCE.sendDeviceCountMsg(data);
                break;
            case AppConstants.EVENT_PUSH_DEVICE_STATUS:
                ISecondScreenModel.INSTANCE.sendDeviceStatusChangeMsg(data);
                DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.DEVICE_STATUS, data);
                EventBus.getDefault().post(new AodDeviceInfoChangeEvent(EVENT_CHANGE_DEVICE_STATUS,data));
                break;
            case AppConstants.EVENT_PUSH_DEVICE_NOTIFY:
                mDeviceStatusModel.showDeviceNotify(data);
                break;
            case AppConstants.EVENT_PUSH_DEVICE_ALERT:
                mDeviceStatusModel.showDeviceAlert(data);
                break;
            case AppConstants.EVENT_PUSH_AIOT_HOME_STATUS:
                mDeviceStatusModel.sendAiotHomeStatusUpdateEvent(data);
                break;
            case AppConstants.EVENT_PUSH_DEVICE_INFO_UPDATE:
                mDeviceStatusModel.updateDeviceInfo(data);
                break;
            case AppConstants.EVENT_PUSH_SCENE_AUTO:
                mSceneChangeModel.receiveSceneExecute(data);
                break;
            case AppConstants.EVENT_PUSH_SCENE_COUNT:
            case AppConstants.EVENT_PUSH_SCENE_STATUS:
                ISmartHomeModel.INSTANCE.getSceneList();
                break;
            case AppConstants.EVENT_PUSH_TTS_MSG:
                mAiotVoiceHandlerModel.playTTS(data);
                break;
//            case Constants.EVENT_PUSH_VOICE_DEVICES_SYNC:
//                mAiotVoiceHandlerModel.deviceListForSync();
//                break;
            case AppConstants.EVENT_PUSH_VIDEO:
                mMediaControlModel.playVideo(data);
                break;
            case AppConstants.EVENT_PUSH_REPORT_CURRENT_MEDIA:
                mMediaControlModel.reportCurrentMedia(data);
                break;
            case AppConstants.EVENT_PUSH_PLAY_STREAM_VIDEO:
                mMediaControlModel.playVideoDoorBell(data);
                break;
            case AppConstants.EVENT_PUSH_STOP_PLAY_STREAM_VIDEO:
                mMediaControlModel.stopPlayVideoDoorBell();
                break;
            case AppConstants.EVENT_PUSH_OTHER_AUTH_SUCCEED:
                mOtherPushModel.otherAccountLoginSucceed(true);
                break;
            case AppConstants.EVENT_PUSH_OTHER_AUTH_FAILED:
                mOtherPushModel.otherAccountLoginSucceed(false);
                break;
            case AppConstants.EVENT_PUSH_JD_AUTH_SUCCEED:
                mOtherPushModel.jdAccountLoginSucceed();
                break;
            case AppConstants.EVENT_PUSH_JD_AUTH_FAILED:
                mOtherPushModel.jdAccountLoginFailed();
                break;
            case AppConstants.EVENT_PUSH_START_OTHER_APP:
                mOtherPushModel.startOtherApp(data);
                break;
            case AppConstants.EVENT_PUSH_NOTIFY_OTHER_APP:
                mOtherPushModel.notifyOtherApp(data);
                break;
            case AppConstants.EVENT_PUSH_DEVICE_IOT_MODIFY:
                mAiotVoiceHandlerModel.handleData(data);
                break;
            default:
                break;
        }
    }

    @Override
    public void onHandleDeviceListData(String data) {
        try {
            if (EmptyUtils.isNotEmpty(data)) {
                List<DeviceInfo> list = JSONObject.parseArray(data, DeviceInfo.class);
                AppData.getInstance().setDeviceInfoList(ISmartHomeModel.INSTANCE.sortDeviceList(list));
            } else {
                AppData.getInstance().setDeviceInfoList(null);
            }
        } catch (Exception e) {
            e.printStackTrace();
            AppData.getInstance().setDeviceInfoList(null);
        }
        EventBus.getDefault().post(new AodDeviceInfoChangeEvent(EVENT_CHANGE_DEVICE_LIST,""));
    }

    @Override
    public void onHandleSceneListData(String data) {
        try {
            if (EmptyUtils.isNotEmpty(data)) {
                List<SceneBean> list = JSONObject.parseArray(data, SceneBean.class);
                AppData.getInstance().setSceneList(ISmartHomeModel.INSTANCE.sortSceneList(list));
            } else {
                AppData.getInstance().setSceneList(null);
            }
        } catch (Exception e) {
            e.printStackTrace();
            AppData.getInstance().setSceneList(null);
        }
    }

    @Override
    public void onHandleFamilyListData(String data) {
        try {
            if (EmptyUtils.isNotEmpty(data)) {
                List<FamilyBean> list = JSONObject.parseArray(data, FamilyBean.class);
                AppData.getInstance().setFamilyList(list);
            } else {
                AppData.getInstance().setFamilyList(null);
                SPCacheData.setFamilyId(SmartHomeTvLib.getContext(),"");
            }
        } catch (Exception e) {
            e.printStackTrace();
            AppData.getInstance().setFamilyList(null);
        }
    }

    @Override
    public void onHandleCurrentFamilyData(String data) {
        try {
            if (EmptyUtils.isNotEmpty(data)) {
                FamilyBean currentFamily = JSONObject.parseObject(data, FamilyBean.class);
                AppData.getInstance().setCurrentFamily(currentFamily);
                SPCacheData.setFamilyId(SmartHomeTvLib.getContext(), currentFamily.family_id);
            } else {
                AppData.getInstance().setCurrentFamily(null);
                SPCacheData.setFamilyId(SmartHomeTvLib.getContext(), "");
            }
        } catch (Exception e) {
            e.printStackTrace();
            AppData.getInstance().setCurrentFamily(null);
        }
    }

    @Override
    public void onHandleDeviceStatusData(String data) {
        List<DeviceInfo> deviceInfoList = AppData.getInstance().getDeviceInfoList();
        DevcieStatusBean devcieStatusBean = JSONObject.parseObject(data, DevcieStatusBean.class);
        if (devcieStatusBean != null && EmptyUtils.isNotEmpty(deviceInfoList)) {
            DeviceInfo bean = null;
            for (int i = 0; i < deviceInfoList.size(); i++) {
                bean = deviceInfoList.get(i);
                if (bean.device_id.equals(devcieStatusBean.device_id)) {
                    if (EmptyUtils.isNotEmpty(devcieStatusBean.device_name)) {
                        bean.device_name = devcieStatusBean.device_name;
                    }
                    if (EmptyUtils.isNotEmpty(devcieStatusBean.online_status)) {
                        bean.online_status = Integer.parseInt(devcieStatusBean.online_status);
                    }
                    if (EmptyUtils.isNotEmpty(devcieStatusBean.device_status_desc)) {
                        bean.device_status_desc = devcieStatusBean.device_status_desc;
                    }
                    if (EmptyUtils.isNotEmpty(devcieStatusBean.status)) {
                        JSONObject updateStatus = JSONObject.parseObject(devcieStatusBean.status);
                        JSONObject detailStatusObject = JSONObject.parseObject(bean.report_status);
                        for (Object key : updateStatus.keySet().toArray()) {
                            detailStatusObject.put((String) key, updateStatus.getString((String) key));
                        }
                        bean.report_status = detailStatusObject.toJSONString();
                    }
                    deviceInfoList.set(i, bean);
                    AppData.getInstance().setDeviceInfoList(deviceInfoList);
                    break;
                }
            }
        }
    }
}
