package com.skyworth.smarthome.devices.apconfig;

import android.view.Gravity;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.skyworth.smarthome.devices.apconfig.view.BindDeviceActivityView;
import com.skyworth.smarthome.common.base.BaseSysDialog;
import com.skyworth.util.Util;

import java.util.Map;

/**
 * @ClassName: BindDeviceActivityDialog
 * @Author: AwenZeng
 * @CreateDate: 2020/9/27 16:32
 * @Description: 美的活动绑定成功奖品弹框
 */
public class BindDeviceActivityDialog extends BaseSysDialog {

    @Override
    protected void initParams() {
    }

    @Override
    protected void initContentView() {
        super.initContentView();
    }

    @Override
    public void showDialog(Map<String, String> params) {
        super.showDialog(params);
        ImageView bg = new ImageView(mContext);
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Util.Div(1920), Util.Div(1080));
        mContentView.addView(bg, layoutParams);

        BindDeviceActivityView bindDeviceActivityView = new BindDeviceActivityView(mContext);
        layoutParams = new FrameLayout.LayoutParams(Util.Div(860), Util.Div(600));
        layoutParams.gravity = Gravity.CENTER;
        mContentView.addView(bindDeviceActivityView, layoutParams);
        bindDeviceActivityView.setBindDeviceListener(new BindDeviceActivityView.BindDeviceOnClickListener() {
            @Override
            public void onOkBtn() {
                dismiss();
            }

            @Override
            public void onCancelBtn() {
                   dismiss();
            }
        });
        openAutoDismissDialog();
    }

    @Override
    protected void onDismiss() {
        super.onDismiss();
    }
}
