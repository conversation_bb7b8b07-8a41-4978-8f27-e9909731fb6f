package com.skyworth.smarthome.home.smartdevice.devicelist;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;

import com.alibaba.fastjson.JSONObject;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.base.BaseCommonDialog;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.dialog.OfflineHelpOtherTVDialog;
import com.skyworth.smarthome.common.dialog.OfflineHelpTVDialog;
import com.skyworth.smarthome.common.dialog.OfflineHelpWifiDialog;
import com.skyworth.smarthome.common.dialog.OfflineHelpZigbeeDialog;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.util.DialogCommonUtil;
import com.skyworth.smarthome.common.util.LogUtil;
import com.skyworth.smarthome.common.util.SystemProperty;
import com.skyworth.smarthome.home.DeviceGuideAcitvity;
import com.skyworth.smarthome.home.HomeUtil;
import com.skyworth.smarthome.home.base.BaseSmartItemView;
import com.skyworth.smarthome.home.base.BaseSmartListView;
import com.skyworth.smarthome.home.smartdevice.controlpanel.DeviceControlDialog;
import com.skyworth.smarthome.service.model.IAIOTModel;
import com.skyworth.smarthome.service.model.IFunctionGoToModel;
import com.skyworth.util.Util;
import com.smarthome.common.utils.CCLog;
import com.smarthome.common.utils.Constants;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XToast;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/4
 */
public class DeviceListView extends BaseSmartListView<DeviceInfo> {

    private final static String TAG = "DeviceListView";
    private OfflineHelpWifiDialog mOfflineHelpWifiDialog;
    private OfflineHelpTVDialog mOfflineHelpTVDialog;
    private OfflineHelpOtherTVDialog mOfflineHelpOtherTVDialog;
    private OfflineHelpZigbeeDialog mOfflineHelpZigbeeDialog;
    private BaseCommonDialog baseCommonDialog;

    public DeviceListView(Context context) {
        super(context);
        CONTENT_TOP_MARGIN = Util.Div(-7);
    }

    @Override
    protected BaseSmartItemView<DeviceInfo> getItemView() {
        return new DeviceItemView(getContext());
    }

    @Override
    protected BaseSmartItemView<String> getAddItemView() {
        return new AddDeviceItemView(getContext());
    }

    @Override
    public void refreshUI(List<DeviceInfo> datas) {
        super.refreshUI(datas);
        if (EmptyUtils.isNotEmpty(HomeUtil.getOpenCtrlPanelDeviceId())) {
            if (Constants.DO_WHAT_ADD_DEVICE.equals(HomeUtil.getOpenCtrlPanelDeviceId())) {
                CCLog.i(TAG, "refreshUI: showAddDevicePage");
                onAddItemClick();
                //清空
                HomeUtil.setOpenCtrlPanelDeviceId("");
            } else if (mDatas.size() > 0) {
                CCLog.i(TAG, "refreshUI: call item click event 1");
                postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (HomeUtil.getCurrentPageIndex() == HomeUtil.getSmartDeviceTabIndex()) {
                            int size = mDatas.size();
                            for (int i = 0; i < size; i++) {
                                DeviceInfo data = mDatas.get(i);
                                if (data.device_id.equals(HomeUtil.getOpenCtrlPanelDeviceId())) {
                                    CCLog.i(TAG, "refreshUI: call item click event 2");
                                    onItemClick(i);
                                    break;
                                }
                            }
                        }
                        //清空
                        HomeUtil.setOpenCtrlPanelDeviceId("");
                    }
                }, 30);
            }
        }
    }

    @Override
    public void refreshItem(DeviceInfo updateData) {
        super.refreshItem(updateData);
        int size = mDatas.size();
        for (int i = 0; i < size; i++) {
            DeviceInfo data = mDatas.get(i);
            if (data.device_id.equals(updateData.device_id)) {
                mDatas.set(i, updateData);
                ((DeviceItemView) getChildAt(i)).refreshUI(updateData,i);
                invalidate();
                break;
            }
        }
    }

    @Override
    public void onItemClick(int position) {
        super.onItemClick(position);
        final DeviceInfo info = mDatas.get(position);
        if (getContext() instanceof Activity && ((Activity) getContext()).isFinishing()) {
            CCLog.i(TAG, "onItemClick: Activity isFinishing.");
            return;
        }
        //新设备标志优先清除
        if (info.is_new) {
            IAIOTModel.INSTANCE.cancelNewDeviceMark(info.device_id,null);
            post(new Runnable() {
                @Override
                public void run() {
                    info.is_new = false;
                    refreshItem(info);
                }
            });
        }

        if (info.online_status == 0) {//离线
            if (info.acess_type == AppConstants.DEVICE_ACESS_TYPE_WIFI && !info.product_type_id.equals("1")) {
                //wifi
                if (mOfflineHelpWifiDialog == null) {
                    mOfflineHelpWifiDialog = new OfflineHelpWifiDialog(getContext());
                }
                mOfflineHelpWifiDialog.show();
                return;
            }
            if (info.acess_type == AppConstants.DEVICE_ACESS_TYPE_WIFI && info.product_type_id.equals("1") && info.device_id.equals(SystemProperty.getDeviceId())) {
                //TV 本机
                if (mOfflineHelpTVDialog == null) {
                    mOfflineHelpTVDialog = new OfflineHelpTVDialog(getContext());
                }
                mOfflineHelpTVDialog.show();
                return;
            }
            if (info.acess_type == AppConstants.DEVICE_ACESS_TYPE_WIFI && info.product_type_id.equals("1") && !info.device_id.equals(SystemProperty.getDeviceId())) {
                //TV 其他
                if (mOfflineHelpOtherTVDialog == null) {
                    mOfflineHelpOtherTVDialog = new OfflineHelpOtherTVDialog(getContext());
                }
                mOfflineHelpOtherTVDialog.show();
                return;
            }
            if (info.acess_type == AppConstants.DEVICE_ACESS_TYPE_ZIGBEE || info.acess_type == AppConstants.DEVICE_ACESS_TYPE_BLE) {
                //zigbee 、ble
                if (mOfflineHelpZigbeeDialog == null) {
                    mOfflineHelpZigbeeDialog = new OfflineHelpZigbeeDialog(getContext());
                }
                mOfflineHelpZigbeeDialog.show();
                return;
            }
            if(info.acess_type == AppConstants.DEVICE_ACESS_TYPE_INFRARED){
                XToast.showToast(getContext(),"红外可控设备已离线");
                return;
            }
        }
        if(info.is_unBind){//未绑定设备
            if(info.isNotConnectedNetwork){//未联网
                IFunctionGoToModel.INSTANCE.goToDeviceConfigManual(info.discoverNetworkDevice);
            }else{//已联网
                LogUtil.androidLog("绑定设备："+ JSONObject.toJSONString(info.discoverNetworkDevice));
                IFunctionGoToModel.INSTANCE.goToDeviceBind(info.discoverNetworkDevice);
            }
        }else if(info.acess_type == AppConstants.DEVICE_ACESS_TYPE_INFRARED){//红外遥控设备
            XToast.showToast(getContext(),getResources().getString(R.string.try_voice_control));
        } else{
            Map<String, String> map = new HashMap<>();
            map.put("device_id", info.device_id);
            if(info.product_type_id.equals("47")||info.is_infrared){
                IFunctionGoToModel.INSTANCE.goToInfraredDeviceList("click",info.device_id);
            }else{
                baseCommonDialog = new DeviceControlDialog(getContext());
                baseCommonDialog.showDialog(map);
            }
            if(info.product_type_id.equals("125")||info.is_infrared){
                if(baseCommonDialog!=null){
                    baseCommonDialog.dismiss();
                }
                XToast.showToast(getContext(),getResources().getString(R.string.more_information));
            }
            if(info.product_type_id.equals("123")||info.is_infrared){
                if(baseCommonDialog!=null){
                    baseCommonDialog.dismiss();
                }
                XToast.showToast(getContext(),getResources().getString(R.string.more_information));
            }
            if(info.product_type_id.equals("124")||info.is_infrared){
                if(baseCommonDialog!=null){
                    baseCommonDialog.dismiss();
                }
                XToast.showToast(getContext(),getResources().getString(R.string.more_information));
            }
            if(info.product_type_id.equals("1")||info.is_infrared){
                if(baseCommonDialog!=null){
                    baseCommonDialog.dismiss();
                }
                XToast.showToast(getContext(),getResources().getString(R.string.more_information));
            }
        }
    }

    @Override
    public void resetFocus() {
        getChildAt(0).requestFocus();
    }

    /**
     * 当Activity看不见的时候，关掉弹窗
     */
    public void onPause(){
        if(baseCommonDialog!=null){
            baseCommonDialog.dismiss();
        }
    }



    @Override
    public void onAddItemClick() {
        super.onAddItemClick();
        try {
            Intent intent = new Intent(getContext(), DeviceGuideAcitvity.class);
            getContext().startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
