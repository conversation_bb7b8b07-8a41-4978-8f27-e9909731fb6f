package com.skyworth.smarthome.common.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:语音指令解析结果推送信息
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/2/19
 */
public class VoiceCommandPushData {
    public String command_type;
    public String command_text;
    public String control_type;
    public CommandData command_data;

    public static class CommandData implements Serializable {
        public String device_id;//设备ID
        public String device_ids;
        public String scene_id;//场景ID
        public String scene_name;
        public String control_params;
        public String result_tips;
        public int result_code;
        public int[] result_codes;
        public List<String> tts_list;
        public List<String> device_list;
    }
}
