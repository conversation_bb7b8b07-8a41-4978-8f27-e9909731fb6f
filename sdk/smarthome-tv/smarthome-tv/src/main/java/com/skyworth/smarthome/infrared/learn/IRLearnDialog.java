package com.skyworth.smarthome.infrared.learn;

import android.view.KeyEvent;
import android.widget.FrameLayout;
import android.widget.ImageView;

import android.support.annotation.NonNull;

import com.skyworth.smarthome.infrared.learn.model.IIRLearnModel;
import com.skyworth.smarthome.infrared.learn.model.IRLearnModelImpl;
import com.skyworth.smarthome.infrared.learn.presenter.IIRLearnPresenter;
import com.skyworth.smarthome.infrared.learn.presenter.IRLearnPresenterImpl;
import com.skyworth.smarthome.infrared.learn.view.IIRLearnView;
import com.skyworth.smarthome.infrared.learn.view.IRLearnViewImpl;
import com.skyworth.smarthome.common.base.BaseSysDialog;
import com.skyworth.smarthome.common.util.DialogLauncherUtil;
import com.skyworth.util.Util;

import java.util.HashMap;
import java.util.Map;

import static com.skyworth.smarthome.common.util.DialogLauncherUtil.DIALOG_KEY_IR_LEARN;


/**
 * Description: <br>
 * Created by XuZexiao on 2019/7/2 18:02.
 */
public class IRLearnDialog extends BaseSysDialog {
    public static final String TAG = "IRLearn";
    private IIRLearnView mView = null;
    private IIRLearnPresenter mPresenter = null;
    private IIRLearnModel mModel = null;

    public static final String PARAM_IR_HOST_DEVICE_ID = "irHostDeviceId";
    public static final String PARAM_IR_HOST_NAME = "irHostName";
    public static final String PARAM_IR_HOST_TYPE_ID = "irHostTypeId";
    public static final String PARAM_IR_SLAVE_NAME = "irSlaveName";
    public static final String PARAM_IR_SLAVE_TYPE_ID = "irSlaveTypeId";

    public static void launch(String irHostDeviceId, String irHostName, String irHostTypeId, String irSlaveName, String irSlaveTypeId) {
        Map<String, String> params = new HashMap<>();
        params.put(PARAM_IR_HOST_DEVICE_ID, irHostDeviceId);
        params.put(PARAM_IR_HOST_NAME, irHostName);
        params.put(PARAM_IR_HOST_TYPE_ID, irHostTypeId);
        params.put(PARAM_IR_SLAVE_NAME, irSlaveName);
        params.put(PARAM_IR_SLAVE_TYPE_ID, irSlaveTypeId);
        DialogLauncherUtil.showIRLearnDialog(params);
    }

    @Override
    protected void initParams() {
        mDialogKey = DIALOG_KEY_IR_LEARN;
    }

    @Override
    public void showDialog(Map<String, String> params) {
        super.showDialog(params);
        addBlackTransparentBg();
        mView = new IRLearnViewImpl();
        mPresenter = new IRLearnPresenterImpl();
        mModel = new IRLearnModelImpl();
        mView.create(getContext(), mPresenter);
        mView.setDialogListener(new IIRLearnView.IDialogListener() {
            @Override
            public void onDismiss() {
                dismiss();
            }
        });
        mContentView.addView(mView.getView());

        mPresenter.create(getContext(), mView, mModel);
        mPresenter.setParams(params);
        mPresenter.start(null);
        openAutoDismissDialog();
    }
    private void addBlackTransparentBg() {
        ImageView bg = new ImageView(mContext);
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Util.Div(1920), Util.Div(1080));
        mContentView.addView(bg, layoutParams);
    }


    @Override
    public boolean dispatchKeyEvent(@NonNull KeyEvent event) {
        if (mView != null && mView.handleKeyEvent(event)) {
            return true;
        }
        return super.dispatchKeyEvent(event);
    }

    @Override
    protected void onDismiss() {
        super.onDismiss();
        if (mView != null) {
            mView.destroy();
        }
        if (mPresenter != null) {
            mPresenter.destroy();
        }
        if (mModel != null) {
            mModel.destroy();
        }
    }
}
