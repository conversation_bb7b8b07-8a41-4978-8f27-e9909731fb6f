package com.skyworth.smarthome.infrared.matchkey.presenter.step;

import com.skyworth.smarthome.infrared.matchkey.presenter.IMatchKeyPresenter;

import static com.skyworth.smarthome.infrared.matchkey.presenter.IMatchKeyPresenterImpl.HANDLE_KEY_CONFIRM_AGAIN;
import static com.skyworth.smarthome.infrared.matchkey.presenter.IMatchKeyPresenterImpl.HANDLE_KEY_CONFIRM_NO;
import static com.skyworth.smarthome.infrared.matchkey.presenter.IMatchKeyPresenterImpl.HANDLE_KEY_CONFIRM_YES;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/4/29 18:31.
 */
public class ConfirmResultStep extends IRConfigBaseStep<IMatchKeyPresenter> {
    public static final String STEP_TAG = "confirm_result";

    @Override
    public void create() {
        super.create();
    }

    @Override
    public void run() {
        super.run();
        presenter.showConfirmView();
    }

    @Override
    public boolean input(String msg, Object... params) {
        switch (msg) {
            case HANDLE_KEY_CONFIRM_YES:
                presenter.keyResponse();
                return true;
            case HANDLE_KEY_CONFIRM_NO:
                presenter.keyNotResponse();
                return true;
            case HANDLE_KEY_CONFIRM_AGAIN:
                jumpTo(SendIRCodeStep.STEP_TAG);
                return true;
        }
        return false;
    }

    @Override
    public String getTag() {
        return STEP_TAG;
    }

    @Override
    public void destroy() {
        super.destroy();
    }
}
