package com.skyworth.smarthome.message.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.bean.MessageBean;
import com.skyworth.smarthome.common.ui.VoiceTipsView;
import com.skyworth.smarthome.common.util.ShadowDrawable;
import com.skyworth.smarthome.common.util.SpannableStringUtils;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;
import com.smarthome.common.utils.EmptyUtils;

/**
 * Describe:设备通知变化View
 * Created by <PERSON><PERSON><PERSON>eng on 2019/3/21
 */
public class MessageView extends LinearLayout {

    private Context mContext;
    private View mDeviceIconImg;
    private TextView mContentTv;
    private LinearLayout mContentLayout;
    private VoiceTipsView mVoiceTipsView;

    public MessageView(Context context) {
        super(context);
        mContext = context;
        initUI();
    }

    private void initUI() {
        setLayerType(View.LAYER_TYPE_SOFTWARE, null);
        Drawable bg = new ShadowDrawable.Builder()
                .setBgColor(Color.parseColor("#FFFFFF"))
                .setShapeRadius(Util.Div(16))
                .setOffsetX(0).setOffsetY(0).builder();
        setOrientation(LinearLayout.VERTICAL);
        mContentLayout = new LinearLayout(getContext());
        mContentLayout.setOrientation(HORIZONTAL);
        mContentLayout.setBackground(bg);
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, Util.Div(140));
        layoutParams.gravity = Gravity.RIGHT;
        addView(mContentLayout,layoutParams);

        mDeviceIconImg = ImageLoader.getLoader().getView(mContext);
        layoutParams = new LayoutParams(Util.Div(60),Util.Div(60));
        layoutParams.gravity = Gravity.CENTER_VERTICAL;
        layoutParams.leftMargin = Util.Div(60);
        mContentLayout.addView(mDeviceIconImg, layoutParams);

        mContentTv = new TextView(mContext);
        mContentTv.setTextSize(Util.Dpi(24));
        mContentTv.setTextColor(Color.parseColor("#333333"));
        layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.gravity = Gravity.CENTER_VERTICAL;
        layoutParams.leftMargin = Util.Div(30);
        layoutParams.rightMargin = Util.Div(60);
        mContentLayout.addView(mContentTv, layoutParams);

        mVoiceTipsView = new VoiceTipsView(getContext());
        layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, Util.Div(54));
        layoutParams.rightMargin = Util.Div(20);
        layoutParams.gravity = Gravity.RIGHT;
        addView(mVoiceTipsView,layoutParams);
        mVoiceTipsView.setVisibility(GONE);
    }

    public void refreshUI(MessageBean messageBean) {
        switch (messageBean.type){
            case SCENE_VOICE_CONTROL_RESULT:
                mDeviceIconImg.setBackground(getContext().getResources().getDrawable(R.drawable.scene_ok));
                break;
            case DEVICE_ALERT:
            case DEVICE_NOTICE:
            case DEVICE_VOICE_CONTROL_RESULT:
                if (EmptyUtils.isNotEmpty(messageBean.imgUrl)) {
                    ImageLoader.getLoader().with(mContext).resize(Util.Div(60), Util.Div(60))
                            .setScaleType(ImageView.ScaleType.FIT_XY).load(Uri.parse(messageBean.imgUrl)).into(mDeviceIconImg);
                } else {
                    mDeviceIconImg.setBackground(getContext().getResources().getDrawable(R.drawable.device_default_icon));
                }
                break;
        }

        if (EmptyUtils.isNotEmpty(messageBean.content)) {
            if(EmptyUtils.isNotEmpty(messageBean.location)){
                mContentTv.setText(SpannableStringUtils.getBuilder(messageBean.location).setForegroundColor(Color.parseColor("#4681FF"))
                        .append(" | ").append(messageBean.content).create());
            }else{
                mContentTv.setText(SpannableStringUtils.getBuilder(messageBean.content).create());
            }
        }

        if(EmptyUtils.isNotEmpty(messageBean.voiceTips)){
            mVoiceTipsView.setVisibility(VISIBLE);
            mVoiceTipsView.startWithList(messageBean.voiceTips);
//            if (messageBean.voiceTips.size() > 2) {
//                mVoiceTipTv.setText(SpannableStringUtils.getBuilder("语音呼出：\" ").append(messageBean.voiceTips.get(0)).append(",").append(messageBean.voiceTips.get(1)).append("\"").create());
//            } else {
//                mVoiceTipTv.setText(SpannableStringUtils.getBuilder("语音呼出：\" ").append(messageBean.voiceTips.get(0)).append("\"").create());
//            }
        }
    }

}
