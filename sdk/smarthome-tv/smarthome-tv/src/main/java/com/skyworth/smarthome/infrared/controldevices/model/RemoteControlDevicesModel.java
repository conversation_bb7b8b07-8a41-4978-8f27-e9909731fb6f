package com.skyworth.smarthome.infrared.controldevices.model;

import com.coocaa.app.core.http.HttpServiceManager;
import com.skyworth.smarthome.common.bean.IRAddDeviceData;
import com.skyworth.smarthome.common.http.SmartDevicesHttpService;
import com.skyworth.smarthome.infrared.electriclist.model.DeviceTypeListData;
import com.smarthome.common.model.SmartBaseData;
import com.smarthome.common.utils.EmptyUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;


/**
 * Describe:
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/5/6
 */
public class RemoteControlDevicesModel implements IRemoteControlDevicesModel {

    @Override
    public List<DeviceTypeListData> getIRDeviceList(String device_id) {
        SmartBaseData<List<DeviceTypeListData>> baseData = null;
        try {
            Call<SmartBaseData<List<DeviceTypeListData>>> call = SmartDevicesHttpService.SERVICE.getIRDeviceList(device_id, 0, 1000);
            baseData = HttpServiceManager.Companion.call(call);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (EmptyUtils.isNotEmpty(baseData) && EmptyUtils.isNotEmpty(baseData.data)) {
            return baseData.data;
        }
        return null;
    }

    @Override
    public boolean deleteIrDevice(String deviceID, DeviceTypeListData data) {
        Map<String, Object> body = new HashMap<>();
        body.put("id",data.id);
        body.put("parent_id",deviceID);
        Call<SmartBaseData<IRAddDeviceData>> call = SmartDevicesHttpService.SERVICE.irDeviceOpt(3, body);
        SmartBaseData<IRAddDeviceData> result = HttpServiceManager.Companion.call(call);
        if(EmptyUtils.isNotEmpty(result)&&result.code.equals("0")){
            return true;
        }
        return false;
    }
}
