package com.skyworth.smarthome.personal;

import android.content.Context;

import com.skyworth.smarthome.common.base.mvp.IBasePresenter;
import com.skyworth.smarthome.common.base.mvp.IBaseView;
import com.skyworth.smarthome.common.bean.DeviceInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2020/3/27
 * @describe
 */
public interface PersonCenterContract {

    interface View extends IBaseView<Presenter> {
        void finish();

        void queryBindDevices(List<DeviceInfo> deviceBeans);
    }

    interface Presenter extends IBasePresenter<View> {
        void init(Context context);

        void logout();

        void autoFindDevice(boolean autoFindDeviceStutus);

        void queryDevices(String familyId);
    }

    interface Callback {
        void deviceNumber(int number);
    }
}
