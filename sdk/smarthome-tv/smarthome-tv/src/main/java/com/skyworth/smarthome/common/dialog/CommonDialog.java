package com.skyworth.smarthome.common.dialog;

import android.app.Dialog;
import android.content.Context;

import com.skyworth.ui.api.SkyDialogView;

/**
 * Created by <PERSON><PERSON><PERSON> on 16-1-4.
 */
public class CommonDialog {


    private Dialog dialog = null;
    private SkyDialogView dialogView = null;
    private Context mContext;

    /**
     * @param context
     * @param twoBtn  是否要两个button，如果为false则只有一个button
     */
    public CommonDialog(Context context, boolean twoBtn) {
        init(context, twoBtn);
    }

    /**
     * @param context
     * @param themeResId dialog主题
     * @param twoBtn     是否要两个button，如果为false则只有一个button
     */
    public CommonDialog(Context context, int themeResId, boolean twoBtn) {
        init(context, twoBtn);
    }

    private void init(Context context, boolean twoBtn) {
        mContext = context;
        dialogView = new SkyDialogView(context, twoBtn);
        dialog = SkyDialogView.showWithDialog(mContext, dialogView);
    }

    /**
     * 设置事件监听接口
     *
     * @param listener
     */
    public void setOnDialogOnKeyListener(SkyDialogView.OnDialogOnKeyListener listener) {
        dialogView.setOnDialogOnKeyListener(listener);
    }

    /**
     * 设置提示语
     *
     * @param tips_1 提示语1
     * @param tips_2 提示语2
     */
    public void setTipsString(String tips_1, String tips_2) {
        dialogView.setTipsString(tips_1, tips_2);
    }

    /**
     * 设置button名称
     *
     * @param btn_1
     * @param btn_2
     */
    public void setBtnString(String btn_1, String btn_2) {
        dialogView.setBtnString(btn_1, btn_2);
    }

    public void setFocusBtn(int index){
        dialogView.setBtnFocus(index);
    }

    public synchronized void show() {
        dialog.show();
    }

    public synchronized void dismiss() {
        dialog.dismiss();
    }
}
