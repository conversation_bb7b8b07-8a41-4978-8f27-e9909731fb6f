package com.skyworth.smarthome.personal.unbinddevice;

import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v7.widget.NewRecycleAdapter;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.RelativeLayout;
import android.widget.TextView;


import com.skyworth.smarthome.common.base.mvp.MvpFragment;
import com.skyworth.smarthome.home.smartdevice.devicelist.BaseDeviceItemView;
import com.skyworth.smarthome.infrared.controldevices.model.IRemoteControlDevicesModel;
import com.skyworth.smarthome.infrared.controldevices.model.RemoteControlDevicesModel;
import com.skyworth.smarthome.service.push.local.DeviceDataPushUtil;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.bean.IRAddDeviceData;
import com.skyworth.smarthome.common.dialog.UnBindDialogFragment;
import com.skyworth.smarthome.common.http.SmartDevicesHttpService;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.ui.CommonFocusBox;
import com.skyworth.smarthome.common.util.Contants;
import com.skyworth.smarthome.common.util.SystemProperty;
import com.skyworth.smarthome.common.util.ViewsBuilder;
import com.skyworth.smarthome.service.model.IFunctionGoToModel;
import com.skyworth.smarthome.service.model.SmartDeviceDataModel;
import com.skyworth.ui.newrecycleview.NewRecycleAdapterItem;
import com.skyworth.ui.newrecycleview.NewRecycleLayout;
import com.skyworth.ui.newrecycleview.OnBoundaryListener;
import com.skyworth.ui.newrecycleview.OnItemClickListener;
import com.skyworth.ui.newrecycleview.OnItemFocusChangeListener;
import com.smarthome.common.model.SmartBaseData;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XToast;
import com.swaiot.aiotlib.common.entity.DeviceBean;

import org.jetbrains.annotations.NotNull;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * @ProjectName: NewTV_SmartHome
 * @Package: com.skyworth.smarthome_tv.personal.unbinddevice
 * @ClassName: UnbindDeviceFragment
 * @Description: java类作用描述
 * @Author: wangyuehui
 * @CreateDate: 2020/6/5 17:52
 * @UpdateUser: 更新者
 * @UpdateDate: 2020/6/5 17:52
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class UnbindDeviceFragment extends MvpFragment<UnbindContract.Presenter> implements UnbindContract.View {

    private final String TAG = UnbindDeviceFragment.class.getSimpleName();

    private UnbindContract.Presenter mPresenter;
    private NewRecycleLayout<DeviceInfo> mRecycleLayout;
    private NewRecycleAdapter<DeviceInfo> mAdapter;
    private RelativeLayout noDevicesLayout;
    private CommonFocusBox noDevicesBtn;
    private TextView noDevicesBtnTxt;
    private long mLastAnimationX;
    private List<DeviceInfo> mDataList;
    private IRemoteControlDevicesModel mRemoteControlDevicesModel;


    public static UnbindDeviceFragment newInstance(String familyId) {
        Bundle args = new Bundle();
        args.putString(Contants.COOCAA_INTENT_CURRENT_FAMILY_ID, familyId);
        UnbindDeviceFragment fragment = new UnbindDeviceFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mRemoteControlDevicesModel = new RemoteControlDevicesModel();
    }

    @Nullable
    @Override
    public View onCreateView(@NotNull @NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return ViewsBuilder.getDevicesLayout(getContext());
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        mRecycleLayout = view.findViewById(R.id.unbind_devices_recycleview);
        noDevicesLayout = view.findViewById(R.id.unbind_devices_no_devices_layout);
        noDevicesBtn = view.findViewById(R.id.unbind_devices_add_device_btn);
        noDevicesBtnTxt = view.findViewById(R.id.unbind_devices_add_device);

        //抖动效果
        mRecycleLayout.setmBoundaryListener(new OnBoundaryListener() {
            @Override
            public boolean onLeftBoundary(View leaveView, int position) {
                lastXAnimation(leaveView, getActivity());
                return false;
            }

            @Override
            public boolean onTopBoundary(View leaveView, int position) {
                lastYAnimation(leaveView, getActivity());
                return false;
            }

            @Override
            public boolean onDownBoundary(View leaveView, int position) {
                lastYAnimation(leaveView, getActivity());
                return true;
            }

            @Override
            public boolean onRightBoundary(View leaveView, int position) {
                lastXAnimation(leaveView, getActivity());
                return false;
            }

            @Override
            public boolean onOtherKeyEvent(View v, int position, int keyCode) {
                return false;
            }
        });

        mRecycleLayout.setmItemClickListener(new OnItemClickListener() {
            @Override
            public void click(View v, final int position) {
                if (v instanceof UnBindDeviceView && ((UnBindDeviceView) v).getMenuViewStatus()) {
                    //弹出对话框
                    Log.d(TAG, "-----setmItemClickListener----:" + Thread.currentThread().getId());
                    if (getFragmentManager() != null) {
                        UnBindDialogFragment unBindDialogFragment = UnBindDialogFragment.newInstance(mDataList.get(position).device_id);
                        unBindDialogFragment.setUnBindCallBack(new UnBindDialogFragment.UnBindCallBack() {
                            @Override
                            public void callBack() {
                                final DeviceInfo info = mDataList.get(position);
                                if (info.is_infrared&& info.device_id.equals(SystemProperty.getDeviceId())) {
                                    //本机电视就是红外电视，禁止解绑万能红外遥控
                                    XToast.showToast(SmartHomeTvLib.getContext(),"电视万能遥控为电视自带功能，不支持解绑");
                                }else if(info.is_unBind){
                                    boolean ret = SmartDeviceDataModel.INSTANCE.removeDiscoverDevice(info.discoverNetworkDevice);
                                    if (ret) {
                                        showDevices(mDataList);
                                        XToast.showToast(getContext(), "删除成功");
                                    } else {
                                        XToast.showToast(getContext(), "删除失败");
                                    }
                                }else if(info.acess_type == AppConstants.DEVICE_ACESS_TYPE_INFRARED){//红外遥控设备
                                    Map<String, Object> body = new HashMap<>();
                                    body.put("id", info.device_id);
                                    body.put("parent_id", info.gateway_id);
                                    Call<SmartBaseData<IRAddDeviceData>> call = SmartDevicesHttpService.SERVICE.irDeviceOpt(3, body);
                                    call.enqueue(new Callback<SmartBaseData<IRAddDeviceData>>() {
                                        @Override
                                        public void onResponse(Call<SmartBaseData<IRAddDeviceData>> call, Response<SmartBaseData<IRAddDeviceData>> response) {
                                            SmartBaseData smartBaseData = response.body();
                                            if (smartBaseData != null && !TextUtils.isEmpty(smartBaseData.code) && smartBaseData.code.equals("0")) {
                                                removeItemChanged(mDataList.get(position).device_id);
                                                XToast.showToast(getContext(), "解绑成功");
                                                DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.DEVICE_LIST, "");
                                            } else {
                                                XToast.showToast(getContext(), "解绑失败");
                                            }
                                        }
                                        @Override
                                        public void onFailure(Call<SmartBaseData<IRAddDeviceData>> call, Throwable throwable) {
                                            XToast.showToast(getContext(), "解绑失败");
                                        }
                                    });
                                }else if(info.acess_type == AppConstants.DEVICE_ACESS_TYPE_BLE||info.acess_type == AppConstants.DEVICE_ACESS_TYPE_ZIGBEE){
                                    Call<SmartBaseData<String>> call = SmartDevicesHttpService.SERVICE.operateGatewayDevice(info, "2");
                                    call.enqueue(new Callback<SmartBaseData<String>>() {
                                        @Override
                                        public void onResponse(Call<SmartBaseData<String>> call, Response<SmartBaseData<String>> response) {
                                            SmartBaseData smartBaseData = response.body();
                                            if (smartBaseData != null && !TextUtils.isEmpty(smartBaseData.code) && smartBaseData.code.equals("0")) {
                                                removeItemChanged(mDataList.get(position).device_id);
                                                XToast.showToast(getContext(), "解绑成功");
                                                DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.DEVICE_LIST, "");
                                            } else {
                                                XToast.showToast(getContext(), "解绑失败");
                                            }
                                        }
                                        @Override
                                        public void onFailure(Call<SmartBaseData<String>> call, Throwable throwable) {
                                            XToast.showToast(getContext(), "解绑失败");
                                        }
                                    });
                                } else{
                                    Call<SmartBaseData> smartBaseDataCall = SmartDevicesHttpService.SERVICE.unBindDevice(mDataList.get(position).device_id);
                                    smartBaseDataCall.enqueue(new Callback<SmartBaseData>() {
                                        @Override
                                        public void onResponse(Call<SmartBaseData> call, Response<SmartBaseData> response) {
                                            SmartBaseData smartBaseData = response.body();
                                            if (smartBaseData != null && !TextUtils.isEmpty(smartBaseData.code) && smartBaseData.code.equals("0")) {
                                                removeItemChanged(mDataList.get(position).device_id);
                                                XToast.showToast(getContext(), "解绑成功");
                                                DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.DEVICE_LIST, "");
                                            } else {
                                                XToast.showToast(getContext(), "解绑失败");
                                            }
                                        }

                                        @Override
                                        public void onFailure(Call<SmartBaseData> call, Throwable t) {
                                            XToast.showToast(getContext(), "解绑失败");
                                        }
                                    });
                                }
                            }
                        });
//                            unBindDialogFragment.showNow(getFragmentManager(),Contants.COOCAA_TAG_UNBIND_DIALOG_FRAGMENT);
                        unBindDialogFragment.show(getFragmentManager(), Contants.COOCAA_TAG_UNBIND_DIALOG_FRAGMENT);
                    }
                }
            }
        });

        mRecycleLayout.setmItemFocusChangeListener(new OnItemFocusChangeListener() {
            @Override
            public void focusChange(View v, int position, boolean hasFocus) {
                ((BaseDeviceItemView) v).onFocusChange(v, hasFocus);
            }
        });

        noDevicesBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                IFunctionGoToModel.INSTANCE.goToAddScanSmartDevice("手动");
            }
        });

        noDevicesBtn.setOnFocusChangeListener(new CommonFocusBox.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                noDevicesBtnTxt.setTextColor(hasFocus ? Color.BLACK : Color.WHITE);
            }
        });
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

        UnBindPresenter mPresenter = new UnBindPresenter(this);
        mPresenter.init(getContext());

    }

    @Override
    public void setPresenter(UnbindContract.Presenter presenter) {
        mPresenter = presenter;
    }

    @Override
    public boolean isActive() {
        return getActivity() != null && !getActivity().isFinishing() && isAdded();
    }

    @Override
    protected UnbindContract.Presenter getPresenter() {
        return null;
    }

    @Override
    public void queryBindDevices(final List<DeviceInfo> deviceBeans) {
//        if (mAtomicBoolean.get()) {
//            mAtomicBoolean.compareAndSet(false,true);
//            return;
//        }
        mDataList = deviceBeans;
        getActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (EmptyUtils.isNotEmpty(deviceBeans) && !deviceBeans.get(0).is_virtual) {
                    showDevices(deviceBeans);
                    mRecycleLayout.setVisibility(View.VISIBLE);
                    noDevicesLayout.setVisibility(View.GONE);
                    noDevicesLayout.clearFocus();
                } else {
                    mRecycleLayout.setVisibility(View.INVISIBLE);
                    noDevicesLayout.setVisibility(View.VISIBLE);
                    noDevicesBtn.requestFocus();
                }
            }
        });

    }

    private void showDevices(List<DeviceInfo> deviceBeans) {

        mAdapter = new NewRecycleAdapter<DeviceInfo>(deviceBeans, 1) {

            @Override
            public NewRecycleAdapterItem<DeviceInfo> onCreateItem(Object type) {
                return new UnBindDeviceView(getActivity());
            }
        };
        mRecycleLayout.setRecyclerAdapter(mAdapter);
        mRecycleLayout.setSelection(0);

    }

    private void lastXAnimation(View view, Context mActivity) {
        long duration = AnimationUtils.currentAnimationTimeMillis() - mLastAnimationX;
        if (duration > 500) {
            Animation animation = AnimationUtils.loadAnimation(mActivity,
                    R.anim.user_share_shake);
            view.startAnimation(animation);
            mLastAnimationX = AnimationUtils.currentAnimationTimeMillis();
        }
    }

    private void lastYAnimation(View view, Context mActivity) {
        long duration = AnimationUtils.currentAnimationTimeMillis() - mLastAnimationX;
        if (duration > 500) {
            Animation animation = AnimationUtils.loadAnimation(mActivity,
                    R.anim.user_share_shake_y);
            view.startAnimation(animation);
            mLastAnimationX = AnimationUtils.currentAnimationTimeMillis();
        }
    }

    public void removeItemChanged(String deviceId) {
        Log.i(TAG, "removeItemChanged: ----deviceId:" + deviceId);
        try {
            int originalSize = mDataList.size();//原始大小
            for (int i = 0; i < originalSize; i++) {
                DeviceBean data = mDataList.get(i);
                if (!TextUtils.isEmpty(data.device_id) && data.device_id.equals(deviceId)) {
                    ((UnBindDeviceView) mRecycleLayout.getItemByPosition(i)).removeUnbindView();
                    mDataList.remove(i);
                    mRecycleLayout.notifyItemRemoved(i);
                    int lastPos = mDataList.size() - 1;
                    if (i == (originalSize - 1)) {//如果操作的是最后一个，手动处理一下落焦，落焦到当前最后一个
                        mRecycleLayout.setSelection(lastPos);
                    } else {
                        mRecycleLayout.setSelection(i);
                    }
                    break;
                }
            }
            int currentSize = mDataList.size();
            if (currentSize == 0) {
                mDataList.clear();
                mRecycleLayout.setVisibility(View.INVISIBLE);
                //显示空数据View，并落焦
                noDevicesLayout.setVisibility(View.VISIBLE);
                noDevicesBtn.requestFocus();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
