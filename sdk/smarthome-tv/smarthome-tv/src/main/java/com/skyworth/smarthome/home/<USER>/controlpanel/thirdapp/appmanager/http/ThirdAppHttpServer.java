package com.skyworth.smarthome.home.smartdevice.controlpanel.thirdapp.appmanager.http;

import android.content.Context;

import com.coocaa.app.core.http.HttpServiceManager;
import com.skyworth.smarthome.common.http.SmartHomeHttpConfig;
import com.skyworth.smarthome.home.smartdevice.controlpanel.thirdapp.appmanager.AppInfo;

import org.jetbrains.annotations.NotNull;

import retrofit2.Call;

/**
 * Created by <PERSON> on 2018/5/28.
 */

public class ThirdAppHttpServer extends HttpServiceManager<ThirdAppHttpMethod> implements ThirdAppHttpMethod {
    private static ThirdAppHttpServer instance = null;

    public static ThirdAppHttpServer getInstance(Context c) {
        if (null == instance)
            instance = new ThirdAppHttpServer(SmartHomeHttpConfig.APPSTORE_SERVER);
        return instance;
    }

    private ThirdAppHttpServer(String server) {
        super(SmartHomeHttpConfig.getServer(server), SmartHomeHttpConfig.SMARTHOME_HEADER_LOADER);
    }

    @NotNull
    @Override
    protected Class<ThirdAppHttpMethod> getServiceClazz() {
        return ThirdAppHttpMethod.class;
    }

    @Override
    public Call<AppInfo.DownloadInfo> getdDownloadInfo(String pkg) {
        return getHttpService().getdDownloadInfo(pkg);
    }

//    @Override
//    public Call<AppDetailInfoData> getAppDetail(String pkg) {
//        return getHttpService().getAppDetail(pkg);
//    }

    @Override
    public Call<AppInfo.UpgradeInfo> getAppUpdateBeans(String pkgs) {
        return getHttpService().getAppUpdateBeans(pkgs);
    }

}
