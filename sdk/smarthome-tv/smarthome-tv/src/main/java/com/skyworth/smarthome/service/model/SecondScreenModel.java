package com.skyworth.smarthome.service.model;

import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.common.bean.SecondScreenData;
import com.skyworth.smarthome.common.bean.VoiceCommandPushData;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.model.UserInfo;
import com.smarthome.common.utils.EmptyUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class SecondScreenModel implements ISecondScreenModel {

    @Override
    public void sendNotifyMsg(String data) {
        //设备通知，通知副屏
        Log.d("ssd", "sendNotifyMsg:" + data);
        if (EmptyUtils.isEmpty(data))
            return;
        JSONObject jsonObject = JSONObject.parseObject(data);
        final String content = jsonObject.getString("notify_msg");
        final String device_name = jsonObject.getString("device_name");
        SecondScreenData ssd = new SecondScreenData();
        ssd.type = 1;
        ssd.title = device_name;
        ssd.content = content;
        sendMsg(ssd);
    }

    @Override
    public void sendAlertMsg(String data) {
        //设备警告，通知副屏
        Log.d("ssd", "sendAlertMsg:" + data);
        if (EmptyUtils.isEmpty(data))
            return;
        JSONObject jsonObject = JSONObject.parseObject(data);
        final String content = jsonObject.getString("alert_msg");
        final String device_name = jsonObject.getString("device_name");
        SecondScreenData ssd = new SecondScreenData();
        ssd.type = 2;
        ssd.title = device_name;
        ssd.content = content;
        sendMsg(ssd);
    }

    @Override
    public void sendDeviceCountMsg(String data) {
        //增减设备，通知副屏
        try {
            Log.d("ssd", "sendDeviceCountMsg:" + data);
            JSONObject jsonObject = JSONObject.parseObject(data);
            String removed = jsonObject.getString("removed");
            String added = jsonObject.getString("added");
            Log.d("ssd", "removed:" + removed);
            Log.d("ssd", "added:" + added);
            if (!TextUtils.isEmpty(removed)) {
                JSONArray array = jsonObject.getJSONArray(removed);
                for (int i = 0; i < array.size(); i++) {
                    JSONObject js = array.getJSONObject(i);
                    DeviceObject obj = JSONObject.parseObject(js.toJSONString(), DeviceObject.class);
                    SecondScreenData ssd = new SecondScreenData();
                    ssd.type = 3;
                    ssd.title = obj.device_name;
                    ssd.content = "已移除";
                    sendMsg(ssd);
                }
            }

            if (!TextUtils.isEmpty(added)) {
                JSONArray array = jsonObject.getJSONArray(added);
                for (int i = 0; i < array.size(); i++) {
                    JSONObject js = array.getJSONObject(i);
                    DeviceObject obj = JSONObject.parseObject(js.toJSONString(), DeviceObject.class);
                    SecondScreenData ssd = new SecondScreenData();
                    ssd.type = 3;
                    ssd.title = obj.device_name;
                    ssd.content = "已接入";
                    sendMsg(ssd);
                }
            }

        } catch (Exception e) {
        }
    }

//    @Override
//    public void queryDeviceListMsg(SecondScreenData data) {
//        sendMsg(data);
//    }
//
//    @Override
//    public void sendSceneStartMsg(SecondScreenData data) {
//        sendMsg(data);
//    }

    @Override
    public void sendVoiceMsg(VoiceCommandPushData voiceCommandPushData) {
        Log.d("ssd", "sendVoiceMsg   voiceCommandPushData.command_type:" + voiceCommandPushData.command_type);
        switch (voiceCommandPushData.command_type) {
            case AppConstants.VOICE_COMMAND_EXECUTE_SCENE:
//                String resulte_tips = "voiceCommandPushData.command_data.results_tips";
//                TTSManager.sendTTS(SmartHomeApplication.getContext(), resulte_tips);
                SecondScreenData ssd = new SecondScreenData();
                ssd.type = 5;
                ssd.title = voiceCommandPushData.command_data.scene_name;
                ssd.content = "已启动";
                ssd.tts = voiceCommandPushData.command_data.tts_list;
                sendMsg(ssd);
                break;
            case AppConstants.VOICE_COMMAND_QUERY_DEVICE_LIST:
                List<String> list = voiceCommandPushData.command_data.device_list;
                if (list == null || list.size() == 0) {
//                    TTSManager.sendTTS(SmartHomeApplication.getContext(), "您当前未绑定智能设备");
                } else {
                    String deviceRes = "您的设备有" + getListString(list);
//                    TTSManager.sendTTS(SmartHomeApplication.getContext(), deviceRes);
                    SecondScreenData ssd2 = new SecondScreenData();
                    ssd2.type = 4;
                    ssd2.content = deviceRes;
                    sendMsg(ssd2);
                }
                break;
            default:
        }
    }

    @Override
    public void sendDeviceStatusChangeMsg(String data) {
        //状态变化，通知副屏
        try {
            Log.d("ssd", "sendDeviceStatusChangeMsg   data:" + data);
            DeviceSimpleData simpleData = JSONObject.parseObject(data, DeviceSimpleData.class);
            String userId = UserInfo.getInstance().getUserID();
            String token = UserInfo.getInstance().getToken();
            SecondScreenData ssd = new SecondScreenData();
            ssd.type = 6;
            SecondScreenData.ModifyData md = new SecondScreenData.ModifyData();
            md.device_id = simpleData.device_id;
            md.device_type = String.valueOf(simpleData.device_type_id);
            md.user_id = userId;
            md.access_token = token;
            md.props = simpleData.status;
            ssd.md = md;
            sendMsg(ssd);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String getListString(List<String> list) {
        if (list != null && list.size() > 0) {
            StringBuffer sb = new StringBuffer();
            for (String str : list) {
                sb.append(str + ",");
            }
            String temp = sb.toString();
            return temp.substring(0, temp.length() - 1);
        }
        return "";
    }

    private void sendMsg(SecondScreenData data) {
        Intent i = new Intent();
        i.setPackage("com.skyworth.lafite.srtnj.speechserver");
        i.setAction("com.skyworth.aiot.broadcast.msg");
        i.putExtra("msg_data", JSONObject.toJSONString(data));
        SmartHomeTvLib.getContext().sendBroadcast(i);
    }

    public static class DeviceObject implements Serializable {
        public String device_id;
        public String device_name;
        public String logo;
    }

    public static class DeviceSimpleData implements Serializable {
        public String device_id;
        public int device_type_id;
        public Map<String, String> status;
    }

}
