package com.skyworth.smarthome.infrared.matchkey.view;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import android.support.annotation.NonNull;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.ui.DialogBg;
import com.skyworth.util.Util;

import static com.skyworth.smarthome.infrared.matchkey.presenter.IMatchKeyPresenterImpl.HANDLE_KEY_SEND_FINISH;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/4/30 11:28.
 */
public class SendIRView extends FrameLayout {
    private TextView second = null;
    private static final int MAX_SECOND = 3;
    private int currentSec = MAX_SECOND;
    private CountDown countDown = null;
    private OnClickListener onClickListener = null;

    public SendIRView(@NonNull Context context, OnClickListener onClickListener) {
        super(context);
        this.onClickListener = onClickListener;
        setTag(HANDLE_KEY_SEND_FINISH);
        initView();
    }

    private void initView() {
        setBackground(new DialogBg());

        TextView tip = new TextView(getContext());
        tip.setTextSize(Util.Dpi(32));
        tip.setTextColor(Color.WHITE);
        tip.setText(R.string.ir_config_sending_key);
        LayoutParams layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.topMargin = Util.Div(122);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        addView(tip, layoutParams);

        second = new TextView(getContext());
        second.setTextSize(Util.Dpi(60));
        second.setTextColor(Color.WHITE);
        second.getPaint().setFakeBoldText(true);
        layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParams.topMargin = Util.Div(218);
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        addView(second, layoutParams);
    }

    public void startSending() {
        currentSec = MAX_SECOND;
        if (countDown == null) {
            countDown = new CountDown();
        }
        post(countDown);
    }

    private class CountDown implements Runnable {
        @Override
        public void run() {
            if (currentSec > 0) {
                second.setText(currentSec + "s");
                currentSec--;
                postDelayed(this, 1000);
            } else {
                onClickListener.onClick(SendIRView.this);
            }
        }
    }
}
