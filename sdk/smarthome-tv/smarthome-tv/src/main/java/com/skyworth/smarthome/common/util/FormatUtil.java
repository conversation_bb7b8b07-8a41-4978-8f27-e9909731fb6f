package com.skyworth.smarthome.common.util;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/7/8 11:11.
 */
public class FormatUtil {
    private static final char[] DIGITS = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'};
    private static final char[] UPPER_CASE_DIGITS = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'};

    public static String bytesToHexString(byte[] bytes, boolean upperCase) {
        char[] digits = upperCase ? UPPER_CASE_DIGITS : DIGITS;
        char[] buf = new char[bytes.length * 2];
        int c = 0;
        byte[] arr$ = bytes;
        int len$ = bytes.length;

        for (int i$ = 0; i$ < len$; ++i$) {
            byte b = arr$[i$];
            buf[c++] = digits[b >> 4 & 15];
            buf[c++] = digits[b & 15];
        }

        return new String(buf);
    }

    public static String intsToHexString(int[] ints) {
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < ints.length; i++) {
            String hex = Integer.toHexString(ints[i]);
            builder.append(hex.length() == 1 ? "0" + hex : hex);
        }
        return builder.toString();
    }
}
