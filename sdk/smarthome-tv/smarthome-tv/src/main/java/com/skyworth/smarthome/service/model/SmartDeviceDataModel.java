package com.skyworth.smarthome.service.model;

import com.skyworth.smarthome.service.model.data.AssembleDataHandleModel;
import com.skyworth.smarthome.service.model.data.DiscoverDeviceDataHandleModel;
import com.skyworth.smarthome.service.model.data.GetDataHandleModel;
import com.skyworth.smarthome.service.model.data.IAssembleDataHandleModel;
import com.skyworth.smarthome.service.model.data.IDiscoverDeviceDataHandleModel;
import com.skyworth.smarthome.service.model.data.IGetDataHandleModel;
import com.skyworth.smarthome.service.model.data.IStatusDataHandleModel;
import com.skyworth.smarthome.service.model.data.StatusDataHandleModel;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.swaiot.aiotlib.common.entity.DiscoverNetworkDevice;
import com.swaiot.aiotlib.common.entity.DiscoverWifiDevice;
import com.swaiot.aiotlib.common.entity.FamilyBean;
import com.swaiot.aiotlib.common.entity.SceneBean;

import java.util.List;

/**
 * Describe:智能设备数据服务Model
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/2/1
 */
public class SmartDeviceDataModel implements ISmartDeviceDataModel {

    private IGetDataHandleModel mGetDataHandleModel;
    private IStatusDataHandleModel mStatusDataHandleModel;
    private IAssembleDataHandleModel mAssembleDataHandleModel;
    private IDiscoverDeviceDataHandleModel mDiscoverDeviceDataHandleModel;

    public SmartDeviceDataModel() {
        mGetDataHandleModel = new GetDataHandleModel();
        mStatusDataHandleModel = new StatusDataHandleModel();
        mAssembleDataHandleModel = new AssembleDataHandleModel();
        mDiscoverDeviceDataHandleModel = new DiscoverDeviceDataHandleModel();
    }


    @Override
    public List<SceneBean> getSceneList() {
        return mGetDataHandleModel.getSceneList();
    }

    @Override
    public List<FamilyBean> getFamilyList() {
        return mGetDataHandleModel.getFamilyList();
    }

    @Override
    public SceneBean getSceneInfo(String sceneID) {
        return mGetDataHandleModel.getSceneInfo(sceneID);
    }

    @Override
    public List<DeviceInfo> getSmartDeviceList() {
        return mGetDataHandleModel.getSmartDeviceList();
    }

    @Override
    public List<DeviceInfo> getCacheSmartDeviceList() {
        return mGetDataHandleModel.getCacheSmartDeviceList();
    }

    @Override
    public DeviceInfo getSmartDeviceInfo(String deviceID) {
        return mGetDataHandleModel.getSmartDeviceInfo(deviceID);
    }

    @Override
    public String getAiotHomeStatus(String familyId) {
        return mGetDataHandleModel.getAiotHomeStatus(familyId);
    }


    @Override
    public String assemblePushHomePageData(String status) {
        return mAssembleDataHandleModel.assemblePushHomePageData(status);
    }

    @Override
    public DiscoverNetworkDevice assembleApconfigDeviceInfo(DiscoverWifiDevice discoverDeviceInfo) {
        return mAssembleDataHandleModel.assembleApconfigDeviceInfo(discoverDeviceInfo);
    }

    @Override
    public void addNearbyDeviceToDeviceList() {
        mDiscoverDeviceDataHandleModel.addNearbyDeviceToDeviceList();
    }

    @Override
    public void addDiscoverDevice(DiscoverNetworkDevice discoverNetworkDevice) {
        mDiscoverDeviceDataHandleModel.addDiscoverDevice(discoverNetworkDevice);
    }


    @Override
    public boolean removeDiscoverDevice(DiscoverNetworkDevice discoverNetworkDevice) {
       return mDiscoverDeviceDataHandleModel.removeDiscoverDevice(discoverNetworkDevice);
    }

    @Override
    public List<DiscoverNetworkDevice> getNearNetworkDeviceList() {
        return mDiscoverDeviceDataHandleModel.getNearNetworkDeviceList();
    }

    @Override
    public void updateControlStatusData(String msg) {
        mStatusDataHandleModel.updateControlStatusData(msg);
    }

    @Override
    public void updateOnlineStatusData(String msg) {
        mStatusDataHandleModel.updateOnlineStatusData(msg);
    }
}
