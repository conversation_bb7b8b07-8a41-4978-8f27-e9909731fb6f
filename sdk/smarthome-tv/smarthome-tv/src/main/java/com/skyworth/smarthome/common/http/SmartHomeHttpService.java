package com.skyworth.smarthome.common.http;

import com.coocaa.app.core.http.HttpServiceManager;
import com.skyworth.smarthome.common.bean.CityBean;
import com.skyworth.smarthome.common.bean.RedirectHttpBean;
import com.skyworth.smarthome.common.bean.WeathBean;
import com.smarthome.common.model.SmartBaseData;

import java.util.Map;

import retrofit2.Call;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/10
 */
public class SmartHomeHttpService extends HttpServiceManager<SmartHomeHttpMethod> {

    public static final SmartHomeHttpService SERVICE = new SmartHomeHttpService(SmartHomeHttpConfig.DEVICE_SERVER);

    public SmartHomeHttpService(String server) {
        super(SmartHomeHttpConfig.getServer(server), SmartHomeHttpConfig.SMARTHOME_HEADER_LOADER);
    }

    @Override
    protected Class<SmartHomeHttpMethod> getServiceClazz() {
        return SmartHomeHttpMethod.class;
    }

    public Call<SmartBaseData<RedirectHttpBean>> getRedirect(String name) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        map.put("name", name);
        return getHttpService().getRedirect(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("name"),
                SmartHomeHttpConfig.getSign(map));
    }

    public Call<SmartBaseData<WeathBean>> getWeatherInfo(String city) {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        map.put("city", city);
        return getHttpService().getWeatherInfo(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                map.get("city"),
                SmartHomeHttpConfig.getSign(map));
    }

    public Call<SmartBaseData<CityBean>> getCityInfo() {
        Map<String, String> map = SmartHomeHttpConfig.getBaseUrlParams();
        return getHttpService().getCityInfo(map.get("appkey"),
                map.get("time"),
                map.get("uid"),
                map.get("ak"),
                map.get("vuid"),
                SmartHomeHttpConfig.getSign(map));
    }


}
