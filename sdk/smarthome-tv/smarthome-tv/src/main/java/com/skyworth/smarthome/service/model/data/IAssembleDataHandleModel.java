package com.skyworth.smarthome.service.model.data;

import com.swaiot.aiotlib.common.entity.DiscoverNetworkDevice;
import com.swaiot.aiotlib.common.entity.DiscoverWifiDevice;

/**
 * Describe:
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/9/17
 */
public interface IAssembleDataHandleModel {

    /**
     * 组装首页信号源）状态变化推送数据
     *
     * @param status
     * @return
     */
    String assemblePushHomePageData(String status);


    /**
     * 组装配网数据信息
     * @param discoverDeviceInfo
     * @return
     */
    DiscoverNetworkDevice assembleApconfigDeviceInfo(DiscoverWifiDevice discoverDeviceInfo);

}
