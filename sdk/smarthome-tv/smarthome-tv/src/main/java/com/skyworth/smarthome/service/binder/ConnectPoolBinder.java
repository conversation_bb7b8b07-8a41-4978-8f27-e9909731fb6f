package com.skyworth.smarthome.service.binder;

import android.os.IBinder;
import android.os.RemoteException;

import com.skyworth.smarthome_tv.IBinderPool;


/**
 * Describe:Binder连接池
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/2/25
 */
public class ConnectPool<PERSON>inder extends IBinderPool.Stub {
    private SmartHomeExtraInfoBinder mSmartHomeExtraInfoBinder = null;
    private SmartHomeAodInfoBinder mSmartHomeAodInfoBinder = null;
    private SmartHomeAodPushBinder mSmartHomeAodPushBinder = null;
    public static final String BIND_SMARTHOME = "com.skyworth.aiot.ISmartHomeService";
    public static final String BIND_SMARTHOME_EXTRA_INFO = "com.skyworth.aiot.ISmartHomeExtraInfo";
    public static final String BIND_SMARTHOME_PUSH = "com.skyworth.aiot.ISmartHomePushService";
    public static final String BIND_SMARTHOME_AOD_PUSH = "com.skyworth.aiot.ISmartHomeAodPushService";
    public static final String BIND_SMARTHOME_AOD_INFO = "com.skyworth.aiot.ISmartHomeAodInfo";

    public ConnectPoolBinder() {
        mSmartHomeExtraInfoBinder = new SmartHomeExtraInfoBinder();
        mSmartHomeAodInfoBinder = new SmartHomeAodInfoBinder();
        mSmartHomeAodPushBinder = new SmartHomeAodPushBinder();
    }


    @Override
    public IBinder queryBinder(String action) throws RemoteException {
        IBinder binder = null;
        switch (action){
            case BIND_SMARTHOME_EXTRA_INFO:
                binder = mSmartHomeExtraInfoBinder;
                break;
            case BIND_SMARTHOME_AOD_INFO:
                binder = mSmartHomeAodInfoBinder;
                break;
            case BIND_SMARTHOME_AOD_PUSH:
                binder = mSmartHomeAodPushBinder;
                break;
                default:
        }
        return binder;
    }

    public SmartHomeAodPushBinder getSmartHomeAodPushBinder() {
        return mSmartHomeAodPushBinder;
    }

}
