package com.skyworth.smarthome.common.ui;

import android.content.Context;
import android.support.v7.widget.AppCompatTextView;
import android.text.TextUtils.TruncateAt;
import android.util.AttributeSet;


/**
 * <AUTHOR>
 * 
 */

public class AlwaysMarqueeTextView extends AppCompatTextView
{
    public AlwaysMarqueeTextView(Context context)
    {
        super(context);
        this.getPaint().setAntiAlias(true);
        this.setSingleLine();
        this.setEllipsize(TruncateAt.MARQUEE);
        this.setMarqueeRepeatLimit(-1);
    }

    public AlwaysMarqueeTextView(Context context, int textSize, int textColor)
    {
        this(context);
        this.setTextSize(textSize);
        this.setTextColor(textColor);
    }

    public AlwaysMarqueeTextView(Context context, AttributeSet attrs)
    {
        super(context, attrs);
        this.getPaint().setAntiAlias(true);

        this.setSingleLine();
        this.setEllipsize(TruncateAt.MARQUEE);
        this.setMarqueeRepeatLimit(-1);
    }

    public AlwaysMarqueeTextView(Context context, AttributeSet attrs, int defStyle)
    {
        super(context, attrs, defStyle);
        this.getPaint().setAntiAlias(true);

        this.setSingleLine();
        this.setEllipsize(TruncateAt.MARQUEE);
        this.setMarqueeRepeatLimit(-1);
    }

    @Override
    public void setSelected(boolean selected)
    {
        super.setSelected(selected);
    }

}
