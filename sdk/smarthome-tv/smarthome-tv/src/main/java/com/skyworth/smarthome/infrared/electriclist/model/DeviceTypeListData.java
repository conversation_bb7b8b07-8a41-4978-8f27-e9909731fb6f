package com.skyworth.smarthome.infrared.electriclist.model;

import java.io.Serializable;
import java.util.List;

/**
 * Created by fc on 2019/5/6
 * Describe:
 */
public class DeviceTypeListData implements Serializable {
    public String id;  //2
    public String name; // "空调",
    public String name_en;  //"air conditioner",
    public String icon_tv;  //"https://tv.doubimeizhi.com/images/tv/ir/0002_logo.png",
    public List<String> hot_brands_cn;  //["格力","美的","海尔","奥克斯","TCL"]
    public boolean isShowDelete;
    public String code_type;//优先支持的码库类型,取值[kk,hxd]分别代表酷控和宏芯达码库
    public String kk_type_id;//酷控码库的品类id
}

