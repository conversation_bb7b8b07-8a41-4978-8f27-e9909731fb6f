package com.skyworth.smarthome.service.model.data;

import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.swaiot.aiotlib.common.entity.FamilyBean;
import com.swaiot.aiotlib.common.entity.SceneBean;

import java.util.List;

/**
 * Describe:
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/9/17
 */
public interface IGetDataHandleModel {

    /**
     * 获取设备列表数据，超过有效期，拉取新数据
     * @return
     */
    List<DeviceInfo> getSmartDeviceList();

    /**
     * 获取家庭列表
     * @return
     */
    List<FamilyBean> getFamilyList();

    /**
     * 获取场景列表
     * @return
     */
    List<SceneBean> getSceneList();

    /**
     * 获取缓存中的数据，不加载新数据
     * @return
     */
    List<DeviceInfo>  getCacheSmartDeviceList();


    /**
     * 通过deviceID获取单个设备的信息
     * @param deviceID
     * @return
     */
    DeviceInfo getSmartDeviceInfo(String deviceID);

    /**
     * 通过sceneId获取单个场景的信息
     * @param sceneId
     * @return
     */
    SceneBean getSceneInfo(String sceneId);

    /**
     * 获取AIOT提供对外接口核心设备状态信息
     * @param familyId
     * @return
     */
    String getAiotHomeStatus(String familyId);
}
