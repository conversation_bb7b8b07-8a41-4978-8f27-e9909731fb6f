package com.skyworth.smarthome.home.smartdevice.controlpanel;

import android.content.Context;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.alibaba.fastjson.JSONObject;
import com.skyworth.smarthome.common.util.DialogCommonUtil;
import com.skyworth.smarthome.home.smartdevice.controlpanel.model.DeviceControlModel;
import com.skyworth.smarthome.home.smartdevice.controlpanel.model.IDeviceControlModel;
import com.skyworth.smarthome.home.smartdevice.controlpanel.view.ControlPanelView;
import com.skyworth.smarthome.home.smartdevice.controlpanel.view.IControlListener;
import com.skyworth.smarthome.service.push.local.DeviceDataPushUtil;
import com.skyworth.smarthome.service.push.local.IHandlerPush;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.common.base.BaseCommonDialog;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.event.CloseDeviceControlDialogEvent;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.service.model.ISmartDeviceDataModel;
import com.skyworth.smarthome.service.model.SmartHomeModel;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XToast;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;
import java.util.Map;


/**
 * @ClassName: DeviceControlDialog
 * @Author: AwenZeng
 * @CreateDate: 2020/6/16 14:49
 * @Description: 设置控制弹框
 */
public class DeviceControlDialog extends BaseCommonDialog {

    private static final String TAG = "DeviceControlDialog";
    private String device_id;
    private ControlPanelView mControlPanelView;
    private IDeviceControlModel mDeviceControlModel;
    private IHandlerPush.IPushListener iPushListener = new IHandlerPush.IPushListener() {

        @Override
        public void onArrive(AppConstants.SSE_PUSH event, String data) {
            //数据变化的回调
            switch (event) {
                case DEVICE_STATUS://设备状态变化
                    Log.i(TAG, "onArrive: ----------event:" + event + "--data:" + data);
                    JSONObject dataObject = JSONObject.parseObject(data);
                    if (dataObject != null) {//解析
                        String localControl = dataObject.getString(SmartHomeModel.LOCAL_CONTROL);
                        if (EmptyUtils.isNotEmpty(localControl)) {
                            mDeviceControlModel.handleDeviceStatusData(data);
                            mControlPanelView.show(ISmartDeviceDataModel.INSTANCE.getSmartDeviceInfo(device_id), true);
                        } else {
                            mControlPanelView.show(ISmartDeviceDataModel.INSTANCE.getSmartDeviceInfo(device_id), false);
                        }
                    }
                    break;
                default:
                    break;
            }
        }
    };


    public DeviceControlDialog(Context context) {
        super(context);
        EventBus.getDefault().register(this);
    }


    @Override
    protected void initParams() {
        mDeviceControlModel = new DeviceControlModel();
    }

    @Override
    protected void initContentView() {
        super.initContentView();
        mControlPanelView = new ControlPanelView(mContext, mDeviceControlModel);
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.gravity = Gravity.CENTER_HORIZONTAL | Gravity.CENTER_VERTICAL;
        mContentView.addView(mControlPanelView, params);
        //触摸弹框之外的部分关掉弹窗逻辑处理
        mControlPanelView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
            }
        });
        mControlPanelView.setFocusable(false);
    }


    @Override
    public void showDialog(Map<String, String> params) {
        super.showDialog(params);
        DialogCommonUtil.putDialog(DialogCommonUtil.DIALOG_KEY_DEVICE_CONTROL,this);
        DeviceDataPushUtil.getPush().regReceiver(iPushListener);
        if (EmptyUtils.isNotEmpty(params)) {
            device_id = params.get("device_id");
        }
        final DeviceInfo deviceInfo = ISmartDeviceDataModel.INSTANCE.getSmartDeviceInfo(device_id);
        mControlPanelView.setControlListener(new IControlListener() {
            @Override
            public void onControl(Map<String, String> status) {
                if (EmptyUtils.isNotEmpty(deviceInfo)) {
                    if (deviceInfo.is_virtual) {
                        mDeviceControlModel.controlVirtualDevice(status, device_id);
                    } else {
                        mDeviceControlModel.controlDevice(status, device_id);
                    }
                }
            }

            @Override
            public void showVoiceTip(List<String> tip) {
            }

            @Override
            public void onExitControlPanel() {
            }
        });
        if (EmptyUtils.isNotEmpty(deviceInfo)) {
            mControlPanelView.show(deviceInfo, true);
            mControlPanelView.requestFocusAtFirstItem();
        } else {
            XToast.showToast(SmartHomeTvLib.getContext(), "设备数据异常");
            dismiss();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void Event(CloseDeviceControlDialogEvent event) {
        dismiss();
    }


    @Override
    protected void onDismiss() {
        DeviceDataPushUtil.getPush().unRegReceiver(iPushListener);
        EventBus.getDefault().unregister(this);
        mControlPanelView.destroy();
        DialogCommonUtil.removeDialog(DialogCommonUtil.DIALOG_KEY_DEVICE_CONTROL);
        super.onDismiss();
    }
}
