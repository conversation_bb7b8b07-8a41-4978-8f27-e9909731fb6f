package com.skyworth.smarthome.home.smartdevice.controlpanel.common.itemdata;

import com.alibaba.fastjson.JSONObject;
import com.skyworth.smarthome.home.smartdevice.controlpanel.view.item.GroupDisplayItem;
import com.skyworth.smarthome.home.smartdevice.controlpanel.view.item.ToggleItem;
import com.skyworth.smarthome.home.smartdevice.controlpanel.view.item.ButtonItem;
import com.skyworth.smarthome.home.smartdevice.controlpanel.view.item.ProgressItem;
import com.skyworth.smarthome.home.smartdevice.controlpanel.view.item.SwitchItem;

public class ControlDataFactory {
    public static BaseControlData getData(String type, String json) {
        switch (type) {
            case SwitchItem.TYPE:
                return JSONObject.parseObject(json, SwitchControlData.class);
            case GroupDisplayItem.TYPE:
                return JSONObject.parseObject(json, GroupDisplayControlData.class);
            case ProgressItem.TYPE:
                return JSONObject.parseObject(json, ProgressControlData.class);
            case ButtonItem.TYPE:
                return JSONObject.parseObject(json, ButtonControlData.class);
            case ToggleItem.TYPE:
                return JSONObject.parseObject(json, ToggleControlData.class);
        }
        return null;
    }
}
