package com.skyworth.smarthome.infrared.learn.presenter;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;

import com.coocaa.app.core.app.AppCoreApplication;
import com.skyworth.smarthome.devices.apconfig.presenter.stepmanager.BaseStepManager;
import com.skyworth.smarthome.infrared.learn.presenter.steps.IRLearnStepFail;
import com.skyworth.smarthome.infrared.learn.presenter.steps.IRLearnStepFinish;
import com.skyworth.smarthome.infrared.learn.presenter.steps.IRLearnStepLearning;
import com.skyworth.smarthome.infrared.learn.presenter.steps.IRLearnStepReady;
import com.skyworth.smarthome.infrared.learn.presenter.steps.IRLearnStepStart;
import com.skyworth.smarthome.infrared.learn.presenter.steps.IRLearnStepSuccess;
import com.skyworth.smarthome.infrared.learn.view.IIRLearnView;
import com.skyworth.smarthome.common.bean.IRLearnKeyBean;
import com.skyworth.smarthome.common.util.FormatUtil;
import com.skyworth.smarthome.common.util.ThreadManager;
import com.skyworth.smarthome.infrared.learn.model.IIRLearnModel;
import com.skyworth.smarthome.service.model.FunctionGoToModel;
import com.smarthome.common.utils.EmptyUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

import static com.skyworth.smarthome.infrared.learn.IRLearnDialog.PARAM_IR_HOST_DEVICE_ID;
import static com.skyworth.smarthome.infrared.learn.IRLearnDialog.PARAM_IR_HOST_NAME;
import static com.skyworth.smarthome.infrared.learn.IRLearnDialog.PARAM_IR_HOST_TYPE_ID;
import static com.skyworth.smarthome.infrared.learn.IRLearnDialog.PARAM_IR_SLAVE_NAME;
import static com.skyworth.smarthome.infrared.learn.IRLearnDialog.PARAM_IR_SLAVE_TYPE_ID;
import static com.skyworth.smarthome.infrared.learn.IRLearnDialog.TAG;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/7/2 14:44.
 */
public class IRLearnPresenterImpl implements IIRLearnPresenter {
    private IIRLearnModel mModel = null;
    private IIRLearnView mView = null;
    private BaseStepManager<IRLearnPresenterImpl> stepManager = null;

    /**
     * 当前红外设备device id，全时AI精灵、红外电视等
     */
    private String mIrHostDeviceId = null;

    /**
     * 当前红外设备名称，AI全时精灵、红外电视等
     */
    private String mIrHostName = null;

    /**
     * 当前红外设备type id，用于区分红外电视、全时AI精灵
     */
    private String mIrHostTypeId = null;

    /**
     * 要学习的红外遥控器名称，例如空调、风扇等
     */
    private String mIrSlaveName = null;

    /**
     * 要学习的红外设备品类id
     */
    private String mIrSlaveTypeId = null;

    /**
     * 红外子设备id，新增时后台返回
     */
    private String mIrSlaveDeviceId = null;

    /**
     * 当前学习按键index
     */
    private int mCurrentLearnIndex = 0;


    /**
     * 学习中超时定时
     */
    private Timer timeOutTimer = null;

    /**
     * 学习失败次数
     */
    private int failCount = 0;

    /**
     * 是否常用按键，1表示常用 0:表示所有
     */
    private static final String IS_COMMON_KEY_LIST = "1";

    public static final String INPUT_I_AM_READY = "INPUT_I_AM_READY";
    public static final String INPUT_LEARN_SUCCESS = "INPUT_LEARN_SUCCESS";
    public static final String INPUT_LEARN_FAILED = "INPUT_LEARN_FAILED";
    public static final String INPUT_SUCCESS_END = "INPUT_SUCCESS_END";
    public static final String INPUT_KEY_BACK = "INPUT_KEY_BACK";

    @Override
    public void create(Context context, IIRLearnView view, IIRLearnModel model) {
        mModel = model;
        mModel.regLearnNotify(learnNotify);
        mView = view;
        stepManager = new BaseStepManager<>(context, this);
        stepManager.addStep(new IRLearnStepReady());
        stepManager.addStep(new IRLearnStepStart());
        stepManager.addStep(new IRLearnStepLearning());
        stepManager.addStep(new IRLearnStepSuccess());
        stepManager.addStep(new IRLearnStepFail());
        stepManager.addStep(new IRLearnStepFinish());
    }

    private static final List<String> LEARNING_STEPS = new ArrayList<>();
    private static final List<Integer> BACK_KEY = new ArrayList<>();
    private static final List<String> HANDLE_BACK_KEY_STEPS = new ArrayList<>();

    static {
        LEARNING_STEPS.add(IRLearnStepLearning.STEP_TAG);
        BACK_KEY.add(KeyEvent.KEYCODE_BACK);
        BACK_KEY.add(KeyEvent.KEYCODE_ESCAPE);
        HANDLE_BACK_KEY_STEPS.add(IRLearnStepStart.STEP_TAG);
        HANDLE_BACK_KEY_STEPS.add(IRLearnStepLearning.STEP_TAG);
    }

    private IIRLearnModel.IIRLearnNotify learnNotify = new IIRLearnModel.IIRLearnNotify() {
        @Override
        public void onLearnStart() {
            Log.i(TAG, "onLearnStart: ");
        }

        @Override
        public void onLearnSuccess(int[] keyCode) {
            Log.i(TAG, "onLearnSuccess: ");
            if (stepManager != null && isLearningStep()) {
                stepManager.input(INPUT_LEARN_SUCCESS, keyCode);
            }
        }

        @Override
        public void onLearnFail() {
            Log.i(TAG, "onLearnFail: ");
            failCount++;
            if (stepManager != null && isLearningStep()) {
                stepManager.jumpTo(IRLearnStepFail.STEP_TAG);
            }
        }
    };

    private boolean isLearningStep() {
        String step = stepManager.getCurrentStepTag();
        return !TextUtils.isEmpty(step) && LEARNING_STEPS.contains(step);
    }

    @Override
    public void setParams(Map<String, String> params) {
        if (params == null || params.size() <= 0) {
            Log.e(TAG, "setParams: params is empty!!!");
            return;
        }
        mIrHostDeviceId = params.get(PARAM_IR_HOST_DEVICE_ID);
        mIrHostName = params.get(PARAM_IR_HOST_NAME);
        mIrHostTypeId = params.get(PARAM_IR_HOST_TYPE_ID);
        mIrSlaveName = params.get(PARAM_IR_SLAVE_NAME);
        mIrSlaveTypeId = params.get(PARAM_IR_SLAVE_TYPE_ID);
        try {
            String ret = EmptyUtils.checkParams(mIrHostDeviceId, mIrHostName, mIrHostTypeId, mIrSlaveName, mIrSlaveTypeId);
            Log.i(TAG, "setParams: " + ret);
        } catch (Exception e) {
            Log.e(TAG, "setParams: " + e.getMessage());
            return;
        }
        mModel.setDeviceId(mIrHostDeviceId);
    }

    @Override
    public void start(Intent intent) {
        mModel.startListen(mIrHostTypeId);
        stepManager.start();
        failCount = 0;
        logLearnStart();
    }

    private void logLearnStart() {
//        LoggerImpl.Companion.onEvent(EVENT_INFRARED_MANUAL_LEARNING_START);
    }

    @Override
    public void iAmReady() {
        stepManager.input(INPUT_I_AM_READY);
    }

    @Override
    public void retry() {
        stepManager.jumpTo(IRLearnStepStart.STEP_TAG);
    }

    @Override
    public void next() {
        if (isLearnFinish()) {
            Log.i(TAG, "next: finish");
            stepManager.jumpTo(IRLearnStepFinish.STEP_TAG);
        }
        mCurrentLearnIndex++;
        Log.i(TAG, "next: " + mCurrentLearnIndex);
        stepManager.jumpTo(IRLearnStepStart.STEP_TAG);
    }

    @Override
    public void exit() {
        ThreadManager.getInstance().uiThread(new Runnable() {
            @Override
            public void run() {
                FunctionGoToModel.INSTANCE.goToInfraredDeviceList("click",mIrHostDeviceId);
                if(mView!=null){
                    mView.dismiss();
                }
            }
        });
    }

    @Override
    public boolean loadLearnList() {
        Log.i(TAG, "loadLearnList");
        return mModel.loadRemoteLearnList(mIrSlaveTypeId, IS_COMMON_KEY_LIST);
    }

    @Override
    public boolean createIRChildDevice() {
        Log.i(TAG, "createIRChildDevice");
        try {
            mIrSlaveDeviceId = mModel.createIRChildDevice(mIrHostDeviceId, Integer.valueOf(mIrSlaveTypeId), mIrSlaveName);
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        Log.i(TAG, "createIRChildDevice: id: " + mIrSlaveDeviceId);
        return !TextUtils.isEmpty(mIrSlaveDeviceId);
    }

    @Override
    public void showReady() {
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mView.showReady(mIrSlaveName);
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void showLearnStart() {
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                IRLearnKeyBean keyBean = getIrLearnKeyBean();
                if (keyBean != null) {
                    Log.i(TAG, "Start learn key: " + keyBean.name);
                    mView.showLearnStart(mCurrentLearnIndex + 1, mModel.getSizeOfLearnList(), mIrSlaveName, keyBean.name, mIrHostName);
                }
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void showLearning() {
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                IRLearnKeyBean keyBean = getIrLearnKeyBean();
                if (keyBean != null) {
                    mView.showLearning(mCurrentLearnIndex + 1, mModel.getSizeOfLearnList(), mIrSlaveName, keyBean.name, mIrHostName);
                }
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public boolean startLearnStatus() {
        IRLearnKeyBean keyBean = getIrLearnKeyBean();
        return keyBean != null && mModel.startLearnStatus(mIrHostDeviceId, String.valueOf(keyBean.id));
    }

    @Override
    public void startTimeOutCheck() {
        timeOutTimer = new Timer();
        Log.i(TAG, "startTimeOutCheck: startTimer");
        timeOutTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                Log.i(TAG, "startTimeOutCheck: timeout!!!");
                stepManager.jumpTo(IRLearnStepFail.STEP_TAG);
            }
        }, 30000);
    }

    @Override
    public void stopTimeOutTimer() {
        if (timeOutTimer != null) {
            Log.i(TAG, "stopTimeOutTimer");
            timeOutTimer.cancel();
        }
    }

    @Override
    public void showLearnFail() {
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mView.showLearnFail();
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void showLearnFinish() {
        Log.i(TAG, "showLearnFinish: " + mIrSlaveDeviceId);
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mView.showLearnFinish(mIrSlaveDeviceId);
                return Unit.INSTANCE;
            }
        });
        logLearnResult();
    }

    @Override
    public boolean isLearnFinish() {
        return mCurrentLearnIndex >= mModel.getSizeOfLearnList() - 1;
    }

    private static final int COUNTDOWN_SECONDS = 3;
    private int currentSecond = COUNTDOWN_SECONDS;

    @Override
    public void showLearnSuccess() {
        final IRLearnKeyBean keyBean = getIrLearnKeyBean();
        if (keyBean == null) {
            return;
        }
        Log.i(TAG, "showLearnSuccess: " + keyBean.name);
        currentSecond = COUNTDOWN_SECONDS;
        mView.getView().post(new Runnable() {
            @Override
            public void run() {
                if (currentSecond > 0) {
                    mView.showLearnSuccess(keyBean.name, currentSecond);
                    currentSecond--;
                } else {
                    currentSecond = COUNTDOWN_SECONDS;
                    stepManager.input(INPUT_SUCCESS_END);
                    return;
                }
                mView.getView().postDelayed(this, 1000);
            }
        });
    }

    private void logLearnResult() {
//        try {
//            Map<String, String> map = new HashMap<>();
//            map.put("learning_success", failCount <= 0 ? "全部按键学习成功" : "部分按键学习成功");
//            String origin;
//            if (DeviceUtil.isIRAI(String.valueOf(mIrHostTypeId))) {
//                origin = DataConstants.KEY_ENTER_INFRARED_ORIGIN_IRAI;
//            } else {
//                origin = DataConstants.KEY_ENTER_INFRARED_ORIGIN_IRTV;
//            }
//            map.put("origin", origin);
//            LoggerImpl.Companion.onEvent(EVENT_INFRARED_MANUAL_LEARNING_RESULT, map);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
    }

    @Override
    public boolean saveLearntKey(int[] code) {
        if (TextUtils.isEmpty(mIrSlaveDeviceId)) {
            if (!createIRChildDevice()) {
                return false;
            }
        }
        IRLearnKeyBean keyBean = getIrLearnKeyBean();
        if (keyBean == null) {
            return false;
        }
        String hexCode = toHexString(code);
        Log.i(TAG, "saveLearntKey: " + hexCode);
        return mModel.saveLearntKey(keyBean.name, keyBean.id, mIrSlaveDeviceId, hexCode);
    }

    private String toHexString(int[] code) {
        byte[] bytes = new byte[code.length];
        for (int i = 0; i < code.length; i++) {
            try {
                bytes[i] = (byte) code[i];
            } catch (Exception e) {
                Log.e(TAG, "fail convert int to hex string: code: " + code[i]);
                e.printStackTrace();
            }
        }
        return FormatUtil.bytesToHexString(bytes, false);
    }

    private IRLearnKeyBean getIrLearnKeyBean() {
        return mModel.getKeyNeedLearn(mCurrentLearnIndex);
    }

    @Override
    public void showLoading() {
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mView.showLoading();
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void hideLoading() {
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mView.hideLoading();
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void showError(final String msg) {
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mView.showError(msg);
                return Unit.INSTANCE;
            }
        });

    }


    @Override
    public boolean handleKeyEvent(KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN && BACK_KEY.contains(event.getKeyCode())) {
            if (HANDLE_BACK_KEY_STEPS.contains(stepManager.getCurrentStepTag())) {
                stepManager.input(INPUT_KEY_BACK);
                stepManager.jumpTo(IRLearnStepFail.STEP_TAG);
                return true;
            }
        }
        return false;
    }

    @Override
    public void destroy() {
        mCurrentLearnIndex = 0;
        mIrHostName = null;
        mIrHostDeviceId = null;
        mIrHostTypeId = null;
        mIrSlaveTypeId = null;
        mIrSlaveName = null;
        failCount = 0;
    }
}
