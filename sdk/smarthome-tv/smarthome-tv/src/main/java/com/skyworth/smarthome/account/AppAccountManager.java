package com.skyworth.smarthome.account;

import android.content.Context;
import android.content.IntentFilter;

import com.skyworth.smarthome.common.event.AccountChangeEvent;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.model.SPCacheData;
import com.skyworth.smarthome.common.model.UserInfo;
import com.skyworth.smarthome.voicehandle.SmartHomeAI;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.smarthome.common.account.AccountInfo;
import com.smarthome.common.account.AccountManager;
import com.smarthome.common.utils.EmptyUtils;

import org.greenrobot.eventbus.EventBus;

/**
 * 账号管理类
 * Created by <PERSON><PERSON><PERSON><PERSON> on 19-9-17.
 */

public class AppAccountManager implements IAppAccountManager {
    private Context mContext;
    private AccountReceiver mAccountReceiver;

    public AppAccountManager() {
        mContext = SmartHomeTvLib.getContext();
        mAccountReceiver = new AccountReceiver();
    }

    @Override
    public void registerAccountReceiver() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(AccountReceiver.ACCOUNT_CHANGED);
        mContext.registerReceiver(mAccountReceiver, intentFilter);
    }

    @Override
    public void unRegisterAccountReceiver(){
        try {
            mContext.unregisterReceiver(mAccountReceiver);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean hasLogin() {
        return AccountManager.getManager(SmartHomeTvLib.getContext()).hasLogin();
    }

    @Override
    public boolean hasLogin(boolean needGotoLogin) {
        boolean hasLogin = hasLogin();
        if (needGotoLogin) {
            if (hasLogin) {
                if (isBindMobile()) {
                    return true;
                } else {
                    gotoBindMobile();
                }
            } else {
                gotoLogin();
            }
        } else {
            return hasLogin;
        }
        return false;
    }


    @Override
    public boolean checkLogin(boolean isRemindVoice) {
        if (hasLogin()) {
            if (isBindMobile()) {
                return true;
            }else{
                if (isRemindVoice) {
                    SmartHomeAI.playVoiceTTS(SmartHomeTvLib.getContext(), SmartHomeTvLib.getContext().getString(R.string.voice_speech_not_bind_phone));
                }
            }
        }else{
            if (isRemindVoice) {
                SmartHomeAI.playVoiceTTS(SmartHomeTvLib.getContext(), SmartHomeTvLib.getContext().getString(R.string.voice_speech_not_login));
            }
        }
        return false;
    }

    @Override
    public boolean isBindMobile() {
        return AccountManager.getManager(mContext).isBindMobile();
    }

    @Override
    public void gotoBindMobile() {
        AccountManager.getManager(mContext).gotoBindMobile();
    }

    @Override
    public void gotoLogin() {
        AccountManager.getManager(mContext).gotoLogin();
    }

    @Override
    public void logout() {
        AccountManager.getManager(mContext).logout();
    }


    @Override
    public AccountInfo getAccountInfo(boolean isNeedPush) {
        AccountInfo info = AccountManager.getManager(SmartHomeTvLib.getContext()).getAccountInfo();
        if (EmptyUtils.isEmpty(info)) {
            info = new AccountInfo();
            UserInfo.getInstance().clear();
            clearAppData();
            if(isNeedPush){
                EventBus.getDefault().post(new AccountChangeEvent());
            }
            return info;
        }
        UserInfo.getInstance().setUserID(info.user_id);
        UserInfo.getInstance().setToken(info.token);
        UserInfo.getInstance().setAvatar(info.avatar);
        UserInfo.getInstance().setNick_name(info.nick_name);
        UserInfo.getInstance().setMobilePhone(info.mobile);
        if (!isBindMobile()) {
            clearAppData();
        }
        if(isNeedPush){
            EventBus.getDefault().post(new AccountChangeEvent());
        }
        return info;
    }

    private void clearAppData() {
        AppData.getInstance().setDeviceInfoList(null);
        AppData.getInstance().setCurrentFamily(null);
        AppData.getInstance().setFamilyList(null);
        AppData.getInstance().setSceneList(null);
        SPCacheData.setFamilyId(SmartHomeTvLib.getContext(), "");
    }

    @Override
    public String getOpenID() {
        return AccountManager.getManager(mContext).getOpenId();
    }

    @Override
    public String getSession() {
        return AccountManager.getManager(mContext).getSession();
    }


}
