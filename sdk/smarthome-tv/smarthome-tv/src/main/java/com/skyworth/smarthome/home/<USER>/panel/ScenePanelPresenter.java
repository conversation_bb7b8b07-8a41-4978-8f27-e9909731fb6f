package com.skyworth.smarthome.home.custom.panel;

import android.content.Context;
import android.view.View;

import com.coocaa.app.core.utils.FuncKt;
import com.coocaa.operate6_0.model.Container;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.home.custom.BaseCustomPresenter;
import com.skyworth.smarthome.home.smartdevice.scene.SceneListView;
import com.skyworth.smarthome.service.model.ISmartDeviceDataModel;
import com.skyworth.smarthome.service.push.local.DeviceDataPushUtil;
import com.skyworth.smarthome.service.push.local.IHandlerPush;
import com.smarthome.common.utils.CCLog;
import com.swaiot.aiotlib.common.entity.SceneBean;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * @Description: 场景自定义Panel
 * @Author: wzh
 * @CreateDate: 2020/6/17
 */
public class ScenePanelPresenter extends BaseCustomPresenter {

    public enum Mode {
        ALL, MANUAL, AUTO
    }

    private Mode mode;

    private SceneListView mSceneListView;

    private IHandlerPush.IPushListener iPushListener = new IHandlerPush.IPushListener() {

        @Override
        public void onArrive(AppConstants.SSE_PUSH event, String data) {
            //数据变化的回调
            if (event == AppConstants.SSE_PUSH.SCENE_LIST) {//场景列表变化
                CCLog.i("ScenePanelPresenter", "onArrive: ----------event:" + event + "--data:" + data);
                FuncKt.runOnUiThread(new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        String currentFamilyId = AppData.getInstance().getCurrentFamilyId();
                        List<SceneBean> all = ISmartDeviceDataModel.INSTANCE.getSceneList();
                        List<SceneBean> list = new ArrayList<>();
                        for (int i = 0; all != null && i < all.size(); ++i) {
                            SceneBean item = all.get(i);
                            if (item != null &&
                                    (currentFamilyId != null && currentFamilyId.equals(item.family_id)) &&
                                    (mode == Mode.ALL ||
                                            (mode == Mode.AUTO && item.is_auto) ||
                                            (mode == Mode.MANUAL && !item.is_auto))) {

                                list.add(item);
                            }
                        }
                        mSceneListView.refreshUI(list);
                        return Unit.INSTANCE;
                    }
                });
            }
        }
    };

    public ScenePanelPresenter(Context context) {
        this(context, Mode.ALL);
    }

    public ScenePanelPresenter(Context context, Mode mode) {
        super(context);
        this.mode = mode;
        mSceneListView = new SceneListView(context,
                getCurrentModeTitle(),
                getCurrentModeIconResId(),
                getMaxShowActonCount());
        mSceneListView.setBoundaryCallback(this);
        DeviceDataPushUtil.getPush().regReceiver(iPushListener);

        // For [add scene] card appear
        mSceneListView.refreshUI(Collections.emptyList());
    }

    @Override
    public View getView() {
        return mSceneListView;
    }

    @Override
    public void setContainer(Container container) {
        super.setContainer(container);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mSceneListView.destroy();
        DeviceDataPushUtil.getPush().unRegReceiver(iPushListener);
    }

    private String getCurrentModeTitle() {
        switch (this.mode) {
            case AUTO:
                return "自动执行";
            case MANUAL:
                return "手动执行";
            default:
            case ALL:
                return "全部场景";
        }
    }

    private int getCurrentModeIconResId() {
        switch (this.mode) {
            case AUTO:
                return R.drawable.automatic;
            case MANUAL:
                return R.drawable.manual;
            default:
            case ALL:
                return R.drawable.automatic;
        }
    }

    private int getMaxShowActonCount() {
        return this.mode == Mode.MANUAL ? 5 : 3;
    }
}
