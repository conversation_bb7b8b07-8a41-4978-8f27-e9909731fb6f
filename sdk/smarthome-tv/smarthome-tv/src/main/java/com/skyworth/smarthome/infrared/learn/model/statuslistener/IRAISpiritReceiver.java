package com.skyworth.smarthome.infrared.learn.model.statuslistener;

import android.text.TextUtils;
import android.util.Log;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.infrared.learn.model.IIRLearnModel;
import com.skyworth.smarthome.service.push.local.DeviceDataPushUtil;
import com.skyworth.smarthome.service.push.local.IHandlerPush;

import java.util.Map;

import static com.skyworth.smarthome.infrared.learn.IRLearnDialog.TAG;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/7/5 16:51.
 */
public class IRAISpiritReceiver implements ILearnStatusReceiver {

    private IIRLearnModel model = null;
    private static final String STD_R = "STD_R";
    private static final String DEVICE_ID_KEY = "device_id";
    private static final String DEVICE_STATUS_KEY = "status";
    private String mDeviceId = null;

    private IHandlerPush.IPushListener deviceStatusListener = new IHandlerPush.IPushListener() {
        @Override
        public void onArrive(AppConstants.SSE_PUSH event, String data) {
            Log.i(TAG, "onArrive: IRAISpiritReceiver");
            if (!AppConstants.SSE_PUSH.DEVICE_STATUS.equals(event)) {
                return;
            }
            JSONObject msgJson = JSON.parseObject(data);
            String deviceId = getIdFromMsg(msgJson);
            if (mDeviceId == null || !mDeviceId.equals(deviceId)) {
                return;
            }
            String status = getStatusFromMsg(msgJson);
            if (TextUtils.isEmpty(status)) {
                return;
            }
            JSONObject statusJson = JSONObject.parseObject(status);
            if (statusJson == null || !statusJson.containsKey(STD_R)) {
                return;
            }
            if (model != null) {
                model.checkLearnResult(statusJson.get(STD_R).toString());
            }
        }
    };

    private String getIdFromMsg(JSONObject json) {
        if (json == null) return "";
        Object obj = json.get(DEVICE_ID_KEY);
        return obj == null ? "" : obj.toString();
    }

    private String getStatusFromMsg(JSONObject json) {
        if (json == null) return "";
        Object obj = json.get(DEVICE_STATUS_KEY);
        return obj == null ? "" : obj.toString();
    }

    @Override
    public void startListen(IIRLearnModel model, Map<String, Object> params) {
        this.model = model;
        mDeviceId = params.get(DEVICE_ID_KEY).toString();
        DeviceDataPushUtil.getPush().regReceiver(deviceStatusListener);
    }

    @Override
    public void stopListen() {
        DeviceDataPushUtil.getPush().unRegReceiver(deviceStatusListener);
    }
}
