package com.skyworth.smarthome.home;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/7/7
 */
public class HomeUtil {
    /**
     * 外部点击设备跳转APP主页携带的设备ID(根据此ID打开控制面板/打开添加设备)
     */
    private static String openCtrlPanelDeviceId = "";
    /**
     * 当前显示的tab页面下标
     */
    private static int currentPageIndex = 0;
    /**
     * 智能设备的tab下标
     */
    private static int smartDeviceTabIndex = 0;

    public static void setOpenCtrlPanelDeviceId(String deviceId) {
        openCtrlPanelDeviceId = deviceId;
    }

    public static String getOpenCtrlPanelDeviceId() {
        return openCtrlPanelDeviceId;
    }

    public static void setCurrentPageIndex(int index) {
        currentPageIndex = index;
    }

    public static int getCurrentPageIndex() {
        return currentPageIndex;
    }

    public static void setSmartDeviceTabIndex(int smartDeviceTabIndex) {
        HomeUtil.smartDeviceTabIndex = smartDeviceTabIndex;
    }

    public static int getSmartDeviceTabIndex() {
        return smartDeviceTabIndex;
    }
}
