package com.skyworth.smarthome.home;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.RelativeLayout;

import com.skyworth.smarthome.common.base.BaseActivity;
import com.skyworth.smarthome.common.util.ViewsBuilder;
import com.skyworth.smarthome.service.model.IFunctionGoToModel;
import com.skyworth.smarthome.R;
import com.smarthome.common.dataer.DataHelpInfo;
import com.smarthome.common.dataer.LogSDK;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/8/11
 */
public class DeviceGuideAcitvity extends BaseActivity {

    private RelativeLayout mScheme2Layout, mScheme3Layout;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(ViewsBuilder.getGuideAddDevices(this));
        mScheme2Layout = findViewById(R.id.guide_add_devices_sheme_2);
        mScheme3Layout = findViewById(R.id.guide_add_devices_sheme_3);

        mScheme2Layout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
                IFunctionGoToModel.INSTANCE.goToAddScanSmartDevice("手动");
                DataHelpInfo.getInstance().setDiscoverDeviceOrigin("手动");
                LogSDK.submit(LogSDK.EVENT_ID_ADD_DEVICE, "source", "智能设备tab点击");
            }
        });

        mScheme3Layout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startActivity(new Intent("com.smarthome.action.THRID_ACCOUNT"));
            }
        });
    }
}
