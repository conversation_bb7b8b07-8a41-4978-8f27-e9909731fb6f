package com.skyworth.smarthome.common.bean;

import java.io.Serializable;

/**
 * Description: 第三方账号列表数据<br>
 * Created by wzh on 2019/2/20 14:53.
 */
public class ThridAccountHttpBean implements Serializable {
    public String account_type;//第三方账号类型, midea表示美的账号
    public String account_title;//第三方账号名称
    public String icon;//第三方账号图标
    public String bind_status;//绑定状态, 0:未绑定 1:已绑定
    public String auth_url;//账号授权url，当bind_status=0时才有效
    public String account_intro;//"京东旗下智慧家居平台"
    public String account_desc;//"自动同步“小京鱼”中已添加的设备，并且支持语音控制"
    public String qrcode_tips;//"请使用“小京鱼”app扫码"
    public String corp_name;//企业名称

}
