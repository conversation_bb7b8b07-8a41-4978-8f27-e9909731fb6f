package com.skyworth.smarthome.home.smartdevice.controlpanel.common;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;

public class CommonValue implements Serializable {
    public LinkedHashMap<String, String> icon_url;//不同状态对应的icon
    public String text;
    public String data_field;
    public String default_title;//默认的值(根据data_field取不到的时候显示)
    public String default_value;//属性值找不到显示default_value
    public String format;
    public boolean transfer;//是否转换数值
    public String unit;//单位  °C、min
    public LinkedHashMap<String, String> values;//显示值与数据映射
    public List<DependList.Depend> depend;
}
