package com.skyworth.smarthome.service.binder;

import android.os.RemoteException;

import com.alibaba.fastjson.JSONObject;
import com.coocaa.app.core.app.AppCoreApplication;
import com.skyworth.smarthome.service.model.IAIOTModel;
import com.skyworth.smarthome.service.model.ISmartHomeModel;
import com.skyworth.smarthome.common.bean.DeviceListHttpBean;
import com.skyworth.smarthome.service.model.IFunctionGoToModel;
import com.skyworth.smarthome_tv.ISmartHomeAodInfo;
import com.smarthome.common.utils.EmptyUtils;

import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * Describe:智慧家庭额外信息Binder
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/1/30
 */
public class SmartHomeAodInfoBinder extends ISmartHomeAodInfo.Stub {


    @Override
    public String getDeviceList() throws RemoteException {
        DeviceListHttpBean deviceListHttpBean = IAIOTModel.INSTANCE.transformDeviceList();
        if(EmptyUtils.isNotEmpty(deviceListHttpBean)){
            return JSONObject.toJSONString(deviceListHttpBean);
        }
        return "";
    }


    @Override
    public void controlDevice(String status, final String deviceID) throws RemoteException {
        try{
            if(EmptyUtils.isNotEmpty(status)){
               final Map map = (Map) JSONObject.parse(status);
                AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        ISmartHomeModel.INSTANCE.controlDevice(deviceID, map);
                        return Unit.INSTANCE;
                    }
                });
            }
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    @Override
    public void goToSmartHome(String type, String param) throws RemoteException {
        switch (type) {
            case "infraredDevice":
                IFunctionGoToModel.INSTANCE.goToInfraredDeviceList("aod",null);
                break;
            case "addDevice":
                IFunctionGoToModel.INSTANCE.goToAddScanSmartDevice("aod");
                break;
                default:
        }
    }
}
