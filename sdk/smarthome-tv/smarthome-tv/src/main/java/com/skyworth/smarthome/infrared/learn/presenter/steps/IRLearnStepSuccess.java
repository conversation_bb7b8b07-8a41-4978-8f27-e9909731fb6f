package com.skyworth.smarthome.infrared.learn.presenter.steps;

import static com.skyworth.smarthome.infrared.learn.presenter.IRLearnPresenterImpl.INPUT_SUCCESS_END;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/7/4 10:21.
 */
public class IRLearnStepSuccess extends BaseIRLearnStep {
    public static final String STEP_TAG = "success";

    @Override
    public void create() {
        super.create();
    }

    @Override
    public void run() {
        super.run();
        presenter.stopTimeOutTimer();
        presenter.showLearnSuccess();
    }

    @Override
    public boolean input(String msg, Object... params) {
        if (INPUT_SUCCESS_END.equals(msg)) {
            presenter.next();
            return true;
        }
        return false;
    }

    @Override
    public String getTag() {
        return STEP_TAG;
    }

    @Override
    public void destroy() {
        super.destroy();
    }
}
