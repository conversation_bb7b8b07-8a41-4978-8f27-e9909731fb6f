package com.skyworth.smarthome.devices.discover.model;


import com.alibaba.fastjson.JSONObject;
import com.coocaa.app.core.app.AppCoreApplication;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.account.IAppAccountManager;
import com.skyworth.smarthome.common.event.DiscoverDeviceEvent;
import com.skyworth.smarthome.common.event.RefreshScanWifiDeviceEvent;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.util.DataCacheUtil;
import com.skyworth.smarthome.common.util.DialogLauncherUtil;
import com.skyworth.smarthome.common.util.LogUtil;
import com.skyworth.smarthome.common.util.NetworkUtils;
import com.skyworth.smarthome.common.util.SystemProperty;
import com.skyworth.smarthome.service.model.ISmartHomeModel;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XToast;
import com.swaiot.aiotlib.common.entity.DiscoverNetworkDevice;
import com.swaiot.aiotlib.common.entity.DiscoverWifiDevice;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * Describe:发现设备Model
 * Created by AwenZeng on 2019/1/3
 */
public class DiscoverDeviceModel {
    /**
     * 发送智能设备信息给智慧家庭（扫描接收）
     *
     * @param deviceInfos
     */
    public void sendSmartDeviceInfo(List<DiscoverWifiDevice> deviceInfos) {
        AppData.getInstance().setDiscoverUnConfigNetDeviceList(deviceInfos);
        if(AppData.getInstance().isStartScanDevice()){
            if (!SystemProperty.isAodDevice()&&EmptyUtils.isNotEmpty(deviceInfos)) {
                EventBus.getDefault().post(new RefreshScanWifiDeviceEvent());
                DialogLauncherUtil.showDiscoverNearDevices(null);
            }
        }else{
            List<DiscoverWifiDevice> connectDevices = new ArrayList<>();
            for (DiscoverWifiDevice item : deviceInfos) {
                String BSSID = item.getWifiInfo().BSSID;
                LogUtil.androidLog("发现智能设备，可以配网的设备："+" BSSID:" + BSSID + "-缓存值："+DataCacheUtil.getInstance().getInt(BSSID));
                if (!SystemProperty.isStoreMode()&&DataCacheUtil.getInstance().getInt(BSSID) < 1&&EmptyUtils.isNotEmpty(item.getDeviceDetail())){
                    connectDevices.add(item);
                    DiscoverDeviceEvent event = new DiscoverDeviceEvent();
                    event.setTypeString(JSONObject.toJSONString(connectDevices));
                    EventBus.getDefault().post(event);
                    break;
                }
            }
        }
    }


    /**
     * 检测附近设备列表的绑定状态
     *
     * @param list
     */
    public void checkDevicesBindStatus(final List<DiscoverNetworkDevice> list) {
        if(!AppData.getInstance().isStartScanDevice()){
            return;
        }
        if(!NetworkUtils.isWifiAvailable(SmartHomeTvLib.getContext())){
            XToast.showToast(SmartHomeTvLib.getContext(),"网络连接异常，请连接网络");
        }
        if (IAppAccountManager.INSTANCE.hasLogin(false)) {
            LogUtil.androidLog("查看设备绑定状态");
            AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
                @Override
                public Unit invoke() {
                    ISmartHomeModel.INSTANCE.getDeviceBindStatus(list);
                    return Unit.INSTANCE;
                }
            });
        }
    }

    /**
     * 已经检测到的设备列表中是否有此设备
     *
     * @param device_id
     * @return
     */
    private boolean isHaveDevice(String device_id,List<DiscoverNetworkDevice> networkDeviceList) {
        for (DiscoverNetworkDevice item : networkDeviceList) {
            if (item.device_id.equals(device_id)) {
                return true;
            }
        }
        return false;
    }



    public void handleApConfigExit(String data){
        String BSSID = data;
        if (EmptyUtils.isNotEmpty(BSSID)) {
            if (DataCacheUtil.getInstance().getInt(BSSID) < 1) {
                DataCacheUtil.getInstance().putInt(BSSID, 1);
            } else {
                DataCacheUtil.getInstance().putInt(BSSID, 2);
            }
        }
    }
}

