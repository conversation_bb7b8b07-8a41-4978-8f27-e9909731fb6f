package com.skyworth.smarthome.infrared.matchkey.view;

import android.content.Context;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.widget.FrameLayout;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.devices.apconfig.view.SmartRateMessageView;
import com.skyworth.smarthome.infrared.matchkey.presenter.IMatchKeyPresenter;
import com.skyworth.util.Util;

import static com.skyworth.smarthome.devices.apconfig.view.SmartRateMessageView.TYPE_INFRARED;
import static com.skyworth.smarthome.infrared.matchkey.presenter.IMatchKeyPresenterImpl.TAG;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/4/29 18:36.
 */
public class IMatchKeyViewImpl implements IMatchKeyView {
    private FrameLayout mLayout = null;
    private Context mContext = null;
    private IMatchKeyPresenter mPresenter = null;

    private MatchReadyView matchReadyView = null;
    private SendIRView sendIRView = null;
    private MatchConfirmView matchConfirmView = null;
    private MatchErrorView matchErrorView = null;
    private SmartRateMessageView smartRateMessageView = null;
    private View.OnClickListener onClickListener = null;

    private final int MARGIN = Util.Div(40);

    @Override
    public void create(Context context, final IMatchKeyPresenter presenter) {
        mContext = context;
        mPresenter = presenter;
        mLayout = new FrameLayout(context);
        onClickListener = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Object tag = v.getTag();
                if (tag == null) {
                    return;
                }
                Log.i(TAG, "onClick: " + tag);
                mPresenter.handleClick(tag.toString());
            }
        };
    }

    @Override
    public void showReadyView(String titleText, String tipText, String buttonText) {
        if (matchReadyView == null) {
            matchReadyView = new MatchReadyView(mContext, onClickListener);
        }
        mLayout.removeAllViews();
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Util.Div(714), Util.Div(400));
        layoutParams.gravity = Gravity.CENTER;
        layoutParams.leftMargin = MARGIN;
        layoutParams.bottomMargin = MARGIN;
        mLayout.addView(matchReadyView, layoutParams);
        matchReadyView.show(titleText, tipText, buttonText);
    }

    @Override
    public void showSendingView() {
        if (sendIRView == null) {
            sendIRView = new SendIRView(mContext, onClickListener);
        }
        mLayout.removeAllViews();
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Util.Div(714), Util.Div(400));
        layoutParams.gravity = Gravity.CENTER;
        layoutParams.leftMargin = MARGIN;
        layoutParams.bottomMargin = MARGIN;
        mLayout.addView(sendIRView, layoutParams);
        sendIRView.startSending();
    }

    @Override
    public void showConfirmView(int step, int totalStep, String tipText, String tipTextLine2) {
        if (matchConfirmView == null) {
            matchConfirmView = new MatchConfirmView(mContext, onClickListener);
        }
        mLayout.removeAllViews();
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Util.Div(714), Util.Div(400));
        layoutParams.gravity = Gravity.CENTER;
        layoutParams.leftMargin = MARGIN;
        layoutParams.bottomMargin = MARGIN;
        mLayout.addView(matchConfirmView, layoutParams);
        matchConfirmView.show(step, totalStep, tipText, tipTextLine2);
    }

    @Override
    public void showMatchErrorView() {
        if (matchErrorView == null) {
            matchErrorView = new MatchErrorView(mContext, onClickListener);
        }
        mLayout.removeAllViews();
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Util.Div(714), Util.Div(400));
        layoutParams.gravity = Gravity.CENTER;
        layoutParams.leftMargin = MARGIN;
        layoutParams.bottomMargin = MARGIN;
        mLayout.addView(matchErrorView, layoutParams);
        matchErrorView.show();
    }

    @Override
    public void showMatchSuccessView(String deviceId) {
        if (smartRateMessageView == null) {
            smartRateMessageView = new SmartRateMessageView(mContext, TYPE_INFRARED);
        }
        mLayout.removeAllViews();
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(Util.Div(480), Util.Div(160));
        params.topMargin = Util.Div(50);
        params.rightMargin = Util.Div(50);
        params.gravity = Gravity.RIGHT;
        mLayout.addView(smartRateMessageView, params);
        smartRateMessageView.setDeviceId(deviceId);
        smartRateMessageView.setTitle(mContext.getResources().getString(R.string.congratulate_add_success));
        smartRateMessageView.updateUI();
        smartRateMessageView.setVisibility(View.VISIBLE);
    }

    @Override
    public View getView() {
        return mLayout;
    }

    @Override
    public void destroy() {

    }
}
