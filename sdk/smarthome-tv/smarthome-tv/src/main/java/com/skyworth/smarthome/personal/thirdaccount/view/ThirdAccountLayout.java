package com.skyworth.smarthome.personal.thirdaccount.view;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.support.v7.widget.NewRecycleAdapter;
import android.support.v7.widget.OrientationHelper;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.account.IAppAccountManager;
import com.skyworth.smarthome.common.bean.ThridAccountHttpBean;
import com.skyworth.smarthome.common.util.ThreadManager;
import com.skyworth.smarthome.personal.thirdaccount.dialog.BindThirdAccountDialogFactory;
import com.skyworth.smarthome.personal.thirdaccount.dialog.IBindThirdAccountDialog;
import com.skyworth.smarthome.personal.thirdaccount.presenter.IThirdAccountPresenter;
import com.skyworth.ui.api.SkyDialogView;
import com.skyworth.ui.api.widget.SimpleFocusDrawable;
import com.skyworth.ui.newrecycleview.NewRecycleAdapterItem;
import com.skyworth.ui.newrecycleview.NewRecycleLayout;
import com.skyworth.ui.newrecycleview.OnItemClickListener;
import com.skyworth.ui.newrecycleview.OnItemFocusChangeListener;
import com.skyworth.util.Util;
import com.smarthome.common.utils.XToast;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/9
 */
public class ThirdAccountLayout extends FrameLayout implements IThirdAccountView, OnItemFocusChangeListener, OnItemClickListener {

    private IThirdAccountPresenter mPresenter;
    private TextView mTitle, mSubTitle;
    private LinearLayout mNoDataView;
    private TextView mNoDataText;
    private Button mRefreshBtn;
    private NewRecycleLayout<ThridAccountHttpBean> mRecycleLayout;
    private NewRecycleAdapter<ThridAccountHttpBean> mAdapter;
    private IBindThirdAccountDialog mBindThridAccDialog;
    private Dialog mUnBindDialog = null;
    private List<ThridAccountHttpBean> mDataList = new ArrayList<>();

    public ThirdAccountLayout(Context context) {
        super(context);

        mTitle = new TextView(getContext());
        mTitle.setTextColor(getResources().getColor(R.color.white));
        mTitle.setTextSize(Util.Dpi(56));
        mTitle.setText("关联账号");
        mTitle.getPaint().setFakeBoldText(true);
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.topMargin = Util.Div(70);
        params.leftMargin = Util.Div(80);
        addView(mTitle, params);

        mSubTitle = new TextView(getContext());
        mSubTitle.setTextColor(getResources().getColor(R.color.white_60));
        mSubTitle.setTextSize(Util.Dpi(28));
        mSubTitle.setText("可控制第三方平台绑定的设备");
        params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.topMargin = Util.Div(94);
        params.leftMargin = Util.Div(324);
        addView(mSubTitle, params);

        mRecycleLayout = new NewRecycleLayout<>(context);
        mRecycleLayout.setOrientation(OrientationHelper.VERTICAL);
        mRecycleLayout.setSpanCount(1);
        mRecycleLayout.setmItemFocusChangeListener(this);
//        mRecycleLayout.setmBoundaryListener(this);
        mRecycleLayout.setmItemClickListener(this);
        mRecycleLayout.setClipChildren(false);
        mRecycleLayout.setClipToPadding(false);
//        mRecycleLayout.setItemSpace(0, Util.Div(5));
        params = new LayoutParams(Util.Div(1000 + 16), ViewGroup.LayoutParams.WRAP_CONTENT);
        params.topMargin = Util.Div(300);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        addView(mRecycleLayout, params);
    }

    private void addNoDataView() {
        mNoDataView = new LinearLayout(getContext());
        mNoDataView.setOrientation(LinearLayout.VERTICAL);
        mNoDataView.setGravity(Gravity.CENTER);
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        addView(mNoDataView, params);
        mNoDataText = new TextView(getContext());
        mNoDataText.setTextColor(getResources().getColor(R.color.white));
        mNoDataText.setTextSize(Util.Dpi(42));
        LinearLayout.LayoutParams ll = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        ll.gravity = Gravity.CENTER;
        mNoDataView.addView(mNoDataText, ll);
        mRefreshBtn = new Button(getContext());
        mRefreshBtn.setGravity(Gravity.CENTER);
        mRefreshBtn.setTextSize(Util.Dpi(36));
        mRefreshBtn.setTextColor(Color.BLACK);
        mRefreshBtn.getPaint().setFakeBoldText(true);
        final SimpleFocusDrawable mFocusBg = new SimpleFocusDrawable(getContext()).setRadius(Util.Div(45));
        mRefreshBtn.setBackground(mFocusBg);
        mRefreshBtn.setText("刷新试试");
        ll = new LinearLayout.LayoutParams(Util.Div(470), Util.Div(90));
        ll.topMargin = Util.Div(132);
        mNoDataView.addView(mRefreshBtn, ll);
        mRefreshBtn.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                mPresenter.loadData();
            }
        });
        mRefreshBtn.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View view, boolean b) {
                mFocusBg.setFocus(b);
            }
        });
    }

    @Override
    public View getView() {
        return this;
    }

    @Override
    public void setPresenter(IThirdAccountPresenter presenter) {
        mPresenter = presenter;
    }

    @Override
    public void refreshUI(List<ThridAccountHttpBean> dataList) {
        try {
            if (dataList != null && dataList.size() > 0) {
                mDataList.clear();
                mDataList.addAll(dataList);
                if (mAdapter == null) {
                    mAdapter = new NewRecycleAdapter<ThridAccountHttpBean>(mDataList, 1) {
                        @Override
                        public NewRecycleAdapterItem<ThridAccountHttpBean> onCreateItem(Object type) {
                            return new ThridAccountItemView(getContext());
                        }
                    };
                    mRecycleLayout.setRecyclerAdapter(mAdapter);
                } else {
                    mRecycleLayout.notifyDataSetChanged();
                }
            }
            mRecycleLayout.setSelection(0);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void showLoading() {

    }

    @Override
    public void hideLoading() {

    }

    @Override
    public void showErrorView(final String errorMsg) {
        ThreadManager.getInstance().uiThread(new Runnable() {
            @Override
            public void run() {
                if (mNoDataView == null) {
                    addNoDataView();
                }
                mNoDataView.setVisibility(VISIBLE);
                mNoDataText.setText(errorMsg);
                mRefreshBtn.requestFocus();
            }
        });
    }

    @Override
    public void hideErrorView() {
        ThreadManager.getInstance().uiThread(new Runnable() {
            @Override
            public void run() {
                if (mNoDataView != null) {
                    mNoDataView.setVisibility(GONE);
                }
            }
        });
    }

    @Override
    public void refreshAccountStatus(int postiion, String status) {
        try {
            mDataList.get(postiion).bind_status = status;
            mRecycleLayout.notifyDataSetChanged();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void showToast(final String msg) {
        ThreadManager.getInstance().uiThread(new Runnable() {
            @Override
            public void run() {
                XToast.showToast(getContext(), msg);
            }
        });
    }

    @Override
    public void click(View v, final int position) {
        try {
            if (IAppAccountManager.INSTANCE.hasLogin(true)) {
                final ThridAccountHttpBean data = mDataList.get(position);
                if ("1".equals(data.bind_status)) {//去解绑
                    SkyDialogView skyDialogView = new SkyDialogView(getContext(), true);
                    skyDialogView.setTipsString(getContext().getString(R.string.unbind_acc_msg, data.account_title), "");
                    skyDialogView.setBtnString(getContext().getString(R.string.Cancel), getContext().getString(R.string.confirm));
                    mUnBindDialog = SkyDialogView.showWithDialog(getContext(), skyDialogView);
                    mUnBindDialog.show();
                    skyDialogView.setOnDialogOnKeyListener(new SkyDialogView.OnDialogOnKeyListener() {
                        @Override
                        public boolean onDialogOnKeyEvent(int keyCode, KeyEvent event) {
                            return false;
                        }

                        @Override
                        public void onFirstBtnOnClickEvent() {
                            mUnBindDialog.dismiss();
                        }

                        @Override
                        public void onSecondBtnOnClickEvent() {
                            mPresenter.unbindAccount(position, data.account_type, data.account_title);
                            mUnBindDialog.dismiss();
                        }
                    });
                } else {//显示二维码绑定账号
                    mBindThridAccDialog = BindThirdAccountDialogFactory.create(data.account_type, getContext());
                    mBindThridAccDialog.setParams(data).setOnBindResultListener(new IBindThirdAccountDialog.OnBindResultListener() {
                        @Override
                        public void result(boolean result) {
                            //刷新UI
                            if (result) {
                                refreshAccountStatus(position, "1");
                            }
                        }
                    }).show();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void focusChange(View v, int position, boolean hasFocus) {
        ((ThridAccountItemView) v).onFocusChange(hasFocus);
    }

    @Override
    public void destroy() {
        mDataList.clear();
        if (mUnBindDialog != null) {
            mUnBindDialog.dismiss();
        }
    }
}
