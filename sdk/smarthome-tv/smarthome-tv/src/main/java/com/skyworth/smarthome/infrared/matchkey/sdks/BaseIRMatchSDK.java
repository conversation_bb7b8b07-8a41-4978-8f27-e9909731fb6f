package com.skyworth.smarthome.infrared.matchkey.sdks;

import android.util.Log;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.skyworth.smarthome.infrared.matchkey.presenter.IMatchKeyPresenterImpl.TAG;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/9/2 16:04.
 */
public abstract class BaseIRMatchSDK<T> implements IBaseIRMatchSDK {
    protected IRMatchKeyResultListener mResultListener = null;
    protected int mCurrentKeyIndex = 0;
    protected List<T> mCurrentKeyList = new ArrayList<>();

    @Override
    public void init(Map<String, String> params) {
        Log.i(TAG, "init: sdk: " + getName());
    }

    @Override
    public void setResultListener(IRMatchKeyResultListener listener) {
        mResultListener = listener;
    }

    protected void resetState() {
        mCurrentKeyIndex = 0;
        mCurrentKeyList.clear();
    }

    @Override
    public int getCurrentIndex() {
        return mCurrentKeyIndex;
    }

    @Override
    public int getCurrentKeyListCount() {
        return mCurrentKeyList.size();
    }

    abstract protected String getName();

    protected void logi(String msg) {
        Log.i(TAG, getName() + ": " + msg);
    }

    protected void loge(String msg) {
        Log.e(TAG, getName() + ": " + msg);
    }

    protected boolean tryNextKeyCode() {
        //当前按键的下一个键值
        if (mCurrentKeyIndex + 1 < getCurrentKeyListCount()) {
            mCurrentKeyIndex++;
            return true;
        } else {
            return false;
        }
    }

    @Override
    public void destroy() {
        resetState();
    }
}
