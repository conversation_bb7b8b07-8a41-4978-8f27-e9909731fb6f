package com.skyworth.smarthome.service.model;

import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.bean.ThridAccountHttpBean;
import com.smarthome.common.model.SmartBaseData;
import com.swaiot.aiotlib.common.entity.DiscoverNetworkDevice;
import com.swaiot.aiotlib.common.entity.SceneBean;
import com.tianci.framework.player.SkyPlayerItem;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON> on 2018/4/18.
 */

public interface ISmartHomeModel {
    SmartHomeModel INSTANCE = new SmartHomeModel();

    void getSmartDeviceList(String familyId);

    void setCurrentFamily(String familyID);

    List<DeviceInfo> sortDeviceList(List<DeviceInfo> device_list);

    void getSceneList();

    List<SceneBean> sortSceneList(List<SceneBean> scene_lsit);

    void getFamilyList();

    boolean handleDevice(String device_id, Map<String, Object> map);

    boolean controlDevice(String device_id, Map<String, String> status);

    int getDeviceBindStatus();

    SmartBaseData<List<Map<String, String>>> getDeviceBindStatus(List<DiscoverNetworkDevice> list);

    String getAiotHomeStaus(String familyId);

    boolean bindSelfDevice();

    boolean forceUnBindDevices();

    SmartBaseData controlScene(String cmd, String scene_id);

    SmartBaseData setSceneAutoSwitch(String scene_id, boolean isEnabled);

    void reportCurrentMedia(String targetDeviceID, SkyPlayerItem skyPlayerItem);

    List<ThridAccountHttpBean> getThirdAccountList(String supportAccounts);

    SmartBaseData unbindThirdAccount(String account_type);

    void commitLog(String eventName, Map<String, String> msg);

    void updateDeviceInfo();

    void updateSelfTvDeviceInfo();

    void bindSelfTvDevice();

    void loadData();

    void loadSceneList();

}
