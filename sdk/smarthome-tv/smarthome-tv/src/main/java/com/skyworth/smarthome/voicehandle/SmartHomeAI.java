package com.skyworth.smarthome.voicehandle;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.util.Log;

import com.alibaba.fastjson.JSONObject;
import com.coocaa.app.core.http.HttpServiceManager;
import com.skyworth.smarthome.common.http.SmartDevicesHttpService;
import com.skyworth.smarthome.common.util.SystemProperty;
import com.smarthome.common.model.SmartBaseData;
import com.swaiot.aiotlib.common.util.ThreadManager;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class SmartHomeAI {

    /**
     * 通过小维AI播放语音
     *
     * @param context
     * @param content
     */
    public static void playVoiceTTS(final Context context, final String content) {
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                if(isSmartHomeAi8Installed(context)){//8.0小维AI
                    Intent newIntent = new Intent("com.skyworth.angel.voice.callback.action");
                    newIntent.putExtra("cmd", 1); //必须为1
                    newIntent.putExtra("content", content); //tts的内容
                    newIntent.putExtra("id", "system"); //tts的id 可以自己定义
                    context.sendBroadcast(newIntent);
                }else{
                    Intent intent = new Intent("com.skyworth.srtnj.lafite.dueros.callback.action");
                    Bundle bundle = new Bundle();
                    bundle.putInt("cmd", 2);
                    bundle.putString("content", content);
                    bundle.putString("utteranceId", "system");
                    intent.putExtras(bundle);
                    context.sendBroadcast(intent);
                }
            }
        });
    }


    /**
     * 通过小维AI透传指令
     *
     * @param c
     * @param response
     */
    public static void reportVoiceResult(Context c, String response) {
        try {
            Log.d("voicecmd", "responsVoice response:" + response);
            Intent in = new Intent();
            in.setAction("com.skyworth.srtnj.lafite.dueros.callback.action");
            Bundle data = new Bundle();
            data.putInt("cmd", 7);
            data.putString("data", response);
            in.putExtras(data);
            c.sendBroadcast(in);
        } catch (Exception e) {
        }
    }


    /**
     * 通过小维AI上报设备状态
     *
     * @param context
     * @param data
     */
    private static void reportDeviceStatus(final Context context, final ReportedData data) {
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {

                String jData = JSONObject.toJSONString(data);
                Log.d("report", "data:" + jData);
                Intent intent = new Intent("com.skyworth.srtnj.lafite.dueros.callback.action");
                Bundle bundle = new Bundle();
                bundle.putInt("cmd", 8);
                bundle.putString("data", jData);
                intent.putExtras(bundle);
                context.sendBroadcast(intent);


                Map<String, Object> statusMap = new HashMap<>();
                statusMap.put(data.name, Integer.parseInt(data.value));
                String devcieID = SystemProperty.getDeviceId();
                SmartBaseData<String> baseData = HttpServiceManager.Companion.call(SmartDevicesHttpService.SERVICE.updateDeviceStatus(devcieID, statusMap));
            }
        });
    }

    /**
     * 是否安装8.0小维AI
     * @param context
     * @return
     */
    private static boolean isSmartHomeAi8Installed(Context context) {
        String pkgName = "com.skyworth.angel.voice";
        PackageInfo packageInfo;
        try {
            packageInfo = context.getPackageManager().getPackageInfo(pkgName, 0);
        } catch (PackageManager.NameNotFoundException e) {
            packageInfo = null;
            e.printStackTrace();
        }
        if (packageInfo == null) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 向小维AI上报TV属性
     */
    public static void respontTVStatus(Context context, String key, int type, String value) {
        ReportedData rData = new ReportedData();
        rData.name = key;
        rData.type = type;
        rData.value = value;
        reportDeviceStatus(context, rData);
    }

    /**
     * 向小维AI上报红外码值
     */
    public static void respontIRCTL(Context context, String key, int type, String value) {
        ReportedData rData = new ReportedData();
        rData.name = key;
        rData.type = type;
        rData.value = value;
        reportDeviceStatus(context, rData);
    }

    public static class ReportedData implements Serializable {
        public String name;
        public int type;
        public String value;
    }


    public static class VirtualPayLoadData implements Serializable {
        public String act;
        public String cube;
    }

    public static class IRCTLData implements Serializable {
        public String DID;
        public String KEY_NUM;
        public int[] KEY_CODE;
        public String MSGID;
    }

    public static class STD_SData implements Serializable {
        public String DID;
        public int KEY_NUM;
        public int CODE;      // 0:开始学习  1：退出学习
        public int MSGID;
    }

    public static class STD_RData implements Serializable {
        public String DID;
        public int KEY_NUM;
        public int CODE;    // 0:正在学习  1：学习成功 2：学习失败  3：停止学习
        public int MSGID;
        public STD_Result RESULT;

        public static class STD_Result implements Serializable {
            public short[] KEY_CODE;
        }
    }

}
