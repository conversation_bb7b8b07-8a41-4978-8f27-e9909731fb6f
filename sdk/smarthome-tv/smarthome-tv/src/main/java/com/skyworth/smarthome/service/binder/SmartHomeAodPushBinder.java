package com.skyworth.smarthome.service.binder;

import android.os.RemoteCallbackList;
import android.os.RemoteException;

import com.alibaba.fastjson.JSONObject;
import com.coocaa.app.core.app.AppCoreApplication;
import com.skyworth.smarthome.service.model.IAIOTModel;
import com.skyworth.smarthome.common.bean.DeviceListHttpBean;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome_tv.ISmartHomePushService;
import com.skyworth.smarthome_tv.ISmartHomePushServiceCallback;
import com.smarthome.common.utils.EmptyUtils;
import com.swaiot.aiotlib.common.util.LogUtil;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * Describe:推送数据到aod Binder
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/1/30
 */
public class SmartHomeAodPushBinder extends ISmartHomePushService.Stub {
    private RemoteCallbackList<ISmartHomePushServiceCallback> mCallbacks = new RemoteCallbackList<>();

    public SmartHomeAodPushBinder() {
    }

    @Override
    public void registerCallback(ISmartHomePushServiceCallback callback) throws RemoteException {
        if (callback != null) {
            mCallbacks.register(callback);
        }
    }

    @Override
    public void unregisterCallback(ISmartHomePushServiceCallback callback) throws RemoteException {
        if (callback != null) {
            mCallbacks.register(callback);
        }
    }


    public void sendDeviceChangeMsg(String event, String data) {
        switch (event) {
            case AppConstants.EVENT_CHANGE_DEVICE_STATUS:
                if (EmptyUtils.isNotEmpty(data)) {
                    LogUtil.androidLog("推送给AOD数据：event:" + event + " data:" + data);
                    sendBroadCast(event, data);
                }
                break;
            case AppConstants.EVENT_CHANGE_DEVICE_LIST:
                DeviceListHttpBean deviceListHttpBean = IAIOTModel.INSTANCE.transformDeviceList();
                String deviceListStr;
                if(EmptyUtils.isNotEmpty(deviceListHttpBean)){
                    deviceListStr = JSONObject.toJSONString(deviceListHttpBean);
                }else{
                    deviceListStr = "";
                }
                sendBroadCast(event, deviceListStr);
                LogUtil.androidLog("推送给AOD数据：event:" + event + " data:" + JSONObject.toJSONString(deviceListHttpBean));
                break;
            case AppConstants.EVENT_CHANGE_DEVICE_ONLINE_STATUS:
                LogUtil.androidLog("推送给AOD数据：event:" + event + " data:" + data);
                sendBroadCast(event, data);
                break;
                default:
        }
    }


    /**
     * 发送广播
     *
     * @param event
     * @param data
     */
    private void sendBroadCast(final String event, final String data) {
        AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                synchronized (this) {
                    try {
                        final int N = mCallbacks.beginBroadcast();
                        for (int i = 0; i < N; i++) {
                            mCallbacks.getBroadcastItem(i).onReceivedData(event, data);
                        }
                        mCallbacks.finishBroadcast();
                    } catch (RemoteException e) {
                        e.printStackTrace();
                    }
                }
                return Unit.INSTANCE;
            }
        });
    }


    public void destroy() {
        if (EmptyUtils.isNotEmpty(mCallbacks)) {
            mCallbacks.kill();
        }
    }
}
