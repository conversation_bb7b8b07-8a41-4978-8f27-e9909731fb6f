package com.skyworth.smarthome.service.push.local;


import com.skyworth.smarthome.common.model.AppConstants;

/**
 * Describe:设备数据变化推送工具类
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/1/28
 */
public class DeviceDataPushUtil {

    private static final IHandlerPush push = new HandlePush();

    /**
     * 处理push信息
     * @param data
     */
    public synchronized static void handlePush(AppConstants.SSE_PUSH event, String data){
        try {
            push.onArrive(event,data);
        } catch (Exception e){
            e.printStackTrace();
        }
    }

    public static IHandlerPush getPush() {
        return push;
    }
}
