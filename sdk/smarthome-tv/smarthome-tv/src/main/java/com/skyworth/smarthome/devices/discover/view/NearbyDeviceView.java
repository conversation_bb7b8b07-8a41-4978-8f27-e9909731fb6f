package com.skyworth.smarthome.devices.discover.view;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.skyworth.smarthome.devices.discover.dialog.ScanNearbyDeviceHelpDialog;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.ui.ScanningView;
import com.skyworth.ui.api.widget.SimpleFocusDrawable;
import com.skyworth.util.Util;


/**
 * Description: 附近的设备<br>
 * Created by wzh on 2019/2/17 17:43.
 */
public class NearbyDeviceView extends FrameLayout {

    private ScanningView mScanningView;//扫描View
//    private ImageView mMediaBindImg;

    public NearbyDeviceView(final Context context) {
        super(context);
        mScanningView = new ScanningView(context);
        mScanningView.setCenterIcon(R.drawable.scanning_wifi);
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.gravity = Gravity.CENTER_HORIZONTAL|Gravity.CENTER_VERTICAL;
        addView(mScanningView, params);
//屏蔽美的活动推荐
//        mMediaBindImg = new ImageView(getContext());
//        mMediaBindImg.setBackgroundResource(R.drawable.pop_media_bg);
//        params = new LayoutParams(Util.Div(220),Util.Div(52));
//        params.leftMargin = Util.Div(120);
//        params.topMargin = Util.Div(128);
//        addView(mMediaBindImg,params);

        TextView mTipsV = new TextView(context);
        mTipsV.setClickable(true);
        mTipsV.setFocusable(true);
        mTipsV.setGravity(Gravity.CENTER);
        mTipsV.setTextColor(Color.parseColor("#000000"));
        mTipsV.setTextSize(Util.Dpi(28));
        mTipsV.getPaint().setFakeBoldText(true);
        mTipsV.setText(context.getString(R.string.look_help));
        SimpleFocusDrawable simpleFocusDrawable = new SimpleFocusDrawable(getContext()).setRadius(Util.Div(50));
        mTipsV.setBackground(simpleFocusDrawable);
        simpleFocusDrawable.setFocus(true);
        params = new LayoutParams(Util.Div(220), Util.Div(70));
        params.gravity = Gravity.CENTER_HORIZONTAL;
        params.topMargin = Util.Div(480);
        addView(mTipsV, params);
        mTipsV.requestFocus();

        mTipsV.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
              ScanNearbyDeviceHelpDialog scanNearbyDeviceHelpDialog =  new ScanNearbyDeviceHelpDialog(R.style.transparent_dialog);
              scanNearbyDeviceHelpDialog.showDialog(null);
            }
        });
        startAnim();
    }

    public void startAnim() {
        mScanningView.startAnim();
    }

    public void stopAnim() {
        mScanningView.stopAnim();
    }

    public void destroy() {
        stopAnim();
    }
}
