package com.skyworth.smarthome.devices.apconfig.view;

import android.content.Context;
import android.graphics.Color;
import android.support.v7.widget.NewRecycleAdapter;
import android.support.v7.widget.OrientationHelper;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;


import com.skyworth.smarthome.devices.apconfig.presenter.IApConfigPresenter;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.bean.DevicePosBean;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.ui.DialogBg;
import com.skyworth.ui.api.widget.SimpleFocusDrawable;
import com.skyworth.ui.newrecycleview.NewRecycleAdapterItem;
import com.skyworth.ui.newrecycleview.NewRecycleLayout;
import com.skyworth.ui.newrecycleview.OnItemClickListener;
import com.skyworth.ui.newrecycleview.OnItemFocusChangeListener;
import com.skyworth.util.Util;
import com.smarthome.common.utils.EmptyUtils;

import java.util.ArrayList;
import java.util.List;

public class SetDevicePosView extends FrameLayout implements OnItemFocusChangeListener, OnItemClickListener {
    private Context mContext;
    private IApConfigPresenter mPresenter = null;
    private static final int ITEM_WIDTH = Util.Div(275);
    private static final int ITEM_HEIGHT = Util.Div(90);

    private NewRecycleLayout<DevicePosBean> mRecycleLayout;
    private NewRecycleAdapter<DevicePosBean> mAdapter;

    public SetDevicePosView(Context context) {
        super(context);
        mContext = context;

        setBackground(new DialogBg());

        TextView title = new TextView(context);
        title.setText(context.getString(R.string.set_device_title));
        title.setTextColor(Color.WHITE);
        title.setTextSize(Util.Dpi(36));
        title.getPaint().setFakeBoldText(true);
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.topMargin = Util.Div(40);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        addView(title, params);

        TextView subTitle = new TextView(context);
        subTitle.setText(context.getString(R.string.set_device_subtitle));
        subTitle.setTextColor(Color.parseColor("#aaFFFFFF"));
        subTitle.setTextSize(Util.Dpi(32));
        params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.topMargin = Util.Div(96);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        addView(subTitle, params);

        mRecycleLayout = new NewRecycleLayout<DevicePosBean>(context);
        mRecycleLayout.setOrientation(OrientationHelper.VERTICAL);
        mRecycleLayout.setSpanCount(2);
        mRecycleLayout.setmItemFocusChangeListener(this);
        mRecycleLayout.setmItemClickListener(this);
        mRecycleLayout.setClipChildren(false);
        mRecycleLayout.setClipToPadding(false);
        params = new LayoutParams(Util.Div(600), ViewGroup.LayoutParams.WRAP_CONTENT);
        params.topMargin = Util.Div(168);
        params.bottomMargin = Util.Div(40);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        addView(mRecycleLayout, params);
    }

    public void setmPresenter(IApConfigPresenter presenter) {
        mPresenter = presenter;
    }

    public void show(String deviceName, List<DevicePosBean> posBeans) {
        if (EmptyUtils.isEmpty(posBeans)||posBeans.size() <= 0) {
            posBeans = new ArrayList<>();
            for(String item: AppConstants.ROOM_LIST){
                DevicePosBean devicePosBean = new DevicePosBean();
                devicePosBean.location = item;
                posBeans.add(devicePosBean);
            }
        }
        mAdapter = new NewRecycleAdapter<DevicePosBean>(posBeans, 1) {

            @Override
            public NewRecycleAdapterItem<DevicePosBean> onCreateItem(Object type) {
                return new DevicePosItem(mContext);
            }
        };
        mRecycleLayout.setRecyclerAdapter(mAdapter);
        mRecycleLayout.setSelection(0);
    }

    @Override
    public void click(View v, int position) {
        if (mPresenter != null) {
            try {
                String location = mAdapter.getData().get(position).location;
                mPresenter.setDevicePosClick(location);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void focusChange(View v, int position, boolean hasFocus) {
        ((DevicePosItem) v).onFocusChange(v, hasFocus);
    }

    private class DevicePosItem extends FrameLayout implements NewRecycleAdapterItem<DevicePosBean> {
        private TextView location;
        private SimpleFocusDrawable mFocusBg;

        public DevicePosItem(Context context) {
            super(context);
            setFocusable(true);
            mFocusBg = new SimpleFocusDrawable(getContext()).setRadius(Util.Div(10));
            setPadding(Util.Div(10), Util.Div(10), Util.Div(10), Util.Div(10));
            location = new TextView(getContext());
            location.setTextSize(Util.Dpi(32));
            location.setTextColor(Color.parseColor("#aaFFFFFF"));
            location.getPaint().setFakeBoldText(true);
            location.setGravity(Gravity.CENTER);
            LayoutParams layoutParams = new LayoutParams(ITEM_WIDTH, ITEM_HEIGHT);
            addView(location, layoutParams);
            location.setBackground(mFocusBg);
        }

        public void onFocusChange(View v, boolean hasFocus) {
            mFocusBg.setFocus(hasFocus);
            if (hasFocus) {
                location.setTextColor(Color.parseColor("#000000"));
            } else {
                location.setTextColor(Color.parseColor("#aaFFFFFF"));
            }
        }

        @Override
        public View getView() {
            return this;
        }

        @Override
        public void onUpdateData(DevicePosBean data, int position) {
            location.setText(data.location);
        }

        @Override
        public void clearItem() {

        }

        @Override
        public void refreshUI() {

        }

        @Override
        public void destroy() {

        }
    }
}
