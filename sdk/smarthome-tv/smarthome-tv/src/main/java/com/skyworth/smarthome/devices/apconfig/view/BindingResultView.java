package com.skyworth.smarthome.devices.apconfig.view;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.ui.DialogBg;
import com.skyworth.ui.api.widget.SimpleFocusDrawable;
import com.skyworth.util.Util;

/**
 * Description:绑定设备结果-View <br>
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/1/8 10:04.
 */
public class BindingResultView extends LinearLayout {

    private Context mContext;
    private TextView mContentTv;
    private Button mOkBtn;

    private int mBindStatus;

    private BindResultCallBack mCallBack;

    public static final int BIND_FAILED = 0;
    public static final int BIND_SUCCESS = 1;

    public interface BindResultCallBack {
        void onBindingResult(int status);
    }

    public BindingResultView(Context context) {
        super(context);
        mContext = context;

        setBackground(new DialogBg());
        setOrientation(LinearLayout.VERTICAL);

        mContentTv = new TextView(context);
        mContentTv.setTextSize(Util.Dpi(36));
        mContentTv.setTextColor(Color.parseColor("#FFFFFF"));
        mContentTv.getPaint().setFakeBoldText(true);
        mContentTv.setGravity(Gravity.CENTER);
        LayoutParams params = new LayoutParams(Util.Div(614), ViewGroup.LayoutParams.WRAP_CONTENT);
        params.topMargin = Util.Div(99);
        params.gravity = Gravity.CENTER_HORIZONTAL;
        addView(mContentTv, params);

        mOkBtn = new Button(mContext);
        mOkBtn.setGravity(Gravity.CENTER);
        mOkBtn.setTextSize(Util.Dpi(32));
        mOkBtn.setTextColor(Color.parseColor("#000000"));
        SimpleFocusDrawable simpleFocusDrawable = new SimpleFocusDrawable(getContext()).setRadius(Util.Div(10));
        mOkBtn.setBackground(simpleFocusDrawable);
        simpleFocusDrawable.setFocus(true);
        mOkBtn.getPaint().setFakeBoldText(true);
        params = new LayoutParams(Util.Div(614), Util.Div(90));
        params.gravity = Gravity.CENTER_HORIZONTAL;
        params.topMargin = Util.Div(125);
        addView(mOkBtn, params);
        mOkBtn.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mCallBack != null) {
                    mCallBack.onBindingResult(mBindStatus);
                }
            }
        });
    }

    /**
     * 设置绑定状态
     *
     * @param status
     */
    public void setBindingStatus(int status) {
        mBindStatus = status;
        if (mBindStatus == BIND_SUCCESS) {
            mContentTv.setText(mContext.getString(R.string.add_success));
            mOkBtn.setText(R.string.confirm);
        } else if (mBindStatus == BIND_FAILED) {
            mContentTv.setText(mContext.getString(R.string.binding_device_failed));
            mOkBtn.setText(mContext.getString(R.string.immediate_binding));
        }
    }

    public void setBindResultCallBack(BindResultCallBack mCallBack) {
        this.mCallBack = mCallBack;
    }

    public void setFocus() {
        if (mOkBtn != null) {
            post(new Runnable() {
                @Override
                public void run() {
                    mOkBtn.requestFocus();
                }
            });
        }
    }

}
