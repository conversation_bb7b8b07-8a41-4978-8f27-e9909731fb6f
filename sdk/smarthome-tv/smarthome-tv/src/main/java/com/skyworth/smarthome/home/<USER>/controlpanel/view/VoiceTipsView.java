package com.skyworth.smarthome.home.smartdevice.controlpanel.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.skyworth.smarthome.R;
import com.skyworth.util.Util;
import com.smarthome.common.utils.EmptyUtils;

import java.util.List;

/**
 * Description:语音提示View-新 <br>
 * Created by wzh on 2019/3/18 09:54.
 */
public class VoiceTipsView extends FrameLayout {

    private Context mContext;
    private LinearLayout mContentLayout;
    private TextView mTitleView;

    public VoiceTipsView( Context context) {
        super(context);
        mContext = context;
        mTitleView = new TextView(context);
        mTitleView.setTextSize(Util.Dpi(22));
        mTitleView.setTextColor(Color.parseColor("#CDD2D8"));
        mTitleView.setText("语音呼出");
        Drawable drawable = getResources().getDrawable(R.drawable.icon_voice_tips);
        drawable.setBounds(0, 0, Util.Div(32), Util.Div(32));
        mTitleView.setCompoundDrawables(drawable, null, null, null);
        mTitleView.setCompoundDrawablePadding(Util.Div(10));
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.leftMargin = Util.Div(10);
        params.topMargin = Util.Div(30);
        addView(mTitleView, params);
        mContentLayout = new LinearLayout(context);
        params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.leftMargin = Util.Div(150);
        params.topMargin = Util.Div(28);
        addView(mContentLayout, params);
    }

    private int mContentWidth = 0;

    private void addTxtView(String content) {
        TextView txtV = new TextView(mContext);
        txtV.setBackgroundResource(R.drawable.voice_txt_bg);
        txtV.setText(content);
        txtV.setTextSize(Util.Dpi(18));
        txtV.setTextColor(Color.parseColor("#FFFFFF"));
        txtV.setGravity(Gravity.CENTER);
        txtV.setSingleLine(true);
        txtV.setMarqueeRepeatLimit(Integer.MAX_VALUE);
        txtV.setFocusable(true);
        txtV.setEllipsize(TextUtils.TruncateAt.MARQUEE);
        txtV.setFocusableInTouchMode(true);
        txtV.setHorizontallyScrolling(true);
        txtV.setSelected(true);
        txtV.setPadding(Util.Div(10), Util.Div(5), Util.Div(10), Util.Div(5));
        LinearLayout.LayoutParams ll = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, Util.Div(35));
        ll.leftMargin = Util.Div(10);
        mContentWidth += content.length() * Util.Div(30) + Util.Div(45) + Util.Div(20);
        Log.i("VoiceTipsView", "addTxtView: mContentWidth--" + mContentWidth);
        if (mContentWidth <= Util.Div(1570)) {
            mContentLayout.addView(txtV, ll);
        }
    }

    public void show(List<String> voices) {
        if (EmptyUtils.isNotEmpty(voices)) {
            mContentLayout.removeAllViews();
            mContentWidth = 0;
            for (String s : voices) {
                if (EmptyUtils.isNotEmpty(s)) {
                    addTxtView(s);
                }
            }
        }
    }

    public void destroy() {

    }
}
