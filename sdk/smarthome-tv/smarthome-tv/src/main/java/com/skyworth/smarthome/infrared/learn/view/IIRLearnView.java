package com.skyworth.smarthome.infrared.learn.view;

import android.view.KeyEvent;

import com.skyworth.smarthome.common.base.IView;
import com.skyworth.smarthome.infrared.learn.presenter.IIRLearnPresenter;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/7/2 14:42.
 */
public interface IIRLearnView extends IView<IIRLearnPresenter> {
    interface IDialogListener {
        void onDismiss();
    }

    void setDialogListener(IDialogListener listener);

    void showReady(String remoteName);

    void showLearnStart(int current, int total, String remoteName, String keyName, String irDeviceName);

    void showLearning(int current, int total, String remoteName, String keyName, String irDeviceName);

    void showLearnSuccess(String keyName, int seconds);

    void showLearnFail();

    void showLearnFinish(String deviceId);

    void showLoading();

    void hideLoading();

    void showError(String msg);

    boolean handleKeyEvent(KeyEvent keyEvent);

    void dismiss();
}
