package com.skyworth.smarthome.service.model.data;

import com.alibaba.fastjson.JSONObject;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.util.DataCacheUtil;
import com.skyworth.smarthome.common.util.ThreadManager;
import com.skyworth.smarthome.devices.discover.view.DiscoverNearDevicesView;
import com.skyworth.smarthome.service.model.SmartDeviceDataModel;
import com.skyworth.smarthome.service.push.local.DeviceDataPushUtil;
import com.smarthome.common.utils.EmptyUtils;
import com.swaiot.aiotlib.common.entity.DiscoverNetworkDevice;
import com.swaiot.aiotlib.common.entity.DiscoverWifiDevice;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import static com.skyworth.smarthome.devices.discover.view.DiscoverNearDevicesView.DEVICE_STATUS_NOT_APCONFIG_NETWORK;

/**
 * Describe:发现附近设备数据处理
 * Created by AwenZeng on 2019/9/17
 */
public class DiscoverDeviceDataHandleModel implements IDiscoverDeviceDataHandleModel {

    public static final String DEFAULT_TAG_NAME = "环境中的设备";
    private static byte[] lock = new byte[0];

    @Override
    public void addDiscoverDevice(DiscoverNetworkDevice discoverNetworkDevice) {
        List<DeviceInfo> deviceInfoList = AppData.getInstance().getDeviceInfoList();
        if (EmptyUtils.isNotEmpty(deviceInfoList)) {
            List<DeviceInfo> device_list = deviceInfoList;
            DeviceInfo item = new DeviceInfo();
            if (EmptyUtils.isNotEmpty(device_list)) {
                if (!isContainDevice(device_list, discoverNetworkDevice)
                        && !isDeviceDeleted(discoverNetworkDevice.device_id)) {
                    item.is_unBind = true;
                    item.device_name = discoverNetworkDevice.device_name;
                    item.device_icon = discoverNetworkDevice.product_type_logo;
                    if (EmptyUtils.isNotEmpty(discoverNetworkDevice.bind_status)&&discoverNetworkDevice.bind_status.equals(DEVICE_STATUS_NOT_APCONFIG_NETWORK)) {
                        item.isNotConnectedNetwork = true;
                    }
                    item.online_status = 1;
                    item.device_id = discoverNetworkDevice.device_id;
                    item.device_position = DEFAULT_TAG_NAME;
                    item.discoverNetworkDevice = discoverNetworkDevice;
                    device_list.add(item);
                }
            } else {
                if(!isDeviceDeleted(discoverNetworkDevice.device_id)){
                    device_list = new ArrayList<>();
                    item.is_unBind = true;
                    item.device_name = discoverNetworkDevice.device_name;
                    item.device_icon = discoverNetworkDevice.product_type_logo;
                    if (EmptyUtils.isNotEmpty(discoverNetworkDevice.bind_status)&&discoverNetworkDevice.bind_status.equals(DEVICE_STATUS_NOT_APCONFIG_NETWORK)) {
                        item.isNotConnectedNetwork = true;
                    }
                    item.online_status = 1;
                    item.device_id = discoverNetworkDevice.device_id;
                    item.device_position = DEFAULT_TAG_NAME;
                    item.discoverNetworkDevice = discoverNetworkDevice;
                    device_list.add(item);
                }
            }
            deviceInfoList = device_list;
            AppData.getInstance().setDeviceInfoList(deviceInfoList);
        }
        updateLocalAddDiscoverDeviceData(discoverNetworkDevice);
    }

    /**
     * 更新本地缓存数据
     */
    private void updateLocalAddDiscoverDeviceData(DiscoverNetworkDevice discoverNetworkDevice){
        //添加到附近设备列表
        List<DiscoverNetworkDevice> dataList = AppData.getInstance().getDiscoverNearbyDeviceList();
        if(EmptyUtils.isNotEmpty(dataList)){
            Iterator<DiscoverNetworkDevice> it = dataList.iterator();
            while (it.hasNext()) {
                DiscoverNetworkDevice device = it.next();
                if (EmptyUtils.isNotEmpty(device.device_id) && EmptyUtils.isNotEmpty(discoverNetworkDevice.device_id)) {
                    if (device.device_id.equals(discoverNetworkDevice.device_id)) {
                        return;
                    }
                }
            }

            if (EmptyUtils.isNotEmpty(discoverNetworkDevice.bind_status)&&!discoverNetworkDevice.bind_status.equals(DEVICE_STATUS_NOT_APCONFIG_NETWORK)) {
                List<DiscoverNetworkDevice> discoverList = AppData.getInstance().getDiscoverUnbindDeviceList();
                if(EmptyUtils.isNotEmpty(discoverList)){
                    if(!isContainDiscoverDevice(discoverList,discoverNetworkDevice.device_id)){
                        discoverList.add(discoverNetworkDevice);
                        AppData.getInstance().setDiscoverUnbindDeviceList(discoverList);
                    }
                }else{
                    discoverList = new ArrayList<>();
                    discoverList.add(discoverNetworkDevice);
                    AppData.getInstance().setDiscoverUnbindDeviceList(discoverList);
                }
            }

            dataList.add(discoverNetworkDevice);
            AppData.getInstance().setDiscoverNearbyDeviceList(dataList);
            DataCacheUtil.getInstance().putString(DataCacheUtil.KEY_SAVE_NEARBY_DEVICELIST, JSONObject.toJSONString(dataList));
        }else{
            dataList = new ArrayList<>();
            dataList.add(discoverNetworkDevice);
            AppData.getInstance().setDiscoverNearbyDeviceList(dataList);
            DataCacheUtil.getInstance().putString(DataCacheUtil.KEY_SAVE_NEARBY_DEVICELIST, JSONObject.toJSONString(dataList));
        }
    }


    @Override
    public boolean removeDiscoverDevice(DiscoverNetworkDevice discoverNetworkDevice) {
        return removeDiscoverDevice(discoverNetworkDevice,true,true);
    }

    public boolean removeDiscoverDevice(DiscoverNetworkDevice discoverNetworkDevice, boolean isSignDelete,boolean isPush) {
        List<DeviceInfo>  deviceInfoList = AppData.getInstance().getDeviceInfoList();
        if (EmptyUtils.isNotEmpty(deviceInfoList) ) {
            synchronized (lock){
                Iterator<DeviceInfo> it = deviceInfoList.iterator();
                while (it.hasNext()) {
                    DeviceInfo item = it.next();
                    if (EmptyUtils.isNotEmpty(item.device_id) && EmptyUtils.isNotEmpty(discoverNetworkDevice.device_id)) {
                        if (item.device_id.equals(discoverNetworkDevice.device_id)||EmptyUtils.isNotEmpty(discoverNetworkDevice.deviceInfo)&&item.device_id.equals(discoverNetworkDevice.deviceInfo.getWifiInfo().BSSID)) {
                            if (item.is_unBind&&isSignDelete) {//标记删除的设备
                                DataCacheUtil.getInstance().putInt("delete" + item.device_id, 1);
                            }
                            it.remove();
                            AppData.getInstance().setDeviceInfoList(deviceInfoList);
                            if(isPush){
                                DeviceDataPushUtil.handlePush(AppConstants.SSE_PUSH.DEVICE_LIST, "");
                            }
                            return true;
                        }
                    }
                }
            }
        }

        //从附近设备列表中删除
        List<DiscoverNetworkDevice> dataList = AppData.getInstance().getDiscoverNearbyDeviceList();
        if(EmptyUtils.isNotEmpty(dataList)){
            Iterator<DiscoverNetworkDevice> it = dataList.iterator();
            while (it.hasNext()) {
                DiscoverNetworkDevice item = it.next();
                if (EmptyUtils.isNotEmpty(item.device_id) && EmptyUtils.isNotEmpty(discoverNetworkDevice.device_id)) {
                    if (item.device_id.equals(discoverNetworkDevice.device_id)) {
                        it.remove();
                        AppData.getInstance().setDiscoverNearbyDeviceList(dataList);
                        DataCacheUtil.getInstance().putString(DataCacheUtil.KEY_SAVE_NEARBY_DEVICELIST, JSONObject.toJSONString(dataList));
                        return true;
                    }
                }
            }
        }
        return false;
    }



    @Override
    public void addNearbyDeviceToDeviceList() {
        try {
            List<DiscoverNetworkDevice> dataList = AppData.getInstance().getDiscoverNearbyDeviceList();
            if (EmptyUtils.isEmpty(dataList)) {
                String json = DataCacheUtil.getInstance().getString(DataCacheUtil.KEY_SAVE_NEARBY_DEVICELIST, "");
                if (EmptyUtils.isJsonStringEmpty(json)) {
                    dataList = JSONObject.parseArray(json, DiscoverNetworkDevice.class);
                }
            }

            if (EmptyUtils.isNotEmpty(dataList)) {
                for (DiscoverNetworkDevice item : dataList) {
                    SmartDeviceDataModel.INSTANCE.addDiscoverDevice(item);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public List<DiscoverNetworkDevice> getNearNetworkDeviceList() {
        List<DiscoverNetworkDevice> dataList;
        List<DiscoverWifiDevice> discoverDeviceInfos = AppData.getInstance().getDiscoverUnConfigNetDeviceList();
        List<DiscoverNetworkDevice> discoverNetworkDevices = AppData.getInstance().getDiscoverUnbindDeviceList();
        if (EmptyUtils.isEmpty(discoverDeviceInfos)) {
            dataList = discoverNetworkDevices;
        } else if (EmptyUtils.isEmpty(discoverNetworkDevices)) {
            dataList = assembleApconfigDevice(discoverDeviceInfos);
        } else {
            dataList = assembleApconfigDevice(discoverDeviceInfos);
            dataList.addAll(discoverNetworkDevices);
        }

        if(EmptyUtils.isNotEmpty(dataList)){
            AppData.getInstance().setDiscoverNearbyDeviceList(dataList);
            ThreadManager.getInstance().ioThread(new Runnable() {
                @Override
                public void run() {
                    DataCacheUtil.getInstance().putString(DataCacheUtil.KEY_SAVE_NEARBY_DEVICELIST, JSONObject.toJSONString(AppData.getInstance().getDiscoverNearbyDeviceList()));
                }
            });
        }

        addNearbyDeviceToDeviceList();

        return dataList;
    }

    private List<DiscoverNetworkDevice> assembleApconfigDevice(List<DiscoverWifiDevice> discoverDeviceInfos) {
        List<DiscoverNetworkDevice> tempList = new ArrayList<>();
        for (DiscoverWifiDevice item : discoverDeviceInfos) {
            DiscoverNetworkDevice device = new DiscoverNetworkDevice();
            if (EmptyUtils.isNotEmpty(item.getDeviceDetail())) {
                device.device_id = item.getWifiInfo().BSSID;
                device.brand_cn = item.getDeviceDetail().getBrand();
                device.device_name = item.getDeviceDetail().getBrand() + item.getDeviceDetail().getProduct();
                device.product_type_id = item.getDeviceDetail().getModel();
                device.product_type_logo = item.getDeviceDetail().getImg_url();
            } else {
                device.device_id = "device_id";
                device.device_name = SmartHomeTvLib.getContext().getString(R.string.unknow_device);
                device.product_type_logo = "";
                continue;
            }
            device.bind_status = DiscoverNearDevicesView.DEVICE_STATUS_NOT_APCONFIG_NETWORK;//未配网
            device.deviceInfo = item;
            tempList.add(device);
        }
        return tempList;
    }


    /**
     * 列表中是否包含设备
     *
     * @param deviceList
     * @param discoverNetworkDevice
     * @return
     */
    private boolean isContainDevice(List<DeviceInfo> deviceList, DiscoverNetworkDevice discoverNetworkDevice) {
        for (DeviceInfo item : deviceList) {
            if(EmptyUtils.isNotEmpty(discoverNetworkDevice.deviceInfo)&&item.device_id.equals(discoverNetworkDevice.deviceInfo.getWifiInfo().BSSID)){//如果有未配网的设备
                DiscoverNetworkDevice device = discoverNetworkDevice;
                device.device_id = discoverNetworkDevice.deviceInfo.getWifiInfo().BSSID;
                removeDiscoverDevice(device,false,false);
                return false;
            }else if(item.device_id.equals(discoverNetworkDevice.device_id)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 列表中是否包含设备
     *
     * @param deviceList
     * @param deviceId
     * @return
     */
    private boolean isContainDiscoverDevice(List<DiscoverNetworkDevice> deviceList, String deviceId) {
        for (DiscoverNetworkDevice item : deviceList) {
            if (item.device_id.equals(deviceId)) {
                return true;
            }
        }
        return false;
    }



    /**
     * 设备是否被删除（从设备列表中）
     *
     * @param deviceId
     * @return
     */
    private boolean isDeviceDeleted(String deviceId) {
        if (DataCacheUtil.getInstance().getInt("delete" + deviceId) == 1) {
            return true;
        }
        return false;
    }

}
