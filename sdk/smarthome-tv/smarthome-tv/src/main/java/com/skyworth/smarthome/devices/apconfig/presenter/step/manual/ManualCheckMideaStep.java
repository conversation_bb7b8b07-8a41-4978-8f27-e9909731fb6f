package com.skyworth.smarthome.devices.apconfig.presenter.step.manual;

import android.text.TextUtils;

import com.skyworth.smarthome.devices.apconfig.presenter.step.BaseStep;

import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_INPUT_GO_BIND_MIDEA;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_INPUT_GO_BIND_MOBILE;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_INPUT_GO_GET_MIDEA_TOKEN;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_INPUT_GO_LOGIN;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/3/21 11:01.
 */
public class ManualCheckMideaStep extends BaseStep {
    public static final String STEP_TAG = "manual_checkMideaAccount";

    @Override
    public void run() {
        super.run();
        presenter.showDialog();
        if (!presenter.isMideaDevice()) {
            next();
            return;
        }
        if (!presenter.isLogin()) {
            showDialog(STEP_MSG_INPUT_GO_LOGIN);
            return;
        }
        if (!presenter.isBindMobile()) {
            showDialog(STEP_MSG_INPUT_GO_BIND_MOBILE);
            return;
        }
        if (!presenter.isBindMidea()) {
            showDialog(STEP_MSG_INPUT_GO_BIND_MIDEA);
            return;
        }
        if (TextUtils.isEmpty(presenter.getMideaToken())) {
            showDialog(STEP_MSG_INPUT_GO_GET_MIDEA_TOKEN);
            return;
        }
        logi("run: checkMidea finish");
        next();
    }

    @Override
    public boolean input(String msg, Object... params) {
        switch (msg) {
            case STEP_MSG_INPUT_GO_LOGIN:
                presenter.goLogin();
                return true;
            case STEP_MSG_INPUT_GO_BIND_MOBILE:
                presenter.goBindMobile();
                return true;
            case STEP_MSG_INPUT_GO_BIND_MIDEA:
                presenter.goBindMideaAccount(true);
                return true;
            case STEP_MSG_INPUT_GO_GET_MIDEA_TOKEN:
                logi("input: get midea token");
                presenter.goBindMideaAccount(false);
                return true;
        }
        return false;
    }

    @Override
    public String getTag() {
        return STEP_TAG;
    }

    @Override
    public void destroy() {
        dialogShowTimes.clear();
        isDialogShowTwice = false;
        presenter.hideNotLogin();
        super.destroy();
    }
}
