package com.skyworth.smarthome.devices.apconfig.presenter.step.manual;


import com.skyworth.smarthome.R;
import com.skyworth.smarthome.devices.apconfig.presenter.step.BaseStep;

import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_CONFIG_FAIL;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_CONFIG_PROGRESS_HIDE;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_CONFIG_SUCCESS;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_INPUT_PASSWORD_HIDE;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_INPUT_PASSWORD_OK;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_INPUT_PASSWORD_SHOW;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/1/7 21:54.
 */
public class ManualStepConfiging extends BaseStep {
    public static final String STEP_TAG = "manual_configing";

    @Override
    public void run() {
        super.run();
        if (presenter.canGetCurrentWifiPassword() || !presenter.isCurrentWifiNeedPassword()) {
            output(STEP_MSG_INPUT_PASSWORD_HIDE);
            startApConfig();
        } else {
            output(STEP_MSG_INPUT_PASSWORD_SHOW, null, context.getString(R.string.apconfig_network_err_exception));
            output(STEP_MSG_CONFIG_PROGRESS_HIDE);
        }
    }

    @Override
    public boolean input(String msg, Object... params) {
        switch (msg) {
            case STEP_MSG_INPUT_PASSWORD_OK:
                output(STEP_MSG_INPUT_PASSWORD_HIDE);
                startApConfig();
                return true;
            case STEP_MSG_CONFIG_SUCCESS:
                presenter.onApConfigComplete();
                presenter.showProgress(100);
                next();
                return true;
            case STEP_MSG_CONFIG_FAIL:
                presenter.onApConfigError();
                jumpTo(ManualStepConfigFailed.STEP_TAG);
                return true;
        }
        return false;
    }

    private void startApConfig() {
        presenter.showProgress(0);
        presenter.speechConfiging();
        waitForSpeech();
        presenter.startApConfig();
    }

    private void waitForSpeech() {
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @Override
    public String getTag() {
        return STEP_TAG;
    }

    @Override
    public void destroy() {
        output(STEP_MSG_INPUT_PASSWORD_HIDE);
        super.destroy();
    }
}
