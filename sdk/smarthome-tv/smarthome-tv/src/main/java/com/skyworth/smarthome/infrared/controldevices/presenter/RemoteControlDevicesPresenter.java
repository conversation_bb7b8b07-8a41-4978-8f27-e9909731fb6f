package com.skyworth.smarthome.infrared.controldevices.presenter;

import com.coocaa.app.core.app.AppCoreApplication;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.util.SystemProperty;
import com.skyworth.smarthome.infrared.controldevices.model.IRemoteControlDevicesModel;
import com.skyworth.smarthome.infrared.controldevices.model.RemoteControlDevicesModel;
import com.skyworth.smarthome.infrared.controldevices.view.IRemoteControlDevicesView;
import com.skyworth.smarthome.infrared.controldevices.view.IShowRemoteControl;
import com.skyworth.smarthome.infrared.electriclist.model.DeviceTypeListData;
import com.skyworth.smarthome.infrared.manager.IREntry;
import com.skyworth.smarthome.service.model.ISmartDeviceDataModel;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XToast;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * Describe:
 * Created by AwenZeng on 2019/4/29
 */
public class RemoteControlDevicesPresenter implements IRemoteControlDevicesPresenter {
    private List<DeviceTypeListData> mDataList;
    private String mDeviceId = "";
    private String mDeviceName = "";
    private String mDeviceTypeId = "";
    private IRemoteControlDevicesView mView;
    private IRemoteControlDevicesModel mModel;

    private IShowRemoteControl mShowRemoteControl;
    private Map<String, Object> mParams;

    public RemoteControlDevicesPresenter() {
        mModel = new RemoteControlDevicesModel();
    }

    @Override
    public void loadData() {
        mView.showLoading();
        AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                mDataList = mModel.getIRDeviceList(mDeviceId);
                AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        mView.hideLoading();
                        mView.refreshUI(mDataList);
                        return Unit.INSTANCE;
                    }
                });
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void setView(IRemoteControlDevicesView view) {
        mView = view;
    }

    @Override
    public void setDeviceId(String deviceId) {
        mDeviceId = deviceId;
    }

    @Override
    public String getDeviceTypeId() {
        return mDeviceTypeId;
    }

    @Override
    public void setDeviceName(String deviceName) {
        mDeviceName = deviceName;
    }

    @Override
    public String getDeviceName() {
        return mDeviceName;
    }

    @Override
    public void setDeviceTypeId(String id) {
        mDeviceTypeId = id;
    }

    @Override
    public List<DeviceTypeListData> assembleData(List<DeviceTypeListData> data, boolean isShowDelete) {
        for (int i = 0; i < data.size(); i++) {
            DeviceTypeListData item = data.get(i);
            item.isShowDelete = isShowDelete;
            data.set(i, item);
        }
        return data;
    }

    @Override
    public void deleletIrDevices(final DeviceTypeListData data) {
        AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                final boolean isSuccess = mModel.deleteIrDevice(mDeviceId, data);
                AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        if (isSuccess) {
                            mView.deleteSuccess(data);
                        } else {
                            mView.deleteFailed();
                        }
                        return Unit.INSTANCE;
                    }
                });
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void setShowAddInfraredDevice(IShowRemoteControl mShowRemoteControl) {
        this.mShowRemoteControl = mShowRemoteControl;
    }

    @Override
    public void showAddInfraredDevice() {
        String device_id = SystemProperty.getDeviceId();
        if (EmptyUtils.isNotEmpty(mDeviceId) && EmptyUtils.isNotEmpty(device_id)) {
            DeviceInfo deviceDetail = ISmartDeviceDataModel.INSTANCE.getSmartDeviceInfo(mDeviceId);
            String device_name = "";
            if (EmptyUtils.isNotEmpty(deviceDetail)) {
                device_name = deviceDetail.device_name;
            } else {
                device_name = SmartHomeTvLib.getContext().getString(R.string.ir_devices_tv);
            }
            //本机是红外电视或设备为全时AI精灵(品类47为AI精灵)
            if (mDeviceId.equals(device_id) || EmptyUtils.isNotEmpty(deviceDetail) && deviceDetail.product_type_id.equals("47")) {
                if (EmptyUtils.isNotEmpty(mShowRemoteControl)) {
                    HashMap<String, Object> map = new HashMap<String, Object>();
                    map.put(IREntry.DEVICE_KEY, mDeviceId);
                    map.put(IREntry.DEVICE_NAME_KEY, mDeviceName);
                    map.put(IREntry.DEVICE_TYPE_ID_KEY, mDeviceTypeId);
                    mShowRemoteControl.showElectricTypeView(map);
                }
            } else {
                XToast.showToast(SmartHomeTvLib.getContext(), String.format(SmartHomeTvLib.getContext().getString(R.string.add_ir_device_tips), device_name));
            }
        }
    }

    @Override
    public void setParams(Map<String, Object> params) {
        mParams = params;
    }
}
