package com.skyworth.smarthome.home.smartdevice.controlpanel.thirdapp.view;

import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.LinearInterpolator;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.base.BaseSysDialog;
import com.skyworth.smarthome.common.util.DialogLauncherUtil;
import com.skyworth.util.Util;
import com.smarthome.common.utils.XThemeUtils;

import java.util.Map;

/**
 * @ClassName: ThirdAppLoadingDialog
 * @Author: XuZeXiao
 * @CreateDate: 2020-02-29 10:20
 * @Description:
 */
public class ThirdAppLoadingDialog extends BaseSysDialog {
    private ThirdAppLoadingView view;

    @Override
    protected void initParams() {
        mDialogKey = DialogLauncherUtil.DIALOG_KEY_THIRD_LOADING;
    }

    @Override
    protected void initContentView() {
        super.initContentView();
        view = new ThirdAppLoadingView(getContext());
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Util.Div(320), Util.Div(170));
        layoutParams.gravity = Gravity.CENTER;
        mContentView.addView(view, layoutParams);
    }

    @Override
    public void showDialog(Map<String, String> params) {
        super.showDialog(params);
        cancelDialogAnimation();
        String tip = "";
        if (params != null && params.containsKey("tip")) {
            tip = params.get("tip");
        }
        if (view != null) {
            view.show(tip);
        }
        openAutoDismissDialog();
    }

    @Override
    protected void onDismiss() {
        super.onDismiss();
        if (view != null) {
            view.destroy();
            view = null;
        }
    }

    private static class ThirdAppLoadingView extends FrameLayout {
        private View loading = null;
        private TextView tip = null;
        private ObjectAnimator animator = null;

        public ThirdAppLoadingView(Context context) {
            super(context);
            initView();
        }

        private void initView() {
            Drawable drawable = XThemeUtils.getDrawable(Color.argb(128, 0, 0, 0), 0, 0, Util.Div(8));
            setBackgroundDrawable(drawable);

            loading = new View(getContext());
            loading.setBackgroundResource(R.drawable.third_app_loading);

            animator = ObjectAnimator.ofFloat(loading, "rotation", 0, 359);
            animator.setDuration(1000);
            animator.setRepeatCount(ValueAnimator.INFINITE);
            animator.setInterpolator(new LinearInterpolator());
            animator.start();

            LayoutParams layoutParams = new LayoutParams(Util.Div(46), Util.Div(46));
            layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
            layoutParams.topMargin = Util.Div(46);
            addView(loading, layoutParams);

            tip = new TextView(getContext());
            tip.setTextSize(Util.Dpi(24));
            tip.setTextColor(Color.WHITE);
            tip.setText(getResources().getString(R.string.third_app_loading_tip, ""));
            layoutParams = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
            layoutParams.topMargin = Util.Div(110);
            addView(tip, layoutParams);
        }

        public void show(String process) {
            if (tip != null) {
                tip.setText(getResources().getString(R.string.third_app_loading_tip, process));
            }
        }

        public void destroy() {
            if (animator != null) {
                animator.cancel();
            }
        }
    }
}
