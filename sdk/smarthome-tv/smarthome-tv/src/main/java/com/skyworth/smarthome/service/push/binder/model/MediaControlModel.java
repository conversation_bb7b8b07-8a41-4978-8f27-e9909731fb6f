package com.skyworth.smarthome.service.push.binder.model;

import com.alibaba.fastjson.JSONObject;
import com.coocaa.app.core.app.AppCoreApplication;
import com.skyworth.smarthome.SmartHomeTvLib;
import com.skyworth.smarthome.common.util.DialogLauncherUtil;
import com.skyworth.smarthome.common.util.SmartHomeServiceManager;
import com.skyworth.smarthome.service.model.SmartHomeModel;
import com.skyworth.smarthome.voicehandle.SmartHomeAI;
import com.smarthome.common.utils.EmptyUtils;
import com.tianci.framework.player.SkyPlayerItem;
import com.tianci.media.api.SkyMediaApi;
import com.tianci.media.api.SkyMediaApiParam;
import com.tianci.media.base.SkyMediaItem;

import java.util.HashMap;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * Describe:
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/3/5
 */
public class MediaControlModel implements IMediaControlModel {

    public MediaControlModel() {
    }

    @Override
    public void reportCurrentMedia(final String data) {

        AppCoreApplication.Companion.workerThread(new Function0<Unit>() {
            @Override
            public Unit invoke(){
                SmartHomeServiceManager.getManager().pausePlayer();
                JSONObject jsonObject = JSONObject.parseObject(data);
                String targetDeviceID = jsonObject.getString("target_device_id");
                String voiceTips = jsonObject.getString("no_media_tts");
                SkyPlayerItem skyPlayerItem = SmartHomeServiceManager.getManager().getCurrentPlayerItem();
                if (EmptyUtils.isNotEmpty(skyPlayerItem)) {
                    skyPlayerItem.position = SmartHomeServiceManager.getManager().getCurrentPosition() + "";
                    SmartHomeModel.INSTANCE.reportCurrentMedia(targetDeviceID, skyPlayerItem);
                } else {
                    if (EmptyUtils.isNotEmpty(voiceTips)) {
                       SmartHomeAI.playVoiceTTS(SmartHomeTvLib.getContext(), voiceTips);
                    }
                }
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void playVideo(String data) {
        if (EmptyUtils.isNotEmpty(data)) {
            JSONObject jsonObject = JSONObject.parseObject(data);
            SkyMediaApiParam param = new SkyMediaApiParam();
            SkyMediaItem[] list = new SkyMediaItem[1];
            list[0] = new SkyMediaItem();
            list[0].type = SkyMediaItem.SkyMediaType.MOVIE;
            list[0].id = jsonObject.getString("id");
            list[0].name = jsonObject.getString("name");
            list[0].extra = new HashMap<>();
            list[0].extra.put("child_id", jsonObject.getString("child_id"));
            list[0].extra.put("url_type", jsonObject.getString("url_type"));
            list[0].extra.put("position", jsonObject.getString("position"));
            list[0].extra.put("source", jsonObject.getString("source"));
            param.setPlayList(list, 0);
            SkyMediaApi.getInstance().startOnlinePlayer(param);
        }
    }

    @Override
    public void playVideoDoorBell(String data) {
        if (EmptyUtils.isEmpty(data)) return;
        JSONObject jsonObject = JSONObject.parseObject(data);
        String videoUrl = jsonObject.getString("video_url");
//        if (EmptyUtils.isNotEmpty(videoUrl)) {
//            Intent intent = new Intent("coocaa.intent.action.smarthome.VIDEO_PLAYER");
//            intent.putExtra("url", videoUrl);
//            intent.putExtra("title", mContext.getString(R.string.video_camera_tip));
//            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//            mContext.startActivity(intent);
//
//            Map<String, String> params = new HashMap<>();
//            params.put(SmartHomePlayerDialog.URL, videoUrl);
//            params.put(SmartHomePlayerDialog.TITLE, mContext.getString(R.string.video_camera_tip));
//            DialogLauncherUtil.showPlayer(params);
//        }
    }


    @Override
    public void stopPlayVideoDoorBell() {
//        SmartHomePlayerActivity.finishMe();
        DialogLauncherUtil.dismissDialog(DialogLauncherUtil.DIALOG_KEY_PLAYER);
    }
}
