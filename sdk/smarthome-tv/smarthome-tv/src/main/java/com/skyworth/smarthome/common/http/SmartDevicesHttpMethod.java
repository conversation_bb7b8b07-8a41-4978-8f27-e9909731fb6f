package com.skyworth.smarthome.common.http;

import com.skyworth.smarthome.common.bean.SceneListBean;
import com.skyworth.smarthome.infrared.electriclist.model.DeviceBrandListData;
import com.skyworth.smarthome.infrared.electriclist.model.DeviceTypeListData;
import com.skyworth.smarthome.common.bean.AlarmLogBean;
import com.skyworth.smarthome.common.bean.DeviceControlData;
import com.skyworth.smarthome.common.bean.DevicePosBean;
import com.skyworth.smarthome.common.bean.GatewayDeviceBean;
import com.skyworth.smarthome.common.bean.IRAddDeviceData;
import com.skyworth.smarthome.common.bean.IRLearnKeyBean;
import com.skyworth.smarthome.common.bean.IRMatchKeyData;
import com.skyworth.smarthome.common.bean.JDQRBean;
import com.skyworth.smarthome.common.bean.SceneListWithConditionDevice;
import com.skyworth.smarthome.common.bean.ThridAccountHttpBean;
import com.smarthome.common.model.SmartBaseData;

import java.util.List;
import java.util.Map;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * Created by Eric on 2018/4/18.
 */

public interface SmartDevicesHttpMethod {


    @POST("smarthome/v1/device/control")
    Call<SmartBaseData<DeviceControlData>> deviceControl(@Query("appkey") String appkey,
                                                         @Query("time") String time,
                                                         @Query("uid") String uid,
                                                         @Query("ak") String ak,
                                                         @Query("vuid") String vuid,
                                                         @Query("sign") String sign,
                                                         @Body Map<String, Object> body);

    @POST("smarthome/v1/scene/control")
    Call<SmartBaseData> sceneControl(@Query("appkey") String appkey,
                                     @Query("time") String time,
                                     @Query("uid") String uid,
                                     @Query("ak") String ak,
                                     @Query("vuid") String vuid,
                                     @Query("sign") String sign,
                                     @Body Map<String, Object> body);

    @POST("devicelink/scene/user-scenes/set-enabled")
    Call<SmartBaseData> setSceneAutoSwitch(@Query("appkey") String appkey,
                                           @Query("time") String time,
                                           @Query("uid") String uid,
                                           @Query("ak") String ak,
                                           @Query("vuid") String vuid,
                                           @Query("sign") String sign,
                                           @Body Map<String, Object> body);


    @GET("smarthome/v1/third-account/list")
    Call<SmartBaseData<List<ThridAccountHttpBean>>> getThirdAccountList(@Query("appkey") String appkey,
                                                                        @Query("time") String time,
                                                                        @Query("uid") String uid,
                                                                        @Query("ak") String ak,
                                                                        @Query("vuid") String vuid,
                                                                        @Query("accept_account_types") String types,
                                                                        @Query("sign") String sign);

    @POST("smarthome/v1/third-account/unbind")
    Call<SmartBaseData> unbindThirdAccount(@Query("appkey") String appkey,
                                           @Query("time") String time,
                                           @Query("uid") String uid,
                                           @Query("ak") String ak,
                                           @Query("vuid") String vuid,
                                           @Query("sign") String sign,
                                           @Body Map<String, Object> body);


    @GET("smarthome/v1/device/shadow/query")
    Call<SmartBaseData<String>> getDeviceDetail(@Query("appkey") String appkey,
                                                @Query("time") String time,
                                                @Query("uid") String uid,
                                                @Query("ak") String ak,
                                                @Query("vuid") String vuid,
                                                @Query("product_brand_id") String product_brand_id,
                                                @Query("product_type_id") String product_type_id,
                                                @Query("product_model") String product_model,
                                                @Query("device_id") String device_id,
                                                @Query("sign") String sign);

    @POST("smarthome/v1/device/shadow/modify")
    Call<SmartBaseData> modefyDevice(@Query("appkey") String appkey,
                                     @Query("time") String time,
                                     @Query("uid") String uid,
                                     @Query("ak") String ak,
                                     @Query("vuid") String vuid,
                                     @Query("device_id") String device_id,
                                     @Query("sign") String sign,
                                     @Body Map<String, Object> body);

    @POST("smarthome/v1/device/info/update")
    Call<SmartBaseData> setDefaultTV(@Query("appkey") String appkey,
                                     @Query("time") String time,
                                     @Query("uid") String uid,
                                     @Query("ak") String ak,
                                     @Query("vuid") String vuid,
                                     @Query("sign") String sign,
                                     @Body Map<String, Object> body);

    @POST("smarthome/v1/device/bind")
    Call<SmartBaseData> bindDevice(@Query("appkey") String appkey,
                                   @Query("time") String time,
                                   @Query("uid") String uid,
                                   @Query("ak") String ak,
                                   @Query("vuid") String vuid,
                                   @Query("sign") String sign,
                                   @Body Map<String, Object> body);

    @POST("smarthome/v1/device/force-unbind-bind")
    Call<SmartBaseData> forceBindDevice(@Query("appkey") String appkey,
                                   @Query("time") String time,
                                   @Query("uid") String uid,
                                   @Query("ak") String ak,
                                   @Query("vuid") String vuid,
                                   @Query("sign") String sign,
                                   @Body Map<String, Object> body);

    @GET("devicemgr/v1/device/unbind")
    Call<SmartBaseData> unBindDevice(@Query("appkey") String appkey,
                                     @Query("time") String time,
                                     @Query("uid") String uid,
                                     @Query("ak") String ak,
                                     @Query("vuid") String vuid,
                                     @Query("device_id") String device_id,
                                     @Query("sign") String sign);

    @POST("smarthome/v1/device/force-unbind")
    Call<SmartBaseData> forceUnBindDevice(@Query("appkey") String appkey,
                                          @Query("time") String time,
                                          @Query("uid") String uid,
                                          @Query("ak") String ak,
                                          @Query("vuid") String vuid,
                                          @Query("sign") String sign,
                                          @Body Map<String, String> body);

    @POST("smarthome/v1/device/get-bindstatus")
    Call<SmartBaseData<List<Map<String, String>>>> getBindStatus(@Query("appkey") String appkey,
                                                                 @Query("time") String time,
                                                                 @Query("uid") String uid,
                                                                 @Query("ak") String ak,
                                                                 @Query("vuid") String vuid,
                                                                 @Query("sign") String sign,
                                                                 @Body List<Map<String, Object>> body);

    @POST("smarthome/v1/device/info/update")
    Call<SmartBaseData> updateDeviceInfo(@Query("appkey") String appkey,
                                         @Query("time") String time,
                                         @Query("uid") String uid,
                                         @Query("ak") String ak,
                                         @Query("vuid") String vuid,
                                         @Query("sign") String sign,
                                         @Body Map<String, Object> body);

    @GET("smarthome/v1/device/locations")
    Call<SmartBaseData<List<DevicePosBean>>> getDeviceLocations(@Query("appkey") String appkey,
                                                                @Query("time") String time,
                                                                @Query("uid") String uid,
                                                                @Query("ak") String ak,
                                                                @Query("vuid") String vuid,
                                                                @Query("sign") String sign);

    @POST("smarthome/v1/device/update-location")
    Call<SmartBaseData> setDeviceLocation(@Query("appkey") String appkey,
                                          @Query("time") String time,
                                          @Query("uid") String uid,
                                          @Query("ak") String ak,
                                          @Query("vuid") String vuid,
                                          @Query("device_id") String device_id,
                                          @Query("location") String location,
                                          @Query("sign") String sign);

    @POST("smarthome/v1/device/bind/midea")
    Call<SmartBaseData> bindMideaDevice(@Query("appkey") String appkey,
                                        @Query("time") String time,
                                        @Query("uid") String uid,
                                        @Query("ak") String ak,
                                        @Query("vuid") String vuid,
                                        @Query("sign") String sign,
                                        @Body Map<String, Object> body);

    @GET("smarthome/v1/third-account/midea-token")
    Call<SmartBaseData> getMideaToken(@Query("appkey") String appkey,
                                      @Query("time") String time,
                                      @Query("uid") String uid,
                                      @Query("ak") String ak,
                                      @Query("vuid") String vuid,
                                      @Query("sign") String sign);

    @GET("smarthome/v1/ircode/match-key")
    Call<SmartBaseData<List<IRMatchKeyData>>> irMatchKey(@Query("appkey") String appkey,
                                                         @Query("time") String time,
                                                         @Query("uid") String uid,
                                                         @Query("ak") String ak,
                                                         @Query("vuid") String vuid,
                                                         @Query("type_id") String type_id,
                                                         @Query("brand") String brand,
                                                         @Query("include_code_tables") String include_code_tables,
                                                         @Query("exclude_keys") String exclude_keys,
                                                         @Query("sign") String sign);

    @POST("smarthome/v1/ircode/send-code")
    Call<SmartBaseData> irSendCode(@Query("appkey") String appkey,
                                   @Query("time") String time,
                                   @Query("uid") String uid,
                                   @Query("ak") String ak,
                                   @Query("vuid") String vuid,
                                   @Query("sign") String sign,
                                   @Body Map<String, Object> body);

    @POST("smarthome/v1/ircode/irdevice-opt")
    Call<SmartBaseData<IRAddDeviceData>> irDeviceOpt(@Query("appkey") String appkey,
                                                     @Query("time") String time,
                                                     @Query("uid") String uid,
                                                     @Query("ak") String ak,
                                                     @Query("vuid") String vuid,
                                                     @Query("option") String option,
                                                     @Query("sign") String sign,
                                                     @Body Map<String, Object> body);

    @GET("smarthome/v1/ircode/device-type-list")
    Call<SmartBaseData<List<DeviceTypeListData>>> getDeviceTypeList(@Query("appkey") String appkey,
                                                                    @Query("time") String time,
                                                                    @Query("uid") String uid,
                                                                    @Query("ak") String ak,
                                                                    @Query("vuid") String vuid,
                                                                    @Query("sign") String sign);

    @GET("smarthome/v1/ircode/device-brand-list/v3")
    Call<SmartBaseData<List<DeviceBrandListData>>> getDeviceBrandList(@Query("appkey") String appkey,
                                                                      @Query("time") String time,
                                                                      @Query("uid") String uid,
                                                                      @Query("ak") String ak,
                                                                      @Query("vuid") String vuid,
                                                                      @Query("type_id") String type_id,
                                                                      @Query("sign") String sign);

    @GET("smarthome/v1/ircode/irdevice-list")
    Call<SmartBaseData<List<DeviceTypeListData>>> getIRDeviceList(@Query("appkey") String appkey,
                                                                  @Query("time") String time,
                                                                  @Query("uid") String uid,
                                                                  @Query("ak") String ak,
                                                                  @Query("vuid") String vuid,
                                                                  @Query("parent_id") String parent_id,
                                                                  @Query("page_index") String page_index,
                                                                  @Query("page_size") String page_size,
                                                                  @Query("sign") String sign);

    @GET("smarthome/v1/third-account/smartjd/get-qrcode")
    Call<SmartBaseData<JDQRBean>> getJDQR(@Query("appkey") String appkey,
                                          @Query("time") String time,
                                          @Query("uid") String uid,
                                          @Query("ak") String ak,
                                          @Query("vuid") String vuid,
                                          @Query("device_id") String device_id,
                                          @Query("sign") String sign);

    @POST("smarthome/v1/third-account/smartjd/abort-scan-qrcode")
    Call<SmartBaseData> cancelGetJDQR(@Query("appkey") String appkey,
                                      @Query("time") String time,
                                      @Query("uid") String uid,
                                      @Query("ak") String ak,
                                      @Query("vuid") String vuid,
                                      @Query("sign") String sign,
                                      @Body Map<String, String> body);

    @GET("smarthome/v1/ircode/key-list")
    Call<SmartBaseData<List<IRLearnKeyBean>>> getIRKeyList(@Query("appkey") String appkey,
                                                           @Query("time") String time,
                                                           @Query("uid") String uid,
                                                           @Query("ak") String ak,
                                                           @Query("vuid") String vuid,
                                                           @Query("type_id") String type_id,
                                                           @Query("is_common") String is_common,
                                                           @Query("sign") String sign);

    @POST("smarthome/v1/ircode/userkey-opt")
    Call<SmartBaseData> userKeyOpt(@Query("appkey") String appkey,
                                   @Query("time") String time,
                                   @Query("uid") String uid,
                                   @Query("ak") String ak,
                                   @Query("vuid") String vuid,
                                   @Query("option") String option,
                                   @Query("sign") String sign,
                                   @Body Map<String, Object> body);

    @GET("smarthome/v1/home/<USER>")
    Call<SmartBaseData<String>> getSmartHomeStatus(@Query("appkey") String appkey,
                                                   @Query("time") String time,
                                                   @Query("uid") String uid,
                                                   @Query("ak") String ak,
                                                   @Query("vuid") String vuid,
                                                   @Query("family_id") String family_id,
                                                   @Query("sign") String sign);

    @GET("devicemgr/v1/gatewaydevice/multiports/getname-full")
    Call<SmartBaseData<List<GatewayDeviceBean>>> getGatewayDevice(@Query("appkey") String appkey,
                                                                  @Query("time") String time,
                                                                  @Query("uid") String uid,
                                                                  @Query("ak") String ak,
                                                                  @Query("vuid") String vuid,
                                                                  @Query("grandpa_gatewayid") String grandpa_gatewayid,
                                                                  @Query("parent_did") String parent_did,
                                                                  @Query("sign") String sign);

    @POST("devicemgr/v1/gatewaydevice/option")
    Call<SmartBaseData<String>> operateGatewayDevice(@Query("appkey") String appkey,
                                                     @Query("time") String time,
                                                     @Query("uid") String uid,
                                                     @Query("ak") String ak,
                                                     @Query("vuid") String vuid,
                                                     @Query("option") String option,
                                                     @Query("sign") String sign,
                                                     @Body Map<String, Object> body);

    @GET("smarthome/v1/scene/list-with-condition-device")
    Call<SmartBaseData<List<SceneListWithConditionDevice>>> getSceneListWithConditionDevice(@Query("appkey") String appkey,
                                                                                            @Query("time") String time,
                                                                                            @Query("uid") String uid,
                                                                                            @Query("ak") String ak,
                                                                                            @Query("vuid") String vuid,
                                                                                            @Query("condition_device_id") String condition_device_id,
                                                                                            @Query("sign") String sign);

    @POST("iot/update-device-status")
    Call<SmartBaseData> updateDeviceStatus(@Query("appkey") String appkey,
                                           @Query("time") String time,
                                           @Query("uid") String uid,
                                           @Query("ak") String ak,
                                           @Query("vuid") String vuid,
                                           @Query("device_id") String option,
                                           @Query("sign") String sign,
                                           @Body Map<String, Object> body);

    @GET("v1/sensor/alarm-logs")
    Call<SmartBaseData<List<AlarmLogBean>>> getSensorAlarmLogsList(@Query("appkey") String appkey,
                                                                   @Query("time") String time,
                                                                   @Query("uid") String uid,
                                                                   @Query("ak") String ak,
                                                                   @Query("vuid") String vuid,
                                                                   @Query("gateway_id") String gateway_id,
                                                                   @Query("did") String did,
                                                                   @Query("page_index") String page_index,
                                                                   @Query("page_size") String page_size,
                                                                   @Query("sign") String sign);

    //查询用户场景列表
    @GET("https://devicelink.doubimeizhi.com/scene/user-scenes/list/v1")
    Call<SmartBaseData<List<SceneListBean>>> getSceneList(@Query("appkey") String appkey,
                                                    @Query("time") String time,
                                                    @Query("uid") String uid,
                                                    @Query("ak") String ak,
                                                    @Query("vuid") String vuid,
                                                    @Query("family_id") String family_id,
                                                    @Query("sign") String sign);


}
