package com.skyworth.smarthome.home.smartdevice.devicelist;

import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.alibaba.fastjson.JSONObject;
import com.bumptech.glide.Glide;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.util.SystemProperty;
import com.skyworth.smarthome.home.base.BaseSmartItemView;
import com.skyworth.ui.api.widget.CCFocusDrawable;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XThemeUtils;
import com.swaiot.aiotlib.common.entity.DeviceBean;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/6
 */
public abstract class BaseDeviceItemView extends BaseSmartItemView<DeviceInfo> {

    protected View mDeviceIcon;
    protected TextView mDeviceName;
    protected TextView mPosition;
    protected TextView mStatus;
    protected TextView mSwitch;
    protected View mNewPoint;
    protected TextView mLocalDevice;
    protected LinearLayout mNameLayout;
    private CCFocusDrawable mFocusBg;

    public BaseDeviceItemView(Context context) {
        super(context);
        mFocusBg = new CCFocusDrawable(context).setRadius(Util.Div(10)).setBorderVisible(false).setSolidColor(getContext().getResources().getColor(R.color.white_10));
        setBackground(mFocusBg);
        addIcon();
        addNameAndPosition();
        addStatus();
        addSwitch();
        addNewPoint();
        addLocalDevice();
    }

    private void addIcon() {
        mDeviceIcon = ImageLoader.getLoader().getView(getContext());
        LayoutParams params = new LayoutParams(Util.Div(100), Util.Div(100));
        params.topMargin = Util.Div(30) + FOCUS_W;
        params.leftMargin = Util.Div(30) + FOCUS_W;
        addView(mDeviceIcon, params);
    }

    private void addNameAndPosition() {
        mNameLayout = new LinearLayout(getContext());
        mNameLayout.setGravity(Gravity.CENTER_VERTICAL);
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.leftMargin = Util.Div(30) + FOCUS_W;
        params.topMargin = Util.Div(140) + FOCUS_W;
        addView(mNameLayout, params);
        mDeviceName = new TextView(getContext());
        mDeviceName.setTextColor(Color.WHITE);
        mDeviceName.setTextSize(Util.Dpi(32));
        mDeviceName.getPaint().setFakeBoldText(true);
        mDeviceName.setMaxWidth(Util.Div(245));
        mDeviceName.setEllipsize(TextUtils.TruncateAt.END);
        mDeviceName.setSingleLine();
        mNameLayout.addView(mDeviceName);

        LinearLayout.LayoutParams ll = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        ll.leftMargin = Util.Div(10);
        mPosition = new TextView(getContext());
        mPosition.setTextColor(Color.parseColor("#aaFFFFFF"));
        mPosition.setTextSize(Util.Dpi(20));
        mPosition.setPadding(Util.Div(15), Util.Div(7), Util.Div(15), Util.Div(7));
        mPosition.setBackground(XThemeUtils.getDrawable(getContext().getResources().getColor(R.color.white_10), 0, 0, Util.Div(12)));
        mNameLayout.addView(mPosition, ll);
    }

    private void addStatus() {
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.leftMargin = Util.Div(30) + FOCUS_W;
        params.topMargin = Util.Div(182) + FOCUS_W;
        mStatus = new TextView(getContext());
        mStatus.setTextColor(getContext().getResources().getColor(R.color.white_40));
        mStatus.setTextSize(Util.Dpi(24));
        addView(mStatus, params);
    }

    private void addSwitch() {
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.rightMargin = Util.Div(20) + FOCUS_W;
        params.topMargin = Util.Div(20) + FOCUS_W;
        params.gravity = Gravity.RIGHT;
        mSwitch = new TextView(getContext());
        mSwitch.setTextSize(Util.Dpi(28));
        mSwitch.getPaint().setFakeBoldText(true);
        addView(mSwitch, params);
    }

    private void addNewPoint() {
        mNewPoint = new View(getContext());
        mNewPoint.setBackground(XThemeUtils.getDrawable(Color.parseColor("#06CFB0"), 0, 0, Util.Div(6)));
        LayoutParams params = new LayoutParams(Util.Div(12), Util.Div(12));
        params.gravity = Gravity.END;
        params.topMargin = Util.Div(5) + FOCUS_W;
        params.rightMargin = Util.Div(5) + FOCUS_W;
        addView(mNewPoint, params);
        mNewPoint.setVisibility(GONE);
    }

    private void addLocalDevice() {
        mLocalDevice = new TextView(getContext());
        mLocalDevice.setText("本机");
        mLocalDevice.setTextColor(Color.WHITE);
        mLocalDevice.setGravity(Gravity.CENTER);
        mLocalDevice.setBackgroundResource(R.drawable.current_tv_device);
        LayoutParams params = new LayoutParams(Util.Div(70), Util.Div(36));
        params.gravity = Gravity.END;
        params.topMargin = Util.Div(30) + FOCUS_W;
        params.rightMargin = Util.Div(190) + FOCUS_W;
        addView(mLocalDevice, params);
        mLocalDevice.setVisibility(GONE);
    }

    @Override
    public void refreshUI(DeviceInfo data, int position) {
        if (data == null) {
            return;
        }
        try {
            mData = data;
            mDeviceName.setText(data.device_name);
            if (EmptyUtils.isNotEmpty(data.device_position)) {
                mPosition.setVisibility(VISIBLE);
                mPosition.setText(data.device_position);
            } else {
                mPosition.setVisibility(GONE);
            }
            if (data.is_new) {
                mNewPoint.setVisibility(VISIBLE);
            } else {
                mNewPoint.setVisibility(GONE);
            }
            if (data.device_id.equals(SystemProperty.getDeviceId())){
                mLocalDevice.setVisibility(VISIBLE);
            }else {
                mLocalDevice.setVisibility(GONE);
            }
            if (data.is_virtual) {
                mStatus.setText("虚拟设备");
                mSwitch.setText("运行中");
                mSwitch.setTextColor(Color.parseColor("#00DCBA"));
            } else if (data.is_unBind) {
                mStatus.setText("未绑定");
            } else if (data.is_infrared) {
                mDeviceName.setText("电视万能遥控");
                mStatus.setText("可控制传统电器");
            } else {
                if (isOnline()) {
                    if (data.acess_type == AppConstants.DEVICE_ACESS_TYPE_INFRARED) {
                        mStatus.setText("红外可控");
                        mSwitch.setText("");
                    } else {
                        mStatus.setText("在线");
                        mSwitch.setText("运行中");
                    }
                    mSwitch.setVisibility(VISIBLE);
                    mSwitch.setTextColor(Color.parseColor("#00DCBA"));

                } else {
                    mSwitch.setVisibility(GONE);
                    mStatus.setText("离线");
                }
            }
            setDeviceStatus();
            setDeviceIcon();
            onFocusChange(this, hasFocus());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void setDeviceStatus() {
        try {
            String on_off = getStatus(mData.status_show);
            if (isOnline() && EmptyUtils.isNotEmpty(on_off)) {
                mSwitch.setVisibility(VISIBLE);
                if (AppConstants.DEVICE_POW_STATUS_ON.equals(on_off)) {
                    setPause_Start(true);
                    if (mData.product_type_id.equals("82")) {
                        mSwitch.setText("已开启");
                    }
                } else {
                    if (EmptyUtils.isNotEmpty(mData.pasue_start)) {
                        setPause_Start(false);
                        if (mData.product_type_id.equals("82")) {
                            mSwitch.setText("关闭");
                        }
                    } else {
                        mSwitch.setText("关闭");
                    }
                }
            } else {
                if(isOnline()&&EmptyUtils.isEmpty(mData.pasue_start)){
                    //不做处理
                }else {
                    setPause_Start(false);
                }
            }
        } catch (Exception e) {
            mSwitch.setVisibility(GONE);
            e.printStackTrace();
        }
    }

    private void setPause_Start(boolean hasPow_S) {
        //有暂停启动状态
        String pasue_start = getStatus(mData.pasue_start);
        if (EmptyUtils.isNotEmpty(pasue_start)) {
            if (AppConstants.DEVICE_POW_STATUS_START.equals(pasue_start)) {
                mSwitch.setTextColor(Color.parseColor("#00DCBA"));
                if (mData.product_type_id.equals("84")) {
                    mSwitch.setText("展开");
                }else {
                    //鞋柜运行中改成已开启
                    mSwitch.setText("运行中");
                }
            } else {
                if (mData.product_type_id.equals("84")) {
                    mSwitch.setText("停止");
                } else {
                    //鞋柜暂停改成关闭
                    mSwitch.setText("暂停");
                }
            }
        } else {
            if (hasPow_S) {
                mSwitch.setTextColor(Color.parseColor("#00DCBA"));
                if (mData.product_type_id.equals("84")) {
                    mSwitch.setText("展开");
                }else {
                    mSwitch.setText("已开启");
                }
            } else {
                if(EmptyUtils.isNotEmpty(mData.status_show)){
                    if (mData.product_type_id.equals("84")) {
                        mSwitch.setText("收起");
                    }else {
                        mSwitch.setText("关闭");
                    }
                }else{
                    mSwitch.setVisibility(GONE);
                }
            }
        }
    }

    private String getStatus(DeviceBean.StatusShowBean statusShow) {
        if (EmptyUtils.isNotEmpty(statusShow) && EmptyUtils.isNotEmpty(mData.report_status)) {
            JSONObject reportStatus = JSONObject.parseObject(mData.report_status);
            JSONObject values = JSONObject.parseObject(statusShow.values);
            if (EmptyUtils.isNotEmpty(values)) {
                String dataField = reportStatus.getString(statusShow.data_field);
                return values.getString(dataField);
            }
        }
        return "";
    }

    private void setDeviceIcon() {
        if (EmptyUtils.isNotEmpty(mData.device_icon)) {
            mDeviceIcon.setBackground(null);
            if (isOnline() || hasFocus()) {
                if (mData.is_infrared) {
                    mDeviceIcon.setBackgroundResource(R.drawable.icon_infrared_device);
                } else {
                    //设备图标抖动问题是使用ImageLoader导致的
//                    ImageLoader.getLoader().with(getContext()).load(mData.device_icon).setScaleType(ImageView.ScaleType.FIT_XY).into(mDeviceIcon);
                    if (mDeviceIcon instanceof ImageView) {
                        Glide.with(getContext()).load(mData.device_icon).into((ImageView)mDeviceIcon);
                    }
                }
            } else {
                if (mData.is_infrared) {
                    mDeviceIcon.setBackgroundResource(R.drawable.icon_infrared_device);
                } else {
//                    ImageLoader.getLoader()
//                            .with(getContext())
//                            .load(mData.device_icon)
//                            .setScaleType(ImageView.ScaleType.FIT_XY)
//                            .into(mDeviceIcon);

                    if (mDeviceIcon instanceof ImageView) {
                        Glide.with(getContext()).load(mData.device_icon).into((ImageView)mDeviceIcon);
                    }

                }
            }
        }
    }

    private boolean isOnline() {
        return mData.online_status == 1;
    }

    private void setSwitchColor(boolean hasFocus) {
        String switchTxt = mSwitch.getText().toString().trim();
        if (mSwitch.getVisibility() == VISIBLE && ("关闭".equals(switchTxt) || "暂停".equals(switchTxt))) {
            if (hasFocus) {
                mSwitch.setTextColor(getContext().getResources().getColor(R.color.black_40));
            } else {
                mSwitch.setTextColor(getContext().getResources().getColor(R.color.white_40));
            }
        }
    }

    @Override
    public void onFocusChange(View view, boolean hasFocus) {
        if (hasFocus) {
            mDeviceName.setTextColor(Color.BLACK);
            mPosition.setTextColor(Color.BLACK);
            mStatus.setTextColor(Color.parseColor("#cc000000"));
            mPosition.setBackground(XThemeUtils.getDrawable(Color.parseColor("#19000000"), 0, 0, Util.Div(12)));
            if (mDeviceIcon.getBackground() != null) {
                mDeviceIcon.getBackground().setAlpha(255);
            }
        } else {
            if (isOnline()) {
                mDeviceName.setTextColor(Color.WHITE);
                mPosition.setTextColor(getContext().getResources().getColor(R.color.white_60));
                mStatus.setTextColor(getContext().getResources().getColor(R.color.white_40));
                mPosition.setBackground(XThemeUtils.getDrawable(getContext().getResources().getColor(R.color.white_10), 0, 0, Util.Div(12)));
                if (mDeviceIcon.getBackground() != null) {
                    mDeviceIcon.getBackground().setAlpha(255);
                }
            } else {
                mDeviceName.setTextColor(getContext().getResources().getColor(R.color.white_30));
                mPosition.setTextColor(getContext().getResources().getColor(R.color.white_15));
                mStatus.setTextColor(getContext().getResources().getColor(R.color.white_10));
                mPosition.setBackground(XThemeUtils.getDrawable(getContext().getResources().getColor(R.color.white_5), 0, 0, Util.Div(12)));
                if (mDeviceIcon.getBackground() != null) {
                    mDeviceIcon.getBackground().setAlpha(80);
                }
            }
        }
        mFocusBg.setBorderVisible(hasFocus).setSolidColor(getResources().getColor(hasFocus ? R.color.white : isOnline() ? R.color.white_10 : R.color.white_5));
        setSwitchColor(hasFocus);
        Util.focusAnimate(view, hasFocus);
    }

    @Override
    public void onDestroy() {
        mDeviceIcon.setBackground(null);
//        if (mData != null && EmptyUtils.isNotEmpty(mData.device_icon)) {
//            ImageLoader.getLoader().clearCacheFromMemory(mData.device_icon);
//        }
        mFocusBg = null;
    }
}
