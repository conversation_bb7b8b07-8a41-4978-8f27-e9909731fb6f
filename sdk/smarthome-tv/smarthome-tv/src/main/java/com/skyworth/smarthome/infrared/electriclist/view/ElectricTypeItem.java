package com.skyworth.smarthome.infrared.electriclist.view;

import android.content.Context;
import android.graphics.Color;
import android.net.Uri;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import android.support.annotation.NonNull;

import com.skyworth.smarthome.infrared.electriclist.model.DeviceTypeListData;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.ui.AlwaysMarqueeTextView;
import com.skyworth.smarthome.common.ui.CardBg;
import com.skyworth.ui.newrecycleview.NewRecycleAdapterItem;
import com.skyworth.util.Util;
import com.skyworth.util.imageloader.ImageLoader;
import com.smarthome.common.utils.EmptyUtils;
import com.smarthome.common.utils.XThemeUtils;

/**
 * Created by fc on 2019/4/24
 * Describe: 设备类型选择item
 */
public class ElectricTypeItem extends FrameLayout implements NewRecycleAdapterItem<DeviceTypeListData> {
    private AlwaysMarqueeTextView mNameView;
    private View mIconView;
    private TextView mDeleteTv;
    private Context mContext;
    private DeviceTypeListData mData;
    private FrameLayout mLayout;

    public ElectricTypeItem(@NonNull Context context) {
        super(context);
        mContext = context;
        setFocusable(true);
        setFocusableInTouchMode(true);
        initView();
    }

    private void initView() {
        mLayout = new FrameLayout(mContext);
        LayoutParams layoutParams = new LayoutParams(Util.Div(172), Util.Div(194));
        addView(mLayout, layoutParams);
        mLayout.setBackground(XThemeUtils.getDrawable(Color.parseColor("#19CCCCCC"), 0, 0, Util.Div(10)));

        mIconView = ImageLoader.getLoader().getView(mContext);
        LayoutParams iconParam = new LayoutParams(Util.Div(92), Util.Div(92));
        iconParam.topMargin = Util.Div(30);
        iconParam.gravity = Gravity.CENTER_HORIZONTAL;
        mLayout.addView(mIconView, iconParam);

        mNameView = new AlwaysMarqueeTextView(mContext);
        mNameView.setGravity(Gravity.CENTER_HORIZONTAL);
        LayoutParams lp = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        lp.topMargin = Util.Div(143);
        lp.leftMargin = Util.Div(10);
        lp.rightMargin = Util.Div(10);
        mNameView.setTextSize(Util.Dpi(24));
        mNameView.setTextColor(Color.parseColor("#FFCDD2D8"));
        mLayout.addView(mNameView, lp);

        mDeleteTv = new TextView(mContext);
        mDeleteTv.setGravity(Gravity.CENTER_HORIZONTAL);
        LayoutParams deleteLP = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, Util.Div(60));
        deleteLP.leftMargin = Util.Div(4);
        deleteLP.rightMargin = Util.Div(4);
        deleteLP.bottomMargin = Util.Div(4);
        deleteLP.gravity = Gravity.BOTTOM;
        mDeleteTv.setText("删除");
        mDeleteTv.setGravity(Gravity.CENTER);
        mDeleteTv.setTextSize(Util.Dpi(28));
        mDeleteTv.setTextColor(Color.parseColor("#FFFFFF"));
        mDeleteTv.setBackgroundResource(R.drawable.button_delete_bg);
        mDeleteTv.setVisibility(GONE);
        mLayout.addView(mDeleteTv, deleteLP);
    }

    @Override
    public View getView() {
        return this;
    }

    @Override
    public void onUpdateData(DeviceTypeListData data, int position) {
        mData = data;
        refreshView();
    }

    @Override
    public void clearItem() {
        if (mNameView != null)
            mNameView.setText("");

    }

    @Override
    public void refreshUI() {

    }

    public void onItemFocus(boolean hasFocus, DeviceTypeListData item) {
        mNameView.setSelected(hasFocus);
        if (hasFocus) {
            mNameView.setTextColor(Color.parseColor("#000000"));
            mLayout.setBackground(new CardBg());
            if (EmptyUtils.isNotEmpty(item)) {
                if (item.isShowDelete) {
                    mDeleteTv.setVisibility(VISIBLE);
                } else {
                    mDeleteTv.setVisibility(GONE);
                }
            }
        } else {
            mDeleteTv.setVisibility(GONE);
            mNameView.setTextColor(Color.parseColor("#FFCDD2D8"));
            mLayout.setBackground(XThemeUtils.getDrawable(Color.parseColor("#19CCCCCC"), 0, 0, Util.Div(10)));
        }
    }

    private void refreshView() {
        if (mData == null)
            return;
        mNameView.setText(mData.name);
        onItemFocus(isFocused(), mData);
        if (EmptyUtils.isNotEmpty(mData.icon_tv)) {
            ImageLoader.getLoader().with(mContext).
                    setScaleType(ImageView.ScaleType.FIT_CENTER)
                    .load(Uri.parse(mData.icon_tv)).into(mIconView);
        }
    }

    @Override
    public void destroy() {
        if (mNameView != null)
            mNameView.setText("");
        if (mData.icon_tv != null && !mData.icon_tv.equals("")) {
            ImageLoader.getLoader().clearCacheFromMemory(mData.icon_tv);
        }
    }
}
