package com.skyworth.smarthome.common.model;


import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.bean.WeathBean;
import com.smarthome.common.utils.EmptyUtils;
import com.swaiot.aiotlib.common.entity.DeviceBean;
import com.swaiot.aiotlib.common.entity.DiscoverNetworkDevice;
import com.swaiot.aiotlib.common.entity.DiscoverWifiDevice;
import com.swaiot.aiotlib.common.entity.FamilyBean;
import com.swaiot.aiotlib.common.entity.SceneBean;

import java.util.List;

/**
 * Describe:应用数据
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/1/9
 */
public class AppData {

    private static AppData instance = null;

    public static AppData getInstance() {
        if (instance == null) {
            synchronized (AppData.class) {
                if (instance == null) {
                    instance = new AppData();
                }
            }
        }
        return instance;
    }
    private List<DeviceInfo> deviceInfoList;
    private List<DeviceBean> deviceListFromServer;
    private List<FamilyBean> familyList;
    private List<SceneBean> sceneList;
    private FamilyBean currentFamily;
    private String screenId;//SID
    private boolean isServiceInit;//服务是否初始化

    //上报数据缓存
    private String pow_s; //开关机
    private String mute;  //静音
    private String volume;//音量

    private boolean isOpenSoftPan = false;
    private boolean isStartScanDevice = false;

    //保存家庭下的设备运行状态
    private String homeStatusData;

    //配网成功设备信息
    private String apconfigDeviceInfo;
    //配网的wifi设备
    private DiscoverWifiDevice apconfigWifiDevice;
    /**
     * 维家APP下载地址
     */
    private String vHomeDownloadUrl;

    public List<DeviceInfo> getDeviceInfoList() {
        return deviceInfoList;
    }

    public void setDeviceInfoList(List<DeviceInfo> deviceInfoList) {
        this.deviceInfoList = deviceInfoList;
    }

    public List<FamilyBean> getFamilyList() {
        return familyList;
    }

    public void setFamilyList(List<FamilyBean> familyList) {
        this.familyList = familyList;
    }

    public List<SceneBean> getSceneList() {
        return sceneList;
    }

    public void setSceneList(List<SceneBean> sceneList) {
        this.sceneList = sceneList;
    }

    public FamilyBean getCurrentFamily() {
        return currentFamily;
    }

    public void setCurrentFamily(FamilyBean currentFamily) {
        this.currentFamily = currentFamily;
    }

    public String getCurrentFamilyId() {
        if(EmptyUtils.isNotEmpty(currentFamily)){
            return currentFamily.family_id;
        }
        return "";
    }

    public String getCurrentFamilyName() {
        if(EmptyUtils.isNotEmpty(currentFamily)){
            return currentFamily.family_name;
        }
        return "暂无";
    }

    public void setVHomeDownloadUrl(String url) {
        vHomeDownloadUrl = url;
    }

    public String getVHomeDownloadUrl() {
        if (EmptyUtils.isNotEmpty(vHomeDownloadUrl)) {
            return vHomeDownloadUrl;
        } else {
            return "https://tvpi.coocaa.com/smarthome/index.html";
        }
    }

    public String getPow_s() {
        return pow_s;
    }

    public void setPow_s(String pow_s) {
        this.pow_s = pow_s;
    }

    public String getMute() {
        return mute;
    }

    public void setMute(String mute) {
        this.mute = mute;
    }

    public String getVolume() {
        return volume;
    }

    public void setVolume(String volume) {
        this.volume = volume;
    }

    public boolean isOpenSoftPan() {
        return isOpenSoftPan;
    }

    public void setOpenSoftPan(boolean openSoftPan) {
        isOpenSoftPan = openSoftPan;
    }

    public String getHomeStatusData() {
        return homeStatusData;
    }

    public void setHomeStatusData(String homeStatusData) {
        this.homeStatusData = homeStatusData;
    }

    public String getApconfigDeviceInfo() {
        return apconfigDeviceInfo;
    }

    public void setApconfigDeviceInfo(String apconfigDeviceInfo) {
        this.apconfigDeviceInfo = apconfigDeviceInfo;
    }

    public DiscoverWifiDevice getApconfigWifiDevice() {
        return apconfigWifiDevice;
    }

    public void setApconfigWifiDevice(DiscoverWifiDevice apconfigWifiDevice) {
        this.apconfigWifiDevice = apconfigWifiDevice;
    }

    public List<DeviceBean> getDeviceListFromServer() {
        return deviceListFromServer;
    }

    public void setDeviceListFromServer(List<DeviceBean> deviceListFromServer) {
        this.deviceListFromServer = deviceListFromServer;
    }

    public boolean isServiceInit() {
        return isServiceInit;
    }

    public void setServiceInit(boolean serviceInit) {
        isServiceInit = serviceInit;
    }

    /*********************** old ***************************/
    private WeathBean weathBean;
    /**
     * 发现未配网设备列表
     */
    private List<DiscoverWifiDevice> discoverUnConfigNetDeviceList;

    /**
     * 发现未绑定设备列表
     */
    private List<DiscoverNetworkDevice> discoverUnbindDeviceList;

    /**
     * 发现附近的设备列表
     */
    private List<DiscoverNetworkDevice> discoverNearbyDeviceList;

    /**
     * 局域网中的设备
     */
    private List<DiscoverNetworkDevice> discoverNetworkDevice;


    private String aiotHomeStatus;

    /**
     * 智能电视是否息屏
     */
    private boolean isCloseScreen = false;
    /**
     * 智能电视是否打开屏保
     */
    private boolean isOpenScreensaver = false;

    public List<DiscoverWifiDevice> getDiscoverUnConfigNetDeviceList() {
        return discoverUnConfigNetDeviceList;
    }

    public void setDiscoverUnConfigNetDeviceList(List<DiscoverWifiDevice> discoverUnConfigNetDeviceList) {
        this.discoverUnConfigNetDeviceList = discoverUnConfigNetDeviceList;
    }

    public List<DiscoverNetworkDevice> getDiscoverUnbindDeviceList() {
        return discoverUnbindDeviceList;
    }

    public void setDiscoverUnbindDeviceList(List<DiscoverNetworkDevice> discoverUnbindDeviceList) {
        this.discoverUnbindDeviceList = discoverUnbindDeviceList;
    }

    public List<DiscoverNetworkDevice> getDiscoverNearbyDeviceList() {
        return discoverNearbyDeviceList;
    }

    public void setDiscoverNearbyDeviceList(List<DiscoverNetworkDevice> discoverNearbyDeviceList) {
        this.discoverNearbyDeviceList = discoverNearbyDeviceList;
    }

    public boolean isCloseScreen() {
        return isCloseScreen;
    }

    public void setCloseScreen(boolean closeScreen) {
        isCloseScreen = closeScreen;
    }

    public boolean isOpenScreensaver() {
        return isOpenScreensaver;
    }

    public void setOpenScreensaver(boolean openScreensaver) {
        isOpenScreensaver = openScreensaver;
    }


    public String getAiotHomeStatus() {
        return aiotHomeStatus;
    }

    public void setAiotHomeStatus(String aiotHomeStatus) {
        this.aiotHomeStatus = aiotHomeStatus;
    }

    public List<DiscoverNetworkDevice> getDiscoverNetworkDevice() {
        return discoverNetworkDevice;
    }

    public void setDiscoverNetworkDevice(List<DiscoverNetworkDevice> discoverNetworkDevice) {
        this.discoverNetworkDevice = discoverNetworkDevice;
    }

    public String getScreenId() {
        return screenId;
    }

    public void setScreenId(String screenId) {
        this.screenId = screenId;
    }

    public boolean isStartScanDevice() {
        return isStartScanDevice;
    }

    public void setStartScanDevice(boolean startScanDevice) {
        isStartScanDevice = startScanDevice;
    }
}
