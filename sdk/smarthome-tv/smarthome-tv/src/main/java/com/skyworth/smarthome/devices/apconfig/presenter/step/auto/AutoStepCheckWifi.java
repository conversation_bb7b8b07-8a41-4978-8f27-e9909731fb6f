package com.skyworth.smarthome.devices.apconfig.presenter.step.auto;

import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.util.NetworkUtils;
import com.skyworth.smarthome.devices.apconfig.presenter.step.BaseStep;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/9/17 11:24.
 */
public class AutoStepCheckWifi extends BaseStep {
    public static final String STEP_TAG = "auto_check_wifi";

    @Override
    public void run() {
        super.run();
        if (NetworkUtils.isCurrentWifi24G(context) && !presenter.isCurrentWifiIsAp()) {
            next();
        } else {
            presenter.recordNotBindAndExit(AppConstants.APCONFIG_FAIL_REASON_WIFI_NOT_2_4G);
        }
    }

    @Override
    public boolean input(String msg, Object... params) {
        return false;
    }

    @Override
    public String getTag() {
        return STEP_TAG;
    }

    @Override
    public void destroy() {
        super.destroy();
    }
}
