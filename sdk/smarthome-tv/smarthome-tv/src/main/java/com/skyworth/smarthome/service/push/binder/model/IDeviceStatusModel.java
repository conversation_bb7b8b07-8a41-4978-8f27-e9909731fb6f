package com.skyworth.smarthome.service.push.binder.model;


import com.skyworth.smarthome.common.bean.MessageBean;

/**
 * Describe:
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/3/5
 */
public interface IDeviceStatusModel {
    /**
     * 展示智能设备核心信息变化提醒
     * @param data
     */
    void showDeviceNotify(String data);

    /**
     * 展示智能设备警告消息提醒
     * @param data
     */
    void showDeviceAlert(String data);

    /**
     * 展示消息弹框
     * @param messageBean
     */
    void showMessageDialog(MessageBean messageBean);

    /**
     * 推送设备在线状态事件
     * @param data
     */
    void sendAiotHomeStatusUpdateEvent(String data);


    /**
     * 更新设备的信息
     * @param data
     */
    void updateDeviceInfo(String data);

}
