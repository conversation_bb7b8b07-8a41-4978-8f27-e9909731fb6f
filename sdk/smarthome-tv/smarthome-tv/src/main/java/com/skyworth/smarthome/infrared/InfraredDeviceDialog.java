package com.skyworth.smarthome.infrared;

import android.view.Gravity;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.skyworth.smarthome.common.base.BaseSysDialog;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.event.CloseInfraredDialogEvent;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.util.DialogLauncherUtil;
import com.skyworth.smarthome.infrared.manager.IIREntry;
import com.skyworth.smarthome.infrared.manager.IREntry;
import com.skyworth.smarthome.service.model.ISmartDeviceDataModel;
import com.skyworth.util.Util;
import com.smarthome.common.utils.EmptyUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.HashMap;
import java.util.Map;

/**
 * Description: 红外设备弹框<br>
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/4/8 17:18.
 */
public class InfraredDeviceDialog extends BaseSysDialog {
    private IIREntry irEntry = null;
    private String device_id;

    @Override
    protected void initParams() {
        mDialogKey = DialogLauncherUtil.DIALOG_KEY_INFRARED_DIALOG;
        EventBus.getDefault().register(this);
    }

    @Override
    protected void initContentView() {
        super.initContentView();
    }

    @Override
    public void showDialog(Map<String, String> params) {
        super.showDialog(params);
        if (EmptyUtils.isNotEmpty(params)) {
            device_id = params.get(AppConstants.KEY_SELECT_ID);
        }
        irEntry = new IREntry(getContext(), new IREntry.IIRControl() {
            @Override
            public void exitIR() {
                if (irEntry != null) {
                    irEntry.onDestroy();
                }
                dismiss();
            }
        });
        ImageView bg = new ImageView(mContext);
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Util.Div(1920), Util.Div(1080));
        mContentView.addView(bg, layoutParams);

        layoutParams = new FrameLayout.LayoutParams(Util.Div(660), Util.Div(810));
        layoutParams.gravity = Gravity.CENTER;
        mContentView.addView(irEntry.getView(), layoutParams);
        DeviceInfo deviceInfo = ISmartDeviceDataModel.INSTANCE.getSmartDeviceInfo(device_id);
        Map<String, Object> param = new HashMap<>();
        param.put(IREntry.DEVICE_KEY, device_id);
        if(EmptyUtils.isNotEmpty(deviceInfo)){
            param.put(IREntry.DEVICE_NAME_KEY, deviceInfo.device_name);
            param.put(IREntry.DEVICE_TYPE_ID_KEY, deviceInfo.device_type_id);
        }
        if(EmptyUtils.isNotEmpty(params)){
            param.put(AppConstants.KEY_ENTER_INFRARED_ADD, params.get(AppConstants.KEY_ENTER_INFRARED_ADD));
        }
        irEntry.show(param);
        openAutoDismissDialog();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void Event(CloseInfraredDialogEvent event) {
        if (irEntry != null) {
            irEntry.onDestroy();
        }
        dismiss();
    }


    @Override
    protected void onDismiss() {
        super.onDismiss();
        EventBus.getDefault().unregister(this);
    }
}
