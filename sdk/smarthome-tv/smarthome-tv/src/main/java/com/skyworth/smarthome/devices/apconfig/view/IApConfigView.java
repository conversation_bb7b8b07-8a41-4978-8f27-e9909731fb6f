package com.skyworth.smarthome.devices.apconfig.view;

import com.skyworth.smarthome.devices.apconfig.model.SwitchWifiInfo;
import com.skyworth.smarthome.devices.apconfig.presenter.IApConfigPresenter;
import com.skyworth.smarthome.common.base.IView;
import com.skyworth.smarthome.common.bean.DevicePosBean;

import java.util.List;

public interface IApConfigView extends IView<IApConfigPresenter> {

    void showDialog();

    void closeDialog();

    void showDiscoveryDialog(String deviceName);

    void hideDiscoveryDialog();

    void showDisconnectEthernet();

    void hideDisconnectEthernet();

    void show24GWifiList(List<SwitchWifiInfo> wifiList);

    void refreshWifiLoading(SwitchWifiInfo wifiInfo);

    void hide24GWifiList();

    void show24GWifiList();

    void showConfiging(int percentage);

    void hideConfiging();

    void showInputPassword(String ssidName, String subTip);

    void hideInputPassword();

    void showBindingResult(boolean isSuccess);

    void hideBindingResult();

    void showConfigFailed(String deviceImg);

    void hideConfigFailed();

    void showToast(String txt);

    void showSetDevicePos(String deviceName, List<DevicePosBean> posBeans);

    void hideSetDevicePos();

    void showSmartRate(String deviceId);

    void hideSmartRate();

    void showNotLogin(String tag);

    void hideNotLogin();

    void showBindConfirm(String deviceType, String deviceImg);

    void hideBindConfirm();

    void showLoginAndBind(String deviceType, String deviceImg);

    void hideLoginAndBind();

    void disableDialogFocus();

    void enableDialogFocus();
}
