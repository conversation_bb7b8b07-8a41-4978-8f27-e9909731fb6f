package com.skyworth.smarthome.devices.apconfig.presenter.helpers;

import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coocaa.app.core.http.HttpServiceManager;
import com.coocaa.app.core.utils.FuncKt;
import com.skyworth.smarthome.devices.apconfig.presenter.step.auto.AutoStepBindDevice;
import com.skyworth.smarthome.devices.apconfig.presenter.step.auto.AutoStepConfirmBind;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.http.SmartDevicesHttpService;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.util.DataCacheUtil;
import com.skyworth.smarthome.devices.apconfig.ApConfigService;
import com.smarthome.common.model.SmartBaseData;
import com.swaiot.aiotlib.common.entity.DiscoverWifiDevice;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import retrofit2.Call;

import static com.skyworth.smarthome.devices.apconfig.ApConfigService.TAG;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/11/6 10:48.
 */
public class ConfirmBindHelper {
    private static final String SP_PRE = "UNCONFIRM@";
    private UnConfirmData unConfirmData = null;
    private static final String ON_LINE = "1";
    private static final String DEVICE_ID = "device_id";
    private static final long CONTINUE_DELAY_MS = 60 * 1000;

    public static class UnConfirmData implements Serializable {
        public static final int STATUS_NOT_LOGIN = 1;
        public static final int STATUS_NOT_BIND = 2;
        public String jsonString;
        public DiscoverWifiDevice deviceInfo;
        public int status = 0;

        public UnConfirmData() {

        }

        public UnConfirmData(String jsonString, DiscoverWifiDevice deviceInfo, int status) {
            this.jsonString = jsonString;
            this.deviceInfo = deviceInfo;
            this.status = status;
        }

        @Override
        public String toString() {
            return JSON.toJSONString(this);
        }
    }

    public void bootupCheckUnConfirm() {
        Log.i(TAG, "bootupCheckUnConfirm");
        getUnConfirmed();
        if (isNotLogin()) {
            Log.i(TAG, "bootupCheckUnConfirm: not login");
//            if (canDiscoverDevice()){
//                Log.i(TAG, "bootupCheckUnConfirm: can discover device");
//                continueLogin();
//            }
        } else if (isNotBind()) {
            Log.i(TAG, "bootupCheckUnConfirm: not bind");
            if (isDeviceOnLine()) {
                Log.i(TAG, "bootupCheckUnConfirm: continue confirm");
                continueConfirm();
            } else {
                Log.i(TAG, "bootupCheckUnConfirm: unbind device");
                unBindDevice();
            }
        }
        Log.i(TAG, "bootupCheckUnConfirm: check finish");
    }

    public void waitForConfirm(String json, DiscoverWifiDevice deviceInfo, int status) {
        Log.i(TAG, "waitForConfirm: " + json + " status: " + status);
        String deviceId = getValueFromJsonString(json, DEVICE_ID);
        String saveJson = new UnConfirmData(json, deviceInfo, status).toString();
        Log.i(TAG, "waitForConfirm: save json: " + saveJson);
        DataCacheUtil.getInstance().putString(getSpKey(deviceId), saveJson);
    }

    public void onConfirmed(String deviceId) {
        Log.i(TAG, "onConfirmed: " + deviceId);
        DataCacheUtil.getInstance().removeItem(getSpKey(deviceId));
    }

    private boolean isNotLogin() {
        return unConfirmData != null && unConfirmData.status == UnConfirmData.STATUS_NOT_LOGIN;
    }

    private boolean isNotBind() {
        return unConfirmData != null && unConfirmData.status == UnConfirmData.STATUS_NOT_BIND;
    }

    private void getUnConfirmed() {
        Map<String, ?> keys = DataCacheUtil.getInstance().getAll();
        if (keys == null) {
            return;
        }
        for (Map.Entry<String, ?> stringEntry : keys.entrySet()) {
            if (stringEntry.getKey().startsWith(SP_PRE)) {
                String saveData = (String) stringEntry.getValue();
                Log.i(TAG, "getUnConfirmed: " + saveData);
                unConfirmData = JSON.parseObject(saveData, UnConfirmData.class);
                break;
            }
        }
    }

    private boolean isDeviceOnLine() {
        if (unConfirmData == null) {
            return false;
        }
        List<DeviceInfo> deviceList = AppData.getInstance().getDeviceInfoList();
        if (deviceList == null ||deviceList.size() <= 0) {
            Log.e(TAG, "isDeviceOnLine: device list empty");
            return false;
        }
        String deviceId = getDeviceId();
        for (DeviceInfo deviceDetailData : deviceList) {
            if (deviceDetailData != null && deviceDetailData.device_id != null && deviceDetailData.device_id.equals(deviceId)) {
                return ON_LINE.equals(deviceDetailData.online_status);
            }
        }
        return false;
    }

    private String getDeviceId() {
        if (unConfirmData == null) {
            return null;
        }
        return getValueFromJsonString(unConfirmData.jsonString, DEVICE_ID);
    }

    private String getValueFromJsonString(String json, String key) {
        JSONObject jsonObject = JSON.parseObject(json);
        if (jsonObject == null) {
            return null;
        }
        return jsonObject.getString(key);
    }

    private void unBindDevice() {
        String deviceId = getDeviceId();
        if (TextUtils.isEmpty(deviceId)) {
            return;
        }
        Call<SmartBaseData> call = SmartDevicesHttpService.SERVICE.unBindDevice(deviceId);
        HttpServiceManager.Companion.call(call);
    }

    private void continueConfirm() {
        final Bundle params = new Bundle();
        params.putString(ApConfigService.EXTRA_KEY, unConfirmData.jsonString);
        params.putString(ApConfigService.TYPE_KEY, ApConfigService.TYPE_AUTO);
        params.putString(ApConfigService.DIRECT_TO_STEP_KEY, AutoStepConfirmBind.STEP_TAG);
        params.putString(ApConfigService.EXTRA_KEY_DEVICE_DETAIL, JSONObject.toJSONString(unConfirmData.deviceInfo));
        FuncKt.uiThread(CONTINUE_DELAY_MS, new Function0<Unit>() {
            @Override
            public Unit invoke() {
                ApConfigService.launch(params);
                onConfirmed(getDeviceId());
                return Unit.INSTANCE;
            }
        });
    }

    private void continueLogin() {
        final Bundle params = new Bundle();
        params.putString(ApConfigService.EXTRA_KEY, unConfirmData.jsonString);
        params.putString(ApConfigService.TYPE_KEY, ApConfigService.TYPE_AUTO);
        params.putString(ApConfigService.DIRECT_TO_STEP_KEY, AutoStepBindDevice.STEP_TAG);
        params.putString(ApConfigService.EXTRA_KEY_DEVICE_DETAIL, JSONObject.toJSONString(unConfirmData.deviceInfo));
        FuncKt.uiThread(CONTINUE_DELAY_MS, new Function0<Unit>() {
            @Override
            public Unit invoke() {
                ApConfigService.launch(params);
                onConfirmed(getDeviceId());
                return Unit.INSTANCE;
            }
        });
    }

    private List<String> localDiscoverDeviceId = new ArrayList<>();
//    private LocalDiscoveryCallback localDiscoveryCallback = new LocalDiscoveryCallback() {
//        @Override
//        public void success(org.json.JSONObject jsonObject) {
//            try {
//                if (jsonObject != null && jsonObject.has(DEVICE_ID)) {
//                    String deviceId = jsonObject.getString(DEVICE_ID);
//                    Log.i(TAG, "confirmHelper local discover: " + deviceId);
//                    localDiscoverDeviceId.add(deviceId);
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//
//        @Override
//        public void error(String s) {
//
//        }
//
//        @Override
//        public void timeout() {
//
//        }
//    };

//    private boolean canDiscoverDevice() {
//        API.getLocalDiscovery().addCallback(localDiscoveryCallback);
//        API.getLocalDiscovery().setSendIntervalTimeMS(500);
//        API.getLocalDiscovery().startListen();
//        try {
//            Thread.sleep(5000);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//        API.getLocalDiscovery().stopListen();
//        API.getLocalDiscovery().removeCallback(localDiscoveryCallback);
//        return localDiscoverDeviceId.contains(getDeviceId());
//    }

    private String getSpKey(String deviceId) {
        return SP_PRE + deviceId;
    }

    public void destroy() {
        unConfirmData = null;
    }
}
