package com.skyworth.smarthome.devices.apconfig.view;

import android.content.Context;
import android.graphics.Color;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;

import com.coocaa.app.core.app.AppCoreApplication;
import com.skyworth.smarthome.devices.apconfig.model.SwitchWifiInfo;
import com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl;
import com.skyworth.smarthome.devices.apconfig.presenter.IApConfigPresenter;
import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.base.BaseSysDialog;
import com.skyworth.smarthome.common.bean.DevicePosBean;
import com.skyworth.smarthome.common.util.DialogLauncherUtil;
import com.skyworth.smarthome.devices.apconfig.ApConfigDialog;
import com.skyworth.util.Util;
import com.smarthome.common.utils.XToast;

import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

import static android.view.View.GONE;
import static com.skyworth.smarthome.devices.apconfig.ApConfigService.TAG;

public class ApConfigViewImpl implements IApConfigView {
    private IApConfigPresenter mPresenter = null;
    private Context mContext = null;
    private FrameLayout mLayout = null;
    private TipDialogLayout discoveryDialog = null;
    private TipDialogLayout checkEthernetDialog = null;
    private SwitchWiFiView switchWiFiView = null;
    private ConfigNetworkView configNetworkView = null;
    private ConfigNetworkErrView configNetworkErrView = null;
    private BindingResultView bindingResultView = null;
    private SetDevicePosView setDevicePosView = null;
    private SmartRateMessageView smartRateMessageView = null;
    private TextWithButtonView mNotLoginTipView = null;
    private AddDeviceResultView mBindAndLoginView = null;
    private View bgLayout = null;
    private FrameLayout mask = null;
    private int currentSec = COUNT_DOWN_SEC;
    private static final int COUNT_DOWN_SEC = 30;

    private BaseSysDialog mDialog = null;
    private Handler mHandler = null;

    @Override
    public void create(Context context, IApConfigPresenter presenter) {
        mPresenter = presenter;
        mContext = context;
        mLayout = new FrameLayout(context);
        mHandler = new Handler();
        showBg();
    }


    private Runnable countDownRunnable = new Runnable() {

        @Override
        public void run() {
            if (currentSec == 0) {
                mLayout.removeCallbacks(countDownRunnable);
                currentSec = COUNT_DOWN_SEC;
                mPresenter.exitConfig();
                return;
            }
            Log.i(TAG, "run: " + currentSec);
            if (currentSec > 0) {
                currentSec--;
                mLayout.postDelayed(this, 1000);
            }
        }
    };

    @Override
    public void showDialog() {
        Log.i(TAG, "showDialog");
        DialogLauncherUtil.showAppConfig(null);
        if (mDialog == null) {
            setPresenter();
        }
    }

    private void setPresenter() {
        mHandler.post(new Runnable() {
            @Override
            public void run() {
                mDialog = DialogLauncherUtil.getDialog(DialogLauncherUtil.DIALOG_KEY_APCONFIG);
                Log.i(TAG, "setPresenter");
                if (mDialog == null) {
                    mHandler.postDelayed(this, 400);
                } else {
                    mDialog.setDialogContentView(mLayout);
                    if (mDialog instanceof ApConfigDialog) {
                        ((ApConfigDialog) mDialog).setPresenter(mPresenter);
                    }
                }
            }
        });
    }

    @Override
    public void closeDialog() {
        DialogLauncherUtil.dismissDialog(DialogLauncherUtil.DIALOG_KEY_APCONFIG);
    }

    @Override
    public void showDiscoveryDialog(String text) {
        if (discoveryDialog == null) {
            discoveryDialog = new TipDialogLayout(mContext, 2);
            discoveryDialog.setTip1Text(mContext.getString(R.string.apconfig_discover_device_name, text));
            discoveryDialog.setTip2Text(mContext.getString(R.string.apconfig_discover_device_tip));
            discoveryDialog.setButton1Text(mContext.getString(R.string.apconfig_discover_device_confirm));
            discoveryDialog.setButton2Text(mContext.getString(R.string.exit));
            discoveryDialog.setListener(new TipDialogLayout.TipDialogListener() {
                @Override
                public void onButton1() {
                    mLayout.removeCallbacks(countDownRunnable);
                    currentSec = COUNT_DOWN_SEC;
                    mPresenter.discoverDialogClick(true);
                }

                @Override
                public void onButton2() {
                    mLayout.removeCallbacks(countDownRunnable);
                    currentSec = COUNT_DOWN_SEC;
                    mPresenter.exitConfig();
                }

                @Override
                public void onFocusChange() {
                    currentSec = COUNT_DOWN_SEC;
                }
            });
            FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Util.Div(714), Util.Div(400));
            layoutParams.gravity = Gravity.BOTTOM;
            layoutParams.leftMargin = Util.Div(40);
            layoutParams.bottomMargin = Util.Div(40);
            mLayout.addView(discoveryDialog, layoutParams);
            //触摸弹框之外的部分关掉弹窗逻辑处理
            discoveryDialog.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                }
            });
        }
        discoveryDialog.setVisibility(View.VISIBLE);
        discoveryDialog.setFocus(0);
        mLayout.post(countDownRunnable);
        mDialog.openAutoDismissDialog();
    }

    @Override
    public void hideDiscoveryDialog() {
        if (discoveryDialog != null) {
            discoveryDialog.setVisibility(GONE);
        }
        mLayout.removeCallbacks(countDownRunnable);
        currentSec = COUNT_DOWN_SEC;
    }

    @Override
    public void showDisconnectEthernet() {
        if (checkEthernetDialog == null) {
            checkEthernetDialog = new TipDialogLayout(mContext, 1);
            checkEthernetDialog.setTip1Text(mContext.getString(R.string.apconfig_check_ethernet_use_wifi));
            checkEthernetDialog.setTip2Text(mContext.getString(R.string.apconfig_check_ethernet_tip));
            checkEthernetDialog.setButton1Text(mContext.getString(R.string.apconfig_check_ethernet_confirm));
            checkEthernetDialog.setListener(new TipDialogLayout.TipDialogListener() {
                @Override
                public void onButton1() {
                    mPresenter.checkEtherNetClick();
                }

                @Override
                public void onButton2() {

                }

                @Override
                public void onFocusChange() {

                }
            });
            FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Util.Div(714), Util.Div(400));
            layoutParams.gravity = Gravity.CENTER_HORIZONTAL|Gravity.CENTER_VERTICAL;
            mLayout.addView(checkEthernetDialog, layoutParams);
            //触摸弹框之外的部分关掉弹窗逻辑处理
            checkEthernetDialog.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                }
            });
        }
        checkEthernetDialog.setVisibility(View.VISIBLE);
        checkEthernetDialog.setFocus(0);
        mDialog.openAutoDismissDialog();
    }

    @Override
    public void hideDisconnectEthernet() {
        if (checkEthernetDialog != null) {
            checkEthernetDialog.setVisibility(GONE);
        }
    }

    @Override
    public void show24GWifiList(List<SwitchWifiInfo> wifiList) {
        if (switchWiFiView == null) {
            switchWiFiView = new SwitchWiFiView(mContext);
            switchWiFiView.setPresenter(mPresenter);
            FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Util.Div(660), Util.Div(810));
            layoutParams.gravity = Gravity.CENTER_HORIZONTAL|Gravity.CENTER_VERTICAL;
            mLayout.addView(switchWiFiView, layoutParams);
            //触摸弹框之外的部分关掉弹窗逻辑处理
            switchWiFiView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                }
            });
        }
        switchWiFiView.refreshUI(wifiList);
        switchWiFiView.setVisibility(View.VISIBLE);
        switchWiFiView.setFocus();
        mDialog.openAutoDismissDialog();
    }

    @Override
    public void hide24GWifiList() {
        if (switchWiFiView != null) {
            switchWiFiView.setVisibility(GONE);
        }
    }

    @Override
    public void show24GWifiList() {
        if (switchWiFiView != null) {
            switchWiFiView.setVisibility(View.VISIBLE);
            switchWiFiView.setFocus();
        }
    }

    @Override
    public void refreshWifiLoading(SwitchWifiInfo wifiInfo) {
        if (switchWiFiView != null) {
            switchWiFiView.showLoading(wifiInfo);
        }
    }

    @Override
    public void showConfiging(int percentage) {
        if (configNetworkView == null) {
            configNetworkView = new ConfigNetworkView(mContext);
            FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Util.Div(714), Util.Div(400));
            layoutParams.gravity = Gravity.CENTER_HORIZONTAL|Gravity.CENTER_VERTICAL;
            mLayout.addView(configNetworkView, layoutParams);
        }
        configNetworkView.setVisibility(View.VISIBLE);
        configNetworkView.refreshUI(percentage);
        mDialog.openAutoDismissDialog();
    }

    @Override
    public void hideConfiging() {
        if (configNetworkView != null) {
            configNetworkView.destroy();
            configNetworkView.setVisibility(GONE);
        }
    }

    @Override
    public void showInputPassword(String ssidName, String subTip) {
        hide24GWifiList();
        if (configNetworkErrView == null) {
            configNetworkErrView = new ConfigNetworkErrView(mContext);
            configNetworkErrView.setPresenter(mPresenter);
            FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            mLayout.addView(configNetworkErrView, layoutParams);
        }
        configNetworkErrView.setWifiName(ssidName);
        configNetworkErrView.connectIsError(!TextUtils.isEmpty(subTip));
        configNetworkErrView.setRedTipText(subTip);
        configNetworkErrView.setVisibility(View.VISIBLE);
        configNetworkErrView.setFocus();
        mDialog.openAutoDismissDialog();
    }

    @Override
    public void hideInputPassword() {
        if (configNetworkErrView != null) {
            configNetworkErrView.setVisibility(GONE);
        }
    }

    @Override
    public void showBindingResult(boolean isSuccess) {
        if (bindingResultView == null) {
            bindingResultView = new BindingResultView(mContext);
            bindingResultView.setBindResultCallBack(new BindingResultView.BindResultCallBack() {
                @Override
                public void onBindingResult(int status) {
                    switch (status) {
                        case BindingResultView.BIND_SUCCESS:
                            break;
                        case BindingResultView.BIND_FAILED:
                            mPresenter.goLogin();
                            hideBindingResult();
                            break;
                    }
                }
            });
            FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Util.Div(714), Util.Div(400), Gravity.CENTER);
            bindingResultView.setLayoutParams(layoutParams);
            //触摸弹框之外的部分关掉弹窗逻辑处理
            bindingResultView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                }
            });
        }
        bindingResultView.setBindingStatus(isSuccess ? BindingResultView.BIND_SUCCESS : BindingResultView.BIND_FAILED);
        bindingResultView.setVisibility(View.VISIBLE);
        bindingResultView.setFocus();
        showMask(bindingResultView);
        mDialog.openAutoDismissDialog();
    }

    @Override
    public void hideBindingResult() {
        if (bindingResultView != null) {
            bindingResultView.setVisibility(GONE);
            hideMask();
        }
    }

    @Override
    public void showConfigFailed(String deviceImg) {
        if (mBindAndLoginView == null) {
            addBindConfirmView();
        }
        mBindAndLoginView.setVisibility(View.VISIBLE);
        mBindAndLoginView.updateUI(AddDeviceResultView.TYPE_BIND_FAILED, mContext.getResources().getString(R.string.add_device_result_fail_title), deviceImg);
        mDialog.openAutoDismissDialog();
    }

    @Override
    public void hideConfigFailed() {
        if (mBindAndLoginView != null) {
            mBindAndLoginView.setVisibility(GONE);
            hideMask();
        }
    }

    private void showBg() {
        if (bgLayout == null) {
            bgLayout = new View(mContext);
            FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(Util.Div(1920), Util.Div(1080));
            mLayout.addView(bgLayout, 0, params);
        }
        bgLayout.setVisibility(View.VISIBLE);
    }

    private void hideBg() {
        if (bgLayout != null) {
            bgLayout.setVisibility(GONE);
        }
    }

    private void showMask(View content) {
        if (mask == null) {
            mask = new FrameLayout(mContext);
            mask.setBackgroundColor(Color.argb(179, 0, 0, 0));
            FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            mLayout.addView(mask, layoutParams);
        }
        mask.removeAllViews();
        mask.setVisibility(View.VISIBLE);
        mask.addView(content);
    }

    private void hideMask() {
        if (mask != null) {
            mask.setVisibility(GONE);
            mask.removeAllViews();
        }
    }

    @Override
    public void showToast(final String txt) {
        Log.i(TAG, "showToast: " + txt);
        AppCoreApplication.Companion.uiThread(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                XToast.showToast(mContext, txt);
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void showSetDevicePos(String deviceName, List<DevicePosBean> posBeans) {
        if (setDevicePosView == null) {
            setDevicePosView = new SetDevicePosView(mContext);
            setDevicePosView.setmPresenter(mPresenter);
            FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Util.Div(660), Util.Div(810));
            layoutParams.gravity = Gravity.CENTER_HORIZONTAL|Gravity.CENTER_VERTICAL;
            mLayout.addView(setDevicePosView, layoutParams);
            //触摸弹框之外的部分关掉弹窗逻辑处理
            setDevicePosView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                }
            });
        }
        setDevicePosView.show(deviceName, posBeans);
        mDialog.openAutoDismissDialog();
    }

    @Override
    public void hideSetDevicePos() {
        if (setDevicePosView != null) {
            setDevicePosView.setVisibility(GONE);
        }
    }

    @Override
    public void showSmartRate(String deviceId) {
        if (smartRateMessageView == null) {
            smartRateMessageView = new SmartRateMessageView(mContext);
            FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(Util.Div(480), Util.Div(140));
            params.topMargin = Util.Div(50);
            params.rightMargin = Util.Div(50);
            params.gravity = Gravity.RIGHT;
            mLayout.addView(smartRateMessageView, params);
        }
        smartRateMessageView.setDeviceId(deviceId);
        smartRateMessageView.updateUI();
        smartRateMessageView.setVisibility(View.VISIBLE);
        smartRateMessageView.postDelayed(new Runnable() {
            @Override
            public void run() {
                mPresenter.startCurrentDeviceDetail();
                mPresenter.exitConfig();
            }
        }, 3000);
    }

    @Override
    public void hideSmartRate() {
        if (smartRateMessageView != null) {
            smartRateMessageView.setVisibility(GONE);
        }
    }

    @Override
    public void showNotLogin(String tag) {
        if (mNotLoginTipView == null) {
            mNotLoginTipView = new TextWithButtonView(mContext, mPresenter);
            FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Util.Div(714), Util.Div(400));
            layoutParams.gravity = Gravity.CENTER_HORIZONTAL|Gravity.CENTER_VERTICAL;
            mLayout.addView(mNotLoginTipView, layoutParams);
            //触摸弹框之外的部分关掉弹窗逻辑处理
            mNotLoginTipView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                }
            });
        }
        String tip = "";
        String buttonTip = "";
        switch (tag) {
            case ApConfigPresenterImpl.STEP_MSG_INPUT_GO_LOGIN:
                tip = mContext.getResources().getString(R.string.apconfig_not_login_tip);
                buttonTip = mContext.getResources().getString(R.string.apconfig_not_login_button_tip);
                break;
            case ApConfigPresenterImpl.STEP_MSG_INPUT_GO_BIND_MOBILE:
                tip = mContext.getResources().getString(R.string.apconfig_not_bind_mobile_tip);
                buttonTip = mContext.getResources().getString(R.string.apconfig_not_bind_button_tip);
                break;
            case ApConfigPresenterImpl.STEP_MSG_INPUT_GO_BIND_MIDEA:
                tip = mContext.getResources().getString(R.string.apconfig_not_bind_midea_tip);
                buttonTip = mContext.getResources().getString(R.string.apconfig_not_bind_button_tip);
                break;
            case ApConfigPresenterImpl.STEP_MSG_INPUT_GO_GET_MIDEA_TOKEN:
                tip = mContext.getResources().getString(R.string.apconfig_not_bind_midea_token_out_of_date);
                buttonTip = mContext.getResources().getString(R.string.apconfig_not_bind_button_tip);
                break;
        }
        mNotLoginTipView.show(tip, buttonTip, tag);
        mNotLoginTipView.setVisibility(View.VISIBLE);
        mDialog.openAutoDismissDialog();
    }

    @Override
    public void hideNotLogin() {
        if (mNotLoginTipView != null) {
            mNotLoginTipView.setVisibility(GONE);
        }
    }

    @Override
    public void showBindConfirm(String deviceType, String deviceImg) {
        if (mBindAndLoginView == null) {
            addBindConfirmView();
        }
        mBindAndLoginView.setVisibility(View.VISIBLE);
        mBindAndLoginView.updateUI(AddDeviceResultView.TYPE_DISCOVER, deviceType, deviceImg);
        mDialog.openAutoDismissDialog();
    }

    @Override
    public void hideBindConfirm() {
        hideBindConfirmView();
    }

    @Override
    public void showLoginAndBind(String deviceType, String deviceImg) {
        mDialog.openAutoDismissDialog();
        if (mBindAndLoginView == null) {
            addBindConfirmView();
        }
        mBindAndLoginView.setVisibility(View.VISIBLE);
        mBindAndLoginView.updateUI(AddDeviceResultView.TYPE_DISCOVER_NOT_LOGIN, deviceType, deviceImg);
    }

    @Override
    public void hideLoginAndBind() {
        hideBindConfirmView();
    }

    @Override
    public void disableDialogFocus() {
        if (mDialog != null) {
            Window window = mDialog.getWindow();
            if (window == null) {
                return;
            }
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
            window.setAttributes(layoutParams);
        }
    }

    @Override
    public void enableDialogFocus() {
        if (mDialog != null) {
            Window window = mDialog.getWindow();
            if (window == null) {
                return;
            }
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.flags &= ~WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL;
            layoutParams.flags &= ~WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
            window.setAttributes(layoutParams);
        }
    }

    private void addBindConfirmView() {
        mBindAndLoginView = new AddDeviceResultView(mContext);
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(Util.Div(714), Util.Div(460));
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL|Gravity.CENTER_VERTICAL;
        mLayout.addView(mBindAndLoginView, layoutParams);
        mBindAndLoginView.setAddDeviceResultCallBack(new AddDeviceResultView.AddDeviceResultCallBack() {
            @Override
            public void onOkClick(int type) {
                switch (type) {
                    case AddDeviceResultView.TYPE_DISCOVER:
                        mPresenter.onBindConfirmClick(true);
                        break;
                    case AddDeviceResultView.TYPE_DISCOVER_NOT_LOGIN:
                        mPresenter.onLoginAndBindClick();
                        break;
                    case AddDeviceResultView.TYPE_BIND_FAILED:
                        mPresenter.reConfig();
                        break;
                }
            }

            @Override
            public void onCancelClick(int type) {
                switch (type) {
                    case AddDeviceResultView.TYPE_DISCOVER:
                        mPresenter.onBindConfirmClick(false);
                        break;
                    case AddDeviceResultView.TYPE_BIND_FAILED:
                        mPresenter.showDownloadPhoneApp(null);
                        break;
                }
            }

            @Override
            public void onHideView(int type) {
//屏蔽自动关闭
//                mPresenter.onDialogAutoDismiss(type);
//                mPresenter.exitConfig();
            }
        });
        //触摸弹框之外的部分关掉弹窗逻辑处理
        mBindAndLoginView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
            }
        });
    }

    private void hideBindConfirmView() {
        if (mBindAndLoginView != null) {
            mBindAndLoginView.setVisibility(GONE);
        }
    }

    @Override
    public View getView() {
        return mLayout;
    }

    @Override
    public void destroy() {
        configNetworkView = null;
        configNetworkErrView = null;
        countDownRunnable = null;
        currentSec = COUNT_DOWN_SEC;
    }
}
