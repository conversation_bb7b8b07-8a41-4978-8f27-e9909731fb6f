package com.skyworth.smarthome.voicehandle.model;

import android.content.Intent;

import com.skyworth.smarthome.voicehandle.SmartHomeAI;

/**
 * @ClassName: IVoiceHandleModel
 * @Author: Awen<PERSON>eng
 * @CreateDate: 2020/7/9 10:39
 * @Description:
 */
public interface IAIOTVoiceHandleModel {

    void registerVoiceHandleReceiver();

    void unRegisterVoiceHandleReceiver();
    /**
     * 语音低于8.0版本处理方式
     * @param intent
     */
    void handleCmd(Intent intent);
    /**
     * 语音8.0版本处理方式
     * @param data
     */
    void handleData(String data);
    /**
     * 控制TV具体控制指令
     * @param cData
     */
    void controlTV(SmartHomeAI.ReportedData cData);
    /**
     * 小维AI语音透传方法
     * @param data
     */
    void voiceCommandExecute(String data);
    /**
     * 设备列表同步
     */
    void deviceListForSync();
    /**
     * 播报语音内容
     * @param data
     */
    void playTTS(String data);
}
