package com.skyworth.smarthome.devices.apconfig.presenter.step.manual;

import com.skyworth.smarthome.common.util.NetworkUtils;
import com.skyworth.smarthome.devices.apconfig.model.ApconfigReportDataModel;
import com.skyworth.smarthome.devices.apconfig.presenter.step.BaseStep;

import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_ETHERNET_CONFIRM;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_ETHERNET_CONFIRM_HIDE;
import static com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl.STEP_MSG_ETHERNET_CONFIRM_SHOW;

/**
 * Description: <br>
 * Created by <PERSON><PERSON>ex<PERSON><PERSON> on 2019/1/7 21:54.
 */
public class ManualStepCheckEtherNet extends BaseStep {
    public static final String STEP_TAG = "manual_checkEtherNet";

    @Override
    public void run() {
        super.run();
        presenter.forgetConfiguredAp();
        ApconfigReportDataModel.reportNetworkType();
        if (NetworkUtils.isEthernetConnect(context)) {
            logi("ethernet connect!");
            output(STEP_MSG_ETHERNET_CONFIRM_SHOW);
        } else {
            logi("ethernet not connect!");
            next();
        }
    }

    @Override
    public boolean input(String msg, Object... params) {
        switch (msg) {
            case STEP_MSG_ETHERNET_CONFIRM:
                reload();
                return true;
        }
        return false;
    }

    @Override
    public String getTag() {
        return STEP_TAG;
    }

    @Override
    public void destroy() {
        output(STEP_MSG_ETHERNET_CONFIRM_HIDE);
        super.destroy();
    }

}
