package com.skyworth.smarthome.devices.apconfig.view;

import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.ViewGroup;
import android.view.animation.LinearInterpolator;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.skyworth.smarthome.R;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.ui.DialogBg;
import com.skyworth.smarthome.common.util.LogUtil;
import com.skyworth.smarthome.common.util.SpannableStringUtils;
import com.skyworth.smarthome.service.model.ISmartDeviceDataModel;
import com.skyworth.util.Util;
import com.smarthome.common.utils.EmptyUtils;

import java.util.List;

/**
 * Describe:智能化率View
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/3/21
 */
public class SmartRateMessageView extends LinearLayout {

    private Context mContext;

    private TextView mTitleTv;
    private TextView mContentTv;
    private ProgressBar mProgressBar;
    private String deviceId;
    private String titleStr;

    private int type = TYPE_NORMAL;
    public static final int TYPE_NORMAL = 0;//正常显示
    public static final int TYPE_INFRARED = 1;//红外设备


    public SmartRateMessageView(Context context) {
        super(context);
        type = TYPE_NORMAL;
        mContext = context;
        initUI();
    }

    public SmartRateMessageView(Context context, int type) {
        super(context);
        this.type = type;
        mContext = context;
        initUI();
    }

    private void initUI() {
        setOrientation(LinearLayout.VERTICAL);
        setBackground(new DialogBg());

        if (type == TYPE_INFRARED) {
            initInfraredView();
        } else {
            initNormalView();
        }
    }

    /**
     * 初始化正常View
     */
    private void initNormalView() {
        setPadding(Util.Div(40), 0, Util.Div(40), 0);
        mTitleTv = new TextView(mContext);
        mTitleTv.setTextSize(Util.Dpi(30));
        mTitleTv.setTextColor(Color.parseColor("#FFFFFF"));
        mTitleTv.setSingleLine(true);
        mTitleTv.setText(mContext.getString(R.string.smart_rate_tips02));
        LayoutParams contentParam = new LayoutParams(Util.Div(400), ViewGroup.LayoutParams.WRAP_CONTENT);
        contentParam.topMargin = Util.Div(20);
        addView(mTitleTv, contentParam);

        mProgressBar = new ProgressBar(mContext, null, android.R.attr.progressBarStyleHorizontal);
        mProgressBar.setBackgroundResource(R.drawable.smart_rate_progressbar_bg);
        mProgressBar.setProgressDrawable(getResources().getDrawable(R.drawable.smart_rate_progressbar));
        mProgressBar.setPadding(Util.Div(3), Util.Div(3), Util.Div(3), Util.Div(3));
        LayoutParams params = new LayoutParams(Util.Div(400), Util.Div(14));
        mProgressBar.setProgress(10);
        params.topMargin = Util.Div(12);
        addView(mProgressBar, params);
    }

    /**
     * 初始化红外View
     */
    private void initInfraredView() {
        mTitleTv = new TextView(mContext);
        mTitleTv.setTextSize(Util.Dpi(28));
        mTitleTv.setTextColor(Color.parseColor("#FFFFFF"));
        mTitleTv.setGravity(Gravity.CENTER_HORIZONTAL);
        mTitleTv.getPaint().setFakeBoldText(true);
        LayoutParams titleParam = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        titleParam.topMargin = Util.Div(36);
        addView(mTitleTv, titleParam);

        mContentTv = new TextView(mContext);
        mContentTv.setTextSize(Util.Dpi(28));
        mContentTv.setTextColor(Color.parseColor("#CCFFFFFF"));
        mContentTv.setText("");
        mContentTv.setGravity(Gravity.CENTER_HORIZONTAL);
        LayoutParams contentParam = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        contentParam.topMargin = Util.Div(16);
        addView(mContentTv, contentParam);
    }


    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public void setTitle(String title) {
        titleStr = title;
    }

    public void updateUI() {
        int smartRate = 0;
        List<DeviceInfo> deviceInfoList = ISmartDeviceDataModel.INSTANCE.getCacheSmartDeviceList();
        if (EmptyUtils.isNotEmpty(deviceInfoList)) {
            try {
                float total = 60;
                float size = 0;
                if (isHaveSmartDevice(deviceInfoList)) {
                    size =deviceInfoList.size();
                } else {
                    size = deviceInfoList.size() + 1;
                }
                if (total > 0) {
                    smartRate = Math.round((size / total) * 100);
                    if (smartRate > 100) {
                        smartRate = 100;
                    }
                    LogUtil.androidLog("智能化率为：" + smartRate + " total总：" + total + "列表数：" + size);
                    showSmartRateTip(smartRate, mContext.getString(R.string.smart_rate_tips01));
                } else {
                    showSmartRateTip(smartRate, (mContext.getString(R.string.smart_rate_tips02)));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            smartRate = Math.round((1 / 60) * 100);
            showSmartRateTip(smartRate, (mContext.getString(R.string.smart_rate_tips02)));
        }
    }

    private void showSmartRateTip(int smartRate, final String tips) {
        if (type == TYPE_INFRARED) {
            mContentTv.setText(SpannableStringUtils.getBuilder(tips).append(" " + smartRate + "%").setForegroundColor(Color.parseColor("#FFFFFF")).create());
            mTitleTv.setText(titleStr);
        } else if(smartRate > 0) {
            ValueAnimator smartRateAnimator = ValueAnimator.ofInt(0, smartRate);
            smartRateAnimator.setDuration(2000);
            smartRateAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                @Override
                public void onAnimationUpdate(ValueAnimator animation) {
                    int animationValue = (int) animation.getAnimatedValue();
                    LogUtil.androidLog("智能化率变化：" + animationValue);
                    mTitleTv.setText(SpannableStringUtils.getBuilder(tips).append(" " + animationValue + "%").setProportion(1.5f).create());
                    mProgressBar.setProgress(animationValue);
                }
            });
            smartRateAnimator.setInterpolator(new LinearInterpolator());
            smartRateAnimator.start();
        }
    }


    /**
     * 设备列表中是否由此设备
     *
     * @param devices
     * @return
     */
    private boolean isHaveSmartDevice(List<DeviceInfo> devices) {
        for (DeviceInfo item : devices) {
            if (item.device_id.equals(deviceId)) {
                return true;
            }
        }
        return false;
    }
}
