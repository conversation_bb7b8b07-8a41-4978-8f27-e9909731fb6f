package com.skyworth.smarthome.service.model;

import com.skyworth.smarthome.common.bean.DeviceListHttpBean;
import com.swaiot.aiotlib.common.base.IResult;
import com.swaiot.aiotlib.device.IDevice;

import java.util.Map;

/**
 * Created by <PERSON> on 2018/4/18.
 */

public interface IAIOTModel {

    AIOTModel INSTANCE = new AIOTModel();

    /**
     * 获取APP配置
     */
    void getAppConfig();
    /**
     * 获取设备列表数据
     * @return
     */
    void getDeviceList(String familyId);

    /**O
     * 获取场景列表数据
     * @return
     */
    void getSceneList();

    /**
     * 获取家庭列表
     * @return
     */
    void getFamilyList();


    /**
     * 获取家庭HomeStatus相关信息
     * @param familyId
     * @return
     */
    void getFamilyStatusData(String familyId);


    /**
     * 控制设备
     * @param device_id
     * @param status
     * @return
     */
    boolean controlDevice(String device_id, Map<String, String> status);

    /**
     * 控制场景
     * @param cmd
     * @param scene_id
     * @return
     */
    boolean controlScene(String cmd, String scene_id);

    /**
     * 取消新设备标志
     * @param deviceId
     * @param result
     */
    void cancelNewDeviceMark(String deviceId, IDevice.IControlResult result);

    /**
     * 取消新场景标志
     * @param deviceId
     * @param result
     */
    void cancelNewSceneMark(String deviceId, IResult result);


    /**
     * 设备列表数据转换
     * @return
     */
    DeviceListHttpBean transformDeviceList();

}
