package com.skyworth.smarthome.service.model;

import com.skyworth.smarthome.common.bean.VoiceCommandPushData;

public interface ISecondScreenModel {
    ISecondScreenModel INSTANCE = new SecondScreenModel();

    void sendNotifyMsg(String data);

    void sendAlertMsg(String data);

    void sendDeviceCountMsg(String data);

//    void queryDeviceListMsg(SecondScreenData data);
//
//    void sendSceneStartMsg(SecondScreenData data);

    void sendVoiceMsg(VoiceCommandPushData voiceCommandPushData);

    void sendDeviceStatusChangeMsg(String data);

}
