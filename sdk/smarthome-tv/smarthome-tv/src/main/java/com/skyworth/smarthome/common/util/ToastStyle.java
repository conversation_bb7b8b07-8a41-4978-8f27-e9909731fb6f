package com.skyworth.smarthome.common.util;

import android.content.Context;
import android.os.CountDownTimer;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import com.skyworth.smarthome.R;
import com.skyworth.util.Util;

/**
 * 作者：nicksong
 * 创建时间：2016/11/21
 * 功能描述:自定义toast样式、显示时长
 */

public class ToastStyle {

    private Toast mToast;
    private TimeCount timeCount;
    private boolean canceled = true;
    private TextView mTextView;

    public ToastStyle(Context context,String msg,int[] location) {
        //自定义布局
        View view = ViewsBuilder.getToastLayout(context);
        //自定义toast文本
        mTextView = view.findViewById(R.id.toast_content_id);
        mTextView.setText(msg);
        if (mToast == null)
            mToast = new Toast(context);
        mToast.setDuration(Toast.LENGTH_LONG);
        mToast.setGravity(Gravity.TOP | Gravity.START, location[0], location[1]-Util.Div(69));
        mToast.setView(view);
    }

    /**
     * 自定义居中显示toast
     */
    public void show() {
        mToast.show();
        Log.i("ToastUtil", "Toast show...");
    }

    /**
     * 自定义时长、居中显示toast
     * @param duration 单位毫秒ms
     */
    public void show(int duration) {

        timeCount = new TimeCount(duration, 1000);
        Log.i("ToastUtil", "Toast show...");
        if (canceled) {
            timeCount.start();
            canceled = false;
            showUntilCancel();
        }
    }

    /**
     * 隐藏toast
     */
    public void hide() {
        if (timeCount != null) {
            timeCount.cancel();
        }

        if (mToast != null) {
            mToast.cancel();
        }
        canceled = true;
        Log.i("ToastUtil", "Toast that customed duration hide...");
    }

    private void showUntilCancel() {
        if (canceled) { //如果已经取消显示，就直接return
            return;
        }
        Log.i("ToastUtil", "Toast showUntilCancel------...");
        mToast.show();
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                Log.i("ToastUtil", "Toast showUntilCancel...");

                showUntilCancel();
            }
        },1000);
    }

    /**
     *  自定义计时器
     */
    private class TimeCount extends CountDownTimer {

        public TimeCount(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval); //millisInFuture总计时长，countDownInterval时间间隔(一般为1000ms)
        }

        @Override
        public void onTick(long millisUntilFinished) {
//            mTextView.setText(message + ": " + millisUntilFinished / 1000 + "s后消失");
        }

        @Override
        public void onFinish() {
            hide();
        }
    }


}
