package com.skyworth.smarthome.personal;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;


import com.skyworth.smarthome.common.base.mvp.BasePresenter;
import com.skyworth.smarthome.common.event.AccountChangeEvent;
import com.skyworth.smarthome.common.model.AppConstants;
import com.skyworth.smarthome.common.model.UserInfo;
import com.skyworth.smarthome.common.util.DataCacheUtil;
import com.skyworth.smarthome.common.util.ThreadManager;
import com.skyworth.smarthome.service.model.ISmartDeviceDataModel;
import com.skyworth.smarthome.service.model.ISmartHomeModel;
import com.skyworth.smarthome.service.push.local.DeviceDataPushUtil;
import com.skyworth.smarthome.service.push.local.IHandlerPush;
import com.skyworth.smarthome.account.AppAccountManager;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * @ProjectName: NewTV_SmartHome
 * @Package: com.skyworth.smarthome_tv.personal.unbinddevice
 * @ClassName: UnBindPresenter
 * @Description: java类作用描述
 * @Author: wangyuehui
 * @CreateDate: 2020/6/5 19:02
 * @UpdateUser: 更新者
 * @UpdateDate: 2020/6/5 19:02
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class PersonCenterPresenter extends BasePresenter<PersonCenterContract.View> implements PersonCenterContract.Presenter {

    private static final String TAG = PersonCenterPresenter.class.getSimpleName();
    private IHandlerPush.IPushListener iPushListener = new IHandlerPush.IPushListener() {

        @Override
        public void onArrive(AppConstants.SSE_PUSH event, String data) {
            Log.i(TAG, "PersonCenterPresenter iPushListener: ----------event:" + event);
            //数据变化的回调
            if (event == AppConstants.SSE_PUSH.DEVICE_LIST) {//设备列表变化
                if (getView() != null && getView().isActive()) {
                    getView().queryBindDevices(ISmartDeviceDataModel.INSTANCE.getCacheSmartDeviceList());
                }
            }
        }
    };

    public PersonCenterPresenter(PersonCenterContract.View view) {
        attachView(view);
        view.setPresenter(this);
    }

    @Override
    public void init(Context context) {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
        if (iPushListener != null) {
            DeviceDataPushUtil.getPush().regReceiver(iPushListener);
        }
    }

    @Override
    public void detachView() {
        super.detachView();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        if (iPushListener != null) {
            DeviceDataPushUtil.getPush().unRegReceiver(iPushListener);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void Event(AccountChangeEvent event) {
        Log.d(TAG, "------event:" + event);
        if (TextUtils.isEmpty(UserInfo.getInstance().getToken())) {
            if (getView() != null && getView().isActive()) {
                getView().finish();
            }
        }
    }

    @Override
    public void logout() {
        ThreadManager.getInstance().ioThread(new Runnable() {
            @Override
            public void run() {
                //退出用户账号
                AppAccountManager.INSTANCE.logout();
            }
        });

    }

    @Override
    public void autoFindDevice(boolean autoFindDeviceStutus) {
        DataCacheUtil.getInstance().putBoolean(DataCacheUtil.KEY_GET_AUTO_FIND_DEVICE_STATUS, autoFindDeviceStutus);
    }

    @Override
    public void queryDevices(final String familyId) {
        ISmartHomeModel.INSTANCE.setCurrentFamily(familyId);
    }
}
