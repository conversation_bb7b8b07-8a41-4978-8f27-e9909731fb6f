package com.skyworth.smarthome.devices.apconfig.presenter.step;

import android.text.TextUtils;

import com.skyworth.smarthome.devices.apconfig.presenter.step.auto.AutoStepBindDevice;
import com.skyworth.smarthome.devices.apconfig.presenter.step.auto.AutoStepCheckEtherNet;
import com.skyworth.smarthome.devices.apconfig.presenter.step.auto.AutoStepCheckMideaStep;
import com.skyworth.smarthome.devices.apconfig.presenter.step.auto.AutoStepCheckWifi;
import com.skyworth.smarthome.devices.apconfig.presenter.step.auto.AutoStepConfiging;
import com.skyworth.smarthome.devices.apconfig.presenter.step.auto.AutoStepConfirmBind;
import com.skyworth.smarthome.devices.apconfig.presenter.step.manual.ManualCheckMideaStep;
import com.skyworth.smarthome.devices.apconfig.presenter.step.manual.ManualStepBindDevice;
import com.skyworth.smarthome.devices.apconfig.presenter.step.manual.ManualStepCheckEtherNet;
import com.skyworth.smarthome.devices.apconfig.presenter.step.manual.ManualStepCheckWifi;
import com.skyworth.smarthome.devices.apconfig.presenter.step.manual.ManualStepConfigFailed;
import com.skyworth.smarthome.devices.apconfig.presenter.step.manual.ManualStepConfiging;
import com.skyworth.smarthome.devices.apconfig.presenter.step.manual.ManualStepDiscoverNew;
import com.skyworth.smarthome.devices.apconfig.ApConfigService;
import com.skyworth.smarthome.devices.apconfig.presenter.stepmanager.BaseStepManager;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/9/17 10:53.
 */
public class ApConfigStepFactory {
    public static void addSteps(BaseStepManager manager, String type, String[] extraFlag) {
        if (TextUtils.isEmpty(type)) {
            return;
        }
        manager.clear();
        switch (type) {
            case ApConfigService.TYPE_AUTO:
                manager.addStep(new AutoStepCheckMideaStep());
                manager.addStep(new AutoStepCheckEtherNet());
                manager.addStep(new AutoStepCheckWifi());
                manager.addStep(new AutoStepConfiging());
                manager.addStep(new AutoStepBindDevice());
                manager.addStep(new AutoStepConfirmBind());
                break;
            case ApConfigService.TYPE_MANUAL:
                manager.addStep(new ManualCheckMideaStep());
                manager.addStep(new ManualStepCheckEtherNet());
                manager.addStep(new ManualStepCheckWifi());
                manager.addStep(new ManualStepConfiging());
                manager.addStep(new ManualStepBindDevice());
                manager.addStep(new ManualStepConfigFailed());
                break;
        }
        if (hasFlag(extraFlag, ApConfigService.EXTRA_FLAG_SHOW_DISCOVER)) {
            manager.addStepAt(0, new ManualStepDiscoverNew());
        }
    }

    private static boolean hasFlag(String[] flags, String target) {
        if (flags == null || target == null) {
            return false;
        }
        for (String flag : flags) {
            if (flag.equals(target)) {
                return true;
            }
        }
        return false;
    }
}
