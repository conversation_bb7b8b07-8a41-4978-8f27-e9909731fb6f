package com.skyworth.smarthome.home.smartdevice.controlpanel.thirdapp;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.alibaba.fastjson.JSON;
import com.skyworth.smarthome.home.smartdevice.controlpanel.thirdapp.appmanager.AppManager;
import com.skyworth.smarthome.home.smartdevice.controlpanel.thirdapp.appmanager.IUpdateListener;
import com.skyworth.smarthome.common.model.UserInfo;
import com.skyworth.smarthome.common.util.DialogLauncherUtil;
import com.skyworth.smarthome.common.util.SystemProperty;
import com.skyworth.smarthome.common.util.Utils;
import com.skyworth.smarthome.third.IThirdLaunch;
import com.skyworth.smarthome.third.IThirdLaunchHostCallback;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

/**
 * @ClassName: ThirdAppLaunchManager
 * @Author: XuZeXiao
 * @CreateDate: 2019-12-19 16:32
 * @Description:
 */
public class ThirdAppLaunchManager {
    public static final String ACTION = "skyworth.intent.action.smarthome.third.launch";
    public static final String TAG = "ThirdAppLaunchManager";
    private static ThirdAppLaunchManager instance = null;
    private String mPkgName;
    private Map<String, ThirdRemote> remotes = new HashMap<>();
    private final List<String> preparingPackages = new ArrayList<>();
    private static final String GET_INFO_USER_INFO = "GET_INFO_USER_INFO";
    public static final String GET_INFO_IS_AI_STAND_BY = "GET_INFO_IS_AI_STAND_BY";
    public static final String GET_INFO_DEVICE_ID = "GET_INFO_DEVICE_ID";

    private ThirdAppLaunchManager() {
    }

    public static ThirdAppLaunchManager getInstance() {
        if (instance == null) {
            synchronized (ThirdAppLaunchManager.class) {
                if (instance == null) {
                    instance = new ThirdAppLaunchManager();
                }
            }
        }
        return instance;
    }

    private IUpdateListener downloadUpdateListener = new IUpdateListener() {
        final Map<String, String> showParams = new HashMap<>(1);

        @Override
        public void onDownloadStart() {

        }

        @Override
        public void onDownloadProcess(int process) {
            if (DialogLauncherUtil.isShow(DialogLauncherUtil.DIALOG_KEY_THIRD_LOADING)) {
                showParams.put("tip", process + "%");
                DialogLauncherUtil.showThirdAppLoadingDialog(showParams);
            }
        }

        @Override
        public void onDownloadEnd(boolean res) {
        }

        @Override
        public void onInstallStart() {

        }

        @Override
        public void onInstallEnd(boolean res, String reason) {
            if (DialogLauncherUtil.isShow(DialogLauncherUtil.DIALOG_KEY_THIRD_LOADING)) {
                clearMarkPreparing(mPkgName);
                DialogLauncherUtil.dismissDialog(DialogLauncherUtil.DIALOG_KEY_THIRD_LOADING);
            }
        }
    };

    private IThirdLaunchHostCallback callback = new IThirdLaunchHostCallback.Stub() {
        @Override
        public String getInfo(String params) throws RemoteException {
            Log.i(TAG, "getInfo: " + params);
            if (GET_INFO_USER_INFO.equals(params)) {
                return JSON.toJSONString(UserInfo.getInstance());
            } else if (GET_INFO_IS_AI_STAND_BY.equals(params)) {
                return String.valueOf(Utils.isAistandbymode());
            } else if (GET_INFO_DEVICE_ID.equals(params)) {
                return SystemProperty.getDeviceId();
            }
            return null;
        }

        @Override
        public boolean controlDevice(String deviceId, Map params) throws RemoteException {
            Log.i(TAG, "controlDevice: " + deviceId);
            return false;
        }

        @Override
        public void exit(String params) throws RemoteException {
            Log.i(TAG, "exit: " + params);
        }
    };

    public boolean handleStartThirdApp(Context context, String packageName, String action) throws RemoteException {
        Log.i(TAG, "handleStartThirdApp: " + packageName);
        if (TextUtils.isEmpty(packageName)) {
            Log.e(TAG, "handleStartThirdApp: packageName is empty");
            return false;
        }
        mPkgName = packageName;
        boolean isReady = false;
        synchronized (this) {
            if (isPreparing(packageName)) {
                Log.i(TAG, "handleStartThirdApp: package is preparing, return");
                DialogLauncherUtil.showThirdAppLoadingDialog(null);
                return false;
            }
            isReady = isApkReady(context, packageName);
            if (isReady) {
                clearMarkPreparing(packageName);
            } else {
                markPreparing(packageName);
                DialogLauncherUtil.showThirdAppLoadingDialog(null);
            }
        }
        if (!isReady) {
            Log.i(TAG, "handleStartThirdApp: downloadAndInstall");
            boolean installResult = AppManager.downloadAndInstall(context, packageName, downloadUpdateListener);
            Log.i(TAG, "handleStartThirdApp: result: " + installResult);
            clearMarkPreparing(packageName);
            DialogLauncherUtil.dismissDialog(DialogLauncherUtil.DIALOG_KEY_THIRD_LOADING);
            if (!installResult) {
                return false;
            }
        }
        IThirdLaunch remote = getRemote(context, packageName);
        if (remote == null) {
            Log.e(TAG, "handleStartThirdApp: get remote fail");
            return false;
        }
        boolean result = false;
        result = remote.start(action);
        Log.i(TAG, "handleStartThirdApp start success? " + result);
        return result;
    }

    private IThirdLaunch getRemote(Context context, String packageName) {
        if (remotes.containsKey(packageName)) {
            Log.i(TAG, "getRemote hit cache: " + packageName);
            return remotes.get(packageName).remote;
        }
        connectToThird(context, packageName);
        return remotes.get(packageName).remote;
    }

    private void markPreparing(String packageName) {
        synchronized (preparingPackages) {
            if (!preparingPackages.contains(packageName)) {
                preparingPackages.add(packageName);
            }
        }
    }

    private void clearMarkPreparing(String packageName) {
        synchronized (preparingPackages) {
            preparingPackages.remove(packageName);
        }
    }

    private boolean isPreparing(String packageName) {
        synchronized (preparingPackages) {
            return preparingPackages.contains(packageName);
        }
    }

    private void connectToThird(final Context context, final String packageName) {
        final CountDownLatch countDownLatch = new CountDownLatch(1);
        boolean bindResult = context.bindService(createIntent(packageName), new ServiceConnection() {
            @Override
            public void onServiceConnected(ComponentName name, IBinder service) {
                Log.i(TAG, "onServiceConnected: " + packageName);
                IThirdLaunch remote = IThirdLaunch.Stub.asInterface(service);
                if (remote == null) {
                    return;
                }
                initRemote(packageName, remote, this);
                countDownLatch.countDown();
            }

            @Override
            public void onServiceDisconnected(ComponentName name) {
                Log.i(TAG, "onServiceDisconnected: " + packageName);
                if (remotes.containsKey(packageName)) {
                    remotes.get(packageName).unBind(context);
                    remotes.remove(packageName);
                }
            }
        }, Context.BIND_AUTO_CREATE);
        Log.i(TAG, "connectToThird: result: " + bindResult);
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    private void initRemote(String packageName, IThirdLaunch remote, ServiceConnection connection) {
        remotes.put(packageName, new ThirdRemote(packageName, remote, connection));
        try {
            remote.setCallback(callback);
            remote.init();
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public void onEvent(String type, Map<String, String> params) {
        if (remotes == null || remotes.size() <= 0) {
            return;
        }
        for (ThirdRemote value : remotes.values()) {
            if (value.remote == null) {
                continue;
            }
            try {
                value.remote.onEvent(type, params);
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    private Intent createIntent(String packageName) {
        Intent intent = new Intent();
        intent.setPackage(packageName);
        intent.setAction(ACTION);
        return intent;
    }

    private boolean isApkReady(Context context, String packageName) {
        boolean result = !AppManager.checkUpdate(context, packageName);
        Log.i(TAG, "isApkReady: " + result);
        return result;
    }

    public void destroy(Context context) {
        if (!remotes.isEmpty()) {
            for (ThirdRemote value : remotes.values()) {
                value.unBind(context);
            }
            remotes.clear();
        }
    }
}
