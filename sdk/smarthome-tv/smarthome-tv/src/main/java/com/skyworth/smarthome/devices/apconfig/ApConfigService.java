package com.skyworth.smarthome.devices.apconfig;

import android.app.Service;
import android.content.Intent;
import android.os.Bundle;
import android.os.IBinder;
import android.util.Log;

import com.skyworth.smarthome.devices.apconfig.model.ApConfigModelImpl;
import com.skyworth.smarthome.devices.apconfig.model.IApConfigModel;
import com.skyworth.smarthome.devices.apconfig.model.StartApConfigModel;
import com.skyworth.smarthome.devices.apconfig.presenter.ApConfigPresenterImpl;
import com.skyworth.smarthome.devices.apconfig.presenter.IApConfigPresenter;
import com.skyworth.smarthome.devices.apconfig.view.ApConfigViewImpl;
import com.skyworth.smarthome.devices.apconfig.view.IApConfigView;
import com.skyworth.smarthome.SmartHomeTvLib;

/**
 * Description: <br>
 * Created by XuZexiao on 2019/9/18 14:51.
 */
public class ApConfigService extends Service {
    public static final String TAG = "apconfig";

    private IApConfigPresenter presenter;
    private IApConfigView view;
    private IApConfigModel model;

    public static final String EXTRA_KEY = "DEVICE_KEY";
    public static final String EXTRA_KEY_DEVICE_DETAIL = "EXTRA_KEY_DEVICE_DETAIL";
    public static final String DIRECT_TO_STEP_KEY = "DIRECT_TO_STEP";
    public static final String TYPE_KEY = "TYPE_KEY";
    public static final String TYPE_AUTO = "TYPE_AUTO";
    public static final String TYPE_MANUAL = "TYPE_MANUAL";
    public static final String EXTRA_FLAG_KEY = "EXTRA_FLAG_KEY";
    public static final String EXTRA_FLAG_SHOW_DISCOVER = "EXTRA_FLAG_SHOW_DISCOVER";

    public static void launch(Bundle bundle) {
        Intent intent = new Intent(SmartHomeTvLib.getContext(), ApConfigService.class);
        intent.putExtras(bundle);
        SmartHomeTvLib.getContext().startService(intent);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        presenter = new ApConfigPresenterImpl();
        view = new ApConfigViewImpl();
        model = new ApConfigModelImpl();
        view.create(this, presenter);
        presenter.create(this, view, model);
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.i(TAG, "onStartCommand: ");
        presenter.start(intent);
        return Service.START_NOT_STICKY;
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.i("apconfig", "onDestroy: ");
        if (presenter != null) {
            presenter.destroy();
        }
        if (model != null) {
            model.destroy();
        }
        if (view != null) {
            view.destroy();
        }
        StartApConfigModel.isConfiging = false;
    }
}
