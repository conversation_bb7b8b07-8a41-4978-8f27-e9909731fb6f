package com.skyworth.smarthome.service.model;

import com.coocaa.app.core.http.HttpServiceManager;
import com.skyworth.smarthome.common.bean.DeviceInfo;
import com.skyworth.smarthome.common.bean.DeviceListHttpBean;
import com.skyworth.smarthome.common.bean.RedirectHttpBean;
import com.skyworth.smarthome.common.http.SmartHomeHttpService;
import com.skyworth.smarthome.common.model.AppData;
import com.skyworth.smarthome.common.util.LogUtil;
import com.smarthome.common.model.SmartBaseData;
import com.smarthome.common.utils.Constants;
import com.smarthome.common.utils.EmptyUtils;
import com.swaiot.aiotlib.AiotLibSDK;
import com.swaiot.aiotlib.common.base.IResult;
import com.swaiot.aiotlib.device.IDevice;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;


/**
 * 智慧家庭主要Model
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/2/1.
 */

public class AIOTModel implements IAIOTModel {

    @Override
    public void getAppConfig() {
        HttpServiceManager.Companion.call(SmartHomeHttpService.SERVICE.getRedirect(Constants.REDIRECT), new Function1<HttpServiceManager.ERROR, Unit>() {
            @Override
            public Unit invoke(HttpServiceManager.ERROR error) {
                LogUtil.androidLog( "loadRedirect onError: " + error.getMsg());
                return Unit.INSTANCE;
            }
        }, new Function1<SmartBaseData<RedirectHttpBean>, Unit>() {
            @Override
            public Unit invoke(SmartBaseData<RedirectHttpBean> baseData) {
                 LogUtil.androidLog( "loadRedirect: onSuccess.");
                if (baseData.data != null && EmptyUtils.isNotEmpty(baseData.data.redirect) && EmptyUtils.isNotEmpty(baseData.data.redirect.qrcode_content)) {
                    AppData.getInstance().setVHomeDownloadUrl(baseData.data.redirect.qrcode_content);
                }
                return Unit.INSTANCE;
            }
        });
    }

    @Override
    public void getDeviceList(String familyId) {
        AiotLibSDK.getDefault().getDeviceImpl().getDeviceList(familyId,null);
    }

    @Override
    public void getSceneList() {
        AiotLibSDK.getDefault().getSceneImp().getSceneList(null);
    }

    @Override
    public void getFamilyList() {
        AiotLibSDK.getDefault().getFamilyImp().getFamilyList(null);
    }

    @Override
    public void getFamilyStatusData(String familyId) {
        AiotLibSDK.getDefault().getFamilyImp().getFamilyStatusData(familyId,null);
    }

    @Override
    public boolean controlDevice(String device_id, Map<String, String> status) {
        AiotLibSDK.getDefault().getDeviceImpl().controlDevice(device_id,status,null);
        return false;
    }

    @Override
    public boolean controlScene(String cmd, String scene_id) {
        AiotLibSDK.getDefault().getSceneImp().controlScene(cmd,scene_id,null);
        return true;
    }

    @Override
    public void cancelNewDeviceMark(String deviceId, IDevice.IControlResult result) {
        AiotLibSDK.getDefault().getDeviceImpl().cancelNewDeviceMark(deviceId,result);
    }

    @Override
    public void cancelNewSceneMark(String sceneId, IResult result) {
        AiotLibSDK.getDefault().getSceneImp().cancelNewSceneMark(sceneId,result);
    }

    @Override
    public DeviceListHttpBean transformDeviceList(){
        DeviceListHttpBean deviceListHttpBean = null;
        List<DeviceInfo> deviceList = ISmartDeviceDataModel.INSTANCE.getCacheSmartDeviceList();
        if(EmptyUtils.isNotEmpty(deviceList)){
            deviceListHttpBean = new DeviceListHttpBean();
            DeviceListHttpBean.DeviceDetailData detailData = null;
            deviceListHttpBean.device_list = new ArrayList<>();
            for(DeviceInfo item:deviceList){
                detailData = new DeviceListHttpBean.DeviceDetailData();
                detailData.device_id = item.device_id;
                detailData.device_name = item.device_name;
                detailData.device_icon = item.device_icon;
                detailData.device_tags = new ArrayList<>();
                detailData.device_tags.add(item.device_position);
                detailData.brand = item.product_brand;
                detailData.detail_layout = item.detail_layout;
                detailData.status = item.report_status;
                detailData.status_desc = item.status_show;
                detailData.is_infrared = item.is_infrared;
                detailData.device_type_id = item.product_type_id;
                detailData.discoverNetworkDevice = item.discoverNetworkDevice;
                detailData.online_status = item.online_status+"";
                detailData.is_unBind = item.is_unBind;
                detailData.isNotConnectedNetwork = item.isNotConnectedNetwork;
                detailData.voice_tips = item.voice_tips;
                deviceListHttpBean.device_list.add(detailData);
            }
        }
        return deviceListHttpBean;
    }
}
