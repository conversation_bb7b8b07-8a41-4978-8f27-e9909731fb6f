package com.skyworth.smarthome.voicehandle;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.skyworth.smarthome.common.util.LogUtil;
import com.skyworth.smarthome.voicehandle.model.AIOTVoiceHandlerModel;
import com.skyworth.smarthome.voicehandle.model.HelpGuideHandlerModel;
import com.skyworth.smarthome.voicehandle.model.IVoiceHandlerModel;
import com.skyworth.smarthome.voicehandle.model.OtherVoiceHandlerModel;

/**
 * Describe:语音处理相关广播
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/1/30
 */
public class VoiceHandleReceiver extends BroadcastReceiver {
    private IVoiceHandlerModel mIaiotVoiceHanlerModel;
    public static final String ACTION_SRTNJ_VOICE_OUTCMD = "com.skyworth.srtnj.action.voice.outcmd";//AIOT指令
    public static final String ACTION_SMARTHOME_VOICEHANDLER = "com.skyworth.smarthome_tv.voicehandler";//其他控制指令
    public static final String ACTION_SMARTHOME_HELPHANDLER = "com.skyworth.smarthome_tv.helphandler";// 新手学习页面中《帮助引导页面》跳转

    @Override
    public void onReceive(final Context context, final Intent intent) {
        LogUtil.androidLog("VoiceHandler action:" + intent.getAction());
        switch (intent.getAction()) {
            case ACTION_SMARTHOME_VOICEHANDLER:
                mIaiotVoiceHanlerModel = new OtherVoiceHandlerModel();
                break;
            case ACTION_SRTNJ_VOICE_OUTCMD:
                mIaiotVoiceHanlerModel = new AIOTVoiceHandlerModel();
                break;
            case ACTION_SMARTHOME_HELPHANDLER:
                mIaiotVoiceHanlerModel = new HelpGuideHandlerModel(context);
                break;
            default:
                break;
        }
        mIaiotVoiceHanlerModel.handleCmd(intent);
    }
}
