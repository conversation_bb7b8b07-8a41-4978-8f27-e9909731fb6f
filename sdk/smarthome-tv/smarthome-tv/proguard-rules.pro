# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with J<PERSON>, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

#---------------------smarthome-app----------start-------
-keep class com.skyworth.smarthome.common.bean.**{*;}
-keep class com.skyworth.smarthome.common.http.**{*;}
-keep class com.skyworth.smarthome.common.event.**{*;}
-keep class com.skyworth.smarthome.common.ui.**{*;}
-keep class com.skyworth.smarthome.home.smartdevice.controlpanel.common.**{*;}

-keep class com.smarthome.common.account.**{*;}
-keep class com.smarthome.common.model.**{*;}

-dontwarn com.skyworth.smarthome_tv.devices.discover.view.NotScannedHelpView
-dontwarn com.skyworth.smarthome_tv.infrared.controldevices.view.InfraredGuideView

#kotlin
-dontwarn kotlin.**
-keep class kotlin.** {*;}

#---MovieStartApi------------
-keep class com.coocaa.moviestartapi.data.**{*;}

#---zxing.jar---
-keep class com.google.zxing.**{*;}

#fresco
-keep class com.facebook.**{*;}

## LogSDK
-dontwarn com.squareup.okhttp.**
-keep class com.squareup.okhttp.**{*;}
-keep public class * implements java.io.Serializable {*;}

#酷控SDK
-dontwarn com.kookong.**
-dontwarn com.lidroid.xutils.**
-dontwarn org.w3c.dom.bootstrap.DOMImplementationRegistry

#保持插件类不被清理掉
-keep class com.smarthome.plugin.**{*;}

#---------------------smarthome-app----------end-------

##---------------------smarthome-aiot-lib----------start-------
-keep class com.swaiot.aiotlib.common.bean.**{*;}
-keep class com.swaiot.aiotlib.common.http.**{*;}

#okhttp3
-dontwarn okhttp3.**
-dontwarn com.squareup.okhttp3.**
-keep class com.squareup.okhttp3.** { *;}
-dontwarn okio.**

#美的Sdk
-dontwarn com.midea.iot.sdk.**

#Gson
-dontwarn com.google.gson.**
-keep class com.google.gson.** {*;}

# 账号
-keep class retrofit2.converter.fastjson.** {*;}
-keep class com.alibaba.fastjson.**{*;}

-keep class * implements java.io.Serializable {*;}

-keep class * implements Android.os.Parcelable {
  public static final Android.os.Parcelable$Creator *;
}

#EventBus
-keep class org.greenrobot.eventbus.**{*;}
-keepattributes *Annotation*
-keepclassmembers class ** {
    @org.greenrobot.eventbus.Subscribe <methods>;
}
-keepclassmembers class ** {
    public void onEvent(**);
}
-keep enum org.greenrobot.eventbus.ThreadMode { *; }
# Only required if you use AsyncExecutor
-keepclassmembers class * extends org.greenrobot.eventbus.util.ThrowableFailureEvent {
    <init>(Java.lang.Throwable);
}

#rustlib库
-keep class com.swaiot.lib.** {*;}

#iot-channel
-keep class swaiotos.channel.iot.**{*;}

#----wisefy-------
-keep class com.isupatches.wisefy.**{*;}

#smarthome-aiot-sdk
-keep class com.swaiot.aiotlib.common.**{*;}
-keep class com.swaiot.aiotlib.IBinderPool{*;}
-keep class com.swaiot.aiotlib.devcie.**{*;}
-keep class com.swaiot.aiotlib.family.**{*;}
-keep class com.swaiot.aiotlib.scene.**{*;}
-keep class com.swaiot.aiotlib.push.**{*;}

##---------------------smarthome-aiot-lib----------end-------