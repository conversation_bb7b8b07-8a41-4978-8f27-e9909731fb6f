package com.swaiot.aiotlib.service.model;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;

import com.alibaba.fastjson.JSONObject;
import com.swaiot.aiotlib.common.model.AiotAppData;
import com.swaiot.aiotlib.common.model.AiotConstants;
import com.swaiot.aiotlib.common.util.EmptyUtils;
import com.swaiot.aiotlib.device.apconfig.module.NetworkInfoWatcher;
import com.swaiot.lib.SkyAIOTContract;

import static android.content.Context.WIFI_SERVICE;


/**
 * @ClassName: NetworkModel
 * @Author: AwenZeng
 * @CreateDate: 2020/5/16 16:42
 * @Description: 网络模块
 */
public class NetworkModel implements INetworkModel, NetworkInfoWatcher.ConnectInfoCallback {
    private Context mContext;
    private NetworkChangeListener mListener;

    private NetworkInfoWatcher mWatcher;
    private ConnectivityManager mConnectivityManager;

    public NetworkModel(Context context) {
        mContext = context;
        mWatcher = new NetworkInfoWatcher();
        mConnectivityManager = (ConnectivityManager) AiotAppData.getInstance().getContext()
                .getSystemService(Context.CONNECTIVITY_SERVICE);
    }

    @Override
    public void setNetworkChangeListener(NetworkChangeListener mListener) {
        this.mListener = mListener;
    }

    @Override
    public void registerNetReceiver() {
        mWatcher.initOnce(mContext, this);
    }

    @Override
    public void unRegisterNetReceiver() {
        mWatcher.release();
    }

    /**
     * 网络是否连接
     *
     * @return
     */
    @Override
    public boolean isNetworkConnected() {
        NetworkInfo networkInfo = mConnectivityManager.getActiveNetworkInfo();
        return networkInfo != null && networkInfo.isConnected();
    }

    @Override
    public String getCurrentWifiSSID() {
        return mWatcher.currentInfo().ssid;
    }

    @Override
    public void onNetworkInfoChanged(NetworkInfoWatcher.SimpleNetworkInfo info) {
        mListener.onNetworkChanged(info.isConnect, info.ssid);
    }

    public interface NetworkChangeListener {
        void onNetworkChanged(boolean isConnect, String ssid);
    }
}
