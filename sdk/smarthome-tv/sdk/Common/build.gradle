apply plugin: 'com.android.library'

android {
    compileSdkVersion COMPILE_SDK_VERSION
    buildToolsVersion BUILDTOOLS_VERSION

    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }


    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }

}

repositories {
    flatDir {
        dirs 'libs'
    }
}

dependencies {
    api fileTree(include: ['*.jar'], dir: 'libs')
    api "com.alibaba:fastjson:${fastjson_version}"
    implementation 'swaiotos.service:user:1.0.57'
    implementation 'swaiotos.support:log:1.0.57'
    implementation 'swaiotos.ui:common:1.0.57'
    implementation 'swaiotos:sal:1.0.57'
}
