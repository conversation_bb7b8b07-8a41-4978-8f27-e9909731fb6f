package com.smarthome.common.utils;

import android.app.Dialog;
import android.content.Context;
import android.os.Handler;
import android.os.Message;
import android.view.KeyEvent;
import android.view.WindowManager;

import com.skyworth.ui.api.SkyDialogView;
import com.smarthome.common.sal.SalImpl;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/7/29
 */
public class XNetworkDialog {

    private static Dialog dialog;
    private static MyHandler mHandler;

    public static void showConnectNetDialog(final Context context) {
        SkyDialogView skyDialogView = new SkyDialogView(context, true);
        skyDialogView.setTipsString("此操作需要网络，现在去连网？", "");
        skyDialogView.setBtnString("去连网", "取消");
        skyDialogView.setOnDialogOnKeyListener(new SkyDialogView.OnDialogOnKeyListener() {
            @Override
            public boolean onDialogOnKeyEvent(int keyCode, KeyEvent event) {
                return false;
            }

            @Override
            public void onFirstBtnOnClickEvent() {
                SalImpl.getSAL(context).showNetSettings();
                dismiss();
            }

            @Override
            public void onSecondBtnOnClickEvent() {
                dismiss();
            }
        });
        dialog = SkyDialogView.showWithDialog(context, skyDialogView);
        if (dialog.getWindow() != null) {
            dialog.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
        }
        dialog.show();
        if (mHandler == null) {
            mHandler = new MyHandler();
        }
        if (mHandler.hasMessages(0)) {
            mHandler.removeMessages(0);
        }
        mHandler.sendEmptyMessageDelayed(0, 60000);
    }

    public static void dismiss() {
        if (mHandler != null && mHandler.hasMessages(0)) {
            mHandler.removeMessages(0);
        }
        if (dialog != null && dialog.isShowing()) {
            dialog.dismiss();
        }
    }

    private static class MyHandler extends Handler {

        public MyHandler() {
        }

        @Override
        public void handleMessage(Message msg) {
            if (msg.what == 0) {
                dismiss();
            }
        }
    }
}
