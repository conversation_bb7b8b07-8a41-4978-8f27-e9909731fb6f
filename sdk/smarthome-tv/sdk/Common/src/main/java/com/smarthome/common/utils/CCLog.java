package com.smarthome.common.utils;

import android.util.Log;

/**
 * Description:<br>
 * Created by wzh on 2019/6/20 16:56.
 */
public class CCLog {
    private static String TAG = "CCLog";
    private static boolean disable = false;

    public static void init(String tag) {
        TAG = tag;
    }

    public static void disable(boolean flag) {
        disable = flag;
        i("CCLog disable switch:" + flag);
    }

    public static void i(String msg) {
        if (!disable) Log.i(TAG, assemMsg(msg));
    }

    public static void d(String msg) {
        if (!disable) Log.d(TAG, assemMsg(msg));
    }

    public static void v(String msg) {
        if (!disable) Log.v(TAG, assemMsg(msg));
    }

    public static void w(String msg) {
        if (!disable) Log.w(TAG, assemMsg(msg));
    }

    public static void e(String msg) {
        if (!disable) Log.e(TAG, assemMsg(msg));
    }

    public static void i(String tag, String msg) {
        if (!disable) Log.i(tag, assemMsg(msg));
    }

    public static void d(String tag, String msg) {
        if (!disable) Log.d(tag, assemMsg(msg));
    }

    public static void v(String tag, String msg) {
        if (!disable) Log.v(tag, assemMsg(msg));
    }

    public static void w(String tag, String msg) {
        if (!disable) Log.w(tag, assemMsg(msg));
    }

    public static void e(String tag, String msg) {
        if (!disable) Log.e(tag, assemMsg(msg));
    }

    private static String assemMsg(String msg) {
        return " CCMsg ~ [ " + msg + " ]";
    }

}
