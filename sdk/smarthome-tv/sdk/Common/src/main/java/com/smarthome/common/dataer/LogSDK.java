package com.smarthome.common.dataer;

import android.content.Context;

import com.coocaa.dataer.api.SkyDataer;
import com.smarthome.common.BuildConfig;
import com.smarthome.common.sal.SalImpl;

import java.util.Map;

/**
 * @Description: 数据采集接口
 * @Author: wzh
 * @CreateDate: 2020/5/13
 */
public class LogSDK {

    public static final String PRODUCT_ID = "SmartHome";
    //首屏-插件曝光
    public static final String EVENT_ID_PLUGIN_SHOW = "Smarthome5.0_plugin_show";
    //首屏-插件点击
    public static final String EVENT_ID_PLUGIN_CLICK = "Smarthome5.0_plugin_click";
    //首屏-未安装插件点击
    public static final String EVENT_ID_NO_PLUGIN_CLICK = "Smarthome5.0_no_plugin_click";
    //信号源-智家设备列表曝光
    public static final String EVENT_ID_LIST_SHOW = "Smarthome5.0_list_show";
    //信号源-智家设备列表点击
    public static final String EVENT_ID_LIST_CLICK = "Smarthome5.0_list_click";
    //app内插件的曝光
    public static final String EVENT_ID_APP_PLUGIN_SHOW = "Smarthome5.0_app_plugin_show";
    //app内未安装插件点击
    public static final String EVENT_ID_APP_NO_PLUGIN_CLICK = "Smarthome5.0_app_no_plugin_click";
    //设备控制
    public static final String EVENT_ID_DEVICE_CONTROL = "Smarthome5.0_device_control";
    //场景控制
    public static final String EVENT_ID_SCENE_CONTROL = "Smarthome5.0_scene_control";
    //添加设备
    public static final String EVENT_ID_ADD_DEVICE = "Smarthome5.0_add_device";
    //发现设备
    public static final String EVENT_ID_DISCOVER_DEVICE = "Smarthome5.0_discover_device";
    //开始配网
    public static final String EVENT_ID_NO_NETWORK_BUTTON = "Smarthome5.0_no_network_button";
    //网络类型
    public static final String EVENT_ID_NETWORK_TYPE = "Smarthome5.0_network_type";
    //开始配网
    public static final String EVENT_ID_NETWORK_START = "Smarthome5.0_network_start";
    //配网结束
    public static final String EVENT_ID_NETWORK_END = "Smarthome5.0_network_end";
    //配网时长
    public static final String EVENT_ID_NETWORK_TIME = "Smarthome5.0_network_time";
    //绑定设备
    public static final String EVENT_ID_DEVICE_BIND = "Smarthome5.0_device_bind";
    //服务启动
    public static final String EVENT_SERVICE_START = "Smarthome_V5.1_service_start";


    private static Thread.UncaughtExceptionHandler createUncaughtExceptionHandler() {
        return new Thread.UncaughtExceptionHandler() {
            @Override
            public void uncaughtException(Thread t, Throwable e) {
                Thread.getDefaultUncaughtExceptionHandler().uncaughtException(t, e);
            }
        };
    }

    public static void init(Context context) {
        try {
            SkyDataer.onCore()
                    .withContext(context)
                    .withCoocaaSystemConnecter(new LogSDKConnecter(context))
                    .withDebugMode(BuildConfig.DEBUG)
                    .withProductID(PRODUCT_ID)
                    .setCommonHeader(SalImpl.getSAL(context).getCommonHeader())
                    .create(createUncaughtExceptionHandler());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void submit(String eventID) {
        try {
            SkyDataer.onEvent().baseEvent()
                    .withEventID(eventID)
                    .submit();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void submit(String eventID, String key, String value) {
        try {
            SkyDataer.onEvent().baseEvent()
                    .withEventID(eventID)
                    .withParam(key, value)
                    .submit();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void submit(String eventID, Map<String, String> params) {
        try {
            SkyDataer.onEvent().baseEvent()
                    .withEventID(eventID)
                    .withParams(params)
                    .submit();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void submitSync(String eventID, Map<String, String> params) {
        try {
            SkyDataer.onEvent().baseEvent()
                    .withEventID(eventID)
                    .withParams(params)
                    .submitSync();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
