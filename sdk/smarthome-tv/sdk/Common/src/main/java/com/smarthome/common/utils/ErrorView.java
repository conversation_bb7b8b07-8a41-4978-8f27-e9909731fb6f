package com.smarthome.common.utils;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.skyworth.ui.api.widget.SimpleFocusDrawable;
import com.skyworth.util.Util;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/8/25
 */
public class ErrorView extends LinearLayout {

    private TextView mErrorMsg;
    private Button mButton;
    private SimpleFocusDrawable mBtnFocusBg;

    public ErrorView(Context context) {
        super(context);
        mBtnFocusBg = new SimpleFocusDrawable(getContext()).setRadius(Util.Div(50));
        setOrientation(VERTICAL);
        setGravity(Gravity.CENTER);
        mErrorMsg = new TextView(getContext());
        mErrorMsg.setTextColor(Color.parseColor("#66FFFFFF"));
        mErrorMsg.setTextSize(Util.Dpi(32));
        LinearLayout.LayoutParams ll = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        ll.gravity = Gravity.CENTER;
        addView(mErrorMsg, ll);
        mButton = new Button(getContext());
        mButton.setGravity(Gravity.CENTER);
        mButton.setTextSize(Util.Dpi(32));
        mButton.setTextColor(Color.WHITE);
        mButton.getPaint().setFakeBoldText(true);
        mButton.setBackground(mBtnFocusBg);
        mButton.setText("刷新");
        ll = new LinearLayout.LayoutParams(Util.Div(460), Util.Div(80));
        ll.topMargin = Util.Div(50);
        addView(mButton, ll);
        mButton.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View view, boolean b) {
                mBtnFocusBg.setFocus(b);
                mButton.setTextColor(b ? Color.BLACK : Color.WHITE);
            }
        });
    }

    public boolean getFocus() {
        return mButton.requestFocus();
    }

    public ErrorView setErrorMsgColor(int color) {
        mErrorMsg.setTextColor(color);
        return this;
    }

    public ErrorView setButtonClickListener(OnClickListener onClickListener) {
        mButton.setOnClickListener(onClickListener);
        return this;
    }

    public ErrorView setButtonOnKeyListener(OnKeyListener onKeyListener) {
        mButton.setOnKeyListener(onKeyListener);
        return this;
    }

    public View getButtonView() {
        return mButton;
    }

    public ErrorView setErrorMsg(String msg) {
        mErrorMsg.setText(msg);
        return this;
    }

    public ErrorView setBtnText(String btnText) {
        mButton.setText(btnText);
        return this;
    }
}
