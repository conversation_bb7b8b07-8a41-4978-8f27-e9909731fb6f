package com.smarthome.common.sal;


import android.content.ComponentName;
import android.content.Context;
import android.util.Log;

import com.coocaa.moviestartapi.SkyPlayerApi;
import com.coocaa.moviestartapi.utils.SkyPlayerApiUtils;
import com.skyworth.framework.skysdk.ipc.SkyApplication;
import com.tianci.framework.player.SkyPlayerItem;
import com.tianci.user.api.SkyUserApi;
import com.tianci.user.data.AccountUtils;
import com.tianci.user.data.UserCmdDefine;

import java.util.Map;

import swaiotos.sal.SalModule;
import swaiotos.sal.audio.CCAudioMode;
import swaiotos.sal.audio.IAudio;
import swaiotos.sal.hardware.IIr;
import swaiotos.sal.hardware.IRLearnListener;
import swaiotos.sal.hardware.IScreen;
import swaiotos.sal.network.INetwork;
import swaiotos.sal.network.wifi.CCWifiItem;
import swaiotos.sal.network.wifi.IWifi;
import swaiotos.sal.picture.CCPictureMode;
import swaiotos.sal.picture.IPicture;
import swaiotos.sal.platform.IDeviceInfo;
import swaiotos.sal.platform.ISystemInfo;
import swaiotos.sal.setting.ISetting;
import swaiotos.sal.system.IScreenshotListener;
import swaiotos.sal.system.ISystem;
import swaiotos.sal.webservice.SALCommonHeader;

public class SalImpl implements ISal {
    private static SalImpl SAL = null;
    private static final String TAG = "sal";

    private Context mContext;
    private ISetting mISetting = null;
    private IDeviceInfo mIDeviceInfo = null;
    private ISystemInfo mISystemInfo = null;
    private ISystem mSystem = null;
    private SkyUserApi mSkyUserApi = null;
    private IPicture mpic = null;
    private IAudio mAudio = null;
    private IScreen mscreen = null;
    private INetwork mNetwork = null;
    private SkyPlayerApi mSkyPlayerApi = null;
    private IIr mIIR = null;
    private IWifi mWifi = null;

    public static SalImpl getSAL(Context c) {
        if (SAL == null) {
            SAL = new SalImpl(c.getApplicationContext());
        }
        return SAL;
    }

    public SalImpl(Context c) {
        mContext = c;
        swaiotos.sal.SAL.init(c);
        SkyApplication.init(c);
        mISetting = swaiotos.sal.SAL.getModule(c, SalModule.SETTING);
        mIDeviceInfo = swaiotos.sal.SAL.getModule(c, SalModule.DEVICE_INFO);
        mISystemInfo = swaiotos.sal.SAL.getModule(c, SalModule.SYSTEM_INFO);
        mSystem = swaiotos.sal.SAL.getModule(c, SalModule.SYSTEM);
        mpic = swaiotos.sal.SAL.getModule(c, SalModule.PICTURE);
        mAudio = swaiotos.sal.SAL.getModule(c, SalModule.AUDIO);
        mscreen = swaiotos.sal.SAL.getModule(c, SalModule.SCREEN);
        mNetwork = swaiotos.sal.SAL.getModule(c, SalModule.NETWORK);
        mSkyUserApi = new SkyUserApi(c);
        mIIR = swaiotos.sal.SAL.getModule(c, SalModule.IR);
        mWifi = swaiotos.sal.SAL.getModule(c, SalModule.WIFI);
//        mSkyPlayerApi = new SkyPlayerApi(c,playerListener);
    }

    private SkyPlayerApiUtils.PlayerApiConnectStateListener playerListener = new SkyPlayerApiUtils.PlayerApiConnectStateListener() {
        @Override
        public void onStateChange(SkyPlayerApiUtils.PlayerApiConnectState state, String s) {

        }
    };

    private void initPlayer(){
        mSkyPlayerApi = new SkyPlayerApi(mContext, SkyApplication.getListener(), playerListener);
    }

    @Override
    public String getDeviceChip() {
        return mIDeviceInfo.getChip();
    }

    @Override
    public String getDeviceModel() {
        return mIDeviceInfo.getModel();
    }

    @Override
    public String getDeviceName() {
        return mSystem.getDeviceName();
    }

    @Override
    public String getActiveID() {
        return mSystem.getActiveId();
    }

    @Override
    public String getBarcode() {
        return mIDeviceInfo.getBarCode();
    }

    @Override
    public Map<String, String> getCommonHeader() {
        return SALCommonHeader.getCommonHeaderMap(mContext);
    }

    @Override
    public boolean startNetSetting() {
        mISetting.startNetSettings();
        return true;
    }

    @Override
    public String getSID() {
        String sid = mSystem.getSystemSessionId();
        return sid;
    }

    @Override
    public String getMAC() {
        return mIDeviceInfo.getMac();
    }

    @Override
    public String getVersionName() {
        return mISystemInfo.getVersionName();
    }

    @Override
    public long getVersionCode() {
        return mISystemInfo.getVersionCode();
    }

    @Override
    public String getDeviceBrand() {
        return mIDeviceInfo.getBrand();
    }

    @Override
    public ComponentName getCurrentLauncher() {
        return mSystem.getHomePackageName();
    }

//    @Override
//    public String getLocation() {
//        return "";
//    }

    @Override
    public boolean startRecoverSetting() {
        mISetting.startRecovery();
        return true;
    }

    @Override
    public boolean showNetSettings() {
        mISetting.startNetSettings();
        return true;
    }

    @Override
    public boolean showAccountManager() {
        SkyUserApi.showAccountManager(mContext, true);
        return true;
    }

    @Override
    public Map<String, Object> getAccoutInfo() {
        try {
            Log.i(TAG, "getAccoutInfo");
            Map<String, Object> map = mSkyUserApi.getAccoutInfo();
            try {
                for (String key : map.keySet()) {
                    Log.d(TAG, "getAccoutInfo key:" + key + ":" + map.get(key));
                }
            } catch (Exception e) {
            }
            return map;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public String getOpenID() {
        try {
            String openId = AccountUtils.getAccountValue(mSkyUserApi.getAccoutInfo(), UserCmdDefine.UserKeyDefine.KEY_OPEN_ID);
            Log.i(TAG, "getOpenID: " + openId);
            return openId;
        } catch (Exception e) {
            Log.i(TAG, "getOpenID err: " + e.getMessage());
            e.printStackTrace();
        }
        return "";
    }

    @Override
    public String getTokenId() {
        try {
            Log.i(TAG, "getTokenId");
            String tokenId = mSkyUserApi.getToken("");
            Log.i(TAG, "getTokenId: " + tokenId);
            return tokenId;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    @Override
    public String getPattern() {
        return mIDeviceInfo.getPattern();
    }

    @Override
    public boolean isStoreMode() {
        return mISystemInfo.isStoreModeOn();
    }

    @Override
    public boolean getScreenshot(int width, int height, IScreenshotListener listener) {
        mSystem.screenShot(width, height, listener);
        return true;
    }

    @Override
    public String getSoundMode() {
        return String.valueOf(mAudio.getCurSoundMode().type);
    }

    @Override
    public String getPictureMode() {
        return String.valueOf(mpic.getCurPictureMode().type);
    }

    //主页已经废弃这个功能了，所以这个可以空实现
    @Override
    public boolean isThirdAPPRecognitionSupported() {
        return false;
    }

    @Override
    public void setSoundMode(String mode) {
        mAudio.setSoundMode(new CCAudioMode(Integer.valueOf(mode)));
    }

    @Override
    public void setPictureMode(String mode) {
        mpic.setPictureMode(new CCPictureMode(Integer.valueOf(mode)));
    }

    @Override
    public boolean isOLED() {
        return mscreen.isOLED();
    }

    @Override
    public boolean startPlayer(String name, String url, boolean needHistory) {
        if(null == mSkyPlayerApi){
            initPlayer();
        }
        return mSkyPlayerApi.startPlayer(name,url,needHistory);
    }

    @Override
    public boolean startMedia(String url, String name, String mediaType) {
        if(null == mSkyPlayerApi){
            initPlayer();
        }
        return mSkyPlayerApi.startPlayer(name,url,false);
    }

    @Override
    public int addNetListener(INetwork.INetworkListener listener) {
        return mNetwork.addNetListener(listener);
    }

    @Override
    public int removeNetListener(INetwork.INetworkListener listener) {
        return mNetwork.removeNetListener(listener);
    }

    @Override
    public void emulateKey(int key) {
        mSystem.invokeKey(key);
    }

    @Override
    public boolean isVolumeMute() {
        return mAudio.isMute(mContext);
    }

    @Override
    public void setVolume(int volume) {
        mAudio.setVolume(volume);
    }

    @Override
    public int getVolume() {
        return mAudio.getVolume();
    }

    @Override
    public void pausePlayer() {
        if(null == mSkyPlayerApi){
            initPlayer();
        }
        mSkyPlayerApi.pause();
    }

    @Override
    public int getCurrentPosition() {
        if(null == mSkyPlayerApi){
            initPlayer();
        }
        return mSkyPlayerApi.getCurrentPosition();
    }

    @Override
    public SkyPlayerItem getCurrentPlayerItem() {
        if(null == mSkyPlayerApi){
            initPlayer();
        }
        return mSkyPlayerApi.getCurrentPlayerItem();
    }

    @Override
    public void SendInfraredCode(byte[] code) {
        try {
            mIIR.sendInfraredCode(code);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public void setLearnInfraredCallBack(IRLearnListener listener, boolean register) {
        try {
            mIIR.setLearnInfraredCallBack(mContext,listener,register);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public void connectWifiByDhcp(CCWifiItem wifiAPItem) {
        mWifi.connectWifi(wifiAPItem);
    }

    @Override
    public void onDestory() {
    }
}
