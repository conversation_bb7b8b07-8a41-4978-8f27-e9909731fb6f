package com.smarthome.common.sal;

import android.content.ComponentName;


import com.tianci.framework.player.SkyPlayerItem;

import java.util.Map;

import swaiotos.sal.hardware.IRLearnListener;
import swaiotos.sal.network.INetwork;
import swaiotos.sal.network.wifi.CCWifiItem;
import swaiotos.sal.system.IScreenshotListener;

public interface ISal {
    String getDeviceChip();

    String getDeviceModel();

    String getDeviceName();

    String getActiveID();

    String getBarcode();

    Map<String, String> getCommonHeader();

    boolean startNetSetting();

    String getSID();

    String getMAC();

    String getVersionName();

    long getVersionCode();

    String getDeviceBrand();

    ComponentName getCurrentLauncher();

//    String getLocation();

    boolean startRecoverSetting();

    boolean showNetSettings();

    boolean showAccountManager();

    Map<String, Object> getAccoutInfo();

    String getOpenID();

    String getTokenId();

    String getPattern();

    boolean isStoreMode();

    boolean getScreenshot(int width, int height, IScreenshotListener listener);

    String getSoundMode();

    String getPictureMode();

    boolean isThirdAPPRecognitionSupported();

    void setSoundMode(String mode);

    void setPictureMode(String mode);

    boolean isOLED();

    boolean startPlayer(String name, String url, boolean needHistory);

    boolean startMedia(String url, String name, String mediaType);

    // net settings
    int addNetListener(INetwork.INetworkListener listener);

    int removeNetListener(INetwork.INetworkListener listener);

    // controller
    void emulateKey(int key);

    boolean isVolumeMute();

    void setVolume(int volume);

    int getVolume();


    // player
    void pausePlayer();

    int getCurrentPosition();

    SkyPlayerItem getCurrentPlayerItem();

    void SendInfraredCode(byte[] code);

    void setLearnInfraredCallBack(IRLearnListener listener, boolean register);

    void connectWifiByDhcp(CCWifiItem wifiItem);

    void onDestory();

}
