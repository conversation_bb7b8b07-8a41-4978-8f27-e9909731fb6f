package com.smarthome.common.utils;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/6/24
 */
public class Constants {
    /**
     * 接口 smarthome/v1/panel-config/get-by-name  参数name取值
     */
    public static final String FEATURED_GAMEPLAY = "featured_gameplay";//玩法精选列表
    public static final String OPERATION_CARD = "operation_card";//运营卡片列表
    public static final String REDIRECT = "redirect";//跳转参数配置

    public static final String SMART_HOME_PKG = "com.skyworth.smarthome_tv";
    public static final String SP_NAME_SMARTHOME = "smarthome_sp";//智慧家庭sp文件名
    public static final String SP_KEY_FAMILY_ID = "family_id";//家庭id key
    public static final String SP_KEY_SCREEN_ID = "screen_id";//本机设备id key

    public static final String KEY_DEVICE_ID = "DEVICE_ID";//intent 传递deviceid的key值
    public static final String DO_WHAT_ADD_DEVICE = "ADDDEVICE";//启动添加设备流程


    public static final String BROADCAST_ACTION_NET_CHANGE = "android.net.conn.CONNECTIVITY_CHANGE";
}
