package com.smarthome.common.dataer;


import android.content.Context;

import com.coocaa.dataer.api.ccc.CoocaaSystemConnecter;
import com.smarthome.common.sal.SalImpl;

/**
 * @Description:
 * @Author: wzh
 * @CreateDate: 2020/5/13
 */
public class LogSDKConnecter implements CoocaaSystemConnecter {

    private Context mContext;

    public LogSDKConnecter(Context context) {
        mContext = context;
    }

    @Override
    public String getOpenID() {
        return SalImpl.getSAL(mContext).getOpenID();
    }

    @Override
    public String getSID() {
        return SalImpl.getSAL(mContext).getSID();
    }

    @Override
    public String getActiveID() {
        return SalImpl.getSAL(mContext).getActiveID();
    }

    @Override
    public String getBarcode() {
        return SalImpl.getSAL(mContext).getBarcode();
    }

    @Override
    public String getMac() {
        return SalImpl.getSAL(mContext).getMAC();
    }

    @Override
    public String getVersionName() {
        return SalImpl.getSAL(mContext).getVersionName();
    }

    @Override
    public int getVersionCode() {
        return (int) SalImpl.getSAL(mContext).getVersionCode();
    }

    @Override
    public String getDeviceBrand() {
        return SalImpl.getSAL(mContext).getDeviceBrand();
    }

    @Override
    public String getDeviceModel() {
        return SalImpl.getSAL(mContext).getDeviceModel();
    }

    @Override
    public String getDeviceChip() {
        return SalImpl.getSAL(mContext).getDeviceChip();
    }
}
