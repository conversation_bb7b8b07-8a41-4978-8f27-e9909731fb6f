package com.smarthome.common.dataer;

/**
 * Describe:数据统计辅助类
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/1/9
 */
public class DataHelpInfo {

    private static DataHelpInfo instance;

    private long deviceApconfigTime;//设备配网时间
    private String discoverDeviceOrigin;//发现设备来源
    private String controlDetail;//控制设备内容

    public static DataHelpInfo getInstance() {
        if (instance == null) {
            synchronized (DataHelpInfo.class) {
                if (instance == null) {
                    instance = new DataHelpInfo();
                }
            }
        }
        return instance;
    }


    public String getDiscoverDeviceOrigin() {
        return discoverDeviceOrigin;
    }

    public void setDiscoverDeviceOrigin(String discoverDeviceOrigin) {
        this.discoverDeviceOrigin = discoverDeviceOrigin;
    }

    public long getDeviceApconfigTime() {
        return deviceApconfigTime;
    }

    public void setDeviceApconfigTime(long deviceApconfigTime) {
        this.deviceApconfigTime = deviceApconfigTime;
    }

    public String getControlDetail() {
        return controlDetail;
    }

    public void setControlDetail(String controlDetail) {
        this.controlDetail = controlDetail;
    }
}
