// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        maven { url 'http://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url 'http://*************:8080/nexus/content/repositories/ClientApp/' }
        google()
        jcenter()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:3.6.2'
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.3.10'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        maven { url 'http://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url "http://*************:8080/nexus/content/repositories/ClientApp/" }
        google()
        jcenter()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

ext {
    platform = "TV"
    COMPILE_SDK_VERSION = 'android-28'
    MIN_SDK_VERSION = '17'
    TARGET_SDK_VERSION = '29'
    BUILDTOOLS_VERSION = '29.0.3'

    kotlin_version = '1.1.3-2'
    anko_version = '0.10.1'
    mockito_android_version = '2.7.9'

    fastjson_version = '1.1.70.android'
    okhttp3_version = '3.6.0'
    okhttp3_logging_interceptor_version = '3.6.0'
    retrofit2_version = '2.1.0'
    retrofit2_converter_fastjson_android_version = '2.1.0'
    zxing_core_version = '3.4.0'
    nanohttpd_webserver = '2.3.1'

    def sign_file = rootProject.file("sign/sign_config.property")
    sign_config = new Properties()
    sign_config.load(new FileInputStream(sign_file))
}