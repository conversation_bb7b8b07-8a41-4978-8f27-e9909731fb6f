File getProjectDir(String dir) {
    return new File("${buildscript.sourceFile.getParent()}/${dir}")
}

include ':smarthome-tv'
project (":smarthome-tv").projectDir = getProjectDir("smarthome-tv")

include ':Common'
project(":Common").projectDir = getProjectDir("sdk/Common")

if (getProjectDir("sdk/smarthome-aiot/aiot_settings.gradle").exists())
    apply from: "sdk/smarthome-aiot/aiot_settings.gradle"

