rootProject.name = 'NewTV_SmartHome'
include ':app'

File getProjectDir(String dir) {
    return new File("${buildscript.sourceFile.getParent()}/${dir}")
}

//component/...
include ':MainPagePlugin'
project(":MainPagePlugin").projectDir = getProjectDir("component/MainPagePluginInterface")
include "PluginManager"
project(":PluginManager").projectDir = getProjectDir("component/PluginManager")
include "CCOSPluginSDK"
project(":CCOSPluginSDK").projectDir = getProjectDir("component/CCOSPluginSDK")

//sdk/...
include ":TvLauncherPluginInterface"
project(":TvLauncherPluginInterface").projectDir = getProjectDir("sdk/TvLauncherPluginInterface")
include ":UISDK"
project(":UISDK").projectDir = getProjectDir("sdk/OperateFramework8.0/app")


if (getProjectDir("sdk/smarthome-tv/aiot_settings.gradle").exists())
    apply from: "sdk/smarthome-tv/aiot_settings.gradle"

if (getProjectDir("sdk/SmartHomePluginInterface/cc_settings.gradle").exists())
    apply from: "sdk/SmartHomePluginInterface/cc_settings.gradle"

if (getProjectDir("sdk/Smart_Mall/cc_settings.gradle").exists())
    apply from: "sdk/Smart_Mall/cc_settings.gradle"
